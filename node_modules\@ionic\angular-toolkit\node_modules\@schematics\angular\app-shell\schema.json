{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularAppShell", "title": "Angular AppShell Options Schema", "type": "object", "description": "Generates an application shell for running a server-side version of an app.", "additionalProperties": false, "properties": {"project": {"type": "string", "description": "The name of the related client app.", "$default": {"$source": "projectName"}}, "route": {"type": "string", "description": "Route path used to produce the application shell.", "default": "shell"}, "appId": {"type": "string", "format": "html-selector", "description": "The application ID to use in withServerTransition().", "default": "serverApp"}, "main": {"type": "string", "description": "The name of the main entry-point file.", "default": "main.server.ts"}, "rootModuleFileName": {"type": "string", "description": "The name of the root module file", "default": "app.server.module.ts"}, "rootModuleClassName": {"type": "string", "format": "html-selector", "description": "The name of the root module class.", "default": "AppServerModule"}}, "required": ["project"]}