{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function hasLift(source) {\n  return isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexport function operate(init) {\n  return source => {\n    if (hasLift(source)) {\n      return source.lift(function (liftedSource) {\n        try {\n          return init(liftedSource, this);\n        } catch (err) {\n          this.error(err);\n        }\n      });\n    }\n    throw new TypeError('Unable to lift unknown Observable type');\n  };\n}\n//# sourceMappingURL=lift.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}