"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateBufferV2Enabled = void 0;
function isEnabled(variable) {
    return variable === '1' || variable.toLowerCase() === 'true';
}
function isPresent(variable) {
    return typeof variable === 'string' && variable !== '';
}
// Use UpdateBuffer2, which uses magic-string internally.
// TODO: Switch this for the next major release to use UpdateBuffer2 by default.
const updateBufferV2 = process.env['NG_UPDATE_BUFFER_V2'];
exports.updateBufferV2Enabled = isPresent(updateBufferV2) && isEnabled(updateBufferV2);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZW52aXJvbm1lbnQtb3B0aW9ucy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2FuZ3VsYXJfZGV2a2l0L3NjaGVtYXRpY3Mvc3JjL3V0aWxpdHkvZW52aXJvbm1lbnQtb3B0aW9ucy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7OztHQU1HOzs7QUFFSCxTQUFTLFNBQVMsQ0FBQyxRQUFnQjtJQUNqQyxPQUFPLFFBQVEsS0FBSyxHQUFHLElBQUksUUFBUSxDQUFDLFdBQVcsRUFBRSxLQUFLLE1BQU0sQ0FBQztBQUMvRCxDQUFDO0FBRUQsU0FBUyxTQUFTLENBQUMsUUFBNEI7SUFDN0MsT0FBTyxPQUFPLFFBQVEsS0FBSyxRQUFRLElBQUksUUFBUSxLQUFLLEVBQUUsQ0FBQztBQUN6RCxDQUFDO0FBRUQseURBQXlEO0FBQ3pELGdGQUFnRjtBQUNoRixNQUFNLGNBQWMsR0FBRyxPQUFPLENBQUMsR0FBRyxDQUFDLHFCQUFxQixDQUFDLENBQUM7QUFDN0MsUUFBQSxxQkFBcUIsR0FBRyxTQUFTLENBQUMsY0FBYyxDQUFDLElBQUksU0FBUyxDQUFDLGNBQWMsQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmZ1bmN0aW9uIGlzRW5hYmxlZCh2YXJpYWJsZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gIHJldHVybiB2YXJpYWJsZSA9PT0gJzEnIHx8IHZhcmlhYmxlLnRvTG93ZXJDYXNlKCkgPT09ICd0cnVlJztcbn1cblxuZnVuY3Rpb24gaXNQcmVzZW50KHZhcmlhYmxlOiBzdHJpbmcgfCB1bmRlZmluZWQpOiB2YXJpYWJsZSBpcyBzdHJpbmcge1xuICByZXR1cm4gdHlwZW9mIHZhcmlhYmxlID09PSAnc3RyaW5nJyAmJiB2YXJpYWJsZSAhPT0gJyc7XG59XG5cbi8vIFVzZSBVcGRhdGVCdWZmZXIyLCB3aGljaCB1c2VzIG1hZ2ljLXN0cmluZyBpbnRlcm5hbGx5LlxuLy8gVE9ETzogU3dpdGNoIHRoaXMgZm9yIHRoZSBuZXh0IG1ham9yIHJlbGVhc2UgdG8gdXNlIFVwZGF0ZUJ1ZmZlcjIgYnkgZGVmYXVsdC5cbmNvbnN0IHVwZGF0ZUJ1ZmZlclYyID0gcHJvY2Vzcy5lbnZbJ05HX1VQREFURV9CVUZGRVJfVjInXTtcbmV4cG9ydCBjb25zdCB1cGRhdGVCdWZmZXJWMkVuYWJsZWQgPSBpc1ByZXNlbnQodXBkYXRlQnVmZmVyVjIpICYmIGlzRW5hYmxlZCh1cGRhdGVCdWZmZXJWMik7XG4iXX0=