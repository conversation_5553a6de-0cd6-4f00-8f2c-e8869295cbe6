{"version": 3, "file": "ionic-angular-common.mjs", "sources": ["../../common/src/providers/menu-controller.ts", "../../common/src/providers/dom-controller.ts", "../../common/src/providers/platform.ts", "../../common/src/providers/nav-controller.ts", "../../common/src/providers/config.ts", "../../common/src/directives/navigation/nav-params.ts", "../../common/src/providers/angular-delegate.ts", "../../common/src/utils/proxy.ts", "../../common/src/overlays/popover.ts", "../../common/src/overlays/modal.ts", "../../common/src/directives/navigation/stack-utils.ts", "../../common/src/directives/navigation/stack-controller.ts", "../../common/src/directives/navigation/router-outlet.ts", "../../common/src/directives/navigation/back-button.ts", "../../common/src/directives/navigation/router-link-delegate.ts", "../../common/src/directives/navigation/nav.ts", "../../common/src/directives/navigation/tabs.ts", "../../common/src/utils/util.ts", "../../common/src/directives/control-value-accessors/value-accessor.ts", "../../common/src/utils/routing.ts", "../../common/src/utils/overlay.ts", "../../common/src/ionic-angular-common.ts"], "sourcesContent": ["import type { MenuControllerI, AnimationBuilder, MenuI, Animation } from '@ionic/core/components';\n\nexport class MenuController implements MenuControllerI {\n  constructor(private menuController: MenuControllerI) {}\n\n  /**\n   * Programmatically open the Menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully opened\n   */\n  open(menuId?: string): Promise<boolean> {\n    return this.menuController.open(menuId);\n  }\n\n  /**\n   * Programmatically close the Menu. If no `menuId` is given as the first\n   * argument then it'll close any menu which is open. If a `menuId`\n   * is given then it'll close that exact menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully closed\n   */\n  close(menuId?: string): Promise<boolean> {\n    return this.menuController.close(menuId);\n  }\n\n  /**\n   * Toggle the menu. If it's closed, it will open, and if opened, it\n   * will close.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu has been toggled\n   */\n  toggle(menuId?: string): Promise<boolean> {\n    return this.menuController.toggle(menuId);\n  }\n\n  /**\n   * Used to enable or disable a menu. For example, there could be multiple\n   * left menus, but only one of them should be able to be opened at the same\n   * time. If there are multiple menus on the same side, then enabling one menu\n   * will also automatically disable all the others that are on the same side.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  enable(shouldEnable: boolean, menuId?: string): Promise<HTMLIonMenuElement | undefined> {\n    return this.menuController.enable(shouldEnable, menuId);\n  }\n\n  /**\n   * Used to enable or disable the ability to swipe open the menu.\n   * @param shouldEnable  True if it should be swipe-able, false if not.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  swipeGesture(shouldEnable: boolean, menuId?: string): Promise<HTMLIonMenuElement | undefined> {\n    return this.menuController.swipeGesture(shouldEnable, menuId);\n  }\n\n  /**\n   * @param [menuId] Optionally get the menu by its id, or side.\n   * @return Returns true if the specified menu is currently open, otherwise false.\n   * If the menuId is not specified, it returns true if ANY menu is currenly open.\n   */\n  isOpen(menuId?: string): Promise<boolean> {\n    return this.menuController.isOpen(menuId);\n  }\n\n  /**\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns true if the menu is currently enabled, otherwise false.\n   */\n  isEnabled(menuId?: string): Promise<boolean> {\n    return this.menuController.isEnabled(menuId);\n  }\n\n  /**\n   * Used to get a menu instance. If a `menuId` is not provided then it'll\n   * return the first menu found. If a `menuId` is `left` or `right`, then\n   * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n   * provided, then it'll try to find the menu using the menu's `id`\n   * property. If a menu is not found then it'll return `null`.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu if found, otherwise `null`.\n   */\n  get(menuId?: string): Promise<HTMLIonMenuElement | undefined> {\n    return this.menuController.get(menuId);\n  }\n\n  /**\n   * @return Returns the instance of the menu already opened, otherwise `null`.\n   */\n  getOpen(): Promise<HTMLIonMenuElement | undefined> {\n    return this.menuController.getOpen();\n  }\n\n  /**\n   * @return Returns an array of all menu instances.\n   */\n  getMenus(): Promise<HTMLIonMenuElement[]> {\n    return this.menuController.getMenus();\n  }\n\n  registerAnimation(name: string, animation: AnimationBuilder): void {\n    return this.menuController.registerAnimation(name, animation);\n  }\n\n  isAnimating(): Promise<boolean> {\n    return this.menuController.isAnimating();\n  }\n\n  _getOpenSync(): HTMLIonMenuElement | undefined {\n    return this.menuController._getOpenSync();\n  }\n\n  _createAnimation(type: string, menuCmp: MenuI): Promise<Animation> {\n    return this.menuController._createAnimation(type, menuCmp);\n  }\n\n  _register(menu: MenuI): void {\n    return this.menuController._register(menu);\n  }\n\n  _unregister(menu: MenuI): void {\n    return this.menuController._unregister(menu);\n  }\n\n  _setOpen(menu: MenuI, shouldOpen: boolean, animated: boolean): Promise<boolean> {\n    return this.menuController._setOpen(menu, shouldOpen, animated);\n  }\n}\n", "import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class Dom<PERSON>ontroller {\n  /**\n   * Schedules a task to run during the READ phase of the next frame.\n   * This task should only read the DOM, but never modify it.\n   */\n  read(cb: RafCallback): void {\n    getQueue().read(cb);\n  }\n\n  /**\n   * Schedules a task to run during the WRITE phase of the next frame.\n   * This task should write the DOM, but never READ it.\n   */\n  write(cb: RafCallback): void {\n    getQueue().write(cb);\n  }\n}\n\nconst getQueue = () => {\n  const win = typeof (window as any) !== 'undefined' ? window : (null as any);\n\n  if (win != null) {\n    const Ionic = win.Ionic;\n    if (Ionic?.queue) {\n      return Ionic.queue;\n    }\n\n    return {\n      read: (cb: any) => win.requestAnimationFrame(cb),\n      write: (cb: any) => win.requestAnimationFrame(cb),\n    };\n  }\n\n  return {\n    read: (cb: any) => cb(),\n    write: (cb: any) => cb(),\n  };\n};\n\nexport type RafCallback = (timeStamp?: number) => void;\n", "import { DOCUMENT } from '@angular/common';\nimport { NgZone, Inject, Injectable } from '@angular/core';\nimport { getPlatforms, isPlatform } from '@ionic/core/components';\nimport type { BackButtonEventDetail, KeyboardEventDetail, Platforms } from '@ionic/core/components';\nimport { Subscription, Subject } from 'rxjs';\n\n// TODO(FW-2827): types\n\nexport interface BackButtonEmitter extends Subject<BackButtonEventDetail> {\n  subscribeWithPriority(\n    priority: number,\n    callback: (processNextHandler: () => void) => Promise<any> | void\n  ): Subscription;\n}\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class Platform {\n  private _readyPromise: Promise<string>;\n  private win: any;\n\n  /**\n   * @hidden\n   */\n  backButton = new Subject<BackButtonEventDetail>() as BackButtonEmitter;\n\n  /**\n   * The keyboardDidShow event emits when the\n   * on-screen keyboard is presented.\n   */\n  keyboardDidShow = new Subject<KeyboardEventDetail>();\n\n  /**\n   * The keyboardDidHide event emits when the\n   * on-screen keyboard is hidden.\n   */\n  keyboardDidHide = new Subject<void>();\n\n  /**\n   * The pause event emits when the native platform puts the application\n   * into the background, typically when the user switches to a different\n   * application. This event would emit when a Cordova app is put into\n   * the background, however, it would not fire on a standard web browser.\n   */\n  pause = new Subject<void>();\n\n  /**\n   * The resume event emits when the native platform pulls the application\n   * out from the background. This event would emit when a Cordova app comes\n   * out from the background, however, it would not fire on a standard web browser.\n   */\n  resume = new Subject<void>();\n\n  /**\n   * The resize event emits when the browser window has changed dimensions. This\n   * could be from a browser window being physically resized, or from a device\n   * changing orientation.\n   */\n  resize = new Subject<void>();\n\n  constructor(@Inject(DOCUMENT) private doc: any, zone: NgZone) {\n    zone.run(() => {\n      this.win = doc.defaultView;\n      this.backButton.subscribeWithPriority = function (priority, callback) {\n        return this.subscribe((ev) => {\n          return ev.register(priority, (processNextHandler) => zone.run(() => callback(processNextHandler)));\n        });\n      };\n\n      proxyEvent(this.pause, doc, 'pause', zone);\n      proxyEvent(this.resume, doc, 'resume', zone);\n      proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n      proxyEvent(this.resize, this.win, 'resize', zone);\n      proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n      proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n\n      let readyResolve: (value: string) => void;\n      this._readyPromise = new Promise((res) => {\n        readyResolve = res;\n      });\n      if (this.win?.['cordova']) {\n        doc.addEventListener(\n          'deviceready',\n          () => {\n            readyResolve('cordova');\n          },\n          { once: true }\n        );\n      } else {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        readyResolve!('dom');\n      }\n    });\n  }\n\n  /**\n   * @returns returns true/false based on platform.\n   * @description\n   * Depending on the platform the user is on, `is(platformName)` will\n   * return `true` or `false`. Note that the same app can return `true`\n   * for more than one platform name. For example, an app running from\n   * an iPad would return `true` for the platform names: `mobile`,\n   * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n   * from Cordova then `cordova` would be true, and if it was running\n   * from a web browser on the iPad then `mobileweb` would be `true`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     if (this.platform.is('ios')) {\n   *       // This will only print when on iOS\n   *       console.log('I am an iOS device!');\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * | Platform Name   | Description                        |\n   * |-----------------|------------------------------------|\n   * | android         | on a device running Android.       |\n   * | capacitor       | on a device running Capacitor.     |\n   * | cordova         | on a device running Cordova.       |\n   * | ios             | on a device running iOS.           |\n   * | ipad            | on an iPad device.                 |\n   * | iphone          | on an iPhone device.               |\n   * | phablet         | on a phablet device.               |\n   * | tablet          | on a tablet device.                |\n   * | electron        | in Electron on a desktop device.   |\n   * | pwa             | as a PWA app.                      |\n   * | mobile          | on a mobile device.                |\n   * | mobileweb       | on a mobile device in a browser.   |\n   * | desktop         | on a desktop device.               |\n   * | hybrid          | is a cordova or capacitor app.     |\n   *\n   */\n  is(platformName: Platforms): boolean {\n    return isPlatform(this.win, platformName);\n  }\n\n  /**\n   * @returns the array of platforms\n   * @description\n   * Depending on what device you are on, `platforms` can return multiple values.\n   * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n   * it would return `mobile`, `ios`, and `iphone`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     // This will print an array of the current platforms\n   *     console.log(this.platform.platforms());\n   *   }\n   * }\n   * ```\n   */\n  platforms(): string[] {\n    return getPlatforms(this.win);\n  }\n\n  /**\n   * Returns a promise when the platform is ready and native functionality\n   * can be called. If the app is running from within a web browser, then\n   * the promise will resolve when the DOM is ready. When the app is running\n   * from an application engine such as Cordova, then the promise will\n   * resolve when Cordova triggers the `deviceready` event.\n   *\n   * The resolved value is the `readySource`, which states which platform\n   * ready was used. For example, when Cordova is ready, the resolved ready\n   * source is `cordova`. The default ready source value will be `dom`. The\n   * `readySource` is useful if different logic should run depending on the\n   * platform the app is running from. For example, only Cordova can execute\n   * the status bar plugin, so the web should not run status bar plugin logic.\n   *\n   * ```\n   * import { Component } from '@angular/core';\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyApp {\n   *   constructor(public platform: Platform) {\n   *     this.platform.ready().then((readySource) => {\n   *       console.log('Platform ready from', readySource);\n   *       // Platform now ready, execute any required native code\n   *     });\n   *   }\n   * }\n   * ```\n   */\n  ready(): Promise<string> {\n    return this._readyPromise;\n  }\n\n  /**\n   * Returns if this app is using right-to-left language direction or not.\n   * We recommend the app's `index.html` file already has the correct `dir`\n   * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n   * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n   */\n  get isRTL(): boolean {\n    return this.doc.dir === 'rtl';\n  }\n\n  /**\n   * Get the query string parameter\n   */\n  getQueryParam(key: string): string | null {\n    return readQueryParam(this.win.location.href, key);\n  }\n\n  /**\n   * Returns `true` if the app is in landscape mode.\n   */\n  isLandscape(): boolean {\n    return !this.isPortrait();\n  }\n\n  /**\n   * Returns `true` if the app is in portrait mode.\n   */\n  isPortrait(): boolean {\n    return this.win.matchMedia?.('(orientation: portrait)').matches;\n  }\n\n  testUserAgent(expression: string): boolean {\n    const nav = this.win.navigator;\n    return !!(nav?.userAgent && nav.userAgent.indexOf(expression) >= 0);\n  }\n\n  /**\n   * Get the current url.\n   */\n  url(): string {\n    return this.win.location.href;\n  }\n\n  /**\n   * Gets the width of the platform's viewport using `window.innerWidth`.\n   */\n  width(): number {\n    return this.win.innerWidth;\n  }\n\n  /**\n   * Gets the height of the platform's viewport using `window.innerHeight`.\n   */\n  height(): number {\n    return this.win.innerHeight;\n  }\n}\n\nconst readQueryParam = (url: string, key: string) => {\n  key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n  const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n  const results = regex.exec(url);\n  return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\n\nconst proxyEvent = <T>(emitter: Subject<T>, el: EventTarget, eventName: string, zone: NgZone) => {\n  if (el) {\n    el.addEventListener(eventName, (ev) => {\n      /**\n       * `zone.run` is required to make sure that we are running inside the Angular zone\n       * at all times. This is necessary since an app that has Capacitor will\n       * override the `document.addEventListener` with its own implementation.\n       * The override causes the event to no longer be in the Angular zone.\n       */\n      zone.run(() => {\n        // ?? cordova might emit \"null\" events\n        const value = ev != null ? (ev as any).detail : undefined;\n        emitter.next(value);\n      });\n    });\n  }\n};\n", "import { Location } from '@angular/common';\nimport { Injectable, Optional } from '@angular/core';\nimport { NavigationExtras, Router, UrlSerializer, UrlTree, NavigationStart } from '@angular/router';\nimport type { AnimationBuilder, NavDirection, RouterDirection } from '@ionic/core/components';\n\nimport { IonRouterOutlet } from '../directives/navigation/router-outlet';\n\nimport { Platform } from './platform';\n\nexport interface AnimationOptions {\n  animated?: boolean;\n  animation?: AnimationBuilder;\n  animationDirection?: 'forward' | 'back';\n}\n\nexport interface NavigationOptions extends NavigationExtras, AnimationOptions {}\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class NavController {\n  private topOutlet?: IonRouterOutlet;\n  private direction: 'forward' | 'back' | 'root' | 'auto' = DEFAULT_DIRECTION;\n  private animated?: NavDirection = DEFAULT_ANIMATED;\n  private animationBuilder?: AnimationBuilder;\n  private guessDirection: RouterDirection = 'forward';\n  private guessAnimation?: NavDirection;\n  private lastNavId = -1;\n\n  constructor(\n    platform: Platform,\n    private location: Location,\n    private serializer: UrlSerializer,\n    @Optional() private router?: Router\n  ) {\n    // Subscribe to router events to detect direction\n    if (router) {\n      router.events.subscribe((ev) => {\n        if (ev instanceof NavigationStart) {\n          const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n          this.guessDirection = id < this.lastNavId ? 'back' : 'forward';\n          this.guessAnimation = !ev.restoredState ? this.guessDirection : undefined;\n          this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n        }\n      });\n    }\n\n    // Subscribe to backButton events\n    platform.backButton.subscribeWithPriority(0, (processNextHandler) => {\n      this.pop();\n      processNextHandler();\n    });\n  }\n\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n   *\n   * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n   * and that it will show a \"forward\" animation by default.\n   *\n   * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n   * ```\n   */\n  navigateForward(url: string | UrlTree | any[], options: NavigationOptions = {}): Promise<boolean> {\n    this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('back');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n   * and that it will show a \"back\" animation by default.\n   *\n   * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n   * ```\n   */\n  navigateBack(url: string | UrlTree | any[], options: NavigationOptions = {}): Promise<boolean> {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('root');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **root** means that all existing pages in the stack will be removed,\n   * and the navigated page will become the single page in the stack.\n   *\n   * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n   * ```\n   */\n  navigateRoot(url: string | UrlTree | any[], options: NavigationOptions = {}): Promise<boolean> {\n    this.setDirection('root', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n\n  /**\n   * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n   * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n   * by default.\n   */\n  back(options: AnimationOptions = { animated: true, animationDirection: 'back' }): void {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.location.back();\n  }\n\n  /**\n   * This methods goes back in the context of Ionic's stack navigation.\n   *\n   * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n   * This is the recommended way to go back when you are using `ion-router-outlet`.\n   *\n   * Resolves to `true` if it was able to pop.\n   */\n  async pop(): Promise<boolean> {\n    let outlet = this.topOutlet;\n\n    while (outlet) {\n      if (await outlet.pop()) {\n        return true;\n      } else {\n        outlet = outlet.parentOutlet;\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * This methods specifies the direction of the next navigation performed by the Angular router.\n   *\n   * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n   *\n   * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n   */\n  setDirection(\n    direction: RouterDirection,\n    animated?: boolean,\n    animationDirection?: 'forward' | 'back',\n    animationBuilder?: AnimationBuilder\n  ): void {\n    this.direction = direction;\n    this.animated = getAnimation(direction, animated, animationDirection);\n    this.animationBuilder = animationBuilder;\n  }\n\n  /**\n   * @internal\n   */\n  setTopOutlet(outlet: IonRouterOutlet): void {\n    this.topOutlet = outlet;\n  }\n\n  /**\n   * @internal\n   */\n  consumeTransition(): {\n    direction: RouterDirection;\n    animation: NavDirection | undefined;\n    animationBuilder: AnimationBuilder | undefined;\n  } {\n    let direction: RouterDirection = 'root';\n    let animation: NavDirection | undefined;\n    const animationBuilder = this.animationBuilder;\n\n    if (this.direction === 'auto') {\n      direction = this.guessDirection;\n      animation = this.guessAnimation;\n    } else {\n      animation = this.animated;\n      direction = this.direction;\n    }\n    this.direction = DEFAULT_DIRECTION;\n    this.animated = DEFAULT_ANIMATED;\n    this.animationBuilder = undefined;\n\n    return {\n      direction,\n      animation,\n      animationBuilder,\n    };\n  }\n\n  private navigate(url: string | UrlTree | any[], options: NavigationOptions) {\n    if (Array.isArray(url)) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router!.navigate(url, options);\n    } else {\n      /**\n       * navigateByUrl ignores any properties that\n       * would change the url, so things like queryParams\n       * would be ignored unless we create a url tree\n       * More Info: https://github.com/angular/angular/issues/18798\n       */\n      const urlTree = this.serializer.parse(url.toString());\n\n      if (options.queryParams !== undefined) {\n        urlTree.queryParams = { ...options.queryParams };\n      }\n\n      if (options.fragment !== undefined) {\n        urlTree.fragment = options.fragment;\n      }\n\n      /**\n       * `navigateByUrl` will still apply `NavigationExtras` properties\n       * that do not modify the url, such as `replaceUrl` which is why\n       * `options` is passed in here.\n       */\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router!.navigateByUrl(urlTree, options);\n    }\n  }\n}\n\nconst getAnimation = (\n  direction: RouterDirection,\n  animated: boolean | undefined,\n  animationDirection: 'forward' | 'back' | undefined\n): NavDirection | undefined => {\n  if (animated === false) {\n    return undefined;\n  }\n  if (animationDirection !== undefined) {\n    return animationDirection;\n  }\n  if (direction === 'forward' || direction === 'back') {\n    return direction;\n  } else if (direction === 'root' && animated === true) {\n    return 'forward';\n  }\n  return undefined;\n};\n\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\n", "import { Injectable, InjectionToken } from '@angular/core';\nimport type { Config as CoreConfig, IonicConfig } from '@ionic/core/components';\n\nimport { IonicWindow } from '../types/interfaces';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class Config {\n  get(key: keyof IonicConfig, fallback?: any): any {\n    const c = getConfig();\n    if (c) {\n      return c.get(key, fallback);\n    }\n    return null;\n  }\n\n  getBoolean(key: keyof IonicConfig, fallback?: boolean): boolean {\n    const c = getConfig();\n    if (c) {\n      return c.getBoolean(key, fallback);\n    }\n    return false;\n  }\n\n  getNumber(key: keyof IonicConfig, fallback?: number): number {\n    const c = getConfig();\n    if (c) {\n      return c.getNumber(key, fallback);\n    }\n    return 0;\n  }\n}\n\nexport const ConfigToken = new InjectionToken<any>('USERCONFIG');\n\nconst getConfig = (): CoreConfig | null => {\n  if (typeof (window as any) !== 'undefined') {\n    const Ionic = (window as any as IonicWindow).Ionic;\n    if (Ionic?.config) {\n      return Ionic.config;\n    }\n  }\n  return null;\n};\n", "/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nexport class NavParams {\n  constructor(public data: { [key: string]: any } = {}) {}\n\n  /**\n   * Get the value of a nav-parameter for the current view\n   *\n   * ```ts\n   * import { NavParams } from 'ionic-angular';\n   *\n   * export class MyClass{\n   *  constructor(public navParams: NavParams){\n   *    // userParams is an object we have in our nav-parameters\n   *    this.navParams.get('userParams');\n   *  }\n   * }\n   * ```\n   *\n   * @param param Which param you want to look up\n   */\n  get<T = any>(param: string): T {\n    return this.data[param];\n  }\n}\n", "import {\n  ApplicationRef,\n  NgZone,\n  Injectable,\n  Injector,\n  EnvironmentInjector,\n  inject,\n  createComponent,\n  InjectionToken,\n  ComponentRef,\n} from '@angular/core';\nimport {\n  FrameworkDelegate,\n  LIFECYCLE_DID_ENTER,\n  LIFECYCLE_DID_LEAVE,\n  LIFECYCLE_WILL_ENTER,\n  LIFECYCLE_WILL_LEAVE,\n  LIFECYCLE_WILL_UNLOAD,\n} from '@ionic/core/components';\n\nimport { NavParams } from '../directives/navigation/nav-params';\n\n// TODO(FW-2827): types\n\n@Injectable()\nexport class AngularDelegate {\n  private zone = inject(NgZone);\n  private applicationRef = inject(ApplicationRef);\n\n  create(\n    environmentInjector: EnvironmentInjector,\n    injector: Injector,\n    elementReferenceKey?: string\n  ): AngularFrameworkDelegate {\n    return new AngularFrameworkDelegate(\n      environmentInjector,\n      injector,\n      this.applicationRef,\n      this.zone,\n      elementReferenceKey\n    );\n  }\n}\n\nexport class AngularFrameworkDelegate implements FrameworkDelegate {\n  private elRefMap = new WeakMap<HTMLElement, ComponentRef<any>>();\n  private elEventsMap = new WeakMap<HTMLElement, () => void>();\n\n  constructor(\n    private environmentInjector: EnvironmentInjector,\n    private injector: Injector,\n    private applicationRef: ApplicationRef,\n    private zone: NgZone,\n    private elementReferenceKey?: string\n  ) {}\n\n  attachViewToDom(container: any, component: any, params?: any, cssClasses?: string[]): Promise<any> {\n    return this.zone.run(() => {\n      return new Promise((resolve) => {\n        const componentProps = {\n          ...params,\n        };\n\n        /**\n         * Ionic Angular passes a reference to a modal\n         * or popover that can be accessed using a\n         * variable in the overlay component. If\n         * elementReferenceKey is defined, then we should\n         * pass a reference to the component using\n         * elementReferenceKey as the key.\n         */\n        if (this.elementReferenceKey !== undefined) {\n          componentProps[this.elementReferenceKey] = container;\n        }\n\n        const el = attachView(\n          this.zone,\n          this.environmentInjector,\n          this.injector,\n          this.applicationRef,\n          this.elRefMap,\n          this.elEventsMap,\n          container,\n          component,\n          componentProps,\n          cssClasses,\n          this.elementReferenceKey\n        );\n        resolve(el);\n      });\n    });\n  }\n\n  removeViewFromDom(_container: any, component: any): Promise<void> {\n    return this.zone.run(() => {\n      return new Promise((resolve) => {\n        const componentRef = this.elRefMap.get(component);\n        if (componentRef) {\n          componentRef.destroy();\n          this.elRefMap.delete(component);\n          const unbindEvents = this.elEventsMap.get(component);\n          if (unbindEvents) {\n            unbindEvents();\n            this.elEventsMap.delete(component);\n          }\n        }\n        resolve();\n      });\n    });\n  }\n}\n\nexport const attachView = (\n  zone: NgZone,\n  environmentInjector: EnvironmentInjector,\n  injector: Injector,\n  applicationRef: ApplicationRef,\n  elRefMap: WeakMap<HTMLElement, ComponentRef<any>>,\n  elEventsMap: WeakMap<HTMLElement, () => void>,\n  container: any,\n  component: any,\n  params: any,\n  cssClasses: string[] | undefined,\n  elementReferenceKey: string | undefined\n): any => {\n  /**\n   * Wraps the injector with a custom injector that\n   * provides NavParams to the component.\n   *\n   * NavParams is a legacy feature from Ionic v3 that allows\n   * Angular developers to provide data to a component\n   * and access it by providing NavParams as a dependency\n   * in the constructor.\n   *\n   * The modern approach is to access the data directly\n   * from the component's class instance.\n   */\n  const childInjector = Injector.create({\n    providers: getProviders(params),\n    parent: injector,\n  });\n\n  const componentRef = createComponent<any>(component, {\n    environmentInjector,\n    elementInjector: childInjector,\n  });\n\n  const instance = componentRef.instance;\n  const hostElement = componentRef.location.nativeElement;\n\n  if (params) {\n    /**\n     * For modals and popovers, a reference to the component is\n     * added to `params` during the call to attachViewToDom. If\n     * a reference using this name is already set, this means\n     * the app is trying to use the name as a component prop,\n     * which will cause collisions.\n     */\n    if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n      console.error(\n        `[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${\n          component.name\n        }.`\n      );\n    }\n\n    Object.assign(instance, params);\n  }\n  if (cssClasses) {\n    for (const cssClass of cssClasses) {\n      hostElement.classList.add(cssClass);\n    }\n  }\n  const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n  container.appendChild(hostElement);\n\n  applicationRef.attachView(componentRef.hostView);\n\n  elRefMap.set(hostElement, componentRef);\n  elEventsMap.set(hostElement, unbindEvents);\n  return hostElement;\n};\n\nconst LIFECYCLES = [\n  LIFECYCLE_WILL_ENTER,\n  LIFECYCLE_DID_ENTER,\n  LIFECYCLE_WILL_LEAVE,\n  LIFECYCLE_DID_LEAVE,\n  LIFECYCLE_WILL_UNLOAD,\n];\n\nexport const bindLifecycleEvents = (zone: NgZone, instance: any, element: HTMLElement): (() => void) => {\n  return zone.run(() => {\n    const unregisters = LIFECYCLES.filter((eventName) => typeof instance[eventName] === 'function').map((eventName) => {\n      const handler = (ev: any) => instance[eventName](ev.detail);\n      element.addEventListener(eventName, handler);\n      return () => element.removeEventListener(eventName, handler);\n    });\n    return () => unregisters.forEach((fn) => fn());\n  });\n};\n\nconst NavParamsToken = new InjectionToken<any>('NavParamsToken');\n\nconst getProviders = (params: { [key: string]: any }) => {\n  return [\n    {\n      provide: NavParamsToken,\n      useValue: params,\n    },\n    {\n      provide: NavParams,\n      useFactory: provideNavParamsInjectable,\n      deps: [NavParamsToken],\n    },\n  ];\n};\n\nconst provideNavParamsInjectable = (params: { [key: string]: any }) => {\n  return new NavParams(params);\n};\n", "// TODO: Is there a way we can grab this from angular-component-lib instead?\n\n/* eslint-disable */\n/* tslint:disable */\nimport { fromEvent } from 'rxjs';\n\nexport const proxyInputs = (Cmp: any, inputs: string[]) => {\n  const Prototype = Cmp.prototype;\n  inputs.forEach((item) => {\n    Object.defineProperty(Prototype, item, {\n      get() {\n        return this.el[item];\n      },\n      set(val: any) {\n        this.z.runOutsideAngular(() => (this.el[item] = val));\n      },\n    });\n  });\n};\n\nexport const proxyMethods = (Cmp: any, methods: string[]) => {\n  const Prototype = Cmp.prototype;\n  methods.forEach((methodName) => {\n    Prototype[methodName] = function () {\n      const args = arguments;\n      return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n    };\n  });\n};\n\nexport const proxyOutputs = (instance: any, el: any, events: string[]) => {\n  events.forEach((eventName) => (instance[eventName] = fromEvent(el, eventName)));\n};\n\n// tslint:disable-next-line: only-arrow-functions\nexport function ProxyCmp(opts: { defineCustomElementFn?: () => void; inputs?: any; methods?: any }) {\n  const decorator = function (cls: any) {\n    const { defineCustomElementFn, inputs, methods } = opts;\n\n    if (defineCustomElementFn !== undefined) {\n      defineCustomElementFn();\n    }\n\n    if (inputs) {\n      proxyInputs(cls, inputs);\n    }\n    if (methods) {\n      proxyMethods(cls, methods);\n    }\n    return cls;\n  };\n  return decorator;\n}\n", "import {\n  ChangeDetectorRef,\n  ContentChild,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  NgZone,\n  TemplateRef,\n} from '@angular/core';\nimport type { Components } from '@ionic/core/components';\n\nimport { ProxyCmp, proxyOutputs } from '../utils/proxy';\n\nexport declare interface IonPopover extends Components.IonPopover {\n  /**\n   * Emitted after the popover has presented.\n   */\n  ionPopoverDidPresent: EventEmitter<CustomEvent>;\n  /**\n   * Emitted before the popover has presented.\n   */\n  ionPopoverWillPresent: EventEmitter<CustomEvent>;\n  /**\n   * Emitted after the popover has dismissed.\n   */\n  ionPopoverWillDismiss: EventEmitter<CustomEvent>;\n  /**\n   * Emitted after the popover has dismissed.\n   */\n  ionPopoverDidDismiss: EventEmitter<CustomEvent>;\n  /**\n   * Emitted after the popover has presented. Shorthand for ionPopoverDidPresent.\n   */\n  didPresent: EventEmitter<CustomEvent>;\n  /**\n   * Emitted before the popover has presented. Shorthand for ionPopoverWillPresent.\n   */\n  willPresent: EventEmitter<CustomEvent>;\n  /**\n   * Emitted after the popover has presented. Shorthand for ionPopoverWillDismiss.\n   */\n  willDismiss: EventEmitter<CustomEvent>;\n  /**\n   * Emitted after the popover has dismissed. Shorthand for ionPopoverDidDismiss.\n   */\n  didDismiss: EventEmitter<CustomEvent>;\n}\n\nconst POPOVER_INPUTS = [\n  'alignment',\n  'animated',\n  'arrow',\n  'keepContentsMounted',\n  'backdropDismiss',\n  'cssClass',\n  'dismissOnSelect',\n  'enterAnimation',\n  'event',\n  'isOpen',\n  'keyboardClose',\n  'leaveAnimation',\n  'mode',\n  'showBackdrop',\n  'translucent',\n  'trigger',\n  'triggerAction',\n  'reference',\n  'size',\n  'side',\n];\n\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\n\n@ProxyCmp({\n  inputs: POPOVER_INPUTS,\n  methods: POPOVER_METHODS,\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */\n@Directive({\n  selector: 'ion-popover',\n  // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n  inputs: POPOVER_INPUTS,\n})\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nexport class IonPopover {\n  // TODO(FW-2827): type\n  @ContentChild(TemplateRef, { static: false }) template: TemplateRef<any>;\n\n  isCmpOpen = false;\n\n  protected el: HTMLElement;\n\n  constructor(c: ChangeDetectorRef, r: ElementRef, protected z: NgZone) {\n    this.el = r.nativeElement;\n\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, [\n      'ionPopoverDidPresent',\n      'ionPopoverWillPresent',\n      'ionPopoverWillDismiss',\n      'ionPopoverDidDismiss',\n      'didPresent',\n      'willPresent',\n      'willDismiss',\n      'didDismiss',\n    ]);\n  }\n}\n", "import {\n  ChangeDetectorRef,\n  ContentChild,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  NgZone,\n  TemplateRef,\n} from '@angular/core';\nimport type { Components, ModalBreakpointChangeEventDetail } from '@ionic/core/components';\n\nimport { ProxyCmp, proxyOutputs } from '../utils/proxy';\n\nexport declare interface IonModal extends Components.IonModal {\n  /**\n   * Emitted after the modal has presented.\n   **/\n  ionModalDidPresent: EventEmitter<CustomEvent>;\n  /**\n   * Emitted before the modal has presented.\n   */\n  ionModalWillPresent: EventEmitter<CustomEvent>;\n  /**\n   * Emitted before the modal has dismissed.\n   */\n  ionModalWillDismiss: EventEmitter<CustomEvent>;\n  /**\n   * Emitted after the modal has dismissed.\n   */\n  ionModalDidDismiss: EventEmitter<CustomEvent>;\n  /**\n   * Emitted after the modal breakpoint has changed.\n   */\n  ionBreakpointDidChange: EventEmitter<CustomEvent<ModalBreakpointChangeEventDetail>>;\n  /**\n   * Emitted after the modal has presented. Shorthand for ionModalDidPresent.\n   */\n  didPresent: EventEmitter<CustomEvent>;\n  /**\n   * Emitted before the modal has presented. Shorthand for ionModalWillPresent.\n   */\n  willPresent: EventEmitter<CustomEvent>;\n  /**\n   * Emitted before the modal has dismissed. Shorthand for ionModalWillDismiss.\n   */\n  willDismiss: EventEmitter<CustomEvent>;\n  /**\n   * Emitted after the modal has dismissed. Shorthand for ionModalDidDismiss.\n   */\n  didDismiss: EventEmitter<CustomEvent>;\n}\n\nconst MODAL_INPUTS = [\n  'animated',\n  'keepContentsMounted',\n  'backdropBreakpoint',\n  'backdropDismiss',\n  'breakpoints',\n  'canDismiss',\n  'cssClass',\n  'enterAnimation',\n  'event',\n  'handle',\n  'handleBehavior',\n  'initialBreakpoint',\n  'isOpen',\n  'keyboardClose',\n  'leaveAnimation',\n  'mode',\n  'presentingElement',\n  'showBackdrop',\n  'translucent',\n  'trigger',\n];\n\nconst MODAL_METHODS = [\n  'present',\n  'dismiss',\n  'onDidDismiss',\n  'onWillDismiss',\n  'setCurrentBreakpoint',\n  'getCurrentBreakpoint',\n];\n\n@ProxyCmp({\n  inputs: MODAL_INPUTS,\n  methods: MODAL_METHODS,\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */\n@Directive({\n  selector: 'ion-modal',\n  // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n  inputs: MODAL_INPUTS,\n})\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nexport class IonModal {\n  // TODO(FW-2827): type\n  @ContentChild(TemplateRef, { static: false }) template: TemplateRef<any>;\n\n  isCmpOpen = false;\n\n  protected el: HTMLElement;\n\n  constructor(c: ChangeDetectorRef, r: ElementRef, protected z: NgZone) {\n    this.el = r.nativeElement;\n\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, [\n      'ionModalDidPresent',\n      'ionModalWillPresent',\n      'ionModalWillDismiss',\n      'ionModalDidDismiss',\n      'ionBreakpointDidChange',\n      'didPresent',\n      'willPresent',\n      'willDismiss',\n      'didDismiss',\n    ]);\n  }\n}\n", "import { ComponentRef } from '@angular/core';\nimport { ActivatedRoute, NavigationExtras, Router } from '@angular/router';\nimport type { AnimationBuilder, NavDirection, RouterDirection } from '@ionic/core/components';\n\nexport const insertView = (views: RouteView[], view: RouteView, direction: RouterDirection): RouteView[] => {\n  if (direction === 'root') {\n    return setRoot(views, view);\n  } else if (direction === 'forward') {\n    return setForward(views, view);\n  } else {\n    return setBack(views, view);\n  }\n};\n\nconst setRoot = (views: RouteView[], view: RouteView) => {\n  views = views.filter((v) => v.stackId !== view.stackId);\n  views.push(view);\n  return views;\n};\n\nconst setForward = (views: RouteView[], view: RouteView) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    views = views.filter((v) => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    views.push(view);\n  }\n  return views;\n};\n\nconst setBack = (views: RouteView[], view: RouteView) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    return views.filter((v) => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    return setRoot(views, view);\n  }\n};\n\nexport const getUrl = (router: Router, activatedRoute: ActivatedRoute): string => {\n  const urlTree = router.createUrlTree(['.'], { relativeTo: activatedRoute });\n  return router.serializeUrl(urlTree);\n};\n\nexport const isTabSwitch = (enteringView: RouteView, leavingView: RouteView | undefined): boolean => {\n  if (!leavingView) {\n    return true;\n  }\n  return enteringView.stackId !== leavingView.stackId;\n};\n\nexport const computeStackId = (prefixUrl: string[] | undefined, url: string): string | undefined => {\n  if (!prefixUrl) {\n    return undefined;\n  }\n  const segments = toSegments(url);\n  for (let i = 0; i < segments.length; i++) {\n    if (i >= prefixUrl.length) {\n      return segments[i];\n    }\n    if (segments[i] !== prefixUrl[i]) {\n      return undefined;\n    }\n  }\n  return undefined;\n};\n\nexport const toSegments = (path: string): string[] => {\n  return path\n    .split('/')\n    .map((s) => s.trim())\n    .filter((s) => s !== '');\n};\n\nexport const destroyView = (view: RouteView | undefined): void => {\n  if (view) {\n    view.ref.destroy();\n    view.unlistenEvents();\n  }\n};\n\nexport interface StackWillChangeEvent {\n  enteringView: RouteView;\n  /**\n   * `true` if the event is trigged as a result of a switch\n   * between tab navigation stacks.\n   */\n  tabSwitch: boolean;\n}\n\nexport interface StackDidChangeEvent {\n  enteringView: RouteView;\n  direction: RouterDirection;\n  animation: NavDirection | undefined;\n  /**\n   * `true` if the event is trigged as a result of a switch\n   * between tab navigation stacks.\n   */\n  tabSwitch: boolean;\n}\n\n// TODO(FW-2827): types\nexport interface RouteView {\n  id: number;\n  url: string;\n  stackId: string | undefined;\n  element: HTMLElement;\n  ref: ComponentRef<any>;\n  savedData?: any;\n  savedExtras?: NavigationExtras;\n  unlistenEvents: () => void;\n  animationBuilder?: AnimationBuilder;\n}\n", "import { Location } from '@angular/common';\nimport { ComponentRef, NgZone } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport type { AnimationBuilder, RouterDirection } from '@ionic/core/components';\n\nimport { bindLifecycleEvents } from '../../providers/angular-delegate';\nimport { NavController } from '../../providers/nav-controller';\n\nimport {\n  RouteView,\n  StackDidChangeEvent,\n  computeStackId,\n  destroyView,\n  getUrl,\n  insertView,\n  isTabSwitch,\n  toSegments,\n} from './stack-utils';\n\n// TODO(FW-2827): types\n\nexport class StackController {\n  private views: RouteView[] = [];\n  private runningTask?: Promise<any>;\n  private skipTransition = false;\n  private tabsPrefix: string[] | undefined;\n  private activeView: RouteView | undefined;\n  private nextId = 0;\n\n  constructor(\n    tabsPrefix: string | undefined,\n    private containerEl: HTMLIonRouterOutletElement,\n    private router: Router,\n    private navCtrl: NavController,\n    private zone: NgZone,\n    private location: Location\n  ) {\n    this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n  }\n\n  createView(ref: ComponentRef<any>, activatedRoute: ActivatedRoute): RouteView {\n    const url = getUrl(this.router, activatedRoute);\n    const element = ref?.location?.nativeElement as HTMLElement;\n    const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n    return {\n      id: this.nextId++,\n      stackId: computeStackId(this.tabsPrefix, url),\n      unlistenEvents,\n      element,\n      ref,\n      url,\n    };\n  }\n\n  getExistingView(activatedRoute: ActivatedRoute): RouteView | undefined {\n    const activatedUrlKey = getUrl(this.router, activatedRoute);\n    const view = this.views.find((vw) => vw.url === activatedUrlKey);\n    if (view) {\n      view.ref.changeDetectorRef.reattach();\n    }\n    return view;\n  }\n\n  setActive(enteringView: RouteView): Promise<StackDidChangeEvent> {\n    const consumeResult = this.navCtrl.consumeTransition();\n    let { direction, animation, animationBuilder } = consumeResult;\n    const leavingView = this.activeView;\n    const tabSwitch = isTabSwitch(enteringView, leavingView);\n    if (tabSwitch) {\n      direction = 'back';\n      animation = undefined;\n    }\n\n    const viewsSnapshot = this.views.slice();\n\n    let currentNavigation;\n\n    const router = this.router as any;\n\n    // Angular >= 7.2.0\n    if (router.getCurrentNavigation) {\n      currentNavigation = router.getCurrentNavigation();\n\n      // Angular < 7.2.0\n    } else if (router.navigations?.value) {\n      currentNavigation = router.navigations.value;\n    }\n\n    /**\n     * If the navigation action\n     * sets `replaceUrl: true`\n     * then we need to make sure\n     * we remove the last item\n     * from our views stack\n     */\n    if (currentNavigation?.extras?.replaceUrl) {\n      if (this.views.length > 0) {\n        this.views.splice(-1, 1);\n      }\n    }\n\n    const reused = this.views.includes(enteringView);\n    const views = this.insertView(enteringView, direction);\n\n    // Trigger change detection before transition starts\n    // This will call ngOnInit() the first time too, just after the view\n    // was attached to the dom, but BEFORE the transition starts\n    if (!reused) {\n      enteringView.ref.changeDetectorRef.detectChanges();\n    }\n\n    /**\n     * If we are going back from a page that\n     * was presented using a custom animation\n     * we should default to using that\n     * unless the developer explicitly\n     * provided another animation.\n     */\n    const customAnimation = enteringView.animationBuilder;\n    if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n      animationBuilder = customAnimation;\n    }\n\n    /**\n     * Save any custom animation so that navigating\n     * back will use this custom animation by default.\n     */\n    if (leavingView) {\n      leavingView.animationBuilder = animationBuilder;\n    }\n\n    // Wait until previous transitions finish\n    return this.zone.runOutsideAngular(() => {\n      return this.wait(() => {\n        // disconnect leaving page from change detection to\n        // reduce jank during the page transition\n        if (leavingView) {\n          leavingView.ref.changeDetectorRef.detach();\n        }\n        // In case the enteringView is the same as the leavingPage we need to reattach()\n        enteringView.ref.changeDetectorRef.reattach();\n\n        return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder)\n          .then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone))\n          .then(() => ({\n            enteringView,\n            direction,\n            animation,\n            tabSwitch,\n          }));\n      });\n    });\n  }\n\n  canGoBack(deep: number, stackId = this.getActiveStackId()): boolean {\n    return this.getStack(stackId).length > deep;\n  }\n\n  pop(deep: number, stackId = this.getActiveStackId()): Promise<boolean> {\n    return this.zone.run(() => {\n      const views = this.getStack(stackId);\n      if (views.length <= deep) {\n        return Promise.resolve(false);\n      }\n      const view = views[views.length - deep - 1];\n      let url = view.url;\n\n      const viewSavedData = view.savedData;\n      if (viewSavedData) {\n        const primaryOutlet = viewSavedData.get('primary');\n        if (primaryOutlet?.route?._routerState?.snapshot.url) {\n          url = primaryOutlet.route._routerState.snapshot.url;\n        }\n      }\n      const { animationBuilder } = this.navCtrl.consumeTransition();\n      return this.navCtrl.navigateBack(url, { ...view.savedExtras, animation: animationBuilder }).then(() => true);\n    });\n  }\n\n  startBackTransition(): Promise<boolean> | Promise<void> {\n    const leavingView = this.activeView;\n    if (leavingView) {\n      const views = this.getStack(leavingView.stackId);\n      const enteringView = views[views.length - 2];\n      const customAnimation = enteringView.animationBuilder;\n\n      return this.wait(() => {\n        return this.transition(\n          enteringView, // entering view\n          leavingView, // leaving view\n          'back',\n          this.canGoBack(2),\n          true,\n          customAnimation\n        );\n      });\n    }\n    return Promise.resolve();\n  }\n\n  endBackTransition(shouldComplete: boolean): void {\n    if (shouldComplete) {\n      this.skipTransition = true;\n      this.pop(1);\n    } else if (this.activeView) {\n      cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n    }\n  }\n\n  getLastUrl(stackId?: string): RouteView | undefined {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[views.length - 1] : undefined;\n  }\n\n  /**\n   * @internal\n   */\n  getRootUrl(stackId?: string): RouteView | undefined {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[0] : undefined;\n  }\n\n  getActiveStackId(): string | undefined {\n    return this.activeView ? this.activeView.stackId : undefined;\n  }\n\n  /**\n   * @internal\n   */\n  getActiveView(): RouteView | undefined {\n    return this.activeView;\n  }\n\n  hasRunningTask(): boolean {\n    return this.runningTask !== undefined;\n  }\n\n  destroy(): void {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.containerEl = undefined!;\n    this.views.forEach(destroyView);\n    this.activeView = undefined;\n    this.views = [];\n  }\n\n  private getStack(stackId: string | undefined) {\n    return this.views.filter((v) => v.stackId === stackId);\n  }\n\n  private insertView(enteringView: RouteView, direction: RouterDirection) {\n    this.activeView = enteringView;\n    this.views = insertView(this.views, enteringView, direction);\n    return this.views.slice();\n  }\n\n  private transition(\n    enteringView: RouteView | undefined,\n    leavingView: RouteView | undefined,\n    direction: 'forward' | 'back' | undefined,\n    showGoBack: boolean,\n    progressAnimation: boolean,\n    animationBuilder?: AnimationBuilder\n  ) {\n    if (this.skipTransition) {\n      this.skipTransition = false;\n      return Promise.resolve(false);\n    }\n    if (leavingView === enteringView) {\n      return Promise.resolve(false);\n    }\n    const enteringEl = enteringView ? enteringView.element : undefined;\n    const leavingEl = leavingView ? leavingView.element : undefined;\n    const containerEl = this.containerEl;\n    if (enteringEl && enteringEl !== leavingEl) {\n      enteringEl.classList.add('ion-page');\n      enteringEl.classList.add('ion-page-invisible');\n      if (enteringEl.parentElement !== containerEl) {\n        containerEl.appendChild(enteringEl);\n      }\n\n      if ((containerEl as any).commit) {\n        return containerEl.commit(enteringEl, leavingEl, {\n          duration: direction === undefined ? 0 : undefined,\n          direction,\n          showGoBack,\n          progressAnimation,\n          animationBuilder,\n        });\n      }\n    }\n    return Promise.resolve(false);\n  }\n\n  private async wait<T>(task: () => Promise<T>): Promise<T> {\n    if (this.runningTask !== undefined) {\n      await this.runningTask;\n      this.runningTask = undefined;\n    }\n    const promise = (this.runningTask = task());\n    promise.finally(() => (this.runningTask = undefined));\n    return promise;\n  }\n}\n\nconst cleanupAsync = (\n  activeRoute: RouteView,\n  views: RouteView[],\n  viewsSnapshot: RouteView[],\n  location: Location,\n  zone: NgZone\n) => {\n  if (typeof (requestAnimationFrame as any) === 'function') {\n    return new Promise<void>((resolve) => {\n      requestAnimationFrame(() => {\n        cleanup(activeRoute, views, viewsSnapshot, location, zone);\n        resolve();\n      });\n    });\n  }\n  return Promise.resolve();\n};\n\nconst cleanup = (\n  activeRoute: RouteView,\n  views: RouteView[],\n  viewsSnapshot: RouteView[],\n  location: Location,\n  zone: NgZone\n) => {\n  /**\n   * Re-enter the Angular zone when destroying page components. This will allow\n   * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n   */\n  zone.run(() => viewsSnapshot.filter((view) => !views.includes(view)).forEach(destroyView));\n\n  views.forEach((view) => {\n    /**\n     * In the event that a user navigated multiple\n     * times in rapid succession, we want to make sure\n     * we don't pre-emptively detach a view while\n     * it is in mid-transition.\n     *\n     * In this instance we also do not care about query\n     * params or fragments as it will be the same view regardless\n     */\n    const locationWithoutParams = location.path().split('?')[0];\n    const locationWithoutFragment = locationWithoutParams.split('#')[0];\n\n    if (view !== activeRoute && view.url !== locationWithoutFragment) {\n      const element = view.element;\n      element.setAttribute('aria-hidden', 'true');\n      element.classList.add('ion-page-hidden');\n      view.ref.changeDetectorRef.detach();\n    }\n  });\n};\n", "import { Location } from '@angular/common';\nimport {\n  ComponentRef,\n  ElementRef,\n  Injector,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  ViewContainerRef,\n  inject,\n  Attribute,\n  Directive,\n  EventEmitter,\n  Optional,\n  Output,\n  SkipSelf,\n  EnvironmentInjector,\n  Input,\n  InjectionToken,\n  Injectable,\n  reflectComponentType,\n} from '@angular/core';\nimport type { Provider } from '@angular/core';\nimport { OutletContext, Router, ActivatedRoute, ChildrenOutletContexts, PRIMARY_OUTLET, Data } from '@angular/router';\nimport { componentOnReady } from '@ionic/core/components';\nimport type { AnimationBuilder } from '@ionic/core/components';\nimport { Observable, BehaviorSubject, Subscription, combineLatest, of } from 'rxjs';\nimport { distinctUntilChanged, filter, switchMap } from 'rxjs/operators';\n\nimport { Config } from '../../providers/config';\nimport { NavController } from '../../providers/nav-controller';\n\nimport { StackController } from './stack-controller';\nimport { RouteView, StackDidChangeEvent, StackWillChangeEvent, getUrl, isTabSwitch } from './stack-utils';\n\n// TODO(FW-2827): types\n\n@Directive({\n  selector: 'ion-router-outlet',\n  exportAs: 'outlet',\n  // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n  inputs: ['animated', 'animation', 'mode', 'swipeGesture'],\n})\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nexport class IonRouterOutlet implements OnDestroy, OnInit {\n  nativeEl: HTMLIonRouterOutletElement;\n  activatedView: RouteView | null = null;\n  tabsPrefix: string | undefined;\n\n  private _swipeGesture?: boolean;\n  private stackCtrl: StackController;\n\n  // Maintain map of activated route proxies for each component instance\n  private proxyMap = new WeakMap<any, ActivatedRoute>();\n  // Keep the latest activated route in a subject for the proxy routes to switch map to\n  private currentActivatedRoute$ = new BehaviorSubject<{ component: any; activatedRoute: ActivatedRoute } | null>(null);\n\n  private activated: ComponentRef<any> | null = null;\n  /** @internal */\n  get activatedComponentRef(): ComponentRef<any> | null {\n    return this.activated;\n  }\n  private _activatedRoute: ActivatedRoute | null = null;\n\n  /**\n   * The name of the outlet\n   */\n  @Input() name = PRIMARY_OUTLET;\n\n  /** @internal */\n  @Output() stackWillChange = new EventEmitter<StackWillChangeEvent>();\n  /** @internal */\n  @Output() stackDidChange = new EventEmitter<StackDidChangeEvent>();\n\n  // eslint-disable-next-line @angular-eslint/no-output-rename\n  @Output('activate') activateEvents = new EventEmitter<any>();\n  // eslint-disable-next-line @angular-eslint/no-output-rename\n  @Output('deactivate') deactivateEvents = new EventEmitter<any>();\n\n  private parentContexts = inject(ChildrenOutletContexts);\n  private location = inject(ViewContainerRef);\n  private environmentInjector = inject(EnvironmentInjector);\n  private inputBinder = inject(INPUT_BINDER, { optional: true });\n  /** @nodoc */\n  readonly supportsBindingToComponentInputs = true;\n\n  // Ionic providers\n  private config = inject(Config);\n  private navCtrl = inject(NavController);\n\n  set animation(animation: AnimationBuilder) {\n    this.nativeEl.animation = animation;\n  }\n\n  set animated(animated: boolean) {\n    this.nativeEl.animated = animated;\n  }\n\n  set swipeGesture(swipe: boolean) {\n    this._swipeGesture = swipe;\n\n    this.nativeEl.swipeHandler = swipe\n      ? {\n          canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n          onStart: () => this.stackCtrl.startBackTransition(),\n          onEnd: (shouldContinue) => this.stackCtrl.endBackTransition(shouldContinue),\n        }\n      : undefined;\n  }\n\n  constructor(\n    @Attribute('name') name: string,\n    @Optional() @Attribute('tabs') tabs: string,\n    commonLocation: Location,\n    elementRef: ElementRef,\n    router: Router,\n    zone: NgZone,\n    activatedRoute: ActivatedRoute,\n    @SkipSelf() @Optional() readonly parentOutlet?: IonRouterOutlet\n  ) {\n    this.nativeEl = elementRef.nativeElement;\n    this.name = name || PRIMARY_OUTLET;\n    this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n    this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n    this.parentContexts.onChildOutletCreated(this.name, this as any);\n  }\n\n  ngOnDestroy(): void {\n    this.stackCtrl.destroy();\n    this.inputBinder?.unsubscribeFromRouteData(this);\n  }\n\n  getContext(): OutletContext | null {\n    return this.parentContexts.getContext(this.name);\n  }\n\n  ngOnInit(): void {\n    this.initializeOutletWithName();\n  }\n\n  // Note: Ionic deviates from the Angular Router implementation here\n  private initializeOutletWithName() {\n    if (!this.activated) {\n      // If the outlet was not instantiated at the time the route got activated we need to populate\n      // the outlet when it is initialized (ie inside a NgIf)\n      const context = this.getContext();\n      if (context?.route) {\n        this.activateWith(context.route, context.injector);\n      }\n    }\n\n    new Promise((resolve) => componentOnReady(this.nativeEl, resolve)).then(() => {\n      if (this._swipeGesture === undefined) {\n        this.swipeGesture = this.config.getBoolean('swipeBackEnabled', (this.nativeEl as any).mode === 'ios');\n      }\n    });\n  }\n\n  get isActivated(): boolean {\n    return !!this.activated;\n  }\n\n  get component(): Record<string, unknown> {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this.activated.instance;\n  }\n\n  get activatedRoute(): ActivatedRoute {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this._activatedRoute as ActivatedRoute;\n  }\n\n  get activatedRouteData(): Data {\n    if (this._activatedRoute) {\n      return this._activatedRoute.snapshot.data;\n    }\n    return {};\n  }\n\n  /**\n   * Called when the `RouteReuseStrategy` instructs to detach the subtree\n   */\n  detach(): ComponentRef<any> {\n    throw new Error('incompatible reuse strategy');\n  }\n\n  /**\n   * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  attach(_ref: ComponentRef<any>, _activatedRoute: ActivatedRoute): void {\n    throw new Error('incompatible reuse strategy');\n  }\n\n  deactivate(): void {\n    if (this.activated) {\n      if (this.activatedView) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext()!;\n        this.activatedView.savedData = new Map(context.children['contexts']);\n\n        /**\n         * Angular v11.2.10 introduced a change\n         * where this route context is cleared out when\n         * a router-outlet is deactivated, However,\n         * we need this route information in order to\n         * return a user back to the correct tab when\n         * leaving and then going back to the tab context.\n         */\n        const primaryOutlet = this.activatedView.savedData.get('primary');\n        if (primaryOutlet && context.route) {\n          primaryOutlet.route = { ...context.route };\n        }\n\n        /**\n         * Ensure we are saving the NavigationExtras\n         * data otherwise it will be lost\n         */\n        this.activatedView.savedExtras = {};\n        if (context.route) {\n          const contextSnapshot = context.route.snapshot;\n\n          this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n          (this.activatedView.savedExtras.fragment as string | null) = contextSnapshot.fragment;\n        }\n      }\n      const c = this.component;\n      this.activatedView = null;\n      this.activated = null;\n      this._activatedRoute = null;\n      this.deactivateEvents.emit(c);\n    }\n  }\n\n  activateWith(activatedRoute: ActivatedRoute, environmentInjector: EnvironmentInjector | null): void {\n    if (this.isActivated) {\n      throw new Error('Cannot activate an already activated outlet');\n    }\n    this._activatedRoute = activatedRoute;\n\n    let cmpRef: any;\n    let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n    if (enteringView) {\n      cmpRef = this.activated = enteringView.ref;\n      const saved = enteringView.savedData;\n      if (saved) {\n        // self-restore\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext()!;\n        context.children['contexts'] = saved;\n      }\n      // Updated activated route proxy for this component\n      this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n    } else {\n      const snapshot = (activatedRoute as any)._futureSnapshot;\n\n      /**\n       * Angular 14 introduces a new `loadComponent` property to the route config.\n       * This function will assign a `component` property to the route snapshot.\n       * We check for the presence of this property to determine if the route is\n       * using standalone components.\n       */\n      const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n\n      // We create an activated route proxy object that will maintain future updates for this component\n      // over its lifecycle in the stack.\n      const component$ = new BehaviorSubject<any>(null);\n      const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n\n      const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const component = snapshot.routeConfig!.component ?? snapshot.component;\n\n      cmpRef = this.activated = this.location.createComponent(component, {\n        index: this.location.length,\n        injector,\n        environmentInjector: environmentInjector ?? this.environmentInjector,\n      });\n\n      // Once the component is created we can push it to our local subject supplied to the proxy\n      component$.next(cmpRef.instance);\n\n      // Calling `markForCheck` to make sure we will run the change detection when the\n      // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n      enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n\n      // Store references to the proxy by component\n      this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n      this.currentActivatedRoute$.next({ component: cmpRef.instance, activatedRoute });\n    }\n\n    this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n\n    this.activatedView = enteringView;\n\n    /**\n     * The top outlet is set prior to the entering view's transition completing,\n     * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n     * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n     * activated.\n     *\n     * In this scenario, activeWith is called for both the tabs and the root router outlet.\n     * To avoid a race condition, we assign the top outlet synchronously.\n     */\n    this.navCtrl.setTopOutlet(this);\n\n    const leavingView = this.stackCtrl.getActiveView();\n\n    this.stackWillChange.emit({\n      enteringView,\n      tabSwitch: isTabSwitch(enteringView, leavingView),\n    });\n\n    this.stackCtrl.setActive(enteringView).then((data) => {\n      this.activateEvents.emit(cmpRef.instance);\n      this.stackDidChange.emit(data);\n    });\n  }\n\n  /**\n   * Returns `true` if there are pages in the stack to go back.\n   */\n  canGoBack(deep = 1, stackId?: string): boolean {\n    return this.stackCtrl.canGoBack(deep, stackId);\n  }\n\n  /**\n   * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n   */\n  pop(deep = 1, stackId?: string): Promise<boolean> {\n    return this.stackCtrl.pop(deep, stackId);\n  }\n\n  /**\n   * Returns the URL of the active page of each stack.\n   */\n  getLastUrl(stackId?: string): string | undefined {\n    const active = this.stackCtrl.getLastUrl(stackId);\n    return active ? active.url : undefined;\n  }\n\n  /**\n   * Returns the RouteView of the active page of each stack.\n   * @internal\n   */\n  getLastRouteView(stackId?: string): RouteView | undefined {\n    return this.stackCtrl.getLastUrl(stackId);\n  }\n\n  /**\n   * Returns the root view in the tab stack.\n   * @internal\n   */\n  getRootView(stackId?: string): RouteView | undefined {\n    return this.stackCtrl.getRootUrl(stackId);\n  }\n\n  /**\n   * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n   */\n  getActiveStackId(): string | undefined {\n    return this.stackCtrl.getActiveStackId();\n  }\n\n  /**\n   * Since the activated route can change over the life time of a component in an ion router outlet, we create\n   * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n   */\n  private createActivatedRouteProxy(component$: Observable<any>, activatedRoute: ActivatedRoute): ActivatedRoute {\n    const proxy: any = new ActivatedRoute();\n\n    proxy._futureSnapshot = (activatedRoute as any)._futureSnapshot;\n    proxy._routerState = (activatedRoute as any)._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n\n    // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n    (proxy as any)._paramMap = this.proxyObservable(component$, 'paramMap');\n    (proxy as any)._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n    proxy.url = this.proxyObservable(component$, 'url');\n    proxy.params = this.proxyObservable(component$, 'params');\n    proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n    proxy.fragment = this.proxyObservable(component$, 'fragment');\n    proxy.data = this.proxyObservable(component$, 'data');\n\n    return proxy as ActivatedRoute;\n  }\n\n  /**\n   * Create a wrapped observable that will switch to the latest activated route matched by the given component\n   */\n  private proxyObservable(component$: Observable<any>, path: string): Observable<any> {\n    return component$.pipe(\n      // First wait until the component instance is pushed\n      filter((component) => !!component),\n      switchMap((component) =>\n        this.currentActivatedRoute$.pipe(\n          filter((current) => current !== null && current.component === component),\n          switchMap((current) => current && (current.activatedRoute as any)[path]),\n          distinctUntilChanged()\n        )\n      )\n    );\n  }\n\n  /**\n   * Updates the activated route proxy for the given component to the new incoming router state\n   */\n  private updateActivatedRouteProxy(component: any, activatedRoute: ActivatedRoute): void {\n    const proxy = this.proxyMap.get(component);\n    if (!proxy) {\n      throw new Error(`Could not find activated route proxy for view`);\n    }\n\n    (proxy as any)._futureSnapshot = (activatedRoute as any)._futureSnapshot;\n    (proxy as any)._routerState = (activatedRoute as any)._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n\n    this.currentActivatedRoute$.next({ component, activatedRoute });\n  }\n}\n\nclass OutletInjector implements Injector {\n  constructor(private route: ActivatedRoute, private childContexts: ChildrenOutletContexts, private parent: Injector) {}\n\n  get(token: any, notFoundValue?: any): any {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n\n    return this.parent.get(token, notFoundValue);\n  }\n}\n\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken<RoutedComponentInputBinder>('');\n\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\n@Injectable()\nclass RoutedComponentInputBinder {\n  private outletDataSubscriptions = new Map<IonRouterOutlet, Subscription>();\n\n  bindActivatedRouteToOutletComponent(outlet: IonRouterOutlet): void {\n    this.unsubscribeFromRouteData(outlet);\n    this.subscribeToRouteData(outlet);\n  }\n\n  unsubscribeFromRouteData(outlet: IonRouterOutlet): void {\n    this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n    this.outletDataSubscriptions.delete(outlet);\n  }\n\n  private subscribeToRouteData(outlet: IonRouterOutlet) {\n    const { activatedRoute } = outlet;\n    const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data])\n      .pipe(\n        switchMap(([queryParams, params, data], index) => {\n          data = { ...queryParams, ...params, ...data };\n          // Get the first result from the data subscription synchronously so it's available to\n          // the component as soon as possible (and doesn't require a second change detection).\n          if (index === 0) {\n            return of(data);\n          }\n          // Promise.resolve is used to avoid synchronously writing the wrong data when\n          // two of the Observables in the `combineLatest` stream emit one after\n          // another.\n          return Promise.resolve(data);\n        })\n      )\n      .subscribe((data) => {\n        // Outlet may have been deactivated or changed names to be associated with a different\n        // route\n        if (\n          !outlet.isActivated ||\n          !outlet.activatedComponentRef ||\n          outlet.activatedRoute !== activatedRoute ||\n          activatedRoute.component === null\n        ) {\n          this.unsubscribeFromRouteData(outlet);\n          return;\n        }\n\n        const mirror = reflectComponentType(activatedRoute.component);\n        if (!mirror) {\n          this.unsubscribeFromRouteData(outlet);\n          return;\n        }\n\n        for (const { templateName } of mirror.inputs) {\n          outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n        }\n      });\n\n    this.outletDataSubscriptions.set(outlet, dataSubscription);\n  }\n}\n\nexport const provideComponentInputBinding = (): Provider => {\n  return {\n    provide: INPUT_BINDER,\n    useFactory: componentInputBindingFactory,\n    deps: [Router],\n  };\n};\n\nfunction componentInputBindingFactory(router?: Router) {\n  /**\n   * We cast the router to any here, since the componentInputBindingEnabled\n   * property is not available until Angular v16.\n   */\n  if ((router as any)?.componentInputBindingEnabled) {\n    return new RoutedComponentInputBinder();\n  }\n  return null;\n}\n", "import { HostListener, Input, Optional, ElementRef, NgZone, ChangeDetectorRef, Directive } from '@angular/core';\nimport type { Components } from '@ionic/core';\nimport type { AnimationBuilder } from '@ionic/core/components';\n\nimport { Config } from '../../providers/config';\nimport { NavController } from '../../providers/nav-controller';\nimport { ProxyCmp } from '../../utils/proxy';\n\nimport { IonRouterOutlet } from './router-outlet';\n\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport declare interface IonBackButton extends Components.IonBackButton {}\n\n@ProxyCmp({\n  inputs: BACK_BUTTON_INPUTS,\n})\n@Directive({\n  // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n  inputs: BACK_BUTTON_INPUTS,\n})\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nexport class IonBackButton {\n  @Input()\n  defaultHref: string | undefined;\n\n  @Input()\n  routerAnimation: AnimationBuilder | undefined;\n\n  protected el: HTMLElement;\n\n  constructor(\n    @Optional() private routerOutlet: IonRouterOutlet,\n    private navCtrl: NavController,\n    private config: Config,\n    private r: ElementRef,\n    protected z: NgZone,\n    c: ChangeDetectorRef\n  ) {\n    c.detach();\n    this.el = this.r.nativeElement;\n  }\n\n  /**\n   * @internal\n   */\n  @HostListener('click', ['$event'])\n  onClick(ev: Event): void {\n    const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n\n    if (this.routerOutlet?.canGoBack()) {\n      this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n      this.routerOutlet.pop();\n      ev.preventDefault();\n    } else if (defaultHref != null) {\n      this.navCtrl.navigateBack(defaultHref, { animation: this.routerAnimation });\n      ev.preventDefault();\n    }\n  }\n}\n", "import { LocationStrategy } from '@angular/common';\nimport { ElementRef, OnChanges, OnInit, Directive, HostListener, Input, Optional } from '@angular/core';\nimport { Router, RouterLink } from '@angular/router';\nimport type { AnimationBuilder, RouterDirection } from '@ionic/core/components';\n\nimport { NavController } from '../../providers/nav-controller';\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\n@Directive({\n  selector: ':not(a):not(area)[routerLink]',\n})\nexport class RouterLinkDelegateDirective implements OnInit, OnChanges {\n  @Input()\n  routerDirection: RouterDirection = 'forward';\n\n  @Input()\n  routerAnimation?: AnimationBuilder;\n\n  constructor(\n    private locationStrategy: LocationStrategy,\n    private navCtrl: NavController,\n    private elementRef: ElementRef,\n    private router: Router,\n    @Optional() private routerLink?: RouterLink\n  ) {}\n\n  ngOnInit(): void {\n    this.updateTargetUrlAndHref();\n  }\n\n  ngOnChanges(): void {\n    this.updateTargetUrlAndHref();\n  }\n\n  private updateTargetUrlAndHref() {\n    if (this.routerLink?.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n\n  /**\n   * @internal\n   */\n  @HostListener('click', ['$event'])\n  onClick(ev: UIEvent): void {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n\n    /**\n     * This prevents the browser from\n     * performing a page reload when pressing\n     * an Ionic component with routerLink.\n     * The page reload interferes with routing\n     * and causes ion-back-button to disappear\n     * since the local history is wiped on reload.\n     */\n    ev.preventDefault();\n  }\n}\n\n@Directive({\n  selector: 'a[routerLink],area[routerLink]',\n})\nexport class RouterLinkWithHrefDelegateDirective implements OnInit, OnChanges {\n  @Input()\n  routerDirection: RouterDirection = 'forward';\n\n  @Input()\n  routerAnimation?: AnimationBuilder;\n\n  constructor(\n    private locationStrategy: LocationStrategy,\n    private navCtrl: NavController,\n    private elementRef: ElementRef,\n    private router: Router,\n    @Optional() private routerLink?: RouterLink\n  ) {}\n\n  ngOnInit(): void {\n    this.updateTargetUrlAndHref();\n  }\n\n  ngOnChanges(): void {\n    this.updateTargetUrlAndHref();\n  }\n\n  private updateTargetUrlAndHref() {\n    if (this.routerLink?.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n\n  /**\n   * @internal\n   */\n  @HostListener('click')\n  onClick(): void {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n  }\n}\n", "import {\n  ElementRef,\n  Injector,\n  EnvironmentInjector,\n  NgZone,\n  ChangeDetectorRef,\n  Directive,\n  EventEmitter,\n} from '@angular/core';\nimport type { Components } from '@ionic/core';\n\nimport { AngularDelegate } from '../../providers/angular-delegate';\nimport { ProxyCmp, proxyOutputs } from '../../utils/proxy';\n\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\n\nconst NAV_METHODS = [\n  'push',\n  'insert',\n  'insertPages',\n  'pop',\n  'popTo',\n  'popToRoot',\n  'removeIndex',\n  'setRoot',\n  'setPages',\n  'getActive',\n  'getByIndex',\n  'canGoBack',\n  'getPrevious',\n];\n\nexport declare interface IonNav extends Components.IonNav {\n  /**\n   * Event fired when the nav will change components\n   */\n  ionNavWillChange: EventEmitter<CustomEvent<void>>;\n  /**\n   * Event fired when the nav has changed components\n   */\n  ionNavDidChange: EventEmitter<CustomEvent<void>>;\n}\n\n@ProxyCmp({\n  inputs: NAV_INPUTS,\n  methods: NAV_METHODS,\n})\n@Directive({\n  // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n  inputs: NAV_INPUTS,\n})\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nexport class IonNav {\n  protected el: HTMLElement;\n  constructor(\n    ref: ElementRef,\n    environmentInjector: EnvironmentInjector,\n    injector: Injector,\n    angularDelegate: AngularDelegate,\n    protected z: NgZone,\n    c: ChangeDetectorRef\n  ) {\n    c.detach();\n    this.el = ref.nativeElement;\n    ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n    proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n  }\n}\n", "import {\n  AfterContentChecked,\n  AfterContentInit,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  HostListener,\n  Output,\n  ViewChild,\n} from '@angular/core';\n\nimport { NavController } from '../../providers/nav-controller';\n\nimport { StackDidChangeEvent, StackWillChangeEvent } from './stack-utils';\n\n@Directive({\n  selector: 'ion-tabs',\n})\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nexport class IonTabs implements AfterContentInit, AfterContentChecked {\n  /**\n   * Note: These must be redeclared on each child class since it needs\n   * access to generated components such as IonRouterOutlet and IonTabBar.\n   */\n  outlet: any;\n  tabBar: any;\n  tabBars: any;\n\n  @ViewChild('tabsInner', { read: ElementRef, static: true }) tabsInner: ElementRef<HTMLDivElement>;\n\n  /**\n   * Emitted before the tab view is changed.\n   */\n  @Output() ionTabsWillChange = new EventEmitter<{ tab: string }>();\n  /**\n   * Emitted after the tab view is changed.\n   */\n  @Output() ionTabsDidChange = new EventEmitter<{ tab: string }>();\n\n  private tabBarSlot = 'bottom';\n\n  constructor(private navCtrl: NavController) {}\n\n  ngAfterContentInit(): void {\n    this.detectSlotChanges();\n  }\n\n  ngAfterContentChecked(): void {\n    this.detectSlotChanges();\n  }\n\n  /**\n   * @internal\n   */\n  onStackWillChange({ enteringView, tabSwitch }: StackWillChangeEvent): void {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      this.ionTabsWillChange.emit({ tab: stackId });\n    }\n  }\n\n  /**\n   * @internal\n   */\n  onStackDidChange({ enteringView, tabSwitch }: StackDidChangeEvent): void {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      if (this.tabBar) {\n        this.tabBar.selectedTab = stackId;\n      }\n      this.ionTabsDidChange.emit({ tab: stackId });\n    }\n  }\n\n  /**\n   * When a tab button is clicked, there are several scenarios:\n   * 1. If the selected tab is currently active (the tab button has been clicked\n   *    again), then it should go to the root view for that tab.\n   *\n   *   a. Get the saved root view from the router outlet. If the saved root view\n   *      matches the tabRootUrl, set the route view to this view including the\n   *      navigation extras.\n   *   b. If the saved root view from the router outlet does\n   *      not match, navigate to the tabRootUrl. No navigation extras are\n   *      included.\n   *\n   * 2. If the current tab tab is not currently selected, get the last route\n   *    view from the router outlet.\n   *\n   *   a. If the last route view exists, navigate to that view including any\n   *      navigation extras\n   *   b. If the last route view doesn't exist, then navigate\n   *      to the default tabRootUrl\n   */\n  @HostListener('ionTabButtonClick', ['$event'])\n  select(tabOrEvent: string | CustomEvent): Promise<boolean> | undefined {\n    const isTabString = typeof tabOrEvent === 'string';\n    const tab = isTabString ? tabOrEvent : (tabOrEvent as CustomEvent).detail.tab;\n    const alreadySelected = this.outlet.getActiveStackId() === tab;\n    const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n\n    /**\n     * If this is a nested tab, prevent the event\n     * from bubbling otherwise the outer tabs\n     * will respond to this event too, causing\n     * the app to get directed to the wrong place.\n     */\n    if (!isTabString) {\n      (tabOrEvent as CustomEvent).stopPropagation();\n    }\n\n    if (alreadySelected) {\n      const activeStackId = this.outlet.getActiveStackId();\n      const activeView = this.outlet.getLastRouteView(activeStackId);\n\n      // If on root tab, do not navigate to root tab again\n      if (activeView?.url === tabRootUrl) {\n        return;\n      }\n\n      const rootView = this.outlet.getRootView(tab);\n      const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n      return this.navCtrl.navigateRoot(tabRootUrl, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back',\n      });\n    } else {\n      const lastRoute = this.outlet.getLastRouteView(tab);\n      /**\n       * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n       * selected tab\n       */\n      const url = lastRoute?.url || tabRootUrl;\n      const navigationExtras = lastRoute?.savedExtras;\n\n      return this.navCtrl.navigateRoot(url, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back',\n      });\n    }\n  }\n\n  getSelected(): string | undefined {\n    return this.outlet.getActiveStackId();\n  }\n\n  /**\n   * Detects changes to the slot attribute of the tab bar.\n   *\n   * If the slot attribute has changed, then the tab bar\n   * should be relocated to the new slot position.\n   */\n  private detectSlotChanges(): void {\n    this.tabBars.forEach((tabBar: any) => {\n      // el is a protected attribute from the generated component wrapper\n      const currentSlot = tabBar.el.getAttribute('slot');\n\n      if (currentSlot !== this.tabBarSlot) {\n        this.tabBarSlot = currentSlot;\n        this.relocateTabBar();\n      }\n    });\n  }\n\n  /**\n   * Relocates the tab bar to the new slot position.\n   */\n  private relocateTabBar(): void {\n    /**\n     * `el` is a protected attribute from the generated component wrapper.\n     * To avoid having to manually create the wrapper for tab bar, we\n     * cast the tab bar to any and access the protected attribute.\n     */\n    const tabBar = (this.tabBar as any).el as HTMLElement;\n\n    if (this.tabBarSlot === 'top') {\n      /**\n       * A tab bar with a slot of \"top\" should be inserted\n       * at the top of the container.\n       */\n      this.tabsInner.nativeElement.before(tabBar);\n    } else {\n      /**\n       * A tab bar with a slot of \"bottom\" or without a slot\n       * should be inserted at the end of the container.\n       */\n      this.tabsInner.nativeElement.after(tabBar);\n    }\n  }\n}\n", "declare const __zone_symbol__requestAnimationFrame: any;\ndeclare const requestAnimationFrame: any;\n\nexport const raf = (h: any): any => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\n", "import { <PERSON><PERSON>iew<PERSON>nit, ElementRef, Injector, On<PERSON><PERSON>roy, Directive, HostListener } from '@angular/core';\nimport { ControlValueAccessor, NgControl } from '@angular/forms';\nimport { Subscription } from 'rxjs';\n\nimport { raf } from '../../utils/util';\n\n// TODO(FW-2827): types\n\n@Directive()\nexport class ValueAccessor implements ControlValueAccessor, AfterViewInit, OnDestroy {\n  private onChange: (value: any) => void = () => {\n    /**/\n  };\n  private onTouched: () => void = () => {\n    /**/\n  };\n  protected lastValue: any;\n  private statusChanges?: Subscription;\n\n  constructor(protected injector: Injector, protected elementRef: ElementRef) {}\n\n  writeValue(value: any): void {\n    this.elementRef.nativeElement.value = this.lastValue = value;\n    setIonicClasses(this.elementRef);\n  }\n\n  /**\n   * Notifies the ControlValueAccessor of a change in the value of the control.\n   *\n   * This is called by each of the ValueAccessor directives when we want to update\n   * the status and validity of the form control. For example with text components this\n   * is called when the ionInput event is fired. For select components this is called\n   * when the ionChange event is fired.\n   *\n   * This also updates the Ionic form status classes on the element.\n   *\n   * @param el The component element.\n   * @param value The new value of the control.\n   */\n  handleValueChange(el: HTMLElement, value: any): void {\n    if (el === this.elementRef.nativeElement) {\n      if (value !== this.lastValue) {\n        this.lastValue = value;\n        this.onChange(value);\n      }\n      setIonicClasses(this.elementRef);\n    }\n  }\n\n  @HostListener('ionBlur', ['$event.target'])\n  _handleBlurEvent(el: any): void {\n    if (el === this.elementRef.nativeElement) {\n      this.onTouched();\n      setIonicClasses(this.elementRef);\n    }\n  }\n\n  registerOnChange(fn: (value: any) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    this.elementRef.nativeElement.disabled = isDisabled;\n  }\n\n  ngOnDestroy(): void {\n    if (this.statusChanges) {\n      this.statusChanges.unsubscribe();\n    }\n  }\n\n  ngAfterViewInit(): void {\n    let ngControl;\n    try {\n      ngControl = this.injector.get<NgControl>(NgControl);\n    } catch {\n      /* No FormControl or ngModel binding */\n    }\n\n    if (!ngControl) {\n      return;\n    }\n\n    // Listen for changes in validity, disabled, or pending states\n    if (ngControl.statusChanges) {\n      this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n    }\n\n    /**\n     * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n     * whenever it is implemented.\n     */\n    const formControl = ngControl.control as any;\n    if (formControl) {\n      const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n      methodsToPatch.forEach((method) => {\n        if (typeof formControl[method] !== 'undefined') {\n          const oldFn = formControl[method].bind(formControl);\n          formControl[method] = (...params: any[]) => {\n            oldFn(...params);\n            setIonicClasses(this.elementRef);\n          };\n        }\n      });\n    }\n  }\n}\n\nexport const setIonicClasses = (element: ElementRef): void => {\n  raf(() => {\n    const input = element.nativeElement as HTMLInputElement;\n    const hasValue = input.value != null && input.value.toString().length > 0;\n    const classes = getClasses(input);\n    setClasses(input, classes);\n    const item = input.closest('ion-item');\n    if (item) {\n      if (hasValue) {\n        setClasses(item, [...classes, 'item-has-value']);\n      } else {\n        setClasses(item, classes);\n      }\n    }\n  });\n};\n\nconst getClasses = (element: HTMLElement) => {\n  const classList = element.classList;\n  const classes: string[] = [];\n  for (let i = 0; i < classList.length; i++) {\n    const item = classList.item(i);\n    if (item !== null && startsWith(item, 'ng-')) {\n      classes.push(`ion-${item.substring(3)}`);\n    }\n  }\n  return classes;\n};\n\nconst setClasses = (element: HTMLElement, classes: string[]) => {\n  const classList = element.classList;\n  classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n  classList.add(...classes);\n};\n\nconst startsWith = (input: string, search: string): boolean => {\n  return input.substring(0, search.length) === search;\n};\n", "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport { ActivatedRouteSnapshot, DetachedRouteHandle, RouteReuseStrategy } from '@angular/router';\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nexport class IonicRouteStrategy implements RouteReuseStrategy {\n  /**\n   * Whether the given route should detach for later reuse.\n   */\n  shouldDetach(_route: ActivatedRouteSnapshot): boolean {\n    return false;\n  }\n\n  /**\n   * Returns `false`, meaning the route (and its subtree) is never reattached\n   */\n  shouldAttach(_route: ActivatedRouteSnapshot): boolean {\n    return false;\n  }\n\n  /**\n   * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n   */\n  store(_route: ActivatedRouteSnapshot, _detachedTree: DetachedRouteHandle): void {\n    return;\n  }\n\n  /**\n   * Returns `null` because this strategy does not store routes for later re-use.\n   */\n  retrieve(_route: ActivatedRouteSnapshot): DetachedRouteHandle | null {\n    return null;\n  }\n\n  /**\n   * Determines if a route should be reused.\n   * This strategy returns `true` when the future route config and\n   * current route config are identical and all route parameters are identical.\n   */\n  shouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {\n    if (future.routeConfig !== curr.routeConfig) {\n      return false;\n    }\n\n    // checking router params\n    const futureParams = future.params;\n    const currentParams = curr.params;\n    const keysA = Object.keys(futureParams);\n    const keysB = Object.keys(currentParams);\n\n    if (keysA.length !== keysB.length) {\n      return false;\n    }\n\n    // Test for A's keys different from B.\n    for (const key of keysA) {\n      if (currentParams[key] !== futureParams[key]) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n", "// TODO(FW-2827): types\n\nexport interface ControllerShape<Opts, HTMLElm> {\n  create(options: Opts): Promise<HTMLElm>;\n  dismiss(data?: any, role?: string, id?: string): Promise<boolean>;\n  getTop(): Promise<HTMLElm | undefined>;\n}\n\nexport class OverlayBaseController<Opts, Overlay> implements ControllerShape<Opts, Overlay> {\n  constructor(private ctrl: ControllerShape<Opts, Overlay>) {}\n\n  /**\n   * Creates a new overlay\n   */\n  create(opts?: Opts): Promise<Overlay> {\n    return this.ctrl.create((opts || {}) as any);\n  }\n\n  /**\n   * When `id` is not provided, it dismisses the top overlay.\n   */\n  dismiss(data?: any, role?: string, id?: string): Promise<boolean> {\n    return this.ctrl.dismiss(data, role, id);\n  }\n\n  /**\n   * Returns the top overlay.\n   */\n  getTop(): Promise<Overlay | undefined> {\n    return this.ctrl.getTop();\n  }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1.Platform", "i2", "i1.IonRouterOutlet", "i2.NavController", "i3.Config", "i1.AngularDelegate", "i1.NavController"], "mappings": ";;;;;;;;;;;;MAEa,cAAc,CAAA;AACzB,IAAA,WAAA,CAAoB,cAA+B,EAAA;AAA/B,QAAA,IAAc,CAAA,cAAA,GAAd,cAAc,CAAiB;KAAI;AAEvD;;;;AAIG;AACH,IAAA,IAAI,CAAC,MAAe,EAAA;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACzC;AAED;;;;;;AAMG;AACH,IAAA,KAAK,CAAC,MAAe,EAAA;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KAC1C;AAED;;;;;AAKG;AACH,IAAA,MAAM,CAAC,MAAe,EAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAC3C;AAED;;;;;;;AAOG;IACH,MAAM,CAAC,YAAqB,EAAE,MAAe,EAAA;QAC3C,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;KACzD;AAED;;;;;AAKG;IACH,YAAY,CAAC,YAAqB,EAAE,MAAe,EAAA;QACjD,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;KAC/D;AAED;;;;AAIG;AACH,IAAA,MAAM,CAAC,MAAe,EAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAC3C;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,MAAe,EAAA;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KAC9C;AAED;;;;;;;;AAQG;AACH,IAAA,GAAG,CAAC,MAAe,EAAA;QACjB,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;KACxC;AAED;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;KACtC;AAED;;AAEG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;KACvC;IAED,iBAAiB,CAAC,IAAY,EAAE,SAA2B,EAAA;QACzD,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAC/D;IAED,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;KAC1C;IAED,YAAY,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;KAC3C;IAED,gBAAgB,CAAC,IAAY,EAAE,OAAc,EAAA;QAC3C,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5D;AAED,IAAA,SAAS,CAAC,IAAW,EAAA;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC5C;AAED,IAAA,WAAW,CAAC,IAAW,EAAA;QACrB,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;KAC9C;AAED,IAAA,QAAQ,CAAC,IAAW,EAAE,UAAmB,EAAE,QAAiB,EAAA;AAC1D,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KACjE;AACF;;MC3HY,aAAa,CAAA;AACxB;;;AAGG;AACH,IAAA,IAAI,CAAC,EAAe,EAAA;AAClB,QAAA,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACrB;AAED;;;AAGG;AACH,IAAA,KAAK,CAAC,EAAe,EAAA;AACnB,QAAA,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;KACtB;;8HAfU,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAb,mBAAA,aAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cAFZ,MAAM,EAAA,CAAA,CAAA;4FAEP,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;iBACnB,CAAA;;AAmBD,MAAM,QAAQ,GAAG,MAAK;AACpB,IAAA,MAAM,GAAG,GAAG,OAAQ,MAAc,KAAK,WAAW,GAAG,MAAM,GAAI,IAAY,CAAC;IAE5E,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,QAAA,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AACxB,QAAA,IAAI,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,KAAK,EAAE;YAChB,OAAO,KAAK,CAAC,KAAK,CAAC;AACpB,SAAA;QAED,OAAO;YACL,IAAI,EAAE,CAAC,EAAO,KAAK,GAAG,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAChD,KAAK,EAAE,CAAC,EAAO,KAAK,GAAG,CAAC,qBAAqB,CAAC,EAAE,CAAC;SAClD,CAAC;AACH,KAAA;IAED,OAAO;AACL,QAAA,IAAI,EAAE,CAAC,EAAO,KAAK,EAAE,EAAE;AACvB,QAAA,KAAK,EAAE,CAAC,EAAO,KAAK,EAAE,EAAE;KACzB,CAAC;AACJ,CAAC;;MCxBY,QAAQ,CAAA;IA2CnB,WAAsC,CAAA,GAAQ,EAAE,IAAY,EAAA;AAAtB,QAAA,IAAG,CAAA,GAAA,GAAH,GAAG,CAAK;AAvC9C;;AAEG;AACH,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAA8C,CAAC;AAEvE;;;AAGG;AACH,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,OAAO,EAAuB,CAAC;AAErD;;;AAGG;AACH,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,OAAO,EAAQ,CAAC;AAEtC;;;;;AAKG;AACH,QAAA,IAAA,CAAA,KAAK,GAAG,IAAI,OAAO,EAAQ,CAAC;AAE5B;;;;AAIG;AACH,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,OAAO,EAAQ,CAAC;AAE7B;;;;AAIG;AACH,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,OAAO,EAAQ,CAAC;AAG3B,QAAA,IAAI,CAAC,GAAG,CAAC,MAAK;;AACZ,YAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC;YAC3B,IAAI,CAAC,UAAU,CAAC,qBAAqB,GAAG,UAAU,QAAQ,EAAE,QAAQ,EAAA;AAClE,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAI;oBAC3B,OAAO,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,kBAAkB,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AACrG,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC;YAEF,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC3C,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC7C,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AACxD,YAAA,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAClD,YAAA,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,oBAAoB,EAAE,IAAI,CAAC,CAAC;AACvE,YAAA,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,oBAAoB,EAAE,IAAI,CAAC,CAAC;AAEvE,YAAA,IAAI,YAAqC,CAAC;YAC1C,IAAI,CAAC,aAAa,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,KAAI;gBACvC,YAAY,GAAG,GAAG,CAAC;AACrB,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,MAAA,IAAI,CAAC,GAAG,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAS,CAAC,EAAE;AACzB,gBAAA,GAAG,CAAC,gBAAgB,CAClB,aAAa,EACb,MAAK;oBACH,YAAY,CAAC,SAAS,CAAC,CAAC;AAC1B,iBAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACf,CAAC;AACH,aAAA;AAAM,iBAAA;;gBAEL,YAAa,CAAC,KAAK,CAAC,CAAC;AACtB,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CG;AACH,IAAA,EAAE,CAAC,YAAuB,EAAA;QACxB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;KAC3C;AAED;;;;;;;;;;;;;;;;;;AAkBG;IACH,SAAS,GAAA;AACP,QAAA,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC/B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;IACH,KAAK,GAAA;QACH,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;AAED;;;;;AAKG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC;KAC/B;AAED;;AAEG;AACH,IAAA,aAAa,CAAC,GAAW,EAAA;AACvB,QAAA,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KACpD;AAED;;AAEG;IACH,WAAW,GAAA;AACT,QAAA,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;KAC3B;AAED;;AAEG;IACH,UAAU,GAAA;;QACR,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,GAAG,EAAC,UAAU,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,EAAA,yBAAyB,CAAE,CAAA,OAAO,CAAC;KACjE;AAED,IAAA,aAAa,CAAC,UAAkB,EAAA;AAC9B,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;QAC/B,OAAO,CAAC,EAAE,CAAA,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,SAAS,KAAI,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;KACrE;AAED;;AAEG;IACH,GAAG,GAAA;AACD,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;KAC/B;AAED;;AAEG;IACH,KAAK,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;KAC5B;AAED;;AAEG;IACH,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;KAC7B;;AA5OU,mBAAA,QAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,kBA2CC,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AA3CjB,mBAAA,QAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,QAAQ,cAFP,MAAM,EAAA,CAAA,CAAA;4FAEP,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAHpB,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;iBACnB,CAAA;;;8BA4Cc,MAAM;+BAAC,QAAQ,CAAA;;;AAoM9B,MAAM,cAAc,GAAG,CAAC,GAAW,EAAE,GAAW,KAAI;IAClD,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IACtC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;IACvD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,OAAO,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC7E,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAI,OAAmB,EAAE,EAAe,EAAE,SAAiB,EAAE,IAAY,KAAI;AAC9F,IAAA,IAAI,EAAE,EAAE;QACN,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,EAAE,KAAI;AACpC;;;;;AAKG;AACH,YAAA,IAAI,CAAC,GAAG,CAAC,MAAK;;AAEZ,gBAAA,MAAM,KAAK,GAAG,EAAE,IAAI,IAAI,GAAI,EAAU,CAAC,MAAM,GAAG,SAAS,CAAC;AAC1D,gBAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtB,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AACJ,KAAA;AACH,CAAC;;MCpQY,aAAa,CAAA;AASxB,IAAA,WAAA,CACE,QAAkB,EACV,QAAkB,EAClB,UAAyB,EACb,MAAe,EAAA;AAF3B,QAAA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;AAClB,QAAA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAe;AACb,QAAA,IAAM,CAAA,MAAA,GAAN,MAAM,CAAS;AAX7B,QAAA,IAAS,CAAA,SAAA,GAAyC,iBAAiB,CAAC;AACpE,QAAA,IAAQ,CAAA,QAAA,GAAkB,gBAAgB,CAAC;AAE3C,QAAA,IAAc,CAAA,cAAA,GAAoB,SAAS,CAAC;AAE5C,QAAA,IAAS,CAAA,SAAA,GAAG,CAAC,CAAC,CAAC;;AASrB,QAAA,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,KAAI;gBAC7B,IAAI,EAAE,YAAY,eAAe,EAAE;AACjC,oBAAA,MAAM,EAAE,GAAG,EAAE,CAAC,aAAa,GAAG,EAAE,CAAC,aAAa,CAAC,YAAY,GAAG,EAAE,CAAC,EAAE,CAAC;AACpE,oBAAA,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,MAAM,GAAG,SAAS,CAAC;AAC/D,oBAAA,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;AAC1E,oBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,KAAK,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACjE,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;;QAGD,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,kBAAkB,KAAI;YAClE,IAAI,CAAC,GAAG,EAAE,CAAC;AACX,YAAA,kBAAkB,EAAE,CAAC;AACvB,SAAC,CAAC,CAAC;KACJ;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,eAAe,CAAC,GAA6B,EAAE,OAAA,GAA6B,EAAE,EAAA;AAC5E,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC9F,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KACpC;AAED;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,YAAY,CAAC,GAA6B,EAAE,OAAA,GAA6B,EAAE,EAAA;AACzE,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC3F,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KACpC;AAED;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAA,YAAY,CAAC,GAA6B,EAAE,OAAA,GAA6B,EAAE,EAAA;AACzE,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC3F,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KACpC;AAED;;;;AAIG;IACH,IAAI,CAAC,OAA4B,GAAA,EAAE,QAAQ,EAAE,IAAI,EAAE,kBAAkB,EAAE,MAAM,EAAE,EAAA;AAC7E,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAC3F,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KAC7B;AAED;;;;;;;AAOG;IACG,GAAG,GAAA;;AACP,YAAA,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;AAE5B,YAAA,OAAO,MAAM,EAAE;AACb,gBAAA,IAAI,MAAM,MAAM,CAAC,GAAG,EAAE,EAAE;AACtB,oBAAA,OAAO,IAAI,CAAC;AACb,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;AAC9B,iBAAA;AACF,aAAA;AAED,YAAA,OAAO,KAAK,CAAC;SACd,CAAA,CAAA;AAAA,KAAA;AAED;;;;;;AAMG;AACH,IAAA,YAAY,CACV,SAA0B,EAC1B,QAAkB,EAClB,kBAAuC,EACvC,gBAAmC,EAAA;AAEnC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;KAC1C;AAED;;AAEG;AACH,IAAA,YAAY,CAAC,MAAuB,EAAA;AAClC,QAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;KACzB;AAED;;AAEG;IACH,iBAAiB,GAAA;QAKf,IAAI,SAAS,GAAoB,MAAM,CAAC;AACxC,QAAA,IAAI,SAAmC,CAAC;AACxC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAE/C,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,EAAE;AAC7B,YAAA,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;AAChC,YAAA,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;AACjC,SAAA;AAAM,aAAA;AACL,YAAA,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1B,YAAA,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC;AACnC,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,OAAO;YACL,SAAS;YACT,SAAS;YACT,gBAAgB;SACjB,CAAC;KACH;IAEO,QAAQ,CAAC,GAA6B,EAAE,OAA0B,EAAA;AACxE,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;;YAEtB,OAAO,IAAI,CAAC,MAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC5C,SAAA;AAAM,aAAA;AACL;;;;;AAKG;AACH,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;AAEtD,YAAA,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;AACrC,gBAAA,OAAO,CAAC,WAAW,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,OAAO,CAAC,WAAW,CAAE,CAAC;AAClD,aAAA;AAED,YAAA,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;AAClC,gBAAA,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACrC,aAAA;AAED;;;;AAIG;;YAEH,OAAO,IAAI,CAAC,MAAO,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACrD,SAAA;KACF;;8HAtNU,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAb,mBAAA,aAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cAFZ,MAAM,EAAA,CAAA,CAAA;4FAEP,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;iBACnB,CAAA;;;8BAcI,QAAQ;;;AA4Mb,MAAM,YAAY,GAAG,CACnB,SAA0B,EAC1B,QAA6B,EAC7B,kBAAkD,KACtB;IAC5B,IAAI,QAAQ,KAAK,KAAK,EAAE;AACtB,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IACD,IAAI,kBAAkB,KAAK,SAAS,EAAE;AACpC,QAAA,OAAO,kBAAkB,CAAC;AAC3B,KAAA;AACD,IAAA,IAAI,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,MAAM,EAAE;AACnD,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AAAM,SAAA,IAAI,SAAS,KAAK,MAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;AACpD,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,MAAM,CAAC;AACjC,MAAM,gBAAgB,GAAG,SAAS;;MCzPrB,MAAM,CAAA;IACjB,GAAG,CAAC,GAAsB,EAAE,QAAc,EAAA;AACxC,QAAA,MAAM,CAAC,GAAG,SAAS,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,EAAE;YACL,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC7B,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,UAAU,CAAC,GAAsB,EAAE,QAAkB,EAAA;AACnD,QAAA,MAAM,CAAC,GAAG,SAAS,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,EAAE;YACL,OAAO,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACpC,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KACd;IAED,SAAS,CAAC,GAAsB,EAAE,QAAiB,EAAA;AACjD,QAAA,MAAM,CAAC,GAAG,SAAS,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,EAAE;YACL,OAAO,CAAC,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACnC,SAAA;AACD,QAAA,OAAO,CAAC,CAAC;KACV;;uHAvBU,MAAM,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AAAN,mBAAA,MAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,MAAM,cAFL,MAAM,EAAA,CAAA,CAAA;4FAEP,MAAM,EAAA,UAAA,EAAA,CAAA;kBAHlB,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;iBACnB,CAAA;;MA2BY,WAAW,GAAG,IAAI,cAAc,CAAM,YAAY,EAAE;AAEjE,MAAM,SAAS,GAAG,MAAwB;AACxC,IAAA,IAAI,OAAQ,MAAc,KAAK,WAAW,EAAE;AAC1C,QAAA,MAAM,KAAK,GAAI,MAA6B,CAAC,KAAK,CAAC;AACnD,QAAA,IAAI,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE;YACjB,OAAO,KAAK,CAAC,MAAM,CAAC;AACrB,SAAA;AACF,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;;AC5CD;;;;;;;;;;;;;;;;;;;AAmBG;MACU,SAAS,CAAA;IACpB,WAAmB,CAAA,OAA+B,EAAE,EAAA;AAAjC,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAA6B;KAAI;AAExD;;;;;;;;;;;;;;;AAeG;AACH,IAAA,GAAG,CAAU,KAAa,EAAA;AACxB,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACzB;AACF;;ACpBD;MAGa,eAAe,CAAA;AAD5B,IAAA,WAAA,GAAA;AAEU,QAAA,IAAA,CAAA,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACtB,QAAA,IAAA,CAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;KAejD;AAbC,IAAA,MAAM,CACJ,mBAAwC,EACxC,QAAkB,EAClB,mBAA4B,EAAA;AAE5B,QAAA,OAAO,IAAI,wBAAwB,CACjC,mBAAmB,EACnB,QAAQ,EACR,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,IAAI,EACT,mBAAmB,CACpB,CAAC;KACH;;gIAhBU,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;oIAAf,eAAe,EAAA,CAAA,CAAA;4FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;;MAoBE,wBAAwB,CAAA;IAInC,WACU,CAAA,mBAAwC,EACxC,QAAkB,EAClB,cAA8B,EAC9B,IAAY,EACZ,mBAA4B,EAAA;AAJ5B,QAAA,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB,CAAqB;AACxC,QAAA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;AAClB,QAAA,IAAc,CAAA,cAAA,GAAd,cAAc,CAAgB;AAC9B,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AACZ,QAAA,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB,CAAS;AAR9B,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,OAAO,EAAkC,CAAC;AACzD,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,OAAO,EAA2B,CAAC;KAQzD;AAEJ,IAAA,eAAe,CAAC,SAAc,EAAE,SAAc,EAAE,MAAY,EAAE,UAAqB,EAAA;AACjF,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACxB,YAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;AAC7B,gBAAA,MAAM,cAAc,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACf,MAAM,CACV,CAAC;AAEF;;;;;;;AAOG;AACH,gBAAA,IAAI,IAAI,CAAC,mBAAmB,KAAK,SAAS,EAAE;AAC1C,oBAAA,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,SAAS,CAAC;AACtD,iBAAA;AAED,gBAAA,MAAM,EAAE,GAAG,UAAU,CACnB,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,WAAW,EAChB,SAAS,EACT,SAAS,EACT,cAAc,EACd,UAAU,EACV,IAAI,CAAC,mBAAmB,CACzB,CAAC;gBACF,OAAO,CAAC,EAAE,CAAC,CAAC;AACd,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;IAED,iBAAiB,CAAC,UAAe,EAAE,SAAc,EAAA;AAC/C,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;AACxB,YAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAI;gBAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAClD,gBAAA,IAAI,YAAY,EAAE;oBAChB,YAAY,CAAC,OAAO,EAAE,CAAC;AACvB,oBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;oBAChC,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACrD,oBAAA,IAAI,YAAY,EAAE;AAChB,wBAAA,YAAY,EAAE,CAAC;AACf,wBAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACpC,qBAAA;AACF,iBAAA;AACD,gBAAA,OAAO,EAAE,CAAC;AACZ,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;AACF,CAAA;AAEM,MAAM,UAAU,GAAG,CACxB,IAAY,EACZ,mBAAwC,EACxC,QAAkB,EAClB,cAA8B,EAC9B,QAAiD,EACjD,WAA6C,EAC7C,SAAc,EACd,SAAc,EACd,MAAW,EACX,UAAgC,EAChC,mBAAuC,KAChC;AACP;;;;;;;;;;;AAWG;AACH,IAAA,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;AACpC,QAAA,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC;AAC/B,QAAA,MAAM,EAAE,QAAQ;AACjB,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,YAAY,GAAG,eAAe,CAAM,SAAS,EAAE;QACnD,mBAAmB;AACnB,QAAA,eAAe,EAAE,aAAa;AAC/B,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;AACvC,IAAA,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;AAExD,IAAA,IAAI,MAAM,EAAE;AACV;;;;;;AAMG;QACH,IAAI,mBAAmB,IAAI,QAAQ,CAAC,mBAAmB,CAAC,KAAK,SAAS,EAAE;YACtE,OAAO,CAAC,KAAK,CACX,CAAA,eAAA,EAAkB,mBAAmB,CAAsC,mCAAA,EAAA,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,CAAA,wBAAA,EAA2B,mBAAmB,CACtJ,gBAAA,EAAA,SAAS,CAAC,IACZ,CAAA,CAAA,CAAG,CACJ,CAAC;AACH,SAAA;AAED,QAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACjC,KAAA;AACD,IAAA,IAAI,UAAU,EAAE;AACd,QAAA,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;AACjC,YAAA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACrC,SAAA;AACF,KAAA;IACD,MAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AACtE,IAAA,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAEnC,IAAA,cAAc,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAEjD,IAAA,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACxC,IAAA,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAC3C,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG;IACjB,oBAAoB;IACpB,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,qBAAqB;CACtB,CAAC;AAEW,MAAA,mBAAmB,GAAG,CAAC,IAAY,EAAE,QAAa,EAAE,OAAoB,KAAkB;AACrG,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,MAAK;QACnB,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,KAAK,OAAO,QAAQ,CAAC,SAAS,CAAC,KAAK,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,KAAI;AAChH,YAAA,MAAM,OAAO,GAAG,CAAC,EAAO,KAAK,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAC5D,YAAA,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC7C,OAAO,MAAM,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC/D,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACjD,KAAC,CAAC,CAAC;AACL,EAAE;AAEF,MAAM,cAAc,GAAG,IAAI,cAAc,CAAM,gBAAgB,CAAC,CAAC;AAEjE,MAAM,YAAY,GAAG,CAAC,MAA8B,KAAI;IACtD,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,cAAc;AACvB,YAAA,QAAQ,EAAE,MAAM;AACjB,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,SAAS;AAClB,YAAA,UAAU,EAAE,0BAA0B;YACtC,IAAI,EAAE,CAAC,cAAc,CAAC;AACvB,SAAA;KACF,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,0BAA0B,GAAG,CAAC,MAA8B,KAAI;AACpE,IAAA,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC;;AC5ND;AAMO,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAE,MAAgB,KAAI;AACxD,IAAA,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;AAChC,IAAA,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACtB,QAAA,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,EAAE;YACrC,GAAG,GAAA;AACD,gBAAA,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;aACtB;AACD,YAAA,GAAG,CAAC,GAAQ,EAAA;AACV,gBAAA,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;aACvD;AACF,SAAA,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEK,MAAM,YAAY,GAAG,CAAC,GAAQ,EAAE,OAAiB,KAAI;AAC1D,IAAA,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;AAChC,IAAA,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;QAC7B,SAAS,CAAC,UAAU,CAAC,GAAG,YAAA;YACtB,MAAM,IAAI,GAAG,SAAS,CAAC;YACvB,OAAO,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;AAClF,SAAC,CAAC;AACJ,KAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEK,MAAM,YAAY,GAAG,CAAC,QAAa,EAAE,EAAO,EAAE,MAAgB,KAAI;IACvE,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS,MAAM,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAClF,CAAC,CAAC;AAEF;AACM,SAAU,QAAQ,CAAC,IAAyE,EAAA;IAChG,MAAM,SAAS,GAAG,UAAU,GAAQ,EAAA;QAClC,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAExD,IAAI,qBAAqB,KAAK,SAAS,EAAE;AACvC,YAAA,qBAAqB,EAAE,CAAC;AACzB,SAAA;AAED,QAAA,IAAI,MAAM,EAAE;AACV,YAAA,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC1B,SAAA;AACD,QAAA,IAAI,OAAO,EAAE;AACX,YAAA,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC5B,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;AACb,KAAC,CAAC;AACF,IAAA,OAAO,SAAS,CAAC;AACnB;;ACJA,MAAM,cAAc,GAAG;IACrB,WAAW;IACX,UAAU;IACV,OAAO;IACP,qBAAqB;IACrB,iBAAiB;IACjB,UAAU;IACV,iBAAiB;IACjB,gBAAgB;IAChB,OAAO;IACP,QAAQ;IACR,eAAe;IACf,gBAAgB;IAChB,MAAM;IACN,cAAc;IACd,aAAa;IACb,SAAS;IACT,eAAe;IACf,WAAW;IACX,MAAM;IACN,MAAM;CACP,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;AAmBnE,IAAA,UAAU,SAAV,UAAU,CAAA;AAQrB,IAAA,WAAA,CAAY,CAAoB,EAAE,CAAa,EAAY,CAAS,EAAA;AAAT,QAAA,IAAC,CAAA,CAAA,GAAD,CAAC,CAAQ;AAJpE,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AAKhB,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,aAAa,CAAC;QAE1B,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAK;AACxC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,CAAC,CAAC,aAAa,EAAE,CAAC;AACpB,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAK;AAC1C,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,CAAC,CAAC,aAAa,EAAE,CAAC;AACpB,SAAC,CAAC,CAAC;AACH,QAAA,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,sBAAsB;YACtB,uBAAuB;YACvB,uBAAuB;YACvB,sBAAsB;YACtB,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;AACb,SAAA,CAAC,CAAC;KACJ;EACF;2HA9BY,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAV,mBAAA,UAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,UAAU,mmBAEP,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;AAFd,UAAU,GAAA,UAAA,CAAA;AAjBtB,IAAA,QAAQ,CAAC;AACR,QAAA,MAAM,EAAE,cAAc;AACtB,QAAA,OAAO,EAAE,eAAe;KACzB,CAAC;AACF;;;;;AAKG;CAQU,EAAA,UAAU,CA8BtB,CAAA;4FA9BY,UAAU,EAAA,UAAA,EAAA,CAAA;kBAPtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;;AAEvB,oBAAA,MAAM,EAAE,cAAc;iBACvB,CAAA;sJAK+C,QAAQ,EAAA,CAAA;sBAArD,YAAY;gBAAC,IAAA,EAAA,CAAA,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;;;ACxC9C,MAAM,YAAY,GAAG;IACnB,UAAU;IACV,qBAAqB;IACrB,oBAAoB;IACpB,iBAAiB;IACjB,aAAa;IACb,YAAY;IACZ,UAAU;IACV,gBAAgB;IAChB,OAAO;IACP,QAAQ;IACR,gBAAgB;IAChB,mBAAmB;IACnB,QAAQ;IACR,eAAe;IACf,gBAAgB;IAChB,MAAM;IACN,mBAAmB;IACnB,cAAc;IACd,aAAa;IACb,SAAS;CACV,CAAC;AAEF,MAAM,aAAa,GAAG;IACpB,SAAS;IACT,SAAS;IACT,cAAc;IACd,eAAe;IACf,sBAAsB;IACtB,sBAAsB;CACvB,CAAC;AAmBW,IAAA,QAAQ,SAAR,QAAQ,CAAA;AAQnB,IAAA,WAAA,CAAY,CAAoB,EAAE,CAAa,EAAY,CAAS,EAAA;AAAT,QAAA,IAAC,CAAA,CAAA,GAAD,CAAC,CAAQ;AAJpE,QAAA,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AAKhB,QAAA,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,aAAa,CAAC;QAE1B,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAK;AACxC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,CAAC,CAAC,aAAa,EAAE,CAAC;AACpB,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAK;AAC1C,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,CAAC,CAAC,aAAa,EAAE,CAAC;AACpB,SAAC,CAAC,CAAC;AACH,QAAA,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,oBAAoB;YACpB,qBAAqB;YACrB,qBAAqB;YACrB,oBAAoB;YACpB,wBAAwB;YACxB,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;AACb,SAAA,CAAC,CAAC;KACJ;EACF;yHA/BY,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAR,mBAAA,QAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,QAAQ,qqBAEL,WAAW,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;AAFd,QAAQ,GAAA,UAAA,CAAA;AAjBpB,IAAA,QAAQ,CAAC;AACR,QAAA,MAAM,EAAE,YAAY;AACpB,QAAA,OAAO,EAAE,aAAa;KACvB,CAAC;AACF;;;;;AAKG;CAQU,EAAA,QAAQ,CA+BpB,CAAA;4FA/BY,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAPpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;;AAErB,oBAAA,MAAM,EAAE,YAAY;iBACrB,CAAA;sJAK+C,QAAQ,EAAA,CAAA;sBAArD,YAAY;gBAAC,IAAA,EAAA,CAAA,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;;;ACnGvC,MAAM,UAAU,GAAG,CAAC,KAAkB,EAAE,IAAe,EAAE,SAA0B,KAAiB;IACzG,IAAI,SAAS,KAAK,MAAM,EAAE;AACxB,QAAA,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7B,KAAA;SAAM,IAAI,SAAS,KAAK,SAAS,EAAE;AAClC,QAAA,OAAO,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAChC,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7B,KAAA;AACH,CAAC,CAAC;AAEF,MAAM,OAAO,GAAG,CAAC,KAAkB,EAAE,IAAe,KAAI;AACtD,IAAA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC;AACxD,IAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjB,IAAA,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,KAAkB,EAAE,IAAe,KAAI;IACzD,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,KAAK,IAAI,CAAC,EAAE;QACd,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AAC5E,KAAA;AAAM,SAAA;AACL,QAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,OAAO,GAAG,CAAC,KAAkB,EAAE,IAAe,KAAI;IACtD,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,KAAK,IAAI,CAAC,EAAE;QACd,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3E,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7B,KAAA;AACH,CAAC,CAAC;AAEK,MAAM,MAAM,GAAG,CAAC,MAAc,EAAE,cAA8B,KAAY;AAC/E,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,EAAE,cAAc,EAAE,CAAC,CAAC;AAC5E,IAAA,OAAO,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC,CAAC;AAEK,MAAM,WAAW,GAAG,CAAC,YAAuB,EAAE,WAAkC,KAAa;IAClG,IAAI,CAAC,WAAW,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,OAAO,YAAY,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,CAAC;AACtD,CAAC,CAAC;AAEK,MAAM,cAAc,GAAG,CAAC,SAA+B,EAAE,GAAW,KAAwB;IACjG,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACD,IAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACjC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,QAAA,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE;AACzB,YAAA,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACpB,SAAA;QACD,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE;AAChC,YAAA,OAAO,SAAS,CAAC;AAClB,SAAA;AACF,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEK,MAAM,UAAU,GAAG,CAAC,IAAY,KAAc;AACnD,IAAA,OAAO,IAAI;SACR,KAAK,CAAC,GAAG,CAAC;SACV,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;SACpB,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;AAC7B,CAAC,CAAC;AAEK,MAAM,WAAW,GAAG,CAAC,IAA2B,KAAU;AAC/D,IAAA,IAAI,IAAI,EAAE;AACR,QAAA,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,EAAE,CAAC;AACvB,KAAA;AACH,CAAC;;AC5DD;MAEa,eAAe,CAAA;IAQ1B,WACE,CAAA,UAA8B,EACtB,WAAuC,EACvC,MAAc,EACd,OAAsB,EACtB,IAAY,EACZ,QAAkB,EAAA;AAJlB,QAAA,IAAW,CAAA,WAAA,GAAX,WAAW,CAA4B;AACvC,QAAA,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;AACd,QAAA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAe;AACtB,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AACZ,QAAA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;AAbpB,QAAA,IAAK,CAAA,KAAA,GAAgB,EAAE,CAAC;AAExB,QAAA,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;AAGvB,QAAA,IAAM,CAAA,MAAA,GAAG,CAAC,CAAC;AAUjB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,KAAK,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;KACjF;IAED,UAAU,CAAC,GAAsB,EAAE,cAA8B,EAAA;;QAC/D,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAChD,QAAA,MAAM,OAAO,GAAG,CAAA,EAAA,GAAA,GAAG,KAAH,IAAA,IAAA,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,aAA4B,CAAC;AAC5D,QAAA,MAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC7E,OAAO;AACL,YAAA,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE;YACjB,OAAO,EAAE,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC;YAC7C,cAAc;YACd,OAAO;YACP,GAAG;YACH,GAAG;SACJ,CAAC;KACH;AAED,IAAA,eAAe,CAAC,cAA8B,EAAA;QAC5C,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAC5D,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,eAAe,CAAC,CAAC;AACjE,QAAA,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;AACvC,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,SAAS,CAAC,YAAuB,EAAA;;QAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QACvD,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,aAAa,CAAC;AAC/D,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,MAAM,SAAS,GAAG,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AACzD,QAAA,IAAI,SAAS,EAAE;YACb,SAAS,GAAG,MAAM,CAAC;YACnB,SAAS,GAAG,SAAS,CAAC;AACvB,SAAA;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAEzC,QAAA,IAAI,iBAAiB,CAAC;AAEtB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAa,CAAC;;QAGlC,IAAI,MAAM,CAAC,oBAAoB,EAAE;AAC/B,YAAA,iBAAiB,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;;AAGnD,SAAA;AAAM,aAAA,IAAI,MAAA,MAAM,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,EAAE;AACpC,YAAA,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;AAC9C,SAAA;AAED;;;;;;AAMG;QACH,IAAI,CAAA,EAAA,GAAA,iBAAiB,KAAA,IAAA,IAAjB,iBAAiB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAjB,iBAAiB,CAAE,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,EAAE;AACzC,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B,aAAA;AACF,SAAA;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;;;;QAKvD,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC;AACpD,SAAA;AAED;;;;;;AAMG;AACH,QAAA,MAAM,eAAe,GAAG,YAAY,CAAC,gBAAgB,CAAC;AACtD,QAAA,IAAI,gBAAgB,KAAK,SAAS,IAAI,SAAS,KAAK,MAAM,IAAI,CAAC,SAAS,IAAI,eAAe,KAAK,SAAS,EAAE;YACzG,gBAAgB,GAAG,eAAe,CAAC;AACpC,SAAA;AAED;;;AAGG;AACH,QAAA,IAAI,WAAW,EAAE;AACf,YAAA,WAAW,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACjD,SAAA;;AAGD,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;AACtC,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAK;;;AAGpB,gBAAA,IAAI,WAAW,EAAE;AACf,oBAAA,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;AAC5C,iBAAA;;AAED,gBAAA,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;gBAE9C,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,gBAAgB,CAAC;qBACrG,IAAI,CAAC,MAAM,YAAY,CAAC,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACtF,qBAAA,IAAI,CAAC,OAAO;oBACX,YAAY;oBACZ,SAAS;oBACT,SAAS;oBACT,SAAS;AACV,iBAAA,CAAC,CAAC,CAAC;AACR,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;IAED,SAAS,CAAC,IAAY,EAAE,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,EAAA;QACvD,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;KAC7C;IAED,GAAG,CAAC,IAAY,EAAE,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,EAAA;AACjD,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAK;;YACxB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrC,YAAA,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,EAAE;AACxB,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/B,aAAA;AACD,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;AAC5C,YAAA,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAEnB,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;AACrC,YAAA,IAAI,aAAa,EAAE;gBACjB,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACnD,gBAAA,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,KAAK,0CAAE,YAAY,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,CAAC,GAAG,EAAE;oBACpD,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC;AACrD,iBAAA;AACF,aAAA;YACD,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC9D,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAO,IAAI,CAAC,WAAW,CAAA,EAAA,EAAE,SAAS,EAAE,gBAAgB,EAAG,CAAA,CAAA,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;AAC/G,SAAC,CAAC,CAAC;KACJ;IAED,mBAAmB,GAAA;AACjB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;AACpC,QAAA,IAAI,WAAW,EAAE;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,YAAA,MAAM,eAAe,GAAG,YAAY,CAAC,gBAAgB,CAAC;AAEtD,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAK;AACpB,gBAAA,OAAO,IAAI,CAAC,UAAU,CACpB,YAAY;AACZ,gBAAA,WAAW;AACX,gBAAA,MAAM,EACN,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EACjB,IAAI,EACJ,eAAe,CAChB,CAAC;AACJ,aAAC,CAAC,CAAC;AACJ,SAAA;AACD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AAED,IAAA,iBAAiB,CAAC,cAAuB,EAAA;AACvC,QAAA,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC3B,YAAA,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACb,SAAA;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5E,SAAA;KACF;AAED,IAAA,UAAU,CAAC,OAAgB,EAAA;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACrC,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;KAC/D;AAED;;AAEG;AACH,IAAA,UAAU,CAAC,OAAgB,EAAA;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrC,QAAA,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;KAChD;IAED,gBAAgB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC;KAC9D;AAED;;AAEG;IACH,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,cAAc,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC;KACvC;IAED,OAAO,GAAA;;AAEL,QAAA,IAAI,CAAC,WAAW,GAAG,SAAU,CAAC;AAC9B,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAChC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;KACjB;AAEO,IAAA,QAAQ,CAAC,OAA2B,EAAA;AAC1C,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;KACxD;IAEO,UAAU,CAAC,YAAuB,EAAE,SAA0B,EAAA;AACpE,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;AAC/B,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;AAC7D,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;KAC3B;IAEO,UAAU,CAChB,YAAmC,EACnC,WAAkC,EAClC,SAAyC,EACzC,UAAmB,EACnB,iBAA0B,EAC1B,gBAAmC,EAAA;QAEnC,IAAI,IAAI,CAAC,cAAc,EAAE;AACvB,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/B,SAAA;QACD,IAAI,WAAW,KAAK,YAAY,EAAE;AAChC,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/B,SAAA;AACD,QAAA,MAAM,UAAU,GAAG,YAAY,GAAG,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC;AACnE,QAAA,MAAM,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC,OAAO,GAAG,SAAS,CAAC;AAChE,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AACrC,QAAA,IAAI,UAAU,IAAI,UAAU,KAAK,SAAS,EAAE;AAC1C,YAAA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACrC,YAAA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;AAC/C,YAAA,IAAI,UAAU,CAAC,aAAa,KAAK,WAAW,EAAE;AAC5C,gBAAA,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AACrC,aAAA;YAED,IAAK,WAAmB,CAAC,MAAM,EAAE;AAC/B,gBAAA,OAAO,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE;oBAC/C,QAAQ,EAAE,SAAS,KAAK,SAAS,GAAG,CAAC,GAAG,SAAS;oBACjD,SAAS;oBACT,UAAU;oBACV,iBAAiB;oBACjB,gBAAgB;AACjB,iBAAA,CAAC,CAAC;AACJ,aAAA;AACF,SAAA;AACD,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC/B;AAEa,IAAA,IAAI,CAAI,IAAsB,EAAA;;AAC1C,YAAA,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;gBAClC,MAAM,IAAI,CAAC,WAAW,CAAC;AACvB,gBAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;AAC9B,aAAA;YACD,MAAM,OAAO,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC,CAAC;AAC5C,YAAA,OAAO,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC;AACtD,YAAA,OAAO,OAAO,CAAC;SAChB,CAAA,CAAA;AAAA,KAAA;AACF,CAAA;AAED,MAAM,YAAY,GAAG,CACnB,WAAsB,EACtB,KAAkB,EAClB,aAA0B,EAC1B,QAAkB,EAClB,IAAY,KACV;AACF,IAAA,IAAI,OAAQ,qBAA6B,KAAK,UAAU,EAAE;AACxD,QAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;YACnC,qBAAqB,CAAC,MAAK;gBACzB,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC3D,gBAAA,OAAO,EAAE,CAAC;AACZ,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;AACJ,KAAA;AACD,IAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3B,CAAC,CAAC;AAEF,MAAM,OAAO,GAAG,CACd,WAAsB,EACtB,KAAkB,EAClB,aAA0B,EAC1B,QAAkB,EAClB,IAAY,KACV;AACF;;;AAGG;AACH,IAAA,IAAI,CAAC,GAAG,CAAC,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AAE3F,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrB;;;;;;;;AAQG;AACH,QAAA,MAAM,qBAAqB,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,MAAM,uBAAuB,GAAG,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpE,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,GAAG,KAAK,uBAAuB,EAAE;AAChE,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7B,YAAA,OAAO,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC5C,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACzC,YAAA,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;AACrC,SAAA;AACH,KAAC,CAAC,CAAC;AACL,CAAC;;AChUD;AAQA;MACa,eAAe,CAAA;AAkE1B,IAAA,WAAA,CACqB,IAAY,EACA,IAAY,EAC3C,cAAwB,EACxB,UAAsB,EACtB,MAAc,EACd,IAAY,EACZ,cAA8B,EACG,YAA8B,EAAA;AAA9B,QAAA,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAkB;AAxEjE,QAAA,IAAa,CAAA,aAAA,GAAqB,IAAI,CAAC;;AAO/B,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,OAAO,EAAuB,CAAC;;QAE9C,IAAA,CAAA,sBAAsB,GAAG,IAAI,eAAe,CAA4D,IAAI,CAAC,CAAC;AAE9G,QAAA,IAAS,CAAA,SAAA,GAA6B,IAAI,CAAC;AAK3C,QAAA,IAAe,CAAA,eAAA,GAA0B,IAAI,CAAC;AAEtD;;AAEG;AACM,QAAA,IAAI,CAAA,IAAA,GAAG,cAAc,CAAC;;AAGrB,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAwB,CAAC;;AAE3D,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,YAAY,EAAuB,CAAC;;AAG/C,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,YAAY,EAAO,CAAC;;AAEvC,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAO,CAAC;AAEzD,QAAA,IAAA,CAAA,cAAc,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAChD,QAAA,IAAA,CAAA,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;AACpC,QAAA,IAAA,CAAA,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAClD,QAAA,IAAW,CAAA,WAAA,GAAG,MAAM,CAAC,YAAY,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;;AAEtD,QAAA,IAAgC,CAAA,gCAAA,GAAG,IAAI,CAAC;;AAGzC,QAAA,IAAA,CAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AACxB,QAAA,IAAA,CAAA,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAgCtC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC;AACzC,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,cAAc,CAAC;AACnC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,KAAK,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,GAAG,SAAS,CAAC;QAC/E,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;QACjH,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAW,CAAC,CAAC;KAClE;;AAlED,IAAA,IAAI,qBAAqB,GAAA;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IA6BD,IAAI,SAAS,CAAC,SAA2B,EAAA;AACvC,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;KACrC;IAED,IAAI,QAAQ,CAAC,QAAiB,EAAA;AAC5B,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;KACnC;IAED,IAAI,YAAY,CAAC,KAAc,EAAA;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAE3B,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK;AAChC,cAAE;AACE,gBAAA,QAAQ,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;gBAC/E,OAAO,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE;AACnD,gBAAA,KAAK,EAAE,CAAC,cAAc,KAAK,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC;AAC5E,aAAA;cACD,SAAS,CAAC;KACf;IAmBD,WAAW,GAAA;;AACT,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,wBAAwB,CAAC,IAAI,CAAC,CAAC;KAClD;IAED,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAClD;IAED,QAAQ,GAAA;QACN,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACjC;;IAGO,wBAAwB,GAAA;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;;;AAGnB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAClC,YAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,KAAK,EAAE;gBAClB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpD,aAAA;AACF,SAAA;QAED,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAK;AAC3E,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AACpC,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,kBAAkB,EAAG,IAAI,CAAC,QAAgB,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;AACvG,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;KACzB;AAED,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC5C,SAAA;AACD,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;KAChC;AAED,IAAA,IAAI,cAAc,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC5C,SAAA;QACD,OAAO,IAAI,CAAC,eAAiC,CAAC;KAC/C;AAED,IAAA,IAAI,kBAAkB,GAAA;QACpB,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC3C,SAAA;AACD,QAAA,OAAO,EAAE,CAAC;KACX;AAED;;AAEG;IACH,MAAM,GAAA;AACJ,QAAA,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;AAED;;AAEG;;IAEH,MAAM,CAAC,IAAuB,EAAE,eAA+B,EAAA;AAC7D,QAAA,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,UAAU,GAAA;QACR,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,IAAI,CAAC,aAAa,EAAE;;AAEtB,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAG,CAAC;AACnC,gBAAA,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAErE;;;;;;;AAOG;AACH,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAClE,gBAAA,IAAI,aAAa,IAAI,OAAO,CAAC,KAAK,EAAE;AAClC,oBAAA,aAAa,CAAC,KAAK,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,OAAO,CAAC,KAAK,CAAE,CAAC;AAC5C,iBAAA;AAED;;;AAGG;AACH,gBAAA,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,EAAE,CAAC;gBACpC,IAAI,OAAO,CAAC,KAAK,EAAE;AACjB,oBAAA,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;oBAE/C,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,GAAG,eAAe,CAAC,WAAW,CAAC;oBACxE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAA0B,GAAG,eAAe,CAAC,QAAQ,CAAC;AACvF,iBAAA;AACF,aAAA;AACD,YAAA,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACzB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAC5B,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,SAAA;KACF;IAED,YAAY,CAAC,cAA8B,EAAE,mBAA+C,EAAA;;QAC1F,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAChE,SAAA;AACD,QAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;AAEtC,QAAA,IAAI,MAAW,CAAC;QAChB,IAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;AAClE,QAAA,IAAI,YAAY,EAAE;YAChB,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC;AAC3C,YAAA,MAAM,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;AACrC,YAAA,IAAI,KAAK,EAAE;;;AAGT,gBAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAG,CAAC;AACnC,gBAAA,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;AACtC,aAAA;;YAED,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACjE,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,QAAQ,GAAI,cAAsB,CAAC,eAAe,CAAC;AAEzD;;;;;AAKG;AACH,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;;;AAIjF,YAAA,MAAM,UAAU,GAAG,IAAI,eAAe,CAAM,IAAI,CAAC,CAAC;YAClD,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;AAEvF,YAAA,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,mBAAmB,EAAE,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;;AAGhG,YAAA,MAAM,SAAS,GAAG,CAAA,EAAA,GAAA,QAAQ,CAAC,WAAY,CAAC,SAAS,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,QAAQ,CAAC,SAAS,CAAC;AAExE,YAAA,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,EAAE;AACjE,gBAAA,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC3B,QAAQ;gBACR,mBAAmB,EAAE,mBAAmB,KAAnB,IAAA,IAAA,mBAAmB,cAAnB,mBAAmB,GAAI,IAAI,CAAC,mBAAmB;AACrE,aAAA,CAAC,CAAC;;AAGH,YAAA,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;;;AAIjC,YAAA,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;;YAGzE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;AACxD,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC;AAClF,SAAA;QAED,CAAA,EAAA,GAAA,IAAI,CAAC,WAAW,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mCAAmC,CAAC,IAAI,CAAC,CAAC;AAE5D,QAAA,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;AAElC;;;;;;;;AAQG;AACH,QAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;AAEnD,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACxB,YAAY;AACZ,YAAA,SAAS,EAAE,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC;AAClD,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAI;YACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC1C,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjC,SAAC,CAAC,CAAC;KACJ;AAED;;AAEG;AACH,IAAA,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE,OAAgB,EAAA;QAClC,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAChD;AAED;;AAEG;AACH,IAAA,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,OAAgB,EAAA;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC1C;AAED;;AAEG;AACH,IAAA,UAAU,CAAC,OAAgB,EAAA;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAClD,OAAO,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,SAAS,CAAC;KACxC;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,OAAgB,EAAA;QAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAC3C;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,OAAgB,EAAA;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAC3C;AAED;;AAEG;IACH,gBAAgB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;KAC1C;AAED;;;AAGG;IACK,yBAAyB,CAAC,UAA2B,EAAE,cAA8B,EAAA;AAC3F,QAAA,MAAM,KAAK,GAAQ,IAAI,cAAc,EAAE,CAAC;AAExC,QAAA,KAAK,CAAC,eAAe,GAAI,cAAsB,CAAC,eAAe,CAAC;AAChE,QAAA,KAAK,CAAC,YAAY,GAAI,cAAsB,CAAC,YAAY,CAAC;AAC1D,QAAA,KAAK,CAAC,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;AACzC,QAAA,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;AACrC,QAAA,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;;QAG1C,KAAa,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACvE,KAAa,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;QAClF,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACpD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC1D,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QACpE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAC9D,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAEtD,QAAA,OAAO,KAAuB,CAAC;KAChC;AAED;;AAEG;IACK,eAAe,CAAC,UAA2B,EAAE,IAAY,EAAA;QAC/D,OAAO,UAAU,CAAC,IAAI;;AAEpB,QAAA,MAAM,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS,CAAC,EAClC,SAAS,CAAC,CAAC,SAAS,KAClB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAC9B,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,KAAK,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,EACxE,SAAS,CAAC,CAAC,OAAO,KAAK,OAAO,IAAK,OAAO,CAAC,cAAsB,CAAC,IAAI,CAAC,CAAC,EACxE,oBAAoB,EAAE,CACvB,CACF,CACF,CAAC;KACH;AAED;;AAEG;IACK,yBAAyB,CAAC,SAAc,EAAE,cAA8B,EAAA;QAC9E,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,6CAAA,CAA+C,CAAC,CAAC;AAClE,SAAA;AAEA,QAAA,KAAa,CAAC,eAAe,GAAI,cAAsB,CAAC,eAAe,CAAC;AACxE,QAAA,KAAa,CAAC,YAAY,GAAI,cAAsB,CAAC,YAAY,CAAC;AACnE,QAAA,KAAK,CAAC,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;AACzC,QAAA,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;AACrC,QAAA,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;QAE3C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC;KACjE;;gIA/XU,eAAe,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAmEb,MAAM,EAAA,SAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EACM,MAAM,EAAA,SAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;oHApEpB,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,cAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,EAAA,QAAA,EAAA,CAAA,QAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;4FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAP3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,QAAQ,EAAE,QAAQ;;oBAElB,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC;iBAC1D,CAAA;;;8BAqEI,SAAS;+BAAC,MAAM,CAAA;;8BAChB,QAAQ;;8BAAI,SAAS;+BAAC,MAAM,CAAA;;8BAM5B,QAAQ;;8BAAI,QAAQ;;yBAnDd,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBAGI,eAAe,EAAA,CAAA;sBAAxB,MAAM;gBAEG,cAAc,EAAA,CAAA;sBAAvB,MAAM;gBAGa,cAAc,EAAA,CAAA;sBAAjC,MAAM;uBAAC,UAAU,CAAA;gBAEI,gBAAgB,EAAA,CAAA;sBAArC,MAAM;uBAAC,YAAY,CAAA;;AAiWtB,MAAM,cAAc,CAAA;AAClB,IAAA,WAAA,CAAoB,KAAqB,EAAU,aAAqC,EAAU,MAAgB,EAAA;AAA9F,QAAA,IAAK,CAAA,KAAA,GAAL,KAAK,CAAgB;AAAU,QAAA,IAAa,CAAA,aAAA,GAAb,aAAa,CAAwB;AAAU,QAAA,IAAM,CAAA,MAAA,GAAN,MAAM,CAAU;KAAI;IAEtH,GAAG,CAAC,KAAU,EAAE,aAAmB,EAAA;QACjC,IAAI,KAAK,KAAK,cAAc,EAAE;YAC5B,OAAO,IAAI,CAAC,KAAK,CAAC;AACnB,SAAA;QAED,IAAI,KAAK,KAAK,sBAAsB,EAAE;YACpC,OAAO,IAAI,CAAC,aAAa,CAAC;AAC3B,SAAA;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;KAC9C;AACF,CAAA;AAED;AACA,MAAM,YAAY,GAAG,IAAI,cAAc,CAA6B,EAAE,CAAC,CAAC;AAExE;;;;;;;;;;;;;AAaG;AACH,MACM,0BAA0B,CAAA;AADhC,IAAA,WAAA,GAAA;AAEU,QAAA,IAAA,CAAA,uBAAuB,GAAG,IAAI,GAAG,EAAiC,CAAC;KAuD5E;AArDC,IAAA,mCAAmC,CAAC,MAAuB,EAAA;AACzD,QAAA,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;AACtC,QAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;KACnC;AAED,IAAA,wBAAwB,CAAC,MAAuB,EAAA;;QAC9C,CAAA,EAAA,GAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,EAAE,CAAC;AACxD,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAC7C;AAEO,IAAA,oBAAoB,CAAC,MAAuB,EAAA;AAClD,QAAA,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC;AAClC,QAAA,MAAM,gBAAgB,GAAG,aAAa,CAAC,CAAC,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC,MAAM,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC;AAC7G,aAAA,IAAI,CACH,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,KAAK,KAAI;AAC/C,YAAA,IAAI,iDAAQ,WAAW,CAAA,EAAK,MAAM,CAAK,EAAA,IAAI,CAAE,CAAC;;;YAG9C,IAAI,KAAK,KAAK,CAAC,EAAE;AACf,gBAAA,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;AACjB,aAAA;;;;AAID,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/B,SAAC,CAAC,CACH;AACA,aAAA,SAAS,CAAC,CAAC,IAAI,KAAI;;;YAGlB,IACE,CAAC,MAAM,CAAC,WAAW;gBACnB,CAAC,MAAM,CAAC,qBAAqB;gBAC7B,MAAM,CAAC,cAAc,KAAK,cAAc;AACxC,gBAAA,cAAc,CAAC,SAAS,KAAK,IAAI,EACjC;AACA,gBAAA,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;gBACtC,OAAO;AACR,aAAA;YAED,MAAM,MAAM,GAAG,oBAAoB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC9D,IAAI,CAAC,MAAM,EAAE;AACX,gBAAA,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;gBACtC,OAAO;AACR,aAAA;YAED,KAAK,MAAM,EAAE,YAAY,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;AAC5C,gBAAA,MAAM,CAAC,qBAAqB,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACzE,aAAA;AACH,SAAC,CAAC,CAAC;QAEL,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;KAC5D;;2IAvDG,0BAA0B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA;+IAA1B,0BAA0B,EAAA,CAAA,CAAA;4FAA1B,0BAA0B,EAAA,UAAA,EAAA,CAAA;kBAD/B,UAAU;;AA2DJ,MAAM,4BAA4B,GAAG,MAAe;IACzD,OAAO;AACL,QAAA,OAAO,EAAE,YAAY;AACrB,QAAA,UAAU,EAAE,4BAA4B;QACxC,IAAI,EAAE,CAAC,MAAM,CAAC;KACf,CAAC;AACJ,EAAE;AAEF,SAAS,4BAA4B,CAAC,MAAe,EAAA;AACnD;;;AAGG;AACH,IAAA,IAAK,MAAc,KAAd,IAAA,IAAA,MAAc,uBAAd,MAAc,CAAE,4BAA4B,EAAE;QACjD,OAAO,IAAI,0BAA0B,EAAE,CAAC;AACzC,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;ACjhBA,MAAM,kBAAkB,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAatG,IAAA,aAAa,SAAb,aAAa,CAAA;IASxB,WACsB,CAAA,YAA6B,EACzC,OAAsB,EACtB,MAAc,EACd,CAAa,EACX,CAAS,EACnB,CAAoB,EAAA;AALA,QAAA,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAiB;AACzC,QAAA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAe;AACtB,QAAA,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;AACd,QAAA,IAAC,CAAA,CAAA,GAAD,CAAC,CAAY;AACX,QAAA,IAAC,CAAA,CAAA,GAAD,CAAC,CAAQ;QAGnB,CAAC,CAAC,MAAM,EAAE,CAAC;QACX,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC;KAChC;AAED;;AAEG;AAEH,IAAA,OAAO,CAAC,EAAS,EAAA;;AACf,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AAEjF,QAAA,IAAI,MAAA,IAAI,CAAC,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAS,EAAE,EAAE;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;AAC9E,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YACxB,EAAE,CAAC,cAAc,EAAE,CAAC;AACrB,SAAA;aAAM,IAAI,WAAW,IAAI,IAAI,EAAE;AAC9B,YAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;YAC5E,EAAE,CAAC,cAAc,EAAE,CAAC;AACrB,SAAA;KACF;EACF;8HArCY,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,aAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;kHAAb,aAAa,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,WAAA,EAAA,aAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;AAAb,aAAa,GAAA,UAAA,CAAA;AARzB,IAAA,QAAQ,CAAC;AACR,QAAA,MAAM,EAAE,kBAAkB;KAC3B,CAAC;CAMW,EAAA,aAAa,CAqCzB,CAAA;4FArCY,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;;AAET,oBAAA,MAAM,EAAE,kBAAkB;iBAC3B,CAAA;;;8BAYI,QAAQ;;yBARX,WAAW,EAAA,CAAA;sBADV,KAAK;gBAIN,eAAe,EAAA,CAAA;sBADd,KAAK;gBAqBN,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;;;ACxCnC;;;;;AAKG;MAIU,2BAA2B,CAAA;IAOtC,WACU,CAAA,gBAAkC,EAClC,OAAsB,EACtB,UAAsB,EACtB,MAAc,EACF,UAAuB,EAAA;AAJnC,QAAA,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;AAClC,QAAA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAe;AACtB,QAAA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;AACtB,QAAA,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;AACF,QAAA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAa;AAV7C,QAAA,IAAe,CAAA,eAAA,GAAoB,SAAS,CAAC;KAWzC;IAEJ,QAAQ,GAAA;QACN,IAAI,CAAC,sBAAsB,EAAE,CAAC;KAC/B;IAED,WAAW,GAAA;QACT,IAAI,CAAC,sBAAsB,EAAE,CAAC;KAC/B;IAEO,sBAAsB,GAAA;;AAC5B,QAAA,IAAI,MAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YACzG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC;AAC3C,SAAA;KACF;AAED;;AAEG;AAEH,IAAA,OAAO,CAAC,EAAW,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;AAE5F;;;;;;;AAOG;QACH,EAAE,CAAC,cAAc,EAAE,CAAC;KACrB;;4IA9CU,2BAA2B,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAAD,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;gIAA3B,2BAA2B,EAAA,QAAA,EAAA,+BAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;4FAA3B,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBAHvC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,+BAA+B;iBAC1C,CAAA;;;8BAaI,QAAQ;;yBAVX,eAAe,EAAA,CAAA;sBADd,KAAK;gBAIN,eAAe,EAAA,CAAA;sBADd,KAAK;gBA8BN,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAA;;MAmBtB,mCAAmC,CAAA;IAO9C,WACU,CAAA,gBAAkC,EAClC,OAAsB,EACtB,UAAsB,EACtB,MAAc,EACF,UAAuB,EAAA;AAJnC,QAAA,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;AAClC,QAAA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAe;AACtB,QAAA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;AACtB,QAAA,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;AACF,QAAA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAa;AAV7C,QAAA,IAAe,CAAA,eAAA,GAAoB,SAAS,CAAC;KAWzC;IAEJ,QAAQ,GAAA;QACN,IAAI,CAAC,sBAAsB,EAAE,CAAC;KAC/B;IAED,WAAW,GAAA;QACT,IAAI,CAAC,sBAAsB,EAAE,CAAC;KAC/B;IAEO,sBAAsB,GAAA;;AAC5B,QAAA,IAAI,MAAA,IAAI,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YACzG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC;AAC3C,SAAA;KACF;AAED;;AAEG;IAEH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;KAC7F;;oJApCU,mCAAmC,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;wIAAnC,mCAAmC,EAAA,QAAA,EAAA,gCAAA,EAAA,MAAA,EAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,WAAA,EAAA,EAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;4FAAnC,mCAAmC,EAAA,UAAA,EAAA,CAAA;kBAH/C,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gCAAgC;iBAC3C,CAAA;;;8BAaI,QAAQ;;yBAVX,eAAe,EAAA,CAAA;sBADd,KAAK;gBAIN,eAAe,EAAA,CAAA;sBADd,KAAK;gBA8BN,OAAO,EAAA,CAAA;sBADN,YAAY;uBAAC,OAAO,CAAA;;;ACvFvB,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;AAEnF,MAAM,WAAW,GAAG;IAClB,MAAM;IACN,QAAQ;IACR,aAAa;IACb,KAAK;IACL,OAAO;IACP,WAAW;IACX,aAAa;IACb,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,WAAW;IACX,aAAa;CACd,CAAC;AAsBW,IAAA,MAAM,SAAN,MAAM,CAAA;IAEjB,WACE,CAAA,GAAe,EACf,mBAAwC,EACxC,QAAkB,EAClB,eAAgC,EACtB,CAAS,EACnB,CAAoB,EAAA;AADV,QAAA,IAAC,CAAA,CAAA,GAAD,CAAC,CAAQ;QAGnB,CAAC,CAAC,MAAM,EAAE,CAAC;AACX,QAAA,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,aAAa,CAAC;AAC5B,QAAA,GAAG,CAAC,aAAa,CAAC,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;AACnF,QAAA,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,CAAC;KACtE;EACF;uHAfY,MAAM,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAAE,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;2GAAN,MAAM,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,UAAA,EAAA,SAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;AAAN,MAAM,GAAA,UAAA,CAAA;AATlB,IAAA,QAAQ,CAAC;AACR,QAAA,MAAM,EAAE,UAAU;AAClB,QAAA,OAAO,EAAE,WAAW;KACrB,CAAC;CAMW,EAAA,MAAM,CAelB,CAAA;4FAfY,MAAM,EAAA,UAAA,EAAA,CAAA;kBALlB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;;AAET,oBAAA,MAAM,EAAE,UAAU;iBACnB,CAAA;;;AChCD;MACa,OAAO,CAAA;AAsBlB,IAAA,WAAA,CAAoB,OAAsB,EAAA;AAAtB,QAAA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAe;AAX1C;;AAEG;AACO,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,YAAY,EAAmB,CAAC;AAClE;;AAEG;AACO,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAmB,CAAC;AAEzD,QAAA,IAAU,CAAA,UAAA,GAAG,QAAQ,CAAC;KAEgB;IAE9C,kBAAkB,GAAA;QAChB,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC1B;IAED,qBAAqB,GAAA;QACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC1B;AAED;;AAEG;AACH,IAAA,iBAAiB,CAAC,EAAE,YAAY,EAAE,SAAS,EAAwB,EAAA;AACjE,QAAA,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;AACrC,QAAA,IAAI,SAAS,IAAI,OAAO,KAAK,SAAS,EAAE;YACtC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;AAC/C,SAAA;KACF;AAED;;AAEG;AACH,IAAA,gBAAgB,CAAC,EAAE,YAAY,EAAE,SAAS,EAAuB,EAAA;AAC/D,QAAA,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;AACrC,QAAA,IAAI,SAAS,IAAI,OAAO,KAAK,SAAS,EAAE;YACtC,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,gBAAA,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC;AACnC,aAAA;YACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;AAC9C,SAAA;KACF;AAED;;;;;;;;;;;;;;;;;;;AAmBG;AAEH,IAAA,MAAM,CAAC,UAAgC,EAAA;AACrC,QAAA,MAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC;AACnD,QAAA,MAAM,GAAG,GAAG,WAAW,GAAG,UAAU,GAAI,UAA0B,CAAC,MAAM,CAAC,GAAG,CAAC;QAC9E,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,GAAG,CAAC;QAC/D,MAAM,UAAU,GAAG,CAAA,EAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,CAAA,EAAI,GAAG,CAAA,CAAE,CAAC;AAEtD;;;;;AAKG;QACH,IAAI,CAAC,WAAW,EAAE;YACf,UAA0B,CAAC,eAAe,EAAE,CAAC;AAC/C,SAAA;AAED,QAAA,IAAI,eAAe,EAAE;YACnB,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACrD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;;YAG/D,IAAI,CAAA,UAAU,KAAA,IAAA,IAAV,UAAU,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAV,UAAU,CAAE,GAAG,MAAK,UAAU,EAAE;gBAClC,OAAO;AACR,aAAA;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAA,MAAM,gBAAgB,GAAG,QAAQ,IAAI,UAAU,KAAK,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC;AACzF,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,EACtC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,gBAAgB,CACnB,EAAA,EAAA,QAAQ,EAAE,IAAI,EACd,kBAAkB,EAAE,MAAM,IAC1B,CAAC;AACJ,SAAA;AAAM,aAAA;YACL,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACpD;;;AAGG;AACH,YAAA,MAAM,GAAG,GAAG,CAAA,SAAS,KAAT,IAAA,IAAA,SAAS,KAAT,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,SAAS,CAAE,GAAG,KAAI,UAAU,CAAC;YACzC,MAAM,gBAAgB,GAAG,SAAS,KAAA,IAAA,IAAT,SAAS,KAAT,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,SAAS,CAAE,WAAW,CAAC;AAEhD,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAC/B,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,gBAAgB,CACnB,EAAA,EAAA,QAAQ,EAAE,IAAI,EACd,kBAAkB,EAAE,MAAM,IAC1B,CAAC;AACJ,SAAA;KACF;IAED,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;KACvC;AAED;;;;;AAKG;IACK,iBAAiB,GAAA;QACvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,KAAI;;YAEnC,MAAM,WAAW,GAAG,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAEnD,YAAA,IAAI,WAAW,KAAK,IAAI,CAAC,UAAU,EAAE;AACnC,gBAAA,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC;gBAC9B,IAAI,CAAC,cAAc,EAAE,CAAC;AACvB,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;AAEG;IACK,cAAc,GAAA;AACpB;;;;AAIG;AACH,QAAA,MAAM,MAAM,GAAI,IAAI,CAAC,MAAc,CAAC,EAAiB,CAAC;AAEtD,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE;AAC7B;;;AAGG;YACH,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7C,SAAA;AAAM,aAAA;AACL;;;AAGG;YACH,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC5C,SAAA;KACF;;wHA3KU,OAAO,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,aAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAP,mBAAA,OAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,OAAO,8RASc,UAAU,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;4FAT/B,OAAO,EAAA,UAAA,EAAA,CAAA;kBAJnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,UAAU;iBACrB,CAAA;iGAW6D,SAAS,EAAA,CAAA;sBAApE,SAAS;uBAAC,WAAW,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,CAAA;gBAKhD,iBAAiB,EAAA,CAAA;sBAA1B,MAAM;gBAIG,gBAAgB,EAAA,CAAA;sBAAzB,MAAM;gBA0DP,MAAM,EAAA,CAAA;sBADL,YAAY;uBAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,CAAA;;;AC3FlC,MAAA,GAAG,GAAG,CAAC,CAAM,KAAS;AACjC,IAAA,IAAI,OAAO,oCAAoC,KAAK,UAAU,EAAE;AAC9D,QAAA,OAAO,oCAAoC,CAAC,CAAC,CAAC,CAAC;AAChD,KAAA;AACD,IAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;AAC/C,QAAA,OAAO,qBAAqB,CAAC,CAAC,CAAC,CAAC;AACjC,KAAA;AACD,IAAA,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;AACvB;;ACLA;MAGa,aAAa,CAAA;IAUxB,WAAsB,CAAA,QAAkB,EAAY,UAAsB,EAAA;AAApD,QAAA,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;AAAY,QAAA,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;AATlE,QAAA,IAAQ,CAAA,QAAA,GAAyB,MAAK;;AAE9C,SAAC,CAAC;AACM,QAAA,IAAS,CAAA,SAAA,GAAe,MAAK;;AAErC,SAAC,CAAC;KAI4E;AAE9E,IAAA,UAAU,CAAC,KAAU,EAAA;AACnB,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC7D,QAAA,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAClC;AAED;;;;;;;;;;;;AAYG;IACH,iBAAiB,CAAC,EAAe,EAAE,KAAU,EAAA;AAC3C,QAAA,IAAI,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE;AACxC,YAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,gBAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,gBAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACtB,aAAA;AACD,YAAA,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAClC,SAAA;KACF;AAGD,IAAA,gBAAgB,CAAC,EAAO,EAAA;AACtB,QAAA,IAAI,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE;YACxC,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,YAAA,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAClC,SAAA;KACF;AAED,IAAA,gBAAgB,CAAC,EAAwB,EAAA;AACvC,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;KACpB;AAED,IAAA,iBAAiB,CAAC,EAAc,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACrB;AAED,IAAA,gBAAgB,CAAC,UAAmB,EAAA;QAClC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,GAAG,UAAU,CAAC;KACrD;IAED,WAAW,GAAA;QACT,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;AAClC,SAAA;KACF;IAED,eAAe,GAAA;AACb,QAAA,IAAI,SAAS,CAAC;QACd,IAAI;YACF,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAY,SAAS,CAAC,CAAC;AACrD,SAAA;QAAC,OAAM,EAAA,EAAA;;AAEP,SAAA;QAED,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;AACR,SAAA;;QAGD,IAAI,SAAS,CAAC,aAAa,EAAE;AAC3B,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;AAChG,SAAA;AAED;;;AAGG;AACH,QAAA,MAAM,WAAW,GAAG,SAAS,CAAC,OAAc,CAAC;AAC7C,QAAA,IAAI,WAAW,EAAE;AACf,YAAA,MAAM,cAAc,GAAG,CAAC,eAAe,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACjH,YAAA,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAChC,gBAAA,IAAI,OAAO,WAAW,CAAC,MAAM,CAAC,KAAK,WAAW,EAAE;oBAC9C,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACpD,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAa,KAAI;AACzC,wBAAA,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;AACjB,wBAAA,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACnC,qBAAC,CAAC;AACH,iBAAA;AACH,aAAC,CAAC,CAAC;AACJ,SAAA;KACF;;8HApGU,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;kHAAb,aAAa,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,SAAA,EAAA,iCAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;4FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,SAAS;wHA0CR,gBAAgB,EAAA,CAAA;sBADf,YAAY;uBAAC,SAAS,EAAE,CAAC,eAAe,CAAC,CAAA;;AA+D/B,MAAA,eAAe,GAAG,CAAC,OAAmB,KAAU;IAC3D,GAAG,CAAC,MAAK;AACP,QAAA,MAAM,KAAK,GAAG,OAAO,CAAC,aAAiC,CAAC;AACxD,QAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1E,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAClC,QAAA,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACvC,QAAA,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,QAAQ,EAAE;gBACZ,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC;AAClD,aAAA;AAAM,iBAAA;AACL,gBAAA,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC3B,aAAA;AACF,SAAA;AACH,KAAC,CAAC,CAAC;AACL,EAAE;AAEF,MAAM,UAAU,GAAG,CAAC,OAAoB,KAAI;AAC1C,IAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACpC,MAAM,OAAO,GAAa,EAAE,CAAC;AAC7B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACzC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,IAAI,KAAK,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;AAC5C,YAAA,OAAO,CAAC,IAAI,CAAC,CAAA,IAAA,EAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;AAC1C,SAAA;AACF,KAAA;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,OAAoB,EAAE,OAAiB,KAAI;AAC7D,IAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AACpC,IAAA,SAAS,CAAC,MAAM,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AAC1G,IAAA,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,KAAa,EAAE,MAAc,KAAa;AAC5D,IAAA,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC;AACtD,CAAC;;AClJD;;AAEG;MACU,kBAAkB,CAAA;AAC7B;;AAEG;AACH,IAAA,YAAY,CAAC,MAA8B,EAAA;AACzC,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;AAEG;AACH,IAAA,YAAY,CAAC,MAA8B,EAAA;AACzC,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;AAEG;IACH,KAAK,CAAC,MAA8B,EAAE,aAAkC,EAAA;QACtE,OAAO;KACR;AAED;;AAEG;AACH,IAAA,QAAQ,CAAC,MAA8B,EAAA;AACrC,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;AAIG;IACH,gBAAgB,CAAC,MAA8B,EAAE,IAA4B,EAAA;AAC3E,QAAA,IAAI,MAAM,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE;AAC3C,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;;AAGD,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;AACnC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QAClC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAEzC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;AACjC,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;;AAGD,QAAA,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;YACvB,IAAI,aAAa,CAAC,GAAG,CAAC,KAAK,YAAY,CAAC,GAAG,CAAC,EAAE;AAC5C,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AACF;;AC/DD;MAQa,qBAAqB,CAAA;AAChC,IAAA,WAAA,CAAoB,IAAoC,EAAA;AAApC,QAAA,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgC;KAAI;AAE5D;;AAEG;AACH,IAAA,MAAM,CAAC,IAAW,EAAA;AAChB,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE,EAAS,CAAC;KAC9C;AAED;;AAEG;AACH,IAAA,OAAO,CAAC,IAAU,EAAE,IAAa,EAAE,EAAW,EAAA;AAC5C,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;KAC1C;AAED;;AAEG;IACH,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;KAC3B;AACF;;AC/BD;;AAEG;;;;"}