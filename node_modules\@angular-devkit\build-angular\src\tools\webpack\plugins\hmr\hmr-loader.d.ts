/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export declare const HmrLoader: string;
export default function localizeExtractLoader(this: import('webpack').LoaderContext<{}>, content: string, map: Parameters<import('webpack').LoaderDefinitionFunction>[1]): void;
