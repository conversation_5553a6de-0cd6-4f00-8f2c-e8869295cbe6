{"name": "@ionic/angular", "version": "7.8.6", "description": "Angular specific wrappers for @ionic/core", "keywords": ["ionic", "framework", "angular", "mobile", "app", "webapp", "capacitor", "<PERSON><PERSON>", "progressive web app", "pwa"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/ionic-framework.git"}, "bugs": {"url": "https://github.com/ionic-team/ionic/issues"}, "homepage": "https://ionicframework.com/", "exports": {"./css/*": {"style": "./css/*"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2020": "./esm2020/ionic-angular.mjs", "es2020": "./fesm2020/ionic-angular.mjs", "es2015": "./fesm2015/ionic-angular.mjs", "node": "./fesm2015/ionic-angular.mjs", "default": "./fesm2020/ionic-angular.mjs"}, "./common": {"types": "./common/index.d.ts", "esm2020": "./esm2020/common/ionic-angular-common.mjs", "es2020": "./fesm2020/ionic-angular-common.mjs", "es2015": "./fesm2015/ionic-angular-common.mjs", "node": "./fesm2015/ionic-angular-common.mjs", "default": "./fesm2020/ionic-angular-common.mjs"}, "./standalone": {"types": "./standalone/index.d.ts", "esm2020": "./esm2020/standalone/ionic-angular-standalone.mjs", "es2020": "./fesm2020/ionic-angular-standalone.mjs", "es2015": "./fesm2015/ionic-angular-standalone.mjs", "node": "./fesm2015/ionic-angular-standalone.mjs", "default": "./fesm2020/ionic-angular-standalone.mjs"}}, "dependencies": {"@ionic/core": "7.8.6", "ionicons": "^7.0.0", "jsonc-parser": "^3.0.0", "tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": ">=14.0.0", "@angular/forms": ">=14.0.0", "@angular/router": ">=14.0.0", "rxjs": ">=7.5.0", "zone.js": ">=0.11.0"}, "schematics": "./schematics/collection.json", "module": "fesm2015/ionic-angular.mjs", "es2020": "fesm2020/ionic-angular.mjs", "esm2020": "esm2020/ionic-angular.mjs", "fesm2020": "fesm2020/ionic-angular.mjs", "fesm2015": "fesm2015/ionic-angular.mjs", "typings": "index.d.ts", "sideEffects": false}