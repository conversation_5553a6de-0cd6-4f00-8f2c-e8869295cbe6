{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularComponent", "title": "Angular Component Options Schema", "type": "object", "description": "Creates a new, generic component definition in the given project.", "additionalProperties": false, "properties": {"path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the component file, relative to the current workspace. Default is a folder with the same name as the component in the project root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "name": {"type": "string", "description": "The name of the component.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the component?"}, "displayBlock": {"description": "Specifies if the style will contain `:host { display: block; }`.", "type": "boolean", "default": false, "alias": "b"}, "inlineStyle": {"description": "Include styles inline in the component.ts file. Only CSS styles can be included inline. By default, an external styles file is created and referenced in the component.ts file.", "type": "boolean", "default": false, "alias": "s", "x-user-analytics": "ep.ng_inline_style"}, "inlineTemplate": {"description": "Include template inline in the component.ts file. By default, an external template file is created and referenced in the component.ts file.", "type": "boolean", "default": false, "alias": "t", "x-user-analytics": "ep.ng_inline_template"}, "standalone": {"description": "Whether the generated component is standalone.", "type": "boolean", "default": false, "x-user-analytics": "ep.ng_standalone"}, "viewEncapsulation": {"description": "The view encapsulation strategy to use in the new component.", "enum": ["Emulated", "None", "ShadowDom"], "type": "string", "alias": "v"}, "changeDetection": {"description": "The change detection strategy to use in the new component.", "enum": ["<PERSON><PERSON><PERSON>", "OnPush"], "type": "string", "default": "<PERSON><PERSON><PERSON>", "alias": "c"}, "prefix": {"type": "string", "description": "The prefix to apply to the generated component selector.", "alias": "p", "oneOf": [{"maxLength": 0}, {"minLength": 1, "format": "html-selector"}]}, "style": {"description": "The file extension or preprocessor to use for style files, or 'none' to skip generating the style file.", "type": "string", "default": "css", "enum": ["css", "scss", "sass", "less", "none"], "x-user-analytics": "ep.ng_style"}, "type": {"type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\".", "default": "Component"}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new component.", "default": false}, "flat": {"type": "boolean", "description": "Create the new files at the top level of the current project.", "default": false}, "skipImport": {"type": "boolean", "description": "Do not import this component into the owning NgModule.", "default": false}, "selector": {"type": "string", "format": "html-selector", "description": "The HTML selector to use for this component."}, "skipSelector": {"type": "boolean", "default": false, "description": "Specifies if the component should have a selector or not."}, "module": {"type": "string", "description": "The declaring NgModule.", "alias": "m"}, "export": {"type": "boolean", "default": false, "description": "The declaring NgModule exports this component."}}, "required": ["name", "project"]}