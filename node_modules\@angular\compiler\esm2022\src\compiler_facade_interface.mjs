/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export var FactoryTarget;
(function (FactoryTarget) {
    FactoryTarget[FactoryTarget["Directive"] = 0] = "Directive";
    FactoryTarget[FactoryTarget["Component"] = 1] = "Component";
    FactoryTarget[FactoryTarget["Injectable"] = 2] = "Injectable";
    FactoryTarget[FactoryTarget["Pipe"] = 3] = "Pipe";
    FactoryTarget[FactoryTarget["NgModule"] = 4] = "NgModule";
})(FactoryTarget || (FactoryTarget = {}));
export var R3TemplateDependencyKind;
(function (R3TemplateDependencyKind) {
    R3TemplateDependencyKind[R3TemplateDependencyKind["Directive"] = 0] = "Directive";
    R3TemplateDependencyKind[R3TemplateDependencyKind["Pipe"] = 1] = "Pipe";
    R3TemplateDependencyKind[R3TemplateDependencyKind["NgModule"] = 2] = "NgModule";
})(R3TemplateDependencyKind || (R3TemplateDependencyKind = {}));
export var ViewEncapsulation;
(function (ViewEncapsulation) {
    ViewEncapsulation[ViewEncapsulation["Emulated"] = 0] = "Emulated";
    // Historically the 1 value was for `Native` encapsulation which has been removed as of v11.
    ViewEncapsulation[ViewEncapsulation["None"] = 2] = "None";
    ViewEncapsulation[ViewEncapsulation["ShadowDom"] = 3] = "ShadowDom";
})(ViewEncapsulation || (ViewEncapsulation = {}));
//# sourceMappingURL=data:application/json;base64,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