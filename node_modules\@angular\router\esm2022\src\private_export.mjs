/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { ɵEmptyOutletComponent } from './components/empty_outlet';
export { loadChildren as ɵloadChildren } from './router_config_loader';
export { ROUTER_PROVIDERS as ɵROUTER_PROVIDERS } from './router_module';
export { afterNextNavigation as ɵafterNextNavigation } from './utils/navigations';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJpdmF0ZV9leHBvcnQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9yb3V0ZXIvc3JjL3ByaXZhdGVfZXhwb3J0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sRUFBQyxxQkFBcUIsRUFBQyxNQUFNLDJCQUEyQixDQUFDO0FBRWhFLE9BQU8sRUFBQyxZQUFZLElBQUksYUFBYSxFQUFDLE1BQU0sd0JBQXdCLENBQUM7QUFDckUsT0FBTyxFQUFDLGdCQUFnQixJQUFJLGlCQUFpQixFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFDdEUsT0FBTyxFQUFDLG1CQUFtQixJQUFJLG9CQUFvQixFQUFDLE1BQU0scUJBQXFCLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuZXhwb3J0IHvJtUVtcHR5T3V0bGV0Q29tcG9uZW50fSBmcm9tICcuL2NvbXBvbmVudHMvZW1wdHlfb3V0bGV0JztcbmV4cG9ydCB7UmVzdG9yZWRTdGF0ZSBhcyDJtVJlc3RvcmVkU3RhdGV9IGZyb20gJy4vbmF2aWdhdGlvbl90cmFuc2l0aW9uJztcbmV4cG9ydCB7bG9hZENoaWxkcmVuIGFzIMm1bG9hZENoaWxkcmVufSBmcm9tICcuL3JvdXRlcl9jb25maWdfbG9hZGVyJztcbmV4cG9ydCB7Uk9VVEVSX1BST1ZJREVSUyBhcyDJtVJPVVRFUl9QUk9WSURFUlN9IGZyb20gJy4vcm91dGVyX21vZHVsZSc7XG5leHBvcnQge2FmdGVyTmV4dE5hdmlnYXRpb24gYXMgybVhZnRlck5leHROYXZpZ2F0aW9ufSBmcm9tICcuL3V0aWxzL25hdmlnYXRpb25zJztcbiJdfQ==