"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelCapLogger = exports.LevelTransformLogger = void 0;
const logger_1 = require("./logger");
class LevelTransformLogger extends logger_1.Logger {
    constructor(name, parent = null, levelTransform) {
        super(name, parent);
        this.name = name;
        this.parent = parent;
        this.levelTransform = levelTransform;
    }
    log(level, message, metadata = {}) {
        return super.log(this.levelTransform(level), message, metadata);
    }
    createChild(name) {
        return new LevelTransformLogger(name, this, this.levelTransform);
    }
}
exports.LevelTransformLogger = LevelTransformLogger;
class LevelCapLogger extends LevelTransformLogger {
    constructor(name, parent = null, levelCap) {
        super(name, parent, (level) => {
            return (LevelCapLogger.levelMap[levelCap][level] || level);
        });
        this.name = name;
        this.parent = parent;
        this.levelCap = levelCap;
    }
}
exports.LevelCapLogger = LevelCapLogger;
LevelCapLogger.levelMap = {
    debug: { debug: 'debug', info: 'debug', warn: 'debug', error: 'debug', fatal: 'debug' },
    info: { debug: 'debug', info: 'info', warn: 'info', error: 'info', fatal: 'info' },
    warn: { debug: 'debug', info: 'info', warn: 'warn', error: 'warn', fatal: 'warn' },
    error: { debug: 'debug', info: 'info', warn: 'warn', error: 'error', fatal: 'error' },
    fatal: { debug: 'debug', info: 'info', warn: 'warn', error: 'error', fatal: 'fatal' },
};
//# sourceMappingURL=data:application/json;base64,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