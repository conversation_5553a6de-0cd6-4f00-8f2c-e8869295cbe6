{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function retryWhen(notifier) {\n  return operate((source, subscriber) => {\n    let innerSub;\n    let syncResub = false;\n    let errors$;\n    const subscribeForRetryWhen = () => {\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, err => {\n        if (!errors$) {\n          errors$ = new Subject();\n          innerFrom(notifier(errors$)).subscribe(createOperatorSubscriber(subscriber, () => innerSub ? subscribeForRetryWhen() : syncResub = true));\n        }\n        if (errors$) {\n          errors$.next(err);\n        }\n      }));\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRetryWhen();\n      }\n    };\n    subscribeForRetryWhen();\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "Subject", "operate", "createOperatorSubscriber", "retry<PERSON><PERSON>", "notifier", "source", "subscriber", "innerSub", "syncResub", "errors$", "subscribeForRetryWhen", "subscribe", "undefined", "err", "next", "unsubscribe"], "sources": ["D:/2025/agmuicon/node_modules/rxjs/dist/esm/internal/operators/retryWhen.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function retryWhen(notifier) {\n    return operate((source, subscriber) => {\n        let innerSub;\n        let syncResub = false;\n        let errors$;\n        const subscribeForRetryWhen = () => {\n            innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, (err) => {\n                if (!errors$) {\n                    errors$ = new Subject();\n                    innerFrom(notifier(errors$)).subscribe(createOperatorSubscriber(subscriber, () => innerSub ? subscribeForRetryWhen() : (syncResub = true)));\n                }\n                if (errors$) {\n                    errors$.next(err);\n                }\n            }));\n            if (syncResub) {\n                innerSub.unsubscribe();\n                innerSub = null;\n                syncResub = false;\n                subscribeForRetryWhen();\n            }\n        };\n        subscribeForRetryWhen();\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAE;EAChC,OAAOH,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ;IACZ,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,OAAO;IACX,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;MAChCH,QAAQ,GAAGF,MAAM,CAACM,SAAS,CAACT,wBAAwB,CAACI,UAAU,EAAEM,SAAS,EAAEA,SAAS,EAAGC,GAAG,IAAK;QAC5F,IAAI,CAACJ,OAAO,EAAE;UACVA,OAAO,GAAG,IAAIT,OAAO,CAAC,CAAC;UACvBD,SAAS,CAACK,QAAQ,CAACK,OAAO,CAAC,CAAC,CAACE,SAAS,CAACT,wBAAwB,CAACI,UAAU,EAAE,MAAMC,QAAQ,GAAGG,qBAAqB,CAAC,CAAC,GAAIF,SAAS,GAAG,IAAK,CAAC,CAAC;QAC/I;QACA,IAAIC,OAAO,EAAE;UACTA,OAAO,CAACK,IAAI,CAACD,GAAG,CAAC;QACrB;MACJ,CAAC,CAAC,CAAC;MACH,IAAIL,SAAS,EAAE;QACXD,QAAQ,CAACQ,WAAW,CAAC,CAAC;QACtBR,QAAQ,GAAG,IAAI;QACfC,SAAS,GAAG,KAAK;QACjBE,qBAAqB,CAAC,CAAC;MAC3B;IACJ,CAAC;IACDA,qBAAqB,CAAC,CAAC;EAC3B,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}