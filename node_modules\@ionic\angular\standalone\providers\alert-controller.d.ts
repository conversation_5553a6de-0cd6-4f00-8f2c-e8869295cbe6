import { OverlayBaseController } from '@ionic/angular/common';
import type { AlertOptions } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class AlertController extends OverlayBaseController<AlertOptions, HTMLIonAlertElement> {
    constructor();
    static ɵfac: i0.ɵɵFactoryDeclaration<AlertController, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<AlertController>;
}
