/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { AnimationMetadataType } from '@angular/animations';
import { AnimationStateStyles, AnimationTransitionFactory } from './animation_transition_factory';
export function buildTrigger(name, ast, normalizer) {
    return new AnimationTrigger(name, ast, normalizer);
}
export class AnimationTrigger {
    constructor(name, ast, _normalizer) {
        this.name = name;
        this.ast = ast;
        this._normalizer = _normalizer;
        this.transitionFactories = [];
        this.states = new Map();
        ast.states.forEach((ast) => {
            const defaultParams = (ast.options && ast.options.params) || {};
            this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));
        });
        balanceProperties(this.states, 'true', '1');
        balanceProperties(this.states, 'false', '0');
        ast.transitions.forEach((ast) => {
            this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));
        });
        this.fallbackTransition = createFallbackTransition(name, this.states, this._normalizer);
    }
    get containsQueries() {
        return this.ast.queryCount > 0;
    }
    matchTransition(currentState, nextState, element, params) {
        const entry = this.transitionFactories.find((f) => f.match(currentState, nextState, element, params));
        return entry || null;
    }
    matchStyles(currentState, params, errors) {
        return this.fallbackTransition.buildStyles(currentState, params, errors);
    }
}
function createFallbackTransition(triggerName, states, normalizer) {
    const matchers = [(fromState, toState) => true];
    const animation = { type: AnimationMetadataType.Sequence, steps: [], options: null };
    const transition = {
        type: AnimationMetadataType.Transition,
        animation,
        matchers,
        options: null,
        queryCount: 0,
        depCount: 0,
    };
    return new AnimationTransitionFactory(triggerName, transition, states);
}
function balanceProperties(stateMap, key1, key2) {
    if (stateMap.has(key1)) {
        if (!stateMap.has(key2)) {
            stateMap.set(key2, stateMap.get(key1));
        }
    }
    else if (stateMap.has(key2)) {
        stateMap.set(key1, stateMap.get(key2));
    }
}
//# sourceMappingURL=data:application/json;base64,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