"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
const generate_from_files_1 = require("../utility/generate-from-files");
function default_1(options) {
    // This schematic uses an older method to implement the flat option
    const flat = options.flat;
    options.flat = true;
    const extraTemplateValues = {
        'if-flat': (s) => (flat ? '' : s),
    };
    return options.functional
        ? (0, generate_from_files_1.generateFromFiles)({ ...options, templateFilesDirectory: './functional-files' }, extraTemplateValues)
        : (0, generate_from_files_1.generateFromFiles)({ ...options, templateFilesDirectory: './class-files' }, extraTemplateValues);
}
exports.default = default_1;
//# sourceMappingURL=data:application/json;base64,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