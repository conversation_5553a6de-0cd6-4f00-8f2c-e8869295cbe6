"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.importsProvidersFrom = exports.findBootstrapApplicationCall = exports.addModuleImportToStandaloneBootstrap = void 0;
var standalone_1 = require("./standalone");
Object.defineProperty(exports, "addModuleImportToStandaloneBootstrap", { enumerable: true, get: function () { return standalone_1.addModuleImportToStandaloneBootstrap; } });
Object.defineProperty(exports, "findBootstrapApplicationCall", { enumerable: true, get: function () { return standalone_1.findBootstrapApplicationCall; } });
Object.defineProperty(exports, "importsProvidersFrom", { enumerable: true, get: function () { return standalone_1.importsProvidersFrom; } });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29tcG9uZW50cy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL3NjaGVtYXRpY3MvYW5ndWxhci9wcml2YXRlL2NvbXBvbmVudHMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBRUgsMkNBSXNCO0FBSHBCLGtJQUFBLG9DQUFvQyxPQUFBO0FBQ3BDLDBIQUFBLDRCQUE0QixPQUFBO0FBQzVCLGtIQUFBLG9CQUFvQixPQUFBIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmV4cG9ydCB7XG4gIGFkZE1vZHVsZUltcG9ydFRvU3RhbmRhbG9uZUJvb3RzdHJhcCxcbiAgZmluZEJvb3RzdHJhcEFwcGxpY2F0aW9uQ2FsbCxcbiAgaW1wb3J0c1Byb3ZpZGVyc0Zyb20sXG59IGZyb20gJy4vc3RhbmRhbG9uZSc7XG4iXX0=