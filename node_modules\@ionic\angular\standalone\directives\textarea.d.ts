import { ChangeDetector<PERSON><PERSON>, <PERSON>ement<PERSON>ef, <PERSON>E<PERSON>ter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { TextareaChangeEventDetail, TextareaInputEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonTextarea extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: Ng<PERSON>one, injector: Injector);
    handleIonInput(el: HTMLIonTextareaElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonTextarea, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonTextarea, "ion-textarea", never, { "autoGrow": "autoGrow"; "autocapitalize": "autocapitalize"; "autofocus": "autofocus"; "clearOnEdit": "clearOnEdit"; "color": "color"; "cols": "cols"; "counter": "counter"; "counterFormatter": "counterFormatter"; "debounce": "debounce"; "disabled": "disabled"; "enterkeyhint": "enterkeyhint"; "errorText": "errorText"; "fill": "fill"; "helperText": "helperText"; "inputmode": "inputmode"; "label": "label"; "labelPlacement": "labelPlacement"; "legacy": "legacy"; "maxlength": "maxlength"; "minlength": "minlength"; "mode": "mode"; "name": "name"; "placeholder": "placeholder"; "readonly": "readonly"; "required": "required"; "rows": "rows"; "shape": "shape"; "spellcheck": "spellcheck"; "value": "value"; "wrap": "wrap"; }, {}, never, ["*"], true>;
}
export declare interface IonTextarea extends Components.IonTextarea {
    /**
     * The `ionChange` event is fired when the user modifies the textarea's value.
  Unlike the `ionInput` event, the `ionChange` event is fired when
  the element loses focus after its value has been modified.
     */
    ionChange: EventEmitter<CustomEvent<TextareaChangeEventDetail>>;
    /**
     * The `ionInput` event is fired each time the user modifies the textarea's value.
  Unlike the `ionChange` event, the `ionInput` event is fired for each alteration
  to the textarea's value. This typically happens for each keystroke as the user types.
  
  When `clearOnEdit` is enabled, the `ionInput` event will be fired when
  the user clears the textarea by performing a keydown event.
     */
    ionInput: EventEmitter<CustomEvent<TextareaInputEventDetail>>;
    /**
     * Emitted when the input loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<FocusEvent>>;
    /**
     * Emitted when the input has focus.
     */
    ionFocus: EventEmitter<CustomEvent<FocusEvent>>;
}
