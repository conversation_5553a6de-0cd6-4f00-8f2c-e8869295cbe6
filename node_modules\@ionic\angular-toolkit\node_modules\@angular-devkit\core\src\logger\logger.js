"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const rxjs_1 = require("rxjs");
class Logger extends rxjs_1.Observable {
    get _observable() {
        return this._obs;
    }
    set _observable(v) {
        if (this._subscription) {
            this._subscription.unsubscribe();
        }
        this._obs = v;
        if (this.parent) {
            this._subscription = this.subscribe((value) => {
                if (this.parent) {
                    this.parent._subject.next(value);
                }
            }, (error) => {
                if (this.parent) {
                    this.parent._subject.error(error);
                }
            }, () => {
                if (this._subscription) {
                    this._subscription.unsubscribe();
                }
                this._subscription = null;
            });
        }
    }
    constructor(name, parent = null) {
        super();
        this.name = name;
        this.parent = parent;
        this._subject = new rxjs_1.Subject();
        this._obs = rxjs_1.EMPTY;
        this._subscription = null;
        const path = [];
        let p = parent;
        while (p) {
            path.push(p.name);
            p = p.parent;
        }
        this._metadata = { name, path };
        this._observable = this._subject.asObservable();
        if (this.parent && this.parent._subject) {
            // When the parent completes, complete us as well.
            this.parent._subject.subscribe(undefined, undefined, () => this.complete());
        }
    }
    asApi() {
        return {
            createChild: (name) => this.createChild(name),
            log: (level, message, metadata) => {
                return this.log(level, message, metadata);
            },
            debug: (message, metadata) => this.debug(message, metadata),
            info: (message, metadata) => this.info(message, metadata),
            warn: (message, metadata) => this.warn(message, metadata),
            error: (message, metadata) => this.error(message, metadata),
            fatal: (message, metadata) => this.fatal(message, metadata),
        };
    }
    createChild(name) {
        return new this.constructor(name, this);
    }
    complete() {
        this._subject.complete();
    }
    log(level, message, metadata = {}) {
        const entry = Object.assign({}, metadata, this._metadata, {
            level,
            message,
            timestamp: +Date.now(),
        });
        this._subject.next(entry);
    }
    next(entry) {
        this._subject.next(entry);
    }
    debug(message, metadata = {}) {
        return this.log('debug', message, metadata);
    }
    info(message, metadata = {}) {
        return this.log('info', message, metadata);
    }
    warn(message, metadata = {}) {
        return this.log('warn', message, metadata);
    }
    error(message, metadata = {}) {
        return this.log('error', message, metadata);
    }
    fatal(message, metadata = {}) {
        return this.log('fatal', message, metadata);
    }
    toString() {
        return `<Logger(${this.name})>`;
    }
    lift(operator) {
        return this._observable.lift(operator);
    }
    subscribe(_observerOrNext, _error, _complete) {
        // eslint-disable-next-line prefer-spread
        return this._observable.subscribe.apply(this._observable, 
        // eslint-disable-next-line prefer-rest-params
        arguments);
    }
    forEach(next, promiseCtor = Promise) {
        return this._observable.forEach(next, promiseCtor);
    }
}
exports.Logger = Logger;
//# sourceMappingURL=data:application/json;base64,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