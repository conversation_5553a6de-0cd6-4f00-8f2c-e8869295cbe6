"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;base64,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