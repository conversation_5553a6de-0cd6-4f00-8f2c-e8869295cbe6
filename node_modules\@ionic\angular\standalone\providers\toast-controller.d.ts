import { OverlayBaseController } from '@ionic/angular/common';
import type { ToastOptions } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class ToastController extends OverlayBaseController<ToastOptions, HTMLIonToastElement> {
    constructor();
    static ɵfac: i0.ɵɵFactoryDeclaration<ToastController, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ToastController>;
}
