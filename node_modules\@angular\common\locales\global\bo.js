/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['bo'] = ["bo",[["སྔ་དྲོ་","ཕྱི་དྲོ་"],u,u],u,[["ཉི","ཟླ","མིག","ལྷག","ཕུར","སངས","སྤེན"],["ཉི་མ་","ཟླ་བ་","མིག་དམར་","ལྷག་པ་","ཕུར་བུ་","པ་སངས་","སྤེན་པ་"],["གཟའ་ཉི་མ་","གཟའ་ཟླ་བ་","གཟའ་མིག་དམར་","གཟའ་ལྷག་པ་","གཟའ་ཕུར་བུ་","གཟའ་པ་སངས་","གཟའ་སྤེན་པ་"],["ཉི་མ་","ཟླ་བ་","མིག་དམར་","ལྷག་པ་","ཕུར་བུ་","པ་སངས་","སྤེན་པ་"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["ཟླ་༡","ཟླ་༢","ཟླ་༣","ཟླ་༤","ཟླ་༥","ཟླ་༦","ཟླ་༧","ཟླ་༨","ཟླ་༩","ཟླ་༡༠","ཟླ་༡༡","ཟླ་༡༢"],["ཟླ་བ་དང་པོ","ཟླ་བ་གཉིས་པ","ཟླ་བ་གསུམ་པ","ཟླ་བ་བཞི་པ","ཟླ་བ་ལྔ་པ","ཟླ་བ་དྲུག་པ","ཟླ་བ་བདུན་པ","ཟླ་བ་བརྒྱད་པ","ཟླ་བ་དགུ་པ","ཟླ་བ་བཅུ་པ","ཟླ་བ་བཅུ་གཅིག་པ","ཟླ་བ་བཅུ་གཉིས་པ"]],[["1","2","3","4","5","6","7","8","9","10","11","12"],["ཟླ་༡","ཟླ་༢","ཟླ་༣","ཟླ་༤","ཟླ་༥","ཟླ་༦","ཟླ་༧","ཟླ་༨","ཟླ་༩","ཟླ་༡༠","ཟླ་༡༡","ཟླ་༡༢"],["ཟླ་བ་དང་པོ་","ཟླ་བ་གཉིས་པ་","ཟླ་བ་གསུམ་པ་","ཟླ་བ་བཞི་པ་","ཟླ་བ་ལྔ་པ་","ཟླ་བ་དྲུག་པ་","ཟླ་བ་བདུན་པ་","ཟླ་བ་བརྒྱད་པ་","ཟླ་བ་དགུ་པ་","ཟླ་བ་བཅུ་པ་","ཟླ་བ་བཅུ་གཅིག་པ་","ཟླ་བ་བཅུ་གཉིས་པ་"]],[["སྤྱི་ལོ་སྔོན་","སྤྱི་ལོ་"],u,u],0,[6,0],["y-MM-dd","y ལོའི་MMMཚེས་d","སྤྱི་ལོ་y MMMMའི་ཚེས་d","y MMMMའི་ཚེས་d, EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"CNY","¥","ཡུ་ཨན་",{"CNY":["¥"],"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    