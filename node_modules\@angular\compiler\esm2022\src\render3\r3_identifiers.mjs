/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const CORE = '@angular/core';
export class Identifiers {
    /* Methods */
    static { this.NEW_METHOD = 'factory'; }
    static { this.TRANSFORM_METHOD = 'transform'; }
    static { this.PATCH_DEPS = 'patchedDeps'; }
    static { this.core = { name: null, moduleName: CORE }; }
    /* Instructions */
    static { this.namespaceHTML = { name: 'ɵɵnamespaceHTML', moduleName: CORE }; }
    static { this.namespaceMathML = { name: 'ɵɵnamespaceMathML', moduleName: CORE }; }
    static { this.namespaceSVG = { name: 'ɵɵnamespaceSVG', moduleName: CORE }; }
    static { this.element = { name: 'ɵɵelement', moduleName: CORE }; }
    static { this.elementStart = { name: 'ɵɵelementStart', moduleName: CORE }; }
    static { this.elementEnd = { name: 'ɵɵelementEnd', moduleName: CORE }; }
    static { this.advance = { name: 'ɵɵadvance', moduleName: CORE }; }
    static { this.syntheticHostProperty = { name: 'ɵɵsyntheticHostProperty', moduleName: CORE }; }
    static { this.syntheticHostListener = { name: 'ɵɵsyntheticHostListener', moduleName: CORE }; }
    static { this.attribute = { name: 'ɵɵattribute', moduleName: CORE }; }
    static { this.attributeInterpolate1 = { name: 'ɵɵattributeInterpolate1', moduleName: CORE }; }
    static { this.attributeInterpolate2 = { name: 'ɵɵattributeInterpolate2', moduleName: CORE }; }
    static { this.attributeInterpolate3 = { name: 'ɵɵattributeInterpolate3', moduleName: CORE }; }
    static { this.attributeInterpolate4 = { name: 'ɵɵattributeInterpolate4', moduleName: CORE }; }
    static { this.attributeInterpolate5 = { name: 'ɵɵattributeInterpolate5', moduleName: CORE }; }
    static { this.attributeInterpolate6 = { name: 'ɵɵattributeInterpolate6', moduleName: CORE }; }
    static { this.attributeInterpolate7 = { name: 'ɵɵattributeInterpolate7', moduleName: CORE }; }
    static { this.attributeInterpolate8 = { name: 'ɵɵattributeInterpolate8', moduleName: CORE }; }
    static { this.attributeInterpolateV = { name: 'ɵɵattributeInterpolateV', moduleName: CORE }; }
    static { this.classProp = { name: 'ɵɵclassProp', moduleName: CORE }; }
    static { this.elementContainerStart = { name: 'ɵɵelementContainerStart', moduleName: CORE }; }
    static { this.elementContainerEnd = { name: 'ɵɵelementContainerEnd', moduleName: CORE }; }
    static { this.elementContainer = { name: 'ɵɵelementContainer', moduleName: CORE }; }
    static { this.styleMap = { name: 'ɵɵstyleMap', moduleName: CORE }; }
    static { this.styleMapInterpolate1 = { name: 'ɵɵstyleMapInterpolate1', moduleName: CORE }; }
    static { this.styleMapInterpolate2 = { name: 'ɵɵstyleMapInterpolate2', moduleName: CORE }; }
    static { this.styleMapInterpolate3 = { name: 'ɵɵstyleMapInterpolate3', moduleName: CORE }; }
    static { this.styleMapInterpolate4 = { name: 'ɵɵstyleMapInterpolate4', moduleName: CORE }; }
    static { this.styleMapInterpolate5 = { name: 'ɵɵstyleMapInterpolate5', moduleName: CORE }; }
    static { this.styleMapInterpolate6 = { name: 'ɵɵstyleMapInterpolate6', moduleName: CORE }; }
    static { this.styleMapInterpolate7 = { name: 'ɵɵstyleMapInterpolate7', moduleName: CORE }; }
    static { this.styleMapInterpolate8 = { name: 'ɵɵstyleMapInterpolate8', moduleName: CORE }; }
    static { this.styleMapInterpolateV = { name: 'ɵɵstyleMapInterpolateV', moduleName: CORE }; }
    static { this.classMap = { name: 'ɵɵclassMap', moduleName: CORE }; }
    static { this.classMapInterpolate1 = { name: 'ɵɵclassMapInterpolate1', moduleName: CORE }; }
    static { this.classMapInterpolate2 = { name: 'ɵɵclassMapInterpolate2', moduleName: CORE }; }
    static { this.classMapInterpolate3 = { name: 'ɵɵclassMapInterpolate3', moduleName: CORE }; }
    static { this.classMapInterpolate4 = { name: 'ɵɵclassMapInterpolate4', moduleName: CORE }; }
    static { this.classMapInterpolate5 = { name: 'ɵɵclassMapInterpolate5', moduleName: CORE }; }
    static { this.classMapInterpolate6 = { name: 'ɵɵclassMapInterpolate6', moduleName: CORE }; }
    static { this.classMapInterpolate7 = { name: 'ɵɵclassMapInterpolate7', moduleName: CORE }; }
    static { this.classMapInterpolate8 = { name: 'ɵɵclassMapInterpolate8', moduleName: CORE }; }
    static { this.classMapInterpolateV = { name: 'ɵɵclassMapInterpolateV', moduleName: CORE }; }
    static { this.styleProp = { name: 'ɵɵstyleProp', moduleName: CORE }; }
    static { this.stylePropInterpolate1 = { name: 'ɵɵstylePropInterpolate1', moduleName: CORE }; }
    static { this.stylePropInterpolate2 = { name: 'ɵɵstylePropInterpolate2', moduleName: CORE }; }
    static { this.stylePropInterpolate3 = { name: 'ɵɵstylePropInterpolate3', moduleName: CORE }; }
    static { this.stylePropInterpolate4 = { name: 'ɵɵstylePropInterpolate4', moduleName: CORE }; }
    static { this.stylePropInterpolate5 = { name: 'ɵɵstylePropInterpolate5', moduleName: CORE }; }
    static { this.stylePropInterpolate6 = { name: 'ɵɵstylePropInterpolate6', moduleName: CORE }; }
    static { this.stylePropInterpolate7 = { name: 'ɵɵstylePropInterpolate7', moduleName: CORE }; }
    static { this.stylePropInterpolate8 = { name: 'ɵɵstylePropInterpolate8', moduleName: CORE }; }
    static { this.stylePropInterpolateV = { name: 'ɵɵstylePropInterpolateV', moduleName: CORE }; }
    static { this.nextContext = { name: 'ɵɵnextContext', moduleName: CORE }; }
    static { this.resetView = { name: 'ɵɵresetView', moduleName: CORE }; }
    static { this.templateCreate = { name: 'ɵɵtemplate', moduleName: CORE }; }
    static { this.defer = { name: 'ɵɵdefer', moduleName: CORE }; }
    static { this.deferWhen = { name: 'ɵɵdeferWhen', moduleName: CORE }; }
    static { this.deferOnIdle = { name: 'ɵɵdeferOnIdle', moduleName: CORE }; }
    static { this.deferOnImmediate = { name: 'ɵɵdeferOnImmediate', moduleName: CORE }; }
    static { this.deferOnTimer = { name: 'ɵɵdeferOnTimer', moduleName: CORE }; }
    static { this.deferOnHover = { name: 'ɵɵdeferOnHover', moduleName: CORE }; }
    static { this.deferOnInteraction = { name: 'ɵɵdeferOnInteraction', moduleName: CORE }; }
    static { this.deferOnViewport = { name: 'ɵɵdeferOnViewport', moduleName: CORE }; }
    static { this.deferPrefetchWhen = { name: 'ɵɵdeferPrefetchWhen', moduleName: CORE }; }
    static { this.deferPrefetchOnIdle = { name: 'ɵɵdeferPrefetchOnIdle', moduleName: CORE }; }
    static { this.deferPrefetchOnImmediate = { name: 'ɵɵdeferPrefetchOnImmediate', moduleName: CORE }; }
    static { this.deferPrefetchOnTimer = { name: 'ɵɵdeferPrefetchOnTimer', moduleName: CORE }; }
    static { this.deferPrefetchOnHover = { name: 'ɵɵdeferPrefetchOnHover', moduleName: CORE }; }
    static { this.deferPrefetchOnInteraction = { name: 'ɵɵdeferPrefetchOnInteraction', moduleName: CORE }; }
    static { this.deferPrefetchOnViewport = { name: 'ɵɵdeferPrefetchOnViewport', moduleName: CORE }; }
    static { this.deferEnableTimerScheduling = { name: 'ɵɵdeferEnableTimerScheduling', moduleName: CORE }; }
    static { this.conditional = { name: 'ɵɵconditional', moduleName: CORE }; }
    static { this.repeater = { name: 'ɵɵrepeater', moduleName: CORE }; }
    static { this.repeaterCreate = { name: 'ɵɵrepeaterCreate', moduleName: CORE }; }
    static { this.repeaterTrackByIndex = { name: 'ɵɵrepeaterTrackByIndex', moduleName: CORE }; }
    static { this.repeaterTrackByIdentity = { name: 'ɵɵrepeaterTrackByIdentity', moduleName: CORE }; }
    static { this.componentInstance = { name: 'ɵɵcomponentInstance', moduleName: CORE }; }
    static { this.text = { name: 'ɵɵtext', moduleName: CORE }; }
    static { this.enableBindings = { name: 'ɵɵenableBindings', moduleName: CORE }; }
    static { this.disableBindings = { name: 'ɵɵdisableBindings', moduleName: CORE }; }
    static { this.getCurrentView = { name: 'ɵɵgetCurrentView', moduleName: CORE }; }
    static { this.textInterpolate = { name: 'ɵɵtextInterpolate', moduleName: CORE }; }
    static { this.textInterpolate1 = { name: 'ɵɵtextInterpolate1', moduleName: CORE }; }
    static { this.textInterpolate2 = { name: 'ɵɵtextInterpolate2', moduleName: CORE }; }
    static { this.textInterpolate3 = { name: 'ɵɵtextInterpolate3', moduleName: CORE }; }
    static { this.textInterpolate4 = { name: 'ɵɵtextInterpolate4', moduleName: CORE }; }
    static { this.textInterpolate5 = { name: 'ɵɵtextInterpolate5', moduleName: CORE }; }
    static { this.textInterpolate6 = { name: 'ɵɵtextInterpolate6', moduleName: CORE }; }
    static { this.textInterpolate7 = { name: 'ɵɵtextInterpolate7', moduleName: CORE }; }
    static { this.textInterpolate8 = { name: 'ɵɵtextInterpolate8', moduleName: CORE }; }
    static { this.textInterpolateV = { name: 'ɵɵtextInterpolateV', moduleName: CORE }; }
    static { this.restoreView = { name: 'ɵɵrestoreView', moduleName: CORE }; }
    static { this.pureFunction0 = { name: 'ɵɵpureFunction0', moduleName: CORE }; }
    static { this.pureFunction1 = { name: 'ɵɵpureFunction1', moduleName: CORE }; }
    static { this.pureFunction2 = { name: 'ɵɵpureFunction2', moduleName: CORE }; }
    static { this.pureFunction3 = { name: 'ɵɵpureFunction3', moduleName: CORE }; }
    static { this.pureFunction4 = { name: 'ɵɵpureFunction4', moduleName: CORE }; }
    static { this.pureFunction5 = { name: 'ɵɵpureFunction5', moduleName: CORE }; }
    static { this.pureFunction6 = { name: 'ɵɵpureFunction6', moduleName: CORE }; }
    static { this.pureFunction7 = { name: 'ɵɵpureFunction7', moduleName: CORE }; }
    static { this.pureFunction8 = { name: 'ɵɵpureFunction8', moduleName: CORE }; }
    static { this.pureFunctionV = { name: 'ɵɵpureFunctionV', moduleName: CORE }; }
    static { this.pipeBind1 = { name: 'ɵɵpipeBind1', moduleName: CORE }; }
    static { this.pipeBind2 = { name: 'ɵɵpipeBind2', moduleName: CORE }; }
    static { this.pipeBind3 = { name: 'ɵɵpipeBind3', moduleName: CORE }; }
    static { this.pipeBind4 = { name: 'ɵɵpipeBind4', moduleName: CORE }; }
    static { this.pipeBindV = { name: 'ɵɵpipeBindV', moduleName: CORE }; }
    static { this.hostProperty = { name: 'ɵɵhostProperty', moduleName: CORE }; }
    static { this.property = { name: 'ɵɵproperty', moduleName: CORE }; }
    static { this.propertyInterpolate = { name: 'ɵɵpropertyInterpolate', moduleName: CORE }; }
    static { this.propertyInterpolate1 = { name: 'ɵɵpropertyInterpolate1', moduleName: CORE }; }
    static { this.propertyInterpolate2 = { name: 'ɵɵpropertyInterpolate2', moduleName: CORE }; }
    static { this.propertyInterpolate3 = { name: 'ɵɵpropertyInterpolate3', moduleName: CORE }; }
    static { this.propertyInterpolate4 = { name: 'ɵɵpropertyInterpolate4', moduleName: CORE }; }
    static { this.propertyInterpolate5 = { name: 'ɵɵpropertyInterpolate5', moduleName: CORE }; }
    static { this.propertyInterpolate6 = { name: 'ɵɵpropertyInterpolate6', moduleName: CORE }; }
    static { this.propertyInterpolate7 = { name: 'ɵɵpropertyInterpolate7', moduleName: CORE }; }
    static { this.propertyInterpolate8 = { name: 'ɵɵpropertyInterpolate8', moduleName: CORE }; }
    static { this.propertyInterpolateV = { name: 'ɵɵpropertyInterpolateV', moduleName: CORE }; }
    static { this.i18n = { name: 'ɵɵi18n', moduleName: CORE }; }
    static { this.i18nAttributes = { name: 'ɵɵi18nAttributes', moduleName: CORE }; }
    static { this.i18nExp = { name: 'ɵɵi18nExp', moduleName: CORE }; }
    static { this.i18nStart = { name: 'ɵɵi18nStart', moduleName: CORE }; }
    static { this.i18nEnd = { name: 'ɵɵi18nEnd', moduleName: CORE }; }
    static { this.i18nApply = { name: 'ɵɵi18nApply', moduleName: CORE }; }
    static { this.i18nPostprocess = { name: 'ɵɵi18nPostprocess', moduleName: CORE }; }
    static { this.pipe = { name: 'ɵɵpipe', moduleName: CORE }; }
    static { this.projection = { name: 'ɵɵprojection', moduleName: CORE }; }
    static { this.projectionDef = { name: 'ɵɵprojectionDef', moduleName: CORE }; }
    static { this.reference = { name: 'ɵɵreference', moduleName: CORE }; }
    static { this.inject = { name: 'ɵɵinject', moduleName: CORE }; }
    static { this.injectAttribute = { name: 'ɵɵinjectAttribute', moduleName: CORE }; }
    static { this.directiveInject = { name: 'ɵɵdirectiveInject', moduleName: CORE }; }
    static { this.invalidFactory = { name: 'ɵɵinvalidFactory', moduleName: CORE }; }
    static { this.invalidFactoryDep = { name: 'ɵɵinvalidFactoryDep', moduleName: CORE }; }
    static { this.templateRefExtractor = { name: 'ɵɵtemplateRefExtractor', moduleName: CORE }; }
    static { this.forwardRef = { name: 'forwardRef', moduleName: CORE }; }
    static { this.resolveForwardRef = { name: 'resolveForwardRef', moduleName: CORE }; }
    static { this.ɵɵdefineInjectable = { name: 'ɵɵdefineInjectable', moduleName: CORE }; }
    static { this.declareInjectable = { name: 'ɵɵngDeclareInjectable', moduleName: CORE }; }
    static { this.InjectableDeclaration = { name: 'ɵɵInjectableDeclaration', moduleName: CORE }; }
    static { this.resolveWindow = { name: 'ɵɵresolveWindow', moduleName: CORE }; }
    static { this.resolveDocument = { name: 'ɵɵresolveDocument', moduleName: CORE }; }
    static { this.resolveBody = { name: 'ɵɵresolveBody', moduleName: CORE }; }
    static { this.getComponentDepsFactory = { name: 'ɵɵgetComponentDepsFactory', moduleName: CORE }; }
    static { this.defineComponent = { name: 'ɵɵdefineComponent', moduleName: CORE }; }
    static { this.declareComponent = { name: 'ɵɵngDeclareComponent', moduleName: CORE }; }
    static { this.setComponentScope = { name: 'ɵɵsetComponentScope', moduleName: CORE }; }
    static { this.ChangeDetectionStrategy = {
        name: 'ChangeDetectionStrategy',
        moduleName: CORE,
    }; }
    static { this.ViewEncapsulation = {
        name: 'ViewEncapsulation',
        moduleName: CORE,
    }; }
    static { this.ComponentDeclaration = {
        name: 'ɵɵComponentDeclaration',
        moduleName: CORE,
    }; }
    static { this.FactoryDeclaration = {
        name: 'ɵɵFactoryDeclaration',
        moduleName: CORE,
    }; }
    static { this.declareFactory = { name: 'ɵɵngDeclareFactory', moduleName: CORE }; }
    static { this.FactoryTarget = { name: 'ɵɵFactoryTarget', moduleName: CORE }; }
    static { this.defineDirective = { name: 'ɵɵdefineDirective', moduleName: CORE }; }
    static { this.declareDirective = { name: 'ɵɵngDeclareDirective', moduleName: CORE }; }
    static { this.DirectiveDeclaration = {
        name: 'ɵɵDirectiveDeclaration',
        moduleName: CORE,
    }; }
    static { this.InjectorDef = { name: 'ɵɵInjectorDef', moduleName: CORE }; }
    static { this.InjectorDeclaration = { name: 'ɵɵInjectorDeclaration', moduleName: CORE }; }
    static { this.defineInjector = { name: 'ɵɵdefineInjector', moduleName: CORE }; }
    static { this.declareInjector = { name: 'ɵɵngDeclareInjector', moduleName: CORE }; }
    static { this.NgModuleDeclaration = {
        name: 'ɵɵNgModuleDeclaration',
        moduleName: CORE,
    }; }
    static { this.ModuleWithProviders = {
        name: 'ModuleWithProviders',
        moduleName: CORE,
    }; }
    static { this.defineNgModule = { name: 'ɵɵdefineNgModule', moduleName: CORE }; }
    static { this.declareNgModule = { name: 'ɵɵngDeclareNgModule', moduleName: CORE }; }
    static { this.setNgModuleScope = { name: 'ɵɵsetNgModuleScope', moduleName: CORE }; }
    static { this.registerNgModuleType = { name: 'ɵɵregisterNgModuleType', moduleName: CORE }; }
    static { this.PipeDeclaration = { name: 'ɵɵPipeDeclaration', moduleName: CORE }; }
    static { this.definePipe = { name: 'ɵɵdefinePipe', moduleName: CORE }; }
    static { this.declarePipe = { name: 'ɵɵngDeclarePipe', moduleName: CORE }; }
    static { this.declareClassMetadata = { name: 'ɵɵngDeclareClassMetadata', moduleName: CORE }; }
    static { this.setClassMetadata = { name: 'ɵsetClassMetadata', moduleName: CORE }; }
    static { this.setClassMetadataAsync = { name: 'ɵsetClassMetadataAsync', moduleName: CORE }; }
    static { this.setClassDebugInfo = { name: 'ɵsetClassDebugInfo', moduleName: CORE }; }
    static { this.queryRefresh = { name: 'ɵɵqueryRefresh', moduleName: CORE }; }
    static { this.viewQuery = { name: 'ɵɵviewQuery', moduleName: CORE }; }
    static { this.loadQuery = { name: 'ɵɵloadQuery', moduleName: CORE }; }
    static { this.contentQuery = { name: 'ɵɵcontentQuery', moduleName: CORE }; }
    // Signal queries
    static { this.viewQuerySignal = { name: 'ɵɵviewQuerySignal', moduleName: CORE }; }
    static { this.contentQuerySignal = { name: 'ɵɵcontentQuerySignal', moduleName: CORE }; }
    static { this.queryAdvance = { name: 'ɵɵqueryAdvance', moduleName: CORE }; }
    // Two-way bindings
    static { this.twoWayProperty = { name: 'ɵɵtwoWayProperty', moduleName: CORE }; }
    static { this.twoWayBindingSet = { name: 'ɵɵtwoWayBindingSet', moduleName: CORE }; }
    static { this.twoWayListener = { name: 'ɵɵtwoWayListener', moduleName: CORE }; }
    static { this.NgOnChangesFeature = { name: 'ɵɵNgOnChangesFeature', moduleName: CORE }; }
    static { this.InheritDefinitionFeature = { name: 'ɵɵInheritDefinitionFeature', moduleName: CORE }; }
    static { this.CopyDefinitionFeature = { name: 'ɵɵCopyDefinitionFeature', moduleName: CORE }; }
    static { this.StandaloneFeature = { name: 'ɵɵStandaloneFeature', moduleName: CORE }; }
    static { this.ProvidersFeature = { name: 'ɵɵProvidersFeature', moduleName: CORE }; }
    static { this.HostDirectivesFeature = { name: 'ɵɵHostDirectivesFeature', moduleName: CORE }; }
    static { this.InputTransformsFeatureFeature = { name: 'ɵɵInputTransformsFeature', moduleName: CORE }; }
    static { this.listener = { name: 'ɵɵlistener', moduleName: CORE }; }
    static { this.getInheritedFactory = {
        name: 'ɵɵgetInheritedFactory',
        moduleName: CORE,
    }; }
    static { this.InputFlags = {
        name: 'ɵɵInputFlags',
        moduleName: CORE,
    }; }
    // sanitization-related functions
    static { this.sanitizeHtml = { name: 'ɵɵsanitizeHtml', moduleName: CORE }; }
    static { this.sanitizeStyle = { name: 'ɵɵsanitizeStyle', moduleName: CORE }; }
    static { this.sanitizeResourceUrl = { name: 'ɵɵsanitizeResourceUrl', moduleName: CORE }; }
    static { this.sanitizeScript = { name: 'ɵɵsanitizeScript', moduleName: CORE }; }
    static { this.sanitizeUrl = { name: 'ɵɵsanitizeUrl', moduleName: CORE }; }
    static { this.sanitizeUrlOrResourceUrl = { name: 'ɵɵsanitizeUrlOrResourceUrl', moduleName: CORE }; }
    static { this.trustConstantHtml = { name: 'ɵɵtrustConstantHtml', moduleName: CORE }; }
    static { this.trustConstantResourceUrl = { name: 'ɵɵtrustConstantResourceUrl', moduleName: CORE }; }
    static { this.validateIframeAttribute = { name: 'ɵɵvalidateIframeAttribute', moduleName: CORE }; }
    // type-checking
    static { this.InputSignalBrandWriteType = { name: 'ɵINPUT_SIGNAL_BRAND_WRITE_TYPE', moduleName: CORE }; }
    static { this.UnwrapDirectiveSignalInputs = { name: 'ɵUnwrapDirectiveSignalInputs', moduleName: CORE }; }
    static { this.unwrapWritableSignal = { name: 'ɵunwrapWritableSignal', moduleName: CORE }; }
}
//# sourceMappingURL=data:application/json;base64,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