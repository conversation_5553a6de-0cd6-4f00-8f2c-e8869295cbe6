"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2885],{5266:(Pe,Y,y)=>{y.r(Y),y.d(Y,{ion_datetime:()=>K,ion_picker:()=>Z,ion_picker_column:()=>G});var C=y(467),o=y(4363),te=y(3126),O=y(5638),w=y(4929),W=y(5083),L=y(333),_=y(6726),T=y(611),r=y(2147),ie=y(7838),P=y(5516),I=y(3503),F=y(4081);y(8476),y(3113),y(8438);const H=(e,i,t,n)=>!!(null===e.day||void 0!==n&&!n.includes(e.day)||i&&(0,r.i)(e,i)||t&&(0,r.b)(e,t)),z=(e,{minParts:i,maxParts:t})=>!!(((e,i,t)=>!!(i&&i.year>e||t&&t.year<e))(e.year,i,t)||i&&(0,r.i)(e,i)||t&&(0,r.b)(e,t)),$=(e,i)=>{var t,n,a,s;(null!==(t=i?.date)&&void 0!==t&&t.timeZone||null!==(n=i?.date)&&void 0!==n&&n.timeZoneName||null!==(a=i?.time)&&void 0!==a&&a.timeZone||null!==(s=i?.time)&&void 0!==s&&s.timeZoneName)&&(0,w.p)('Datetime: "timeZone" and "timeZoneName" are not supported in "formatOptions".',e)},R=(e,i,t)=>{if(t)switch(i){case"date":case"month-year":case"month":case"year":void 0===t.date&&(0,w.p)(`Datetime: The '${i}' presentation requires a date object in formatOptions.`,e);break;case"time":void 0===t.time&&(0,w.p)("Datetime: The 'time' presentation requires a time object in formatOptions.",e);break;case"date-time":case"time-date":void 0===t.date&&void 0===t.time&&(0,w.p)(`Datetime: The '${i}' presentation requires either a date or time object (or both) in formatOptions.`,e)}},K=class{constructor(e){var i=this;(0,o.r)(this,e),this.ionCancel=(0,o.d)(this,"ionCancel",7),this.ionChange=(0,o.d)(this,"ionChange",7),this.ionValueChange=(0,o.d)(this,"ionValueChange",7),this.ionFocus=(0,o.d)(this,"ionFocus",7),this.ionBlur=(0,o.d)(this,"ionBlur",7),this.ionStyle=(0,o.d)(this,"ionStyle",7),this.ionRender=(0,o.d)(this,"ionRender",7),this.inputId="ion-dt-"+ce++,this.prevPresentation=null,this.warnIfIncorrectValueUsage=()=>{const{multiple:t,value:n}=this;!t&&Array.isArray(n)&&(0,w.p)(`ion-datetime was passed an array of values, but multiple="false". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the "value" property when multiple="false".\n\n  Value Passed: [${n.map(a=>`'${a}'`).join(", ")}]\n`,this.el)},this.setValue=t=>{this.value=t,this.ionChange.emit({value:t})},this.getActivePartsWithFallback=()=>{var t;const{defaultParts:n}=this;return null!==(t=this.getActivePart())&&void 0!==t?t:n},this.getActivePart=()=>{const{activeParts:t}=this;return Array.isArray(t)?t[0]:t},this.closeParentOverlay=()=>{const t=this.el.closest("ion-modal, ion-popover");t&&t.dismiss()},this.setWorkingParts=t=>{this.workingParts=Object.assign({},t)},this.setActiveParts=(t,n=!1)=>{if(this.readonly)return;const{multiple:a,minParts:s,maxParts:l,activeParts:d}=this,c=(0,r.v)(t,s,l);if(this.setWorkingParts(c),a){const h=Array.isArray(d)?d:[d];this.activeParts=n?h.filter(u=>!(0,r.c)(u,c)):[...h,c]}else this.activeParts=Object.assign({},c);null!==this.el.querySelector('[slot="buttons"]')||this.showDefaultButtons||this.confirm()},this.initializeKeyboardListeners=()=>{const t=this.calendarBodyRef;if(!t)return;const n=this.el.shadowRoot,a=t.querySelector(".calendar-month:nth-of-type(2)"),l=new MutationObserver(d=>{var c;null!==(c=d[0].oldValue)&&void 0!==c&&c.includes("ion-focused")||!t.classList.contains("ion-focused")||this.focusWorkingDay(a)});l.observe(t,{attributeFilter:["class"],attributeOldValue:!0}),this.destroyKeyboardMO=()=>{l?.disconnect()},t.addEventListener("keydown",d=>{const c=n.activeElement;if(!c||!c.classList.contains("calendar-day"))return;const p=(0,r.f)(c);let h;switch(d.key){case"ArrowDown":d.preventDefault(),h=(0,r.p)(p);break;case"ArrowUp":d.preventDefault(),h=(0,r.o)(p);break;case"ArrowRight":d.preventDefault(),h=(0,r.n)(p);break;case"ArrowLeft":d.preventDefault(),h=(0,r.m)(p);break;case"Home":d.preventDefault(),h=(0,r.l)(p);break;case"End":d.preventDefault(),h=(0,r.k)(p);break;case"PageUp":d.preventDefault(),h=d.shiftKey?(0,r.j)(p):(0,r.d)(p);break;case"PageDown":d.preventDefault(),h=d.shiftKey?(0,r.h)(p):(0,r.e)(p);break;default:return}H(h,this.minParts,this.maxParts)||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),h)),requestAnimationFrame(()=>this.focusWorkingDay(a)))})},this.focusWorkingDay=t=>{const n=t.querySelectorAll(".calendar-day-padding"),{day:a}=this.workingParts;if(null===a)return;const s=t.querySelector(`.calendar-day-wrapper:nth-of-type(${n.length+a}) .calendar-day`);s&&s.focus()},this.processMinParts=()=>{const{min:t,defaultParts:n}=this;this.minParts=void 0!==t?(0,r.q)(t,n):void 0},this.processMaxParts=()=>{const{max:t,defaultParts:n}=this;this.maxParts=void 0!==t?(0,r.r)(t,n):void 0},this.initializeCalendarListener=()=>{const t=this.calendarBodyRef;if(!t)return;const n=t.querySelectorAll(".calendar-month"),a=n[0],s=n[1],l=n[2],c="ios"===(0,T.b)(this)&&typeof navigator<"u"&&navigator.maxTouchPoints>1;(0,o.w)(()=>{t.scrollLeft=a.clientWidth*((0,W.i)(this.el)?-1:1);const p=f=>{const x=t.getBoundingClientRect(),b=t.scrollLeft<=2?a:l,k=b.getBoundingClientRect();if(Math.abs(k.x-x.x)>2)return;const{forceRenderDate:v}=this;return void 0!==v?{month:v.month,year:v.year,day:v.day}:b===a?(0,r.d)(f):b===l?(0,r.e)(f):void 0},h=()=>{c&&(t.style.removeProperty("pointer-events"),g=!1);const f=p(this.workingParts);if(!f)return;const{month:x,day:b,year:k}=f;z({month:x,year:k,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})})||(t.style.setProperty("overflow","hidden"),(0,o.w)(()=>{this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:x,day:b,year:k})),t.scrollLeft=s.clientWidth*((0,W.i)(this.el)?-1:1),t.style.removeProperty("overflow"),this.resolveForceDateScrolling&&this.resolveForceDateScrolling()}))};let u,g=!1;const m=()=>{u&&clearTimeout(u),!g&&c&&(t.style.setProperty("pointer-events","none"),g=!0),u=setTimeout(h,50)};t.addEventListener("scroll",m),this.destroyCalendarListener=()=>{t.removeEventListener("scroll",m)}})},this.destroyInteractionListeners=()=>{const{destroyCalendarListener:t,destroyKeyboardMO:n}=this;void 0!==t&&t(),void 0!==n&&n()},this.processValue=t=>{const n=null!=t&&(!Array.isArray(t)||t.length>0),a=n?(0,r.s)(t):this.defaultParts,{minParts:s,maxParts:l,workingParts:d,el:c}=this;if(this.warnIfIncorrectValueUsage(),!a)return;n&&(0,r.w)(a,s,l);const p=Array.isArray(a)?a[0]:a,h=(0,r.P)(p,s,l),{month:u,day:g,year:m,hour:f,minute:x}=h,b=(0,r.Q)(f);this.activeParts=n?Array.isArray(a)?[...a]:{month:u,day:g,year:m,hour:f,minute:x,ampm:b}:[];const k=void 0!==u&&u!==d.month||void 0!==m&&m!==d.year,v=c.classList.contains("datetime-ready"),{isGridStyle:A,showMonthAndYear:D}=this;let M=!0;if(Array.isArray(a)){const j=a[0].month;for(const B of a)if(B.month!==j){M=!1;break}}M&&(A&&k&&v&&!D?this.animateToDate(h):this.setWorkingParts({month:u,day:g,year:m,hour:f,minute:x,ampm:b}))},this.animateToDate=function(){var t=(0,C.A)(function*(n){const{workingParts:a}=i;i.forceRenderDate=n;const s=new Promise(d=>{i.resolveForceDateScrolling=d});(0,r.i)(n,a)?i.prevMonth():i.nextMonth(),yield s,i.resolveForceDateScrolling=void 0,i.forceRenderDate=void 0});return function(n){return t.apply(this,arguments)}}(),this.onFocus=()=>{this.ionFocus.emit()},this.onBlur=()=>{this.ionBlur.emit()},this.hasValue=()=>null!=this.value,this.nextMonth=()=>{const t=this.calendarBodyRef;if(!t)return;const n=t.querySelector(".calendar-month:last-of-type");n&&t.scrollTo({top:0,left:2*n.offsetWidth*((0,W.i)(this.el)?-1:1),behavior:"smooth"})},this.prevMonth=()=>{const t=this.calendarBodyRef;!t||!t.querySelector(".calendar-month:first-of-type")||t.scrollTo({top:0,left:0,behavior:"smooth"})},this.toggleMonthAndYearView=()=>{this.showMonthAndYear=!this.showMonthAndYear},this.showMonthAndYear=!1,this.activeParts=[],this.workingParts={month:5,day:28,year:2021,hour:13,minute:52,ampm:"pm"},this.isTimePopoverOpen=!1,this.forceRenderDate=void 0,this.color="primary",this.name=this.inputId,this.disabled=!1,this.formatOptions=void 0,this.readonly=!1,this.isDateEnabled=void 0,this.min=void 0,this.max=void 0,this.presentation="date-time",this.cancelText="Cancel",this.doneText="Done",this.clearText="Clear",this.yearValues=void 0,this.monthValues=void 0,this.dayValues=void 0,this.hourValues=void 0,this.minuteValues=void 0,this.locale="default",this.firstDayOfWeek=0,this.titleSelectedDatesFormatter=void 0,this.multiple=!1,this.highlightedDates=void 0,this.value=void 0,this.showDefaultTitle=!1,this.showDefaultButtons=!1,this.showClearButton=!1,this.showDefaultTimeLabel=!0,this.hourCycle=void 0,this.size="fixed",this.preferWheel=!1}formatOptionsChanged(){const{el:e,formatOptions:i,presentation:t}=this;R(e,t,i),$(e,i)}disabledChanged(){this.emitStyle()}minChanged(){this.processMinParts()}maxChanged(){this.processMaxParts()}presentationChanged(){const{el:e,formatOptions:i,presentation:t}=this;R(e,t,i)}get isGridStyle(){const{presentation:e,preferWheel:i}=this;return("date"===e||"date-time"===e||"time-date"===e)&&!i}yearValuesChanged(){this.parsedYearValues=(0,r.t)(this.yearValues)}monthValuesChanged(){this.parsedMonthValues=(0,r.t)(this.monthValues)}dayValuesChanged(){this.parsedDayValues=(0,r.t)(this.dayValues)}hourValuesChanged(){this.parsedHourValues=(0,r.t)(this.hourValues)}minuteValuesChanged(){this.parsedMinuteValues=(0,r.t)(this.minuteValues)}valueChanged(){var e=this;return(0,C.A)(function*(){const{value:i}=e;e.hasValue()&&e.processValue(i),e.emitStyle(),e.ionValueChange.emit({value:i})})()}confirm(){var e=this;return(0,C.A)(function*(i=!1){const{isCalendarPicker:t,activeParts:n,preferWheel:a,workingParts:s}=e;(void 0!==n||!t)&&(Array.isArray(n)&&0===n.length?e.setValue(a?(0,r.u)(s):void 0):e.setValue((0,r.u)(n))),i&&e.closeParentOverlay()}).apply(this,arguments)}reset(e){var i=this;return(0,C.A)(function*(){i.processValue(e)})()}cancel(){var e=this;return(0,C.A)(function*(i=!1){e.ionCancel.emit(),i&&e.closeParentOverlay()}).apply(this,arguments)}get isCalendarPicker(){const{presentation:e}=this;return"date"===e||"date-time"===e||"time-date"===e}connectedCallback(){this.clearFocusVisible=(0,te.startFocusVisible)(this.el).destroy}disconnectedCallback(){this.clearFocusVisible&&(this.clearFocusVisible(),this.clearFocusVisible=void 0)}initializeListeners(){this.initializeCalendarListener(),this.initializeKeyboardListeners()}componentDidLoad(){const{el:e,intersectionTrackerRef:i}=this,n=new IntersectionObserver(d=>{d[0].isIntersecting&&(this.initializeListeners(),(0,o.w)(()=>{this.el.classList.add("datetime-ready")}))},{threshold:.01,root:e});(0,O.r)(()=>n?.observe(i));const s=new IntersectionObserver(d=>{d[0].isIntersecting||(this.destroyInteractionListeners(),this.showMonthAndYear=!1,(0,o.w)(()=>{this.el.classList.remove("datetime-ready")}))},{threshold:0,root:e});(0,O.r)(()=>s?.observe(i));const l=(0,O.g)(this.el);l.addEventListener("ionFocus",d=>d.stopPropagation()),l.addEventListener("ionBlur",d=>d.stopPropagation())}componentDidRender(){const{presentation:e,prevPresentation:i,calendarBodyRef:t,minParts:n,preferWheel:a,forceRenderDate:s}=this,l=!a&&["date-time","time-date","date"].includes(e);if(void 0!==n&&l&&t){const d=t.querySelector(".calendar-month:nth-of-type(1)");d&&void 0===s&&(t.scrollLeft=d.clientWidth*((0,W.i)(this.el)?-1:1))}null!==i?e!==i&&(this.prevPresentation=e,this.destroyInteractionListeners(),this.initializeListeners(),this.showMonthAndYear=!1,(0,O.r)(()=>{this.ionRender.emit()})):this.prevPresentation=e}componentWillLoad(){const{el:e,formatOptions:i,highlightedDates:t,multiple:n,presentation:a,preferWheel:s}=this;n&&("date"!==a&&(0,w.p)('Multiple date selection is only supported for presentation="date".',e),s&&(0,w.p)('Multiple date selection is not supported with preferWheel="true".',e)),void 0!==t&&("date"!==a&&"date-time"!==a&&"time-date"!==a&&(0,w.p)("The highlightedDates property is only supported with the date, date-time, and time-date presentations.",e),s&&(0,w.p)('The highlightedDates property is not supported with preferWheel="true".',e)),i&&(R(e,a,i),$(e,i));const l=this.parsedHourValues=(0,r.t)(this.hourValues),d=this.parsedMinuteValues=(0,r.t)(this.minuteValues),c=this.parsedMonthValues=(0,r.t)(this.monthValues),p=this.parsedYearValues=(0,r.t)(this.yearValues),h=this.parsedDayValues=(0,r.t)(this.dayValues),u=this.todayParts=(0,r.s)((0,r.x)());this.processMinParts(),this.processMaxParts(),this.defaultParts=(0,r.y)({refParts:u,monthValues:c,dayValues:h,yearValues:p,hourValues:l,minuteValues:d,minParts:this.minParts,maxParts:this.maxParts}),this.processValue(this.value),this.emitStyle()}emitStyle(){this.ionStyle.emit({interactive:!0,datetime:!0,"interactive-disabled":this.disabled})}renderFooter(){const{disabled:e,readonly:i,showDefaultButtons:t,showClearButton:n}=this,a=e||i;if(null===this.el.querySelector('[slot="buttons"]')&&!t&&!n)return;const l=()=>{this.reset(),this.setValue(void 0)};return(0,o.h)("div",{class:"datetime-footer"},(0,o.h)("div",{class:"datetime-buttons"},(0,o.h)("div",{class:{"datetime-action-buttons":!0,"has-clear-button":this.showClearButton}},(0,o.h)("slot",{name:"buttons"},(0,o.h)("ion-buttons",null,t&&(0,o.h)("ion-button",{id:"cancel-button",color:this.color,onClick:()=>this.cancel(!0),disabled:a},this.cancelText),(0,o.h)("div",{class:"datetime-action-buttons-container"},n&&(0,o.h)("ion-button",{id:"clear-button",color:this.color,onClick:()=>l(),disabled:a},this.clearText),t&&(0,o.h)("ion-button",{id:"confirm-button",color:this.color,onClick:()=>this.confirm(!0),disabled:a},this.doneText)))))))}renderWheelPicker(e=this.presentation){const i="time-date"===e?[this.renderTimePickerColumns(e),this.renderDatePickerColumns(e)]:[this.renderDatePickerColumns(e),this.renderTimePickerColumns(e)];return(0,o.h)("ion-picker-internal",null,i)}renderDatePickerColumns(e){return"date-time"===e||"time-date"===e?this.renderCombinedDatePickerColumn():this.renderIndividualDatePickerColumns(e)}renderCombinedDatePickerColumn(){const{defaultParts:e,disabled:i,workingParts:t,locale:n,minParts:a,maxParts:s,todayParts:l,isDateEnabled:d}=this,c=this.getActivePartsWithFallback(),p=(0,r.z)(t),h=p[p.length-1];p[0].day=1,h.day=(0,r.A)(h.month,h.year);const u=void 0!==a&&(0,r.b)(a,p[0])?a:p[0],g=void 0!==s&&(0,r.i)(s,h)?s:h,m=(0,r.B)(n,l,u,g,this.parsedDayValues,this.parsedMonthValues);let f=m.items;const x=m.parts;return d&&(f=f.map((k,v)=>{const A=x[v];let D;try{D=!d((0,r.u)(A))}catch(M){(0,w.a)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",M)}return Object.assign(Object.assign({},k),{disabled:D})})),(0,o.h)("ion-picker-column-internal",{class:"date-column",color:this.color,disabled:i,items:f,value:null!==t.day?`${t.year}-${t.month}-${t.day}`:`${e.year}-${e.month}-${e.day}`,onIonChange:k=>{this.destroyCalendarListener&&this.destroyCalendarListener();const{value:v}=k.detail,A=x.find(({month:D,day:M,year:j})=>v===`${j}-${D}-${M}`);this.setWorkingParts(Object.assign(Object.assign({},t),A)),this.setActiveParts(Object.assign(Object.assign({},c),A)),this.initializeCalendarListener(),k.stopPropagation()}})}renderIndividualDatePickerColumns(e){const{workingParts:i,isDateEnabled:t}=this,a="year"!==e&&"time"!==e?(0,r.C)(this.locale,i,this.minParts,this.maxParts,this.parsedMonthValues):[];let l="date"===e?(0,r.D)(this.locale,i,this.minParts,this.maxParts,this.parsedDayValues):[];t&&(l=l.map(u=>{const{value:g}=u,m="string"==typeof g?parseInt(g):g,f={month:i.month,day:m,year:i.year};let x;try{x=!t((0,r.u)(f))}catch(b){(0,w.a)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",b)}return Object.assign(Object.assign({},u),{disabled:x})}));const c="month"!==e&&"time"!==e?(0,r.E)(this.locale,this.defaultParts,this.minParts,this.maxParts,this.parsedYearValues):[];let h=[];return h=(0,r.F)(this.locale,{month:"numeric",day:"numeric"})?[this.renderMonthPickerColumn(a),this.renderDayPickerColumn(l),this.renderYearPickerColumn(c)]:[this.renderDayPickerColumn(l),this.renderMonthPickerColumn(a),this.renderYearPickerColumn(c)],h}renderDayPickerColumn(e){var i;if(0===e.length)return[];const{disabled:t,workingParts:n}=this,a=this.getActivePartsWithFallback();return(0,o.h)("ion-picker-column-internal",{class:"day-column",color:this.color,disabled:t,items:e,value:null!==(i=null!==n.day?n.day:this.defaultParts.day)&&void 0!==i?i:void 0,onIonChange:s=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},n),{day:s.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{day:s.detail.value})),this.initializeCalendarListener(),s.stopPropagation()}})}renderMonthPickerColumn(e){if(0===e.length)return[];const{disabled:i,workingParts:t}=this,n=this.getActivePartsWithFallback();return(0,o.h)("ion-picker-column-internal",{class:"month-column",color:this.color,disabled:i,items:e,value:t.month,onIonChange:a=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},t),{month:a.detail.value})),this.setActiveParts(Object.assign(Object.assign({},n),{month:a.detail.value})),this.initializeCalendarListener(),a.stopPropagation()}})}renderYearPickerColumn(e){if(0===e.length)return[];const{disabled:i,workingParts:t}=this,n=this.getActivePartsWithFallback();return(0,o.h)("ion-picker-column-internal",{class:"year-column",color:this.color,disabled:i,items:e,value:t.year,onIonChange:a=>{this.destroyCalendarListener&&this.destroyCalendarListener(),this.setWorkingParts(Object.assign(Object.assign({},t),{year:a.detail.value})),this.setActiveParts(Object.assign(Object.assign({},n),{year:a.detail.value})),this.initializeCalendarListener(),a.stopPropagation()}})}renderTimePickerColumns(e){if(["date","month","month-year","year"].includes(e))return[];const t=void 0!==this.getActivePart(),{hoursData:n,minutesData:a,dayPeriodData:s}=(0,r.G)(this.locale,this.workingParts,this.hourCycle,t?this.minParts:void 0,t?this.maxParts:void 0,this.parsedHourValues,this.parsedMinuteValues);return[this.renderHourPickerColumn(n),this.renderMinutePickerColumn(a),this.renderDayPeriodPickerColumn(s)]}renderHourPickerColumn(e){const{disabled:i,workingParts:t}=this;if(0===e.length)return[];const n=this.getActivePartsWithFallback();return(0,o.h)("ion-picker-column-internal",{color:this.color,disabled:i,value:n.hour,items:e,numericInput:!0,onIonChange:a=>{this.setWorkingParts(Object.assign(Object.assign({},t),{hour:a.detail.value})),this.setActiveParts(Object.assign(Object.assign({},n),{hour:a.detail.value})),a.stopPropagation()}})}renderMinutePickerColumn(e){const{disabled:i,workingParts:t}=this;if(0===e.length)return[];const n=this.getActivePartsWithFallback();return(0,o.h)("ion-picker-column-internal",{color:this.color,disabled:i,value:n.minute,items:e,numericInput:!0,onIonChange:a=>{this.setWorkingParts(Object.assign(Object.assign({},t),{minute:a.detail.value})),this.setActiveParts(Object.assign(Object.assign({},n),{minute:a.detail.value})),a.stopPropagation()}})}renderDayPeriodPickerColumn(e){const{disabled:i,workingParts:t}=this;if(0===e.length)return[];const n=this.getActivePartsWithFallback(),a=(0,r.H)(this.locale);return(0,o.h)("ion-picker-column-internal",{style:a?{order:"-1"}:{},color:this.color,disabled:i,value:n.ampm,items:e,onIonChange:s=>{const l=(0,r.R)(t,s.detail.value);this.setWorkingParts(Object.assign(Object.assign({},t),{ampm:s.detail.value,hour:l})),this.setActiveParts(Object.assign(Object.assign({},n),{ampm:s.detail.value,hour:l})),s.stopPropagation()}})}renderWheelView(e){const{locale:i}=this,n=(0,r.F)(i)?"month-first":"year-first";return(0,o.h)("div",{class:{[`wheel-order-${n}`]:!0}},this.renderWheelPicker(e))}renderCalendarHeader(e){const{disabled:i}=this,t="ios"===e?_.l:_.p,n="ios"===e?_.o:_.q,a=i||((e,i,t)=>{const n=Object.assign(Object.assign({},(0,r.d)(this.workingParts)),{day:null});return z(n,{minParts:i,maxParts:t})})(0,this.minParts,this.maxParts),s=i||((e,i)=>{const t=Object.assign(Object.assign({},(0,r.e)(this.workingParts)),{day:null});return z(t,{maxParts:i})})(0,this.maxParts),l=this.el.getAttribute("dir")||void 0;return(0,o.h)("div",{class:"calendar-header"},(0,o.h)("div",{class:"calendar-action-buttons"},(0,o.h)("div",{class:"calendar-month-year"},(0,o.h)("ion-item",{part:"month-year-button",ref:d=>this.monthYearToggleItemRef=d,button:!0,"aria-label":"Show year picker",detail:!1,lines:"none",disabled:i,onClick:()=>{var d;this.toggleMonthAndYearView();const{monthYearToggleItemRef:c}=this;if(c){const p=null===(d=c.shadowRoot)||void 0===d?void 0:d.querySelector(".item-native");p&&p.setAttribute("aria-label",this.showMonthAndYear?"Hide year picker":"Show year picker")}}},(0,o.h)("ion-label",null,(0,r.J)(this.locale,this.workingParts),(0,o.h)("ion-icon",{"aria-hidden":"true",icon:this.showMonthAndYear?t:n,lazy:!1,flipRtl:!0})))),(0,o.h)("div",{class:"calendar-next-prev"},(0,o.h)("ion-buttons",null,(0,o.h)("ion-button",{"aria-label":"Previous month",disabled:a,onClick:()=>this.prevMonth()},(0,o.h)("ion-icon",{dir:l,"aria-hidden":"true",slot:"icon-only",icon:_.c,lazy:!1,flipRtl:!0})),(0,o.h)("ion-button",{"aria-label":"Next month",disabled:s,onClick:()=>this.nextMonth()},(0,o.h)("ion-icon",{dir:l,"aria-hidden":"true",slot:"icon-only",icon:_.o,lazy:!1,flipRtl:!0}))))),(0,o.h)("div",{class:"calendar-days-of-week","aria-hidden":"true"},(0,r.I)(this.locale,e,this.firstDayOfWeek%7).map(d=>(0,o.h)("div",{class:"day-of-week"},d))))}renderMonth(e,i){const{disabled:t,readonly:n}=this,a=void 0===this.parsedYearValues||this.parsedYearValues.includes(i),s=void 0===this.parsedMonthValues||this.parsedMonthValues.includes(e),l=!a||!s,d=t||n,c=t||z({month:e,year:i,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})}),p=this.workingParts.month===e&&this.workingParts.year===i,h=this.getActivePartsWithFallback();return(0,o.h)("div",{"aria-hidden":p?null:"true",class:{"calendar-month":!0,"calendar-month-disabled":!p&&c}},(0,o.h)("div",{class:"calendar-month-grid"},(0,r.K)(e,i,this.firstDayOfWeek%7).map((u,g)=>{const{day:m,dayOfWeek:f}=u,{el:x,highlightedDates:b,isDateEnabled:k,multiple:v}=this,A={month:e,day:m,year:i},D=null===m,{isActive:M,isToday:j,ariaLabel:B,ariaSelected:ye,disabled:xe,text:ke}=((e,i,t,n,a,s,l)=>{const c=void 0!==(Array.isArray(t)?t:[t]).find(u=>(0,r.c)(i,u)),p=(0,r.c)(i,n);return{disabled:H(i,a,s,l),isActive:c,isToday:p,ariaSelected:c?"true":null,ariaLabel:(0,r.g)(e,p,i),text:null!=i.day?(0,r.a)(e,i):null}})(this.locale,A,this.activeParts,this.todayParts,this.minParts,this.maxParts,this.parsedDayValues),Q=(0,r.u)(A);let E=l||xe;if(!E&&void 0!==k)try{E=!k(Q)}catch(S){(0,w.a)("Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",x,S)}const ve=E&&d,we=E||d;let V,ee;return void 0!==b&&!M&&null!==m&&(V=((e,i,t)=>{if(Array.isArray(e)){const n=i.split("T")[0],a=e.find(s=>s.date===n);if(a)return{textColor:a.textColor,backgroundColor:a.backgroundColor}}else try{return e(i)}catch(n){(0,w.a)("Exception thrown from provided `highlightedDates` callback. Please check your function and try again.",t,n)}})(b,Q,x)),D||(ee=`calendar-day${M?" active":""}${j?" today":""}${E?" disabled":""}`),(0,o.h)("div",{class:"calendar-day-wrapper"},(0,o.h)("button",{ref:S=>{S&&(S.style.setProperty("color",`${V?V.textColor:""}`,"important"),S.style.setProperty("background-color",`${V?V.backgroundColor:""}`,"important"))},tabindex:"-1","data-day":m,"data-month":e,"data-year":i,"data-index":g,"data-day-of-week":f,disabled:we,class:{"calendar-day-padding":D,"calendar-day":!0,"calendar-day-active":M,"calendar-day-constrained":ve,"calendar-day-today":j},part:ee,"aria-hidden":D?"true":null,"aria-selected":ye,"aria-label":B,onClick:()=>{D||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:e,day:m,year:i})),v?this.setActiveParts({month:e,day:m,year:i},M):this.setActiveParts(Object.assign(Object.assign({},h),{month:e,day:m,year:i})))}},ke))})))}renderCalendarBody(){return(0,o.h)("div",{class:"calendar-body ion-focusable",ref:e=>this.calendarBodyRef=e,tabindex:"0"},(0,r.z)(this.workingParts,this.forceRenderDate).map(({month:e,year:i})=>this.renderMonth(e,i)))}renderCalendar(e){return(0,o.h)("div",{class:"datetime-calendar",key:"datetime-calendar"},this.renderCalendarHeader(e),this.renderCalendarBody())}renderTimeLabel(){if(null!==this.el.querySelector('[slot="time-label"]')||this.showDefaultTimeLabel)return(0,o.h)("slot",{name:"time-label"},"Time")}renderTimeOverlay(){var e=this;const{disabled:i,hourCycle:t,isTimePopoverOpen:n,locale:a,formatOptions:s}=this,l=(0,r.L)(a,t),d=this.getActivePartsWithFallback();return[(0,o.h)("div",{class:"time-header"},this.renderTimeLabel()),(0,o.h)("button",{class:{"time-body":!0,"time-body-active":n},part:"time-button"+(n?" active":""),"aria-expanded":"false","aria-haspopup":"true",disabled:i,onClick:(c=(0,C.A)(function*(p){const{popoverRef:h}=e;h&&(e.isTimePopoverOpen=!0,h.present(new CustomEvent("ionShadowTarget",{detail:{ionShadowTarget:p.target}})),yield h.onWillDismiss(),e.isTimePopoverOpen=!1)}),function(h){return c.apply(this,arguments)})},(0,r.M)(a,d,l,s?.time)),(0,o.h)("ion-popover",{alignment:"center",translucent:!0,overlayIndex:1,arrow:!1,onWillPresent:c=>{c.target.querySelectorAll("ion-picker-column-internal").forEach(h=>h.scrollActiveItemIntoView())},style:{"--offset-y":"-10px","--min-width":"fit-content"},keyboardEvents:!0,ref:c=>this.popoverRef=c},this.renderWheelPicker("time"))];var c}getHeaderSelectedDateText(){var e;const{activeParts:i,formatOptions:t,multiple:n,titleSelectedDatesFormatter:a}=this,s=Array.isArray(i);let l;if(n&&s&&1!==i.length){if(l=`${i.length} days`,void 0!==a)try{l=a((0,r.u)(i))}catch(d){(0,w.a)("Exception in provided `titleSelectedDatesFormatter`: ",d)}}else l=(0,r.N)(this.locale,this.getActivePartsWithFallback(),null!==(e=t?.date)&&void 0!==e?e:{weekday:"short",month:"short",day:"numeric"});return l}renderHeader(e=!0){if(null!==this.el.querySelector('[slot="title"]')||this.showDefaultTitle)return(0,o.h)("div",{class:"datetime-header"},(0,o.h)("div",{class:"datetime-title"},(0,o.h)("slot",{name:"title"},"Select Date")),e&&(0,o.h)("div",{class:"datetime-selected-date"},this.getHeaderSelectedDateText()))}renderTime(){const{presentation:e}=this;return(0,o.h)("div",{class:"datetime-time"},"time"===e?this.renderWheelPicker():this.renderTimeOverlay())}renderCalendarViewMonthYearPicker(){return(0,o.h)("div",{class:"datetime-year"},this.renderWheelView("month-year"))}renderDatetime(e){const{presentation:i,preferWheel:t}=this;if(t&&("date"===i||"date-time"===i||"time-date"===i))return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];switch(i){case"date-time":return[this.renderHeader(),this.renderCalendar(e),this.renderCalendarViewMonthYearPicker(),this.renderTime(),this.renderFooter()];case"time-date":return[this.renderHeader(),this.renderTime(),this.renderCalendar(e),this.renderCalendarViewMonthYearPicker(),this.renderFooter()];case"time":return[this.renderHeader(!1),this.renderTime(),this.renderFooter()];case"month":case"month-year":case"year":return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];default:return[this.renderHeader(),this.renderCalendar(e),this.renderCalendarViewMonthYearPicker(),this.renderFooter()]}}render(){const{name:e,value:i,disabled:t,el:n,color:a,readonly:s,showMonthAndYear:l,preferWheel:d,presentation:c,size:p,isGridStyle:h}=this,u=(0,T.b)(this),g="year"===c||"month"===c||"month-year"===c,m=l||g,f=l&&!g,b=("date"===c||"date-time"===c||"time-date"===c)&&d;return(0,O.d)(!0,n,e,(0,r.O)(i),t),(0,o.h)(o.H,{key:"8490192beb6c5c6064ed8f2a7be2d51846f84f36","aria-disabled":t?"true":null,onFocus:this.onFocus,onBlur:this.onBlur,class:Object.assign({},(0,L.c)(a,{[u]:!0,"datetime-readonly":s,"datetime-disabled":t,"show-month-and-year":m,"month-year-picker-open":f,[`datetime-presentation-${c}`]:!0,[`datetime-size-${p}`]:!0,"datetime-prefer-wheel":b,"datetime-grid":h}))},(0,o.h)("div",{key:"a2959c07ed871f9004a2f11ab1385a5a7b5737fd",class:"intersection-tracker",ref:k=>this.intersectionTrackerRef=k}),this.renderDatetime(u))}get el(){return(0,o.f)(this)}static get watchers(){return{formatOptions:["formatOptionsChanged"],disabled:["disabledChanged"],min:["minChanged"],max:["maxChanged"],presentation:["presentationChanged"],yearValues:["yearValuesChanged"],monthValues:["monthValuesChanged"],dayValues:["dayValuesChanged"],hourValues:["hourValuesChanged"],minuteValues:["minuteValuesChanged"],value:["valueChanged"]}}};let ce=0;K.style={ios:":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{position:absolute;visibility:hidden;pointer-events:none}@supports (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{inset-inline-start:-99999px}}@supports not (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}@supports selector(:dir(rtl)){:host(.show-month-and-year:dir(rtl)) .calendar-next-prev,:host(.show-month-and-year:dir(rtl)) .calendar-days-of-week,:host(.show-month-and-year:dir(rtl)) .calendar-body,:host(.show-month-and-year:dir(rtl)) .datetime-time{left:unset;right:unset;right:-99999px}}}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:auto}:host .calendar-action-buttons ion-item ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-light, #ffffff);--background-rgb:var(--ion-color-light-rgb);--title-color:var(--ion-color-step-600, #666666)}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, #cccccc);font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}:host .calendar-action-buttons ion-item{--padding-start:16px;--background-hover:transparent;--background-activated:transparent;font-size:min(1rem, 25.6px);font-weight:600}:host .calendar-action-buttons ion-item ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, #b3b3b3);font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, #cccccc)}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}",md:":host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}ion-picker-column-internal{min-width:26px}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}@supports (background: -webkit-named-image(apple-pay-logo-black)) and (not (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{position:absolute;visibility:hidden;pointer-events:none}@supports (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{inset-inline-start:-99999px}}@supports not (inset-inline-start: 0){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{left:-99999px}:host-context([dir=rtl]):host(.show-month-and-year) .calendar-next-prev,:host-context([dir=rtl]).show-month-and-year .calendar-next-prev,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-days-of-week,:host-context([dir=rtl]).show-month-and-year .calendar-days-of-week,:host-context([dir=rtl]):host(.show-month-and-year) .calendar-body,:host-context([dir=rtl]).show-month-and-year .calendar-body,:host-context([dir=rtl]):host(.show-month-and-year) .datetime-time,:host-context([dir=rtl]).show-month-and-year .datetime-time{left:unset;right:unset;right:-99999px}@supports selector(:dir(rtl)){:host(.show-month-and-year:dir(rtl)) .calendar-next-prev,:host(.show-month-and-year:dir(rtl)) .calendar-days-of-week,:host(.show-month-and-year:dir(rtl)) .calendar-body,:host(.show-month-and-year:dir(rtl)) .datetime-time{left:unset;right:unset;right:-99999px}}}}@supports (not (background: -webkit-named-image(apple-pay-logo-black))) or ((background: -webkit-named-image(apple-pay-logo-black)) and (aspect-ratio: 1/1)){:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--background:translucent}:host .calendar-action-buttons ion-item ion-label{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:auto}:host .calendar-action-buttons ion-item ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, #edeef0);color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons ion-item{--color:var(--ion-color-base)}:host{--background:var(--ion-color-step-100, #ffffff);--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .datetime-calendar .calendar-action-buttons ion-item{--padding-start:20px}:host .calendar-action-buttons ion-item,:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, #595959)}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, gray);font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, #595959)}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}"};const U=e=>{const i=(0,I.c)(),t=(0,I.c)(),n=(0,I.c)();return t.addElement(e.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),n.addElement(e.querySelector(".picker-wrapper")).fromTo("transform","translateY(100%)","translateY(0%)"),i.addElement(e).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([t,n])},N=e=>{const i=(0,I.c)(),t=(0,I.c)(),n=(0,I.c)();return t.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",.01),n.addElement(e.querySelector(".picker-wrapper")).fromTo("transform","translateY(0%)","translateY(100%)"),i.addElement(e).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([t,n])},Z=class{constructor(e){(0,o.r)(this,e),this.didPresent=(0,o.d)(this,"ionPickerDidPresent",7),this.willPresent=(0,o.d)(this,"ionPickerWillPresent",7),this.willDismiss=(0,o.d)(this,"ionPickerWillDismiss",7),this.didDismiss=(0,o.d)(this,"ionPickerDidDismiss",7),this.didPresentShorthand=(0,o.d)(this,"didPresent",7),this.willPresentShorthand=(0,o.d)(this,"willPresent",7),this.willDismissShorthand=(0,o.d)(this,"willDismiss",7),this.didDismissShorthand=(0,o.d)(this,"didDismiss",7),this.delegateController=(0,P.d)(this),this.lockController=(0,ie.c)(),this.triggerController=(0,P.e)(),this.onBackdropTap=()=>{this.dismiss(void 0,P.B)},this.dispatchCancelHandler=i=>{if((0,P.i)(i.detail.role)){const n=this.buttons.find(a=>"cancel"===a.role);this.callButtonHandler(n)}},this.presented=!1,this.overlayIndex=void 0,this.delegate=void 0,this.hasController=!1,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.buttons=[],this.columns=[],this.cssClass=void 0,this.duration=0,this.showBackdrop=!0,this.backdropDismiss=!0,this.animated=!0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0}onIsOpenChange(e,i){!0===e&&!1===i?this.present():!1===e&&!0===i&&this.dismiss()}triggerChanged(){const{trigger:e,el:i,triggerController:t}=this;e&&t.addClickListener(i,e)}connectedCallback(){(0,P.j)(this.el),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){(0,P.k)(this.el)}componentDidLoad(){!0===this.isOpen&&(0,O.r)(()=>this.present()),this.triggerChanged()}present(){var e=this;return(0,C.A)(function*(){const i=yield e.lockController.lock();yield e.delegateController.attachViewToDom(),yield(0,P.f)(e,"pickerEnter",U,U,void 0),e.duration>0&&(e.durationTimeout=setTimeout(()=>e.dismiss(),e.duration)),i()})()}dismiss(e,i){var t=this;return(0,C.A)(function*(){const n=yield t.lockController.lock();t.durationTimeout&&clearTimeout(t.durationTimeout);const a=yield(0,P.g)(t,e,i,"pickerLeave",N,N);return a&&t.delegateController.removeViewFromDom(),n(),a})()}onDidDismiss(){return(0,P.h)(this.el,"ionPickerDidDismiss")}onWillDismiss(){return(0,P.h)(this.el,"ionPickerWillDismiss")}getColumn(e){return Promise.resolve(this.columns.find(i=>i.name===e))}buttonClick(e){var i=this;return(0,C.A)(function*(){const t=e.role;return(0,P.i)(t)?i.dismiss(void 0,t):(yield i.callButtonHandler(e))?i.dismiss(i.getSelected(),e.role):Promise.resolve()})()}callButtonHandler(e){var i=this;return(0,C.A)(function*(){return!(e&&!1===(yield(0,P.s)(e.handler,i.getSelected())))})()}getSelected(){const e={};return this.columns.forEach((i,t)=>{const n=void 0!==i.selectedIndex?i.options[i.selectedIndex]:void 0;e[i.name]={text:n?n.text:void 0,value:n?n.value:void 0,columnIndex:t}}),e}render(){const{htmlAttributes:e}=this,i=(0,T.b)(this);return(0,o.h)(o.H,Object.assign({key:"eb5f91ea74fb11daa6942f779ef461742cad9ecb","aria-modal":"true",tabindex:"-1"},e,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[i]:!0,[`picker-${i}`]:!0,"overlay-hidden":!0},(0,L.g)(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonPickerWillDismiss:this.dispatchCancelHandler}),(0,o.h)("ion-backdrop",{key:"7ea872d939e62f14129fff15334b2822ad2360c9",visible:this.showBackdrop,tappable:this.backdropDismiss}),(0,o.h)("div",{key:"2d77c225091eacab0207e28c96b966122afafef0",tabindex:"0"}),(0,o.h)("div",{key:"630d21e0c60ad97b71462cdc540858bb6ced0b8f",class:"picker-wrapper ion-overlay-wrapper",role:"dialog"},(0,o.h)("div",{key:"fa8553ec8d2ce8bf93e16e02334b6475cb51b5d4",class:"picker-toolbar"},this.buttons.map(t=>(0,o.h)("div",{class:me(t)},(0,o.h)("button",{type:"button",onClick:()=>this.buttonClick(t),class:ue(t)},t.text)))),(0,o.h)("div",{key:"177d1bcbd0ce38f16d9c936295a917fb981d02d7",class:"picker-columns"},(0,o.h)("div",{key:"be99b6e0279c210ef91a88ccc81acc7d37917a53",class:"picker-above-highlight"}),this.presented&&this.columns.map(t=>(0,o.h)("ion-picker-column",{col:t})),(0,o.h)("div",{key:"b36b21e8133b59e873e1d3447a1279f1b971c854",class:"picker-below-highlight"}))),(0,o.h)("div",{key:"17cea6dd24dbb0a08073ca4a84bfe027eb24833d",tabindex:"0"}))}get el(){return(0,o.f)(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},me=e=>({[`picker-toolbar-${e.role}`]:void 0!==e.role,"picker-toolbar-button":!0}),ue=e=>Object.assign({"picker-button":!0,"ion-activatable":!0},(0,L.g)(e.cssClass));Z.style={ios:".sc-ion-picker-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}@supports (inset-inline-start: 0){.sc-ion-picker-ios-h{inset-inline-start:0}}@supports not (inset-inline-start: 0){.sc-ion-picker-ios-h{left:0}[dir=rtl].sc-ion-picker-ios-h,[dir=rtl] .sc-ion-picker-ios-h{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.sc-ion-picker-ios-h:dir(rtl){left:unset;right:unset;right:0}}}.overlay-hidden.sc-ion-picker-ios-h{display:none}.picker-wrapper.sc-ion-picker-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-ios:active,.picker-button.sc-ion-picker-ios:focus{outline:none}.picker-columns.sc-ion-picker-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-ios,.picker-below-highlight.sc-ion-picker-ios{display:none;pointer-events:none}.sc-ion-picker-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-ios:last-child .picker-button.sc-ion-picker-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-ios,.picker-button.ion-activated.sc-ion-picker-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:16px}.picker-columns.sc-ion-picker-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}@supports (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-ios{left:0}[dir=rtl].sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-ios .picker-above-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-above-highlight.sc-ion-picker-ios:dir(rtl){left:unset;right:unset;right:0}}}.picker-below-highlight.sc-ion-picker-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}@supports (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-ios{left:0}[dir=rtl].sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios,[dir=rtl] .sc-ion-picker-ios-h .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-ios .picker-below-highlight.sc-ion-picker-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-below-highlight.sc-ion-picker-ios:dir(rtl){left:unset;right:unset;right:0}}}",md:".sc-ion-picker-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}@supports (inset-inline-start: 0){.sc-ion-picker-md-h{inset-inline-start:0}}@supports not (inset-inline-start: 0){.sc-ion-picker-md-h{left:0}[dir=rtl].sc-ion-picker-md-h,[dir=rtl] .sc-ion-picker-md-h{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.sc-ion-picker-md-h:dir(rtl){left:unset;right:unset;right:0}}}.overlay-hidden.sc-ion-picker-md-h{display:none}.picker-wrapper.sc-ion-picker-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-md:active,.picker-button.sc-ion-picker-md:focus{outline:none}.picker-columns.sc-ion-picker-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-md,.picker-below-highlight.sc-ion-picker-md{display:none;pointer-events:none}.sc-ion-picker-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-md,.picker-button.ion-activated.sc-ion-picker-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #3880ff);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}@supports (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-above-highlight.sc-ion-picker-md{left:0}[dir=rtl].sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-md .picker-above-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-above-highlight.sc-ion-picker-md:dir(rtl){left:unset;right:unset;right:0}}}.picker-below-highlight.sc-ion-picker-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}@supports (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-below-highlight.sc-ion-picker-md{left:0}[dir=rtl].sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md,[dir=rtl] .sc-ion-picker-md-h .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-picker-md .picker-below-highlight.sc-ion-picker-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-below-highlight.sc-ion-picker-md:dir(rtl){left:unset;right:unset;right:0}}}"};const G=class{constructor(e){(0,o.r)(this,e),this.ionPickerColChange=(0,o.d)(this,"ionPickerColChange",7),this.optHeight=0,this.rotateFactor=0,this.scaleFactor=1,this.velocity=0,this.y=0,this.noAnimate=!0,this.colDidChange=!1,this.col=void 0}colChanged(){this.colDidChange=!0}connectedCallback(){var e=this;return(0,C.A)(function*(){let i=0,t=.81;"ios"===(0,T.b)(e)&&(i=-.46,t=1),e.rotateFactor=i,e.scaleFactor=t,e.gesture=(yield Promise.resolve().then(y.bind(y,405))).createGesture({el:e.el,gestureName:"picker-swipe",gesturePriority:100,threshold:0,passive:!1,onStart:a=>e.onStart(a),onMove:a=>e.onMove(a),onEnd:a=>e.onEnd(a)}),e.gesture.enable(),e.tmrId=setTimeout(()=>{e.noAnimate=!1,e.refresh(!0)},250)})()}componentDidLoad(){this.onDomChange()}componentDidUpdate(){this.colDidChange&&(this.onDomChange(!0,!1),this.colDidChange=!1)}disconnectedCallback(){void 0!==this.rafId&&cancelAnimationFrame(this.rafId),this.tmrId&&clearTimeout(this.tmrId),this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}emitColChange(){this.ionPickerColChange.emit(this.col)}setSelected(e,i){const t=e>-1?-e*this.optHeight:0;this.velocity=0,void 0!==this.rafId&&cancelAnimationFrame(this.rafId),this.update(t,i,!0),this.emitColChange()}update(e,i,t){if(!this.optsEl)return;let n=0,a=0;const{col:s,rotateFactor:l}=this,d=s.selectedIndex,c=s.selectedIndex=this.indexForY(-e),p=0===i?"":i+"ms",h=`scale(${this.scaleFactor})`,u=this.optsEl.children;for(let g=0;g<u.length;g++){const m=u[g],f=s.options[g],x=g*this.optHeight+e;let b="";if(0!==l){const v=x*l;Math.abs(v)<=90?(n=0,a=90,b=`rotateX(${v}deg) `):n=-9999}else a=0,n=x;const k=c===g;b+=`translate3d(0px,${n}px,${a}px) `,1!==this.scaleFactor&&!k&&(b+=h),this.noAnimate?(f.duration=0,m.style.transitionDuration=""):i!==f.duration&&(f.duration=i,m.style.transitionDuration=p),b!==f.transform&&(f.transform=b),m.style.transform=b,f.selected=k,k?m.classList.add(q):m.classList.remove(q)}this.col.prevSelected=d,t&&(this.y=e),this.lastIndex!==c&&((0,F.b)(),this.lastIndex=c)}decelerate(){if(0!==this.velocity){this.velocity*=be,this.velocity=this.velocity>0?Math.max(this.velocity,1):Math.min(this.velocity,-1);let e=this.y+this.velocity;e>this.minY?(e=this.minY,this.velocity=0):e<this.maxY&&(e=this.maxY,this.velocity=0),this.update(e,0,!0),Math.round(e)%this.optHeight!=0||Math.abs(this.velocity)>1?this.rafId=requestAnimationFrame(()=>this.decelerate()):(this.velocity=0,this.emitColChange(),(0,F.h)())}else if(this.y%this.optHeight!=0){const e=Math.abs(this.y%this.optHeight);this.velocity=e>this.optHeight/2?1:-1,this.decelerate()}}indexForY(e){return Math.min(Math.max(Math.abs(Math.round(e/this.optHeight)),0),this.col.options.length-1)}onStart(e){e.event.cancelable&&e.event.preventDefault(),e.event.stopPropagation(),(0,F.a)(),void 0!==this.rafId&&cancelAnimationFrame(this.rafId);const i=this.col.options;let t=i.length-1,n=0;for(let a=0;a<i.length;a++)i[a].disabled||(t=Math.min(t,a),n=Math.max(n,a));this.minY=-t*this.optHeight,this.maxY=-n*this.optHeight}onMove(e){e.event.cancelable&&e.event.preventDefault(),e.event.stopPropagation();let i=this.y+e.deltaY;i>this.minY?(i=Math.pow(i,.8),this.bounceFrom=i):i<this.maxY?(i+=Math.pow(this.maxY-i,.9),this.bounceFrom=i):this.bounceFrom=0,this.update(i,0,!1)}onEnd(e){if(this.bounceFrom>0)return this.update(this.minY,100,!0),void this.emitColChange();if(this.bounceFrom<0)return this.update(this.maxY,100,!0),void this.emitColChange();if(this.velocity=(0,O.l)(-X,23*e.velocityY,X),0===this.velocity&&0===e.deltaY){const i=e.event.target.closest(".picker-opt");i?.hasAttribute("opt-index")&&this.setSelected(parseInt(i.getAttribute("opt-index"),10),J)}else{if(this.y+=e.deltaY,Math.abs(e.velocityY)<.05){const i=e.deltaY>0,t=Math.abs(this.y)%this.optHeight/this.optHeight;i&&t>.5?this.velocity=-1*Math.abs(this.velocity):!i&&t<=.5&&(this.velocity=Math.abs(this.velocity))}this.decelerate()}}refresh(e,i){var t;let n=this.col.options.length-1,a=0;const s=this.col.options;for(let d=0;d<s.length;d++)s[d].disabled||(n=Math.min(n,d),a=Math.max(a,d));if(0!==this.velocity)return;const l=(0,O.l)(n,null!==(t=this.col.selectedIndex)&&void 0!==t?t:0,a);if(this.col.prevSelected!==l||e){const d=l*this.optHeight*-1,c=i?J:0;this.velocity=0,this.update(d,c,!0)}}onDomChange(e,i){const t=this.optsEl;t&&(this.optHeight=t.firstElementChild?t.firstElementChild.clientHeight:0),this.refresh(e,i)}render(){const e=this.col,i=(0,T.b)(this);return(0,o.h)(o.H,{key:"49bb4c67a67c7318d4c305df78ceabae36355112",class:Object.assign({[i]:!0,"picker-col":!0,"picker-opts-left":"left"===this.col.align,"picker-opts-right":"right"===this.col.align},(0,L.g)(e.cssClass)),style:{"max-width":this.col.columnWidth}},e.prefix&&(0,o.h)("div",{key:"7e65761d24473e4ba0ce2d4fc707a5c5e8127903",class:"picker-prefix",style:{width:e.prefixWidth}},e.prefix),(0,o.h)("div",{key:"65c3aea609401e8ae4ea6d363a1b9436796c0a86",class:"picker-opts",style:{maxWidth:e.optionsWidth},ref:t=>this.optsEl=t},e.options.map((t,n)=>(0,o.h)("button",{"aria-label":t.ariaLabel,class:{"picker-opt":!0,"picker-opt-disabled":!!t.disabled},"opt-index":n},t.text))),e.suffix&&(0,o.h)("div",{key:"c2e5a324ba95dd8832d3eb81b139e1f674d74a35",class:"picker-suffix",style:{width:e.suffixWidth}},e.suffix))}get el(){return(0,o.f)(this)}static get watchers(){return{col:["colChanged"]}}},q="picker-opt-selected",be=.97,X=90,J=150;G.style={ios:".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}@supports (inset-inline-start: 0){.picker-opt{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-opt{left:0}:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}[dir=rtl] .picker-opt{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){left:unset;right:unset;right:0}}}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}",md:".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}@supports (inset-inline-start: 0){.picker-opt{inset-inline-start:0}}@supports not (inset-inline-start: 0){.picker-opt{left:0}:host-context([dir=rtl]) .picker-opt{left:unset;right:unset;right:0}[dir=rtl] .picker-opt{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){left:unset;right:unset;right:0}}}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #3880ff)}"}}}]);