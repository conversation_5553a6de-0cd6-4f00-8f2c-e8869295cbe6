/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { createEngine as ɵcreateEngine } from './create_engine';
export { Animation as ɵAnimation } from './dsl/animation';
export { AnimationStyleNormalizer as ɵAnimationStyleNormalizer, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, } from './dsl/style_normalization/animation_style_normalizer';
export { WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer } from './dsl/style_normalization/web_animations_style_normalizer';
export { AnimationEngine as ɵAnimationEngine } from './render/animation_engine_next';
export { AnimationRendererFactory as ɵAnimationRendererFactory } from './render/animation_renderer';
export { AnimationRenderer as ɵAnimationRenderer, BaseAnimationRenderer as ɵBaseAnimationRenderer, } from './render/renderer';
export { containsElement as ɵcontainsElement, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, validateStyleProperty as ɵvalidateStyleProperty, validateWebAnimatableStyleProperty as ɵvalidateWebAnimatableStyleProperty, } from './render/shared';
export { WebAnimationsDriver as ɵWebAnimationsDriver } from './render/web_animations/web_animations_driver';
export { WebAnimationsPlayer as ɵWebAnimationsPlayer } from './render/web_animations/web_animations_player';
export { allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, camelCaseToDashCase as ɵcamelCaseToDashCase, normalizeKeyframes as ɵnormalizeKeyframes, } from './util';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJpdmF0ZV9leHBvcnQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9hbmltYXRpb25zL2Jyb3dzZXIvc3JjL3ByaXZhdGVfZXhwb3J0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUNILE9BQU8sRUFBQyxZQUFZLElBQUksYUFBYSxFQUFDLE1BQU0saUJBQWlCLENBQUM7QUFDOUQsT0FBTyxFQUFDLFNBQVMsSUFBSSxVQUFVLEVBQUMsTUFBTSxpQkFBaUIsQ0FBQztBQUN4RCxPQUFPLEVBQ0wsd0JBQXdCLElBQUkseUJBQXlCLEVBQ3JELDRCQUE0QixJQUFJLDZCQUE2QixHQUM5RCxNQUFNLHNEQUFzRCxDQUFDO0FBQzlELE9BQU8sRUFBQyw0QkFBNEIsSUFBSSw2QkFBNkIsRUFBQyxNQUFNLDJEQUEyRCxDQUFDO0FBQ3hJLE9BQU8sRUFBQyxlQUFlLElBQUksZ0JBQWdCLEVBQUMsTUFBTSxnQ0FBZ0MsQ0FBQztBQUNuRixPQUFPLEVBQUMsd0JBQXdCLElBQUkseUJBQXlCLEVBQUMsTUFBTSw2QkFBNkIsQ0FBQztBQUNsRyxPQUFPLEVBQ0wsaUJBQWlCLElBQUksa0JBQWtCLEVBQ3ZDLHFCQUFxQixJQUFJLHNCQUFzQixHQUNoRCxNQUFNLG1CQUFtQixDQUFDO0FBQzNCLE9BQU8sRUFDTCxlQUFlLElBQUksZ0JBQWdCLEVBQ25DLGdCQUFnQixJQUFJLGlCQUFpQixFQUNyQyxXQUFXLElBQUksWUFBWSxFQUMzQixxQkFBcUIsSUFBSSxzQkFBc0IsRUFDL0Msa0NBQWtDLElBQUksbUNBQW1DLEdBQzFFLE1BQU0saUJBQWlCLENBQUM7QUFDekIsT0FBTyxFQUFDLG1CQUFtQixJQUFJLG9CQUFvQixFQUFDLE1BQU0sK0NBQStDLENBQUM7QUFDMUcsT0FBTyxFQUFDLG1CQUFtQixJQUFJLG9CQUFvQixFQUFDLE1BQU0sK0NBQStDLENBQUM7QUFDMUcsT0FBTyxFQUNMLDhCQUE4QixJQUFJLCtCQUErQixFQUNqRSxtQkFBbUIsSUFBSSxvQkFBb0IsRUFDM0Msa0JBQWtCLElBQUksbUJBQW1CLEdBQzFDLE1BQU0sUUFBUSxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5leHBvcnQge2NyZWF0ZUVuZ2luZSBhcyDJtWNyZWF0ZUVuZ2luZX0gZnJvbSAnLi9jcmVhdGVfZW5naW5lJztcbmV4cG9ydCB7QW5pbWF0aW9uIGFzIMm1QW5pbWF0aW9ufSBmcm9tICcuL2RzbC9hbmltYXRpb24nO1xuZXhwb3J0IHtcbiAgQW5pbWF0aW9uU3R5bGVOb3JtYWxpemVyIGFzIMm1QW5pbWF0aW9uU3R5bGVOb3JtYWxpemVyLFxuICBOb29wQW5pbWF0aW9uU3R5bGVOb3JtYWxpemVyIGFzIMm1Tm9vcEFuaW1hdGlvblN0eWxlTm9ybWFsaXplcixcbn0gZnJvbSAnLi9kc2wvc3R5bGVfbm9ybWFsaXphdGlvbi9hbmltYXRpb25fc3R5bGVfbm9ybWFsaXplcic7XG5leHBvcnQge1dlYkFuaW1hdGlvbnNTdHlsZU5vcm1hbGl6ZXIgYXMgybVXZWJBbmltYXRpb25zU3R5bGVOb3JtYWxpemVyfSBmcm9tICcuL2RzbC9zdHlsZV9ub3JtYWxpemF0aW9uL3dlYl9hbmltYXRpb25zX3N0eWxlX25vcm1hbGl6ZXInO1xuZXhwb3J0IHtBbmltYXRpb25FbmdpbmUgYXMgybVBbmltYXRpb25FbmdpbmV9IGZyb20gJy4vcmVuZGVyL2FuaW1hdGlvbl9lbmdpbmVfbmV4dCc7XG5leHBvcnQge0FuaW1hdGlvblJlbmRlcmVyRmFjdG9yeSBhcyDJtUFuaW1hdGlvblJlbmRlcmVyRmFjdG9yeX0gZnJvbSAnLi9yZW5kZXIvYW5pbWF0aW9uX3JlbmRlcmVyJztcbmV4cG9ydCB7XG4gIEFuaW1hdGlvblJlbmRlcmVyIGFzIMm1QW5pbWF0aW9uUmVuZGVyZXIsXG4gIEJhc2VBbmltYXRpb25SZW5kZXJlciBhcyDJtUJhc2VBbmltYXRpb25SZW5kZXJlcixcbn0gZnJvbSAnLi9yZW5kZXIvcmVuZGVyZXInO1xuZXhwb3J0IHtcbiAgY29udGFpbnNFbGVtZW50IGFzIMm1Y29udGFpbnNFbGVtZW50LFxuICBnZXRQYXJlbnRFbGVtZW50IGFzIMm1Z2V0UGFyZW50RWxlbWVudCxcbiAgaW52b2tlUXVlcnkgYXMgybVpbnZva2VRdWVyeSxcbiAgdmFsaWRhdGVTdHlsZVByb3BlcnR5IGFzIMm1dmFsaWRhdGVTdHlsZVByb3BlcnR5LFxuICB2YWxpZGF0ZVdlYkFuaW1hdGFibGVTdHlsZVByb3BlcnR5IGFzIMm1dmFsaWRhdGVXZWJBbmltYXRhYmxlU3R5bGVQcm9wZXJ0eSxcbn0gZnJvbSAnLi9yZW5kZXIvc2hhcmVkJztcbmV4cG9ydCB7V2ViQW5pbWF0aW9uc0RyaXZlciBhcyDJtVdlYkFuaW1hdGlvbnNEcml2ZXJ9IGZyb20gJy4vcmVuZGVyL3dlYl9hbmltYXRpb25zL3dlYl9hbmltYXRpb25zX2RyaXZlcic7XG5leHBvcnQge1dlYkFuaW1hdGlvbnNQbGF5ZXIgYXMgybVXZWJBbmltYXRpb25zUGxheWVyfSBmcm9tICcuL3JlbmRlci93ZWJfYW5pbWF0aW9ucy93ZWJfYW5pbWF0aW9uc19wbGF5ZXInO1xuZXhwb3J0IHtcbiAgYWxsb3dQcmV2aW91c1BsYXllclN0eWxlc01lcmdlIGFzIMm1YWxsb3dQcmV2aW91c1BsYXllclN0eWxlc01lcmdlLFxuICBjYW1lbENhc2VUb0Rhc2hDYXNlIGFzIMm1Y2FtZWxDYXNlVG9EYXNoQ2FzZSxcbiAgbm9ybWFsaXplS2V5ZnJhbWVzIGFzIMm1bm9ybWFsaXplS2V5ZnJhbWVzLFxufSBmcm9tICcuL3V0aWwnO1xuIl19