{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest(...args) {\n  const scheduler = popScheduler(args);\n  const resultSelector = popResultSelector(args);\n  const {\n    args: observables,\n    keys\n  } = argsArgArrayOrObject(args);\n  if (observables.length === 0) {\n    return from([], scheduler);\n  }\n  const result = new Observable(combineLatestInit(observables, scheduler, keys ? values => createObject(keys, values) : identity));\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform = identity) {\n  return subscriber => {\n    maybeSchedule(scheduler, () => {\n      const {\n        length\n      } = observables;\n      const values = new Array(length);\n      let active = length;\n      let remainingFirstValues = length;\n      for (let i = 0; i < length; i++) {\n        maybeSchedule(scheduler, () => {\n          const source = from(observables[i], scheduler);\n          let hasFirstValue = false;\n          source.subscribe(createOperatorSubscriber(subscriber, value => {\n            values[i] = value;\n            if (!hasFirstValue) {\n              hasFirstValue = true;\n              remainingFirstValues--;\n            }\n            if (!remainingFirstValues) {\n              subscriber.next(valueTransform(values.slice()));\n            }\n          }, () => {\n            if (! --active) {\n              subscriber.complete();\n            }\n          }));\n        }, subscriber);\n      }\n    }, subscriber);\n  };\n}\nfunction maybeSchedule(scheduler, execute, subscription) {\n  if (scheduler) {\n    executeSchedule(subscription, scheduler, execute);\n  } else {\n    execute();\n  }\n}", "map": {"version": 3, "names": ["Observable", "argsArgArrayOrObject", "from", "identity", "mapOneOrManyArgs", "popResultSelector", "popScheduler", "createObject", "createOperatorSubscriber", "executeSchedule", "combineLatest", "args", "scheduler", "resultSelector", "observables", "keys", "length", "result", "combineLatestInit", "values", "pipe", "valueTransform", "subscriber", "maybeSchedule", "Array", "active", "remainingFirstValues", "i", "source", "hasFirstValue", "subscribe", "value", "next", "slice", "complete", "execute", "subscription"], "sources": ["D:/2025/agmuicon/node_modules/rxjs/dist/esm/internal/observable/combineLatest.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest(...args) {\n    const scheduler = popScheduler(args);\n    const resultSelector = popResultSelector(args);\n    const { args: observables, keys } = argsArgArrayOrObject(args);\n    if (observables.length === 0) {\n        return from([], scheduler);\n    }\n    const result = new Observable(combineLatestInit(observables, scheduler, keys\n        ?\n            (values) => createObject(keys, values)\n        :\n            identity));\n    return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform = identity) {\n    return (subscriber) => {\n        maybeSchedule(scheduler, () => {\n            const { length } = observables;\n            const values = new Array(length);\n            let active = length;\n            let remainingFirstValues = length;\n            for (let i = 0; i < length; i++) {\n                maybeSchedule(scheduler, () => {\n                    const source = from(observables[i], scheduler);\n                    let hasFirstValue = false;\n                    source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                        values[i] = value;\n                        if (!hasFirstValue) {\n                            hasFirstValue = true;\n                            remainingFirstValues--;\n                        }\n                        if (!remainingFirstValues) {\n                            subscriber.next(valueTransform(values.slice()));\n                        }\n                    }, () => {\n                        if (!--active) {\n                            subscriber.complete();\n                        }\n                    }));\n                }, subscriber);\n            }\n        }, subscriber);\n    };\n}\nfunction maybeSchedule(scheduler, execute, subscription) {\n    if (scheduler) {\n        executeSchedule(subscription, scheduler, execute);\n    }\n    else {\n        execute();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,cAAc;AAC9D,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,SAASC,aAAaA,CAAC,GAAGC,IAAI,EAAE;EACnC,MAAMC,SAAS,GAAGN,YAAY,CAACK,IAAI,CAAC;EACpC,MAAME,cAAc,GAAGR,iBAAiB,CAACM,IAAI,CAAC;EAC9C,MAAM;IAAEA,IAAI,EAAEG,WAAW;IAAEC;EAAK,CAAC,GAAGd,oBAAoB,CAACU,IAAI,CAAC;EAC9D,IAAIG,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOd,IAAI,CAAC,EAAE,EAAEU,SAAS,CAAC;EAC9B;EACA,MAAMK,MAAM,GAAG,IAAIjB,UAAU,CAACkB,iBAAiB,CAACJ,WAAW,EAAEF,SAAS,EAAEG,IAAI,GAEnEI,MAAM,IAAKZ,YAAY,CAACQ,IAAI,EAAEI,MAAM,CAAC,GAEtChB,QAAQ,CAAC,CAAC;EAClB,OAAOU,cAAc,GAAGI,MAAM,CAACG,IAAI,CAAChB,gBAAgB,CAACS,cAAc,CAAC,CAAC,GAAGI,MAAM;AAClF;AACA,OAAO,SAASC,iBAAiBA,CAACJ,WAAW,EAAEF,SAAS,EAAES,cAAc,GAAGlB,QAAQ,EAAE;EACjF,OAAQmB,UAAU,IAAK;IACnBC,aAAa,CAACX,SAAS,EAAE,MAAM;MAC3B,MAAM;QAAEI;MAAO,CAAC,GAAGF,WAAW;MAC9B,MAAMK,MAAM,GAAG,IAAIK,KAAK,CAACR,MAAM,CAAC;MAChC,IAAIS,MAAM,GAAGT,MAAM;MACnB,IAAIU,oBAAoB,GAAGV,MAAM;MACjC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,MAAM,EAAEW,CAAC,EAAE,EAAE;QAC7BJ,aAAa,CAACX,SAAS,EAAE,MAAM;UAC3B,MAAMgB,MAAM,GAAG1B,IAAI,CAACY,WAAW,CAACa,CAAC,CAAC,EAAEf,SAAS,CAAC;UAC9C,IAAIiB,aAAa,GAAG,KAAK;UACzBD,MAAM,CAACE,SAAS,CAACtB,wBAAwB,CAACc,UAAU,EAAGS,KAAK,IAAK;YAC7DZ,MAAM,CAACQ,CAAC,CAAC,GAAGI,KAAK;YACjB,IAAI,CAACF,aAAa,EAAE;cAChBA,aAAa,GAAG,IAAI;cACpBH,oBAAoB,EAAE;YAC1B;YACA,IAAI,CAACA,oBAAoB,EAAE;cACvBJ,UAAU,CAACU,IAAI,CAACX,cAAc,CAACF,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;YACnD;UACJ,CAAC,EAAE,MAAM;YACL,IAAI,CAAC,GAAER,MAAM,EAAE;cACXH,UAAU,CAACY,QAAQ,CAAC,CAAC;YACzB;UACJ,CAAC,CAAC,CAAC;QACP,CAAC,EAAEZ,UAAU,CAAC;MAClB;IACJ,CAAC,EAAEA,UAAU,CAAC;EAClB,CAAC;AACL;AACA,SAASC,aAAaA,CAACX,SAAS,EAAEuB,OAAO,EAAEC,YAAY,EAAE;EACrD,IAAIxB,SAAS,EAAE;IACXH,eAAe,CAAC2B,YAAY,EAAExB,SAAS,EAAEuB,OAAO,CAAC;EACrD,CAAC,MACI;IACDA,OAAO,CAAC,CAAC;EACb;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}