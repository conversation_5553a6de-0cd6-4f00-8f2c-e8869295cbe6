"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinkedList = void 0;
class LinkedList {
    constructor(_head) {
        this._head = _head;
    }
    get(l) {
        let c = this._head;
        while (c && l > 0) {
            l--;
            c = c.next;
        }
        return c;
    }
    get head() {
        return this._head;
    }
    get length() {
        let c = this._head;
        let i = 0;
        while (c) {
            i++;
            c = c.next;
        }
        return i;
    }
    reduce(accumulator, seed) {
        let c = this._head;
        let acc = seed;
        let i = 0;
        while (c) {
            acc = accumulator(acc, c, i);
            i++;
            c = c.next;
        }
        return acc;
    }
    find(predicate) {
        let c = this._head;
        let i = 0;
        while (c) {
            if (predicate(c, i)) {
                break;
            }
            i++;
            c = c.next;
        }
        return c;
    }
    forEach(visitor) {
        let c = this._head;
        let i = 0;
        while (c) {
            visitor(c, i);
            i++;
            c = c.next;
        }
    }
}
exports.LinkedList = LinkedList;
//# sourceMappingURL=data:application/json;base64,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