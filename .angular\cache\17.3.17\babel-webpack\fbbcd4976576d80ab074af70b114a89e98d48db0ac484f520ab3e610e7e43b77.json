{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nexport function race(...sources) {\n  sources = argsOrArgArray(sources);\n  return sources.length === 1 ? innerFrom(sources[0]) : new Observable(raceInit(sources));\n}\nexport function raceInit(sources) {\n  return subscriber => {\n    let subscriptions = [];\n    for (let i = 0; subscriptions && !subscriber.closed && i < sources.length; i++) {\n      subscriptions.push(innerFrom(sources[i]).subscribe(createOperatorSubscriber(subscriber, value => {\n        if (subscriptions) {\n          for (let s = 0; s < subscriptions.length; s++) {\n            s !== i && subscriptions[s].unsubscribe();\n          }\n          subscriptions = null;\n        }\n        subscriber.next(value);\n      })));\n    }\n  };\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "argsOrArgArray", "createOperatorSubscriber", "race", "sources", "length", "raceInit", "subscriber", "subscriptions", "i", "closed", "push", "subscribe", "value", "s", "unsubscribe", "next"], "sources": ["D:/2025/agmuicon/node_modules/rxjs/dist/esm/internal/observable/race.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nexport function race(...sources) {\n    sources = argsOrArgArray(sources);\n    return sources.length === 1 ? innerFrom(sources[0]) : new Observable(raceInit(sources));\n}\nexport function raceInit(sources) {\n    return (subscriber) => {\n        let subscriptions = [];\n        for (let i = 0; subscriptions && !subscriber.closed && i < sources.length; i++) {\n            subscriptions.push(innerFrom(sources[i]).subscribe(createOperatorSubscriber(subscriber, (value) => {\n                if (subscriptions) {\n                    for (let s = 0; s < subscriptions.length; s++) {\n                        s !== i && subscriptions[s].unsubscribe();\n                    }\n                    subscriptions = null;\n                }\n                subscriber.next(value);\n            })));\n        }\n    };\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,OAAO,SAASC,IAAIA,CAAC,GAAGC,OAAO,EAAE;EAC7BA,OAAO,GAAGH,cAAc,CAACG,OAAO,CAAC;EACjC,OAAOA,OAAO,CAACC,MAAM,KAAK,CAAC,GAAGL,SAAS,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAIL,UAAU,CAACO,QAAQ,CAACF,OAAO,CAAC,CAAC;AAC3F;AACA,OAAO,SAASE,QAAQA,CAACF,OAAO,EAAE;EAC9B,OAAQG,UAAU,IAAK;IACnB,IAAIC,aAAa,GAAG,EAAE;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAED,aAAa,IAAI,CAACD,UAAU,CAACG,MAAM,IAAID,CAAC,GAAGL,OAAO,CAACC,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC5ED,aAAa,CAACG,IAAI,CAACX,SAAS,CAACI,OAAO,CAACK,CAAC,CAAC,CAAC,CAACG,SAAS,CAACV,wBAAwB,CAACK,UAAU,EAAGM,KAAK,IAAK;QAC/F,IAAIL,aAAa,EAAE;UACf,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,aAAa,CAACH,MAAM,EAAES,CAAC,EAAE,EAAE;YAC3CA,CAAC,KAAKL,CAAC,IAAID,aAAa,CAACM,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC7C;UACAP,aAAa,GAAG,IAAI;QACxB;QACAD,UAAU,CAACS,IAAI,CAACH,KAAK,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC;IACR;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}