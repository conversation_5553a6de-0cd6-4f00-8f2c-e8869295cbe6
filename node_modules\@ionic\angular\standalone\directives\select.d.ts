import { ChangeDetector<PERSON><PERSON>, <PERSON>ement<PERSON>ef, EventEmitter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { SelectChangeEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonSelect extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    handleIonChange(el: HTMLIonSelectElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonSelect, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonSelect, "ion-select", never, { "cancelText": "cancelText"; "color": "color"; "compareWith": "compareWith"; "disabled": "disabled"; "expandedIcon": "expandedIcon"; "fill": "fill"; "interface": "interface"; "interfaceOptions": "interfaceOptions"; "justify": "justify"; "label": "label"; "labelPlacement": "labelPlacement"; "legacy": "legacy"; "mode": "mode"; "multiple": "multiple"; "name": "name"; "okText": "okText"; "placeholder": "placeholder"; "selectedText": "selectedText"; "shape": "shape"; "toggleIcon": "toggleIcon"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonSelect extends Components.IonSelect {
    /**
     * Emitted when the value has changed.
     */
    ionChange: EventEmitter<CustomEvent<SelectChangeEventDetail>>;
    /**
     * Emitted when the selection is cancelled.
     */
    ionCancel: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the overlay is dismissed.
     */
    ionDismiss: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the select has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the select loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
}
