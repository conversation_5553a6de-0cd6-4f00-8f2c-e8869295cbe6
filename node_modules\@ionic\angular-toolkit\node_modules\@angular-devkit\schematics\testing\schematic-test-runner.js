"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchematicTestRunner = exports.UnitTestTree = void 0;
const core_1 = require("@angular-devkit/core");
const rxjs_1 = require("rxjs");
const src_1 = require("../src");
const call_1 = require("../src/rules/call");
const node_1 = require("../tasks/node");
const tools_1 = require("../tools");
class UnitTestTree extends src_1.DelegateTree {
    get files() {
        const result = [];
        this.visit((path) => result.push(path));
        return result;
    }
    readContent(path) {
        const buffer = this.read(path);
        if (buffer === null) {
            return '';
        }
        return buffer.toString();
    }
}
exports.UnitTestTree = UnitTestTree;
class SchematicTestRunner {
    constructor(_collectionName, collectionPath) {
        this._collectionName = _collectionName;
        this._engineHost = new tools_1.NodeModulesTestEngineHost();
        this._engine = new src_1.SchematicEngine(this._engineHost);
        this._engineHost.registerCollection(_collectionName, collectionPath);
        this._logger = new core_1.logging.Logger('test');
        const registry = new core_1.schema.CoreSchemaRegistry(src_1.formats.standardFormats);
        registry.addPostTransform(core_1.schema.transforms.addUndefinedDefaults);
        this._engineHost.registerOptionsTransform((0, tools_1.validateOptionsWithSchema)(registry));
        this._engineHost.registerTaskExecutor(node_1.BuiltinTaskExecutor.NodePackage);
        this._engineHost.registerTaskExecutor(node_1.BuiltinTaskExecutor.RepositoryInitializer);
        this._engineHost.registerTaskExecutor(node_1.BuiltinTaskExecutor.RunSchematic);
        this._collection = this._engine.createCollection(this._collectionName);
    }
    get engine() {
        return this._engine;
    }
    get logger() {
        return this._logger;
    }
    get tasks() {
        return [...this._engineHost.tasks];
    }
    registerCollection(collectionName, collectionPath) {
        this._engineHost.registerCollection(collectionName, collectionPath);
    }
    async runSchematic(schematicName, opts, tree) {
        const schematic = this._collection.createSchematic(schematicName, true);
        const host = (0, rxjs_1.of)(tree || new src_1.HostTree());
        this._engineHost.clearTasks();
        const newTree = await schematic.call(opts || {}, host, { logger: this._logger }).toPromise();
        return new UnitTestTree(newTree);
    }
    /**
     * @deprecated since version 15.1. Use `runSchematic` instead.
     */
    runSchematicAsync(schematicName, opts, tree) {
        return (0, rxjs_1.from)(this.runSchematic(schematicName, opts, tree));
    }
    async runExternalSchematic(collectionName, schematicName, opts, tree) {
        const externalCollection = this._engine.createCollection(collectionName);
        const schematic = externalCollection.createSchematic(schematicName, true);
        const host = (0, rxjs_1.of)(tree || new src_1.HostTree());
        this._engineHost.clearTasks();
        const newTree = await schematic.call(opts || {}, host, { logger: this._logger }).toPromise();
        return new UnitTestTree(newTree);
    }
    /**
     * @deprecated since version 15.1. Use `runExternalSchematic` instead.
     */
    runExternalSchematicAsync(collectionName, schematicName, opts, tree) {
        return (0, rxjs_1.from)(this.runExternalSchematic(collectionName, schematicName, opts, tree));
    }
    callRule(rule, tree, parentContext) {
        const context = this._engine.createContext({}, parentContext);
        return (0, call_1.callRule)(rule, (0, rxjs_1.of)(tree), context);
    }
}
exports.SchematicTestRunner = SchematicTestRunner;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2NoZW1hdGljLXRlc3QtcnVubmVyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvYW5ndWxhcl9kZXZraXQvc2NoZW1hdGljcy90ZXN0aW5nL3NjaGVtYXRpYy10ZXN0LXJ1bm5lci50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7OztHQU1HOzs7QUFFSCwrQ0FBdUQ7QUFDdkQsK0JBQTREO0FBQzVELGdDQVdnQjtBQUNoQiw0Q0FBNkM7QUFDN0Msd0NBQW9EO0FBQ3BELG9DQUFnRjtBQUVoRixNQUFhLFlBQWEsU0FBUSxrQkFBWTtJQUM1QyxJQUFJLEtBQUs7UUFDUCxNQUFNLE1BQU0sR0FBYSxFQUFFLENBQUM7UUFDNUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO1FBRXhDLE9BQU8sTUFBTSxDQUFDO0lBQ2hCLENBQUM7SUFFRCxXQUFXLENBQUMsSUFBWTtRQUN0QixNQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQy9CLElBQUksTUFBTSxLQUFLLElBQUksRUFBRTtZQUNuQixPQUFPLEVBQUUsQ0FBQztTQUNYO1FBRUQsT0FBTyxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUM7SUFDM0IsQ0FBQztDQUNGO0FBaEJELG9DQWdCQztBQUVELE1BQWEsbUJBQW1CO0lBTTlCLFlBQW9CLGVBQXVCLEVBQUUsY0FBc0I7UUFBL0Msb0JBQWUsR0FBZixlQUFlLENBQVE7UUFMbkMsZ0JBQVcsR0FBRyxJQUFJLGlDQUF5QixFQUFFLENBQUM7UUFDOUMsWUFBTyxHQUE0QixJQUFJLHFCQUFlLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBSy9FLElBQUksQ0FBQyxXQUFXLENBQUMsa0JBQWtCLENBQUMsZUFBZSxFQUFFLGNBQWMsQ0FBQyxDQUFDO1FBQ3JFLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxjQUFPLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBRTFDLE1BQU0sUUFBUSxHQUFHLElBQUksYUFBTSxDQUFDLGtCQUFrQixDQUFDLGFBQU8sQ0FBQyxlQUFlLENBQUMsQ0FBQztRQUN4RSxRQUFRLENBQUMsZ0JBQWdCLENBQUMsYUFBTSxDQUFDLFVBQVUsQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1FBRWxFLElBQUksQ0FBQyxXQUFXLENBQUMsd0JBQXdCLENBQUMsSUFBQSxpQ0FBeUIsRUFBQyxRQUFRLENBQUMsQ0FBQyxDQUFDO1FBQy9FLElBQUksQ0FBQyxXQUFXLENBQUMsb0JBQW9CLENBQUMsMEJBQW1CLENBQUMsV0FBVyxDQUFDLENBQUM7UUFDdkUsSUFBSSxDQUFDLFdBQVcsQ0FBQyxvQkFBb0IsQ0FBQywwQkFBbUIsQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1FBQ2pGLElBQUksQ0FBQyxXQUFXLENBQUMsb0JBQW9CLENBQUMsMEJBQW1CLENBQUMsWUFBWSxDQUFDLENBQUM7UUFFeEUsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLGdCQUFnQixDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztJQUN6RSxDQUFDO0lBRUQsSUFBSSxNQUFNO1FBQ1IsT0FBTyxJQUFJLENBQUMsT0FBTyxDQUFDO0lBQ3RCLENBQUM7SUFDRCxJQUFJLE1BQU07UUFDUixPQUFPLElBQUksQ0FBQyxPQUFPLENBQUM7SUFDdEIsQ0FBQztJQUNELElBQUksS0FBSztRQUNQLE9BQU8sQ0FBQyxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDckMsQ0FBQztJQUVELGtCQUFrQixDQUFDLGNBQXNCLEVBQUUsY0FBc0I7UUFDL0QsSUFBSSxDQUFDLFdBQVcsQ0FBQyxrQkFBa0IsQ0FBQyxjQUFjLEVBQUUsY0FBYyxDQUFDLENBQUM7SUFDdEUsQ0FBQztJQUVELEtBQUssQ0FBQyxZQUFZLENBQ2hCLGFBQXFCLEVBQ3JCLElBQXVCLEVBQ3ZCLElBQVc7UUFFWCxNQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLGVBQWUsQ0FBQyxhQUFhLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDeEUsTUFBTSxJQUFJLEdBQUcsSUFBQSxTQUFZLEVBQUMsSUFBSSxJQUFJLElBQUksY0FBUSxFQUFFLENBQUMsQ0FBQztRQUNsRCxJQUFJLENBQUMsV0FBVyxDQUFDLFVBQVUsRUFBRSxDQUFDO1FBRTlCLE1BQU0sT0FBTyxHQUFHLE1BQU0sU0FBUyxDQUFDLElBQUksQ0FBQyxJQUFJLElBQUksRUFBRSxFQUFFLElBQUksRUFBRSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxTQUFTLEVBQUUsQ0FBQztRQUU3RixPQUFPLElBQUksWUFBWSxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQ25DLENBQUM7SUFFRDs7T0FFRztJQUNILGlCQUFpQixDQUNmLGFBQXFCLEVBQ3JCLElBQXVCLEVBQ3ZCLElBQVc7UUFFWCxPQUFPLElBQUEsV0FBSSxFQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsYUFBYSxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDO0lBQzVELENBQUM7SUFFRCxLQUFLLENBQUMsb0JBQW9CLENBQ3hCLGNBQXNCLEVBQ3RCLGFBQXFCLEVBQ3JCLElBQXVCLEVBQ3ZCLElBQVc7UUFFWCxNQUFNLGtCQUFrQixHQUFHLElBQUksQ0FBQyxPQUFPLENBQUMsZ0JBQWdCLENBQUMsY0FBYyxDQUFDLENBQUM7UUFDekUsTUFBTSxTQUFTLEdBQUcsa0JBQWtCLENBQUMsZUFBZSxDQUFDLGFBQWEsRUFBRSxJQUFJLENBQUMsQ0FBQztRQUMxRSxNQUFNLElBQUksR0FBRyxJQUFBLFNBQVksRUFBQyxJQUFJLElBQUksSUFBSSxjQUFRLEVBQUUsQ0FBQyxDQUFDO1FBQ2xELElBQUksQ0FBQyxXQUFXLENBQUMsVUFBVSxFQUFFLENBQUM7UUFFOUIsTUFBTSxPQUFPLEdBQUcsTUFBTSxTQUFTLENBQUMsSUFBSSxDQUFDLElBQUksSUFBSSxFQUFFLEVBQUUsSUFBSSxFQUFFLEVBQUUsTUFBTSxFQUFFLElBQUksQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDLFNBQVMsRUFBRSxDQUFDO1FBRTdGLE9BQU8sSUFBSSxZQUFZLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDbkMsQ0FBQztJQUVEOztPQUVHO0lBQ0gseUJBQXlCLENBQ3ZCLGNBQXNCLEVBQ3RCLGFBQXFCLEVBQ3JCLElBQXVCLEVBQ3ZCLElBQVc7UUFFWCxPQUFPLElBQUEsV0FBSSxFQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxjQUFjLEVBQUUsYUFBYSxFQUFFLElBQUksRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDO0lBQ3BGLENBQUM7SUFFRCxRQUFRLENBQUMsSUFBVSxFQUFFLElBQVUsRUFBRSxhQUF5QztRQUN4RSxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxFQUF1QixFQUFFLGFBQWEsQ0FBQyxDQUFDO1FBRW5GLE9BQU8sSUFBQSxlQUFRLEVBQUMsSUFBSSxFQUFFLElBQUEsU0FBWSxFQUFDLElBQUksQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDO0lBQ3JELENBQUM7Q0FDRjtBQTdGRCxrREE2RkMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHsgbG9nZ2luZywgc2NoZW1hIH0gZnJvbSAnQGFuZ3VsYXItZGV2a2l0L2NvcmUnO1xuaW1wb3J0IHsgT2JzZXJ2YWJsZSwgZnJvbSwgb2YgYXMgb2JzZXJ2YWJsZU9mIH0gZnJvbSAncnhqcyc7XG5pbXBvcnQge1xuICBDb2xsZWN0aW9uLFxuICBEZWxlZ2F0ZVRyZWUsXG4gIEhvc3RUcmVlLFxuICBSdWxlLFxuICBTY2hlbWF0aWMsXG4gIFNjaGVtYXRpY0NvbnRleHQsXG4gIFNjaGVtYXRpY0VuZ2luZSxcbiAgVGFza0NvbmZpZ3VyYXRpb24sXG4gIFRyZWUsXG4gIGZvcm1hdHMsXG59IGZyb20gJy4uL3NyYyc7XG5pbXBvcnQgeyBjYWxsUnVsZSB9IGZyb20gJy4uL3NyYy9ydWxlcy9jYWxsJztcbmltcG9ydCB7IEJ1aWx0aW5UYXNrRXhlY3V0b3IgfSBmcm9tICcuLi90YXNrcy9ub2RlJztcbmltcG9ydCB7IE5vZGVNb2R1bGVzVGVzdEVuZ2luZUhvc3QsIHZhbGlkYXRlT3B0aW9uc1dpdGhTY2hlbWEgfSBmcm9tICcuLi90b29scyc7XG5cbmV4cG9ydCBjbGFzcyBVbml0VGVzdFRyZWUgZXh0ZW5kcyBEZWxlZ2F0ZVRyZWUge1xuICBnZXQgZmlsZXMoKSB7XG4gICAgY29uc3QgcmVzdWx0OiBzdHJpbmdbXSA9IFtdO1xuICAgIHRoaXMudmlzaXQoKHBhdGgpID0+IHJlc3VsdC5wdXNoKHBhdGgpKTtcblxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cblxuICByZWFkQ29udGVudChwYXRoOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IGJ1ZmZlciA9IHRoaXMucmVhZChwYXRoKTtcbiAgICBpZiAoYnVmZmVyID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gJyc7XG4gICAgfVxuXG4gICAgcmV0dXJuIGJ1ZmZlci50b1N0cmluZygpO1xuICB9XG59XG5cbmV4cG9ydCBjbGFzcyBTY2hlbWF0aWNUZXN0UnVubmVyIHtcbiAgcHJpdmF0ZSBfZW5naW5lSG9zdCA9IG5ldyBOb2RlTW9kdWxlc1Rlc3RFbmdpbmVIb3N0KCk7XG4gIHByaXZhdGUgX2VuZ2luZTogU2NoZW1hdGljRW5naW5lPHt9LCB7fT4gPSBuZXcgU2NoZW1hdGljRW5naW5lKHRoaXMuX2VuZ2luZUhvc3QpO1xuICBwcml2YXRlIF9jb2xsZWN0aW9uOiBDb2xsZWN0aW9uPHt9LCB7fT47XG4gIHByaXZhdGUgX2xvZ2dlcjogbG9nZ2luZy5Mb2dnZXI7XG5cbiAgY29uc3RydWN0b3IocHJpdmF0ZSBfY29sbGVjdGlvbk5hbWU6IHN0cmluZywgY29sbGVjdGlvblBhdGg6IHN0cmluZykge1xuICAgIHRoaXMuX2VuZ2luZUhvc3QucmVnaXN0ZXJDb2xsZWN0aW9uKF9jb2xsZWN0aW9uTmFtZSwgY29sbGVjdGlvblBhdGgpO1xuICAgIHRoaXMuX2xvZ2dlciA9IG5ldyBsb2dnaW5nLkxvZ2dlcigndGVzdCcpO1xuXG4gICAgY29uc3QgcmVnaXN0cnkgPSBuZXcgc2NoZW1hLkNvcmVTY2hlbWFSZWdpc3RyeShmb3JtYXRzLnN0YW5kYXJkRm9ybWF0cyk7XG4gICAgcmVnaXN0cnkuYWRkUG9zdFRyYW5zZm9ybShzY2hlbWEudHJhbnNmb3Jtcy5hZGRVbmRlZmluZWREZWZhdWx0cyk7XG5cbiAgICB0aGlzLl9lbmdpbmVIb3N0LnJlZ2lzdGVyT3B0aW9uc1RyYW5zZm9ybSh2YWxpZGF0ZU9wdGlvbnNXaXRoU2NoZW1hKHJlZ2lzdHJ5KSk7XG4gICAgdGhpcy5fZW5naW5lSG9zdC5yZWdpc3RlclRhc2tFeGVjdXRvcihCdWlsdGluVGFza0V4ZWN1dG9yLk5vZGVQYWNrYWdlKTtcbiAgICB0aGlzLl9lbmdpbmVIb3N0LnJlZ2lzdGVyVGFza0V4ZWN1dG9yKEJ1aWx0aW5UYXNrRXhlY3V0b3IuUmVwb3NpdG9yeUluaXRpYWxpemVyKTtcbiAgICB0aGlzLl9lbmdpbmVIb3N0LnJlZ2lzdGVyVGFza0V4ZWN1dG9yKEJ1aWx0aW5UYXNrRXhlY3V0b3IuUnVuU2NoZW1hdGljKTtcblxuICAgIHRoaXMuX2NvbGxlY3Rpb24gPSB0aGlzLl9lbmdpbmUuY3JlYXRlQ29sbGVjdGlvbih0aGlzLl9jb2xsZWN0aW9uTmFtZSk7XG4gIH1cblxuICBnZXQgZW5naW5lKCkge1xuICAgIHJldHVybiB0aGlzLl9lbmdpbmU7XG4gIH1cbiAgZ2V0IGxvZ2dlcigpOiBsb2dnaW5nLkxvZ2dlciB7XG4gICAgcmV0dXJuIHRoaXMuX2xvZ2dlcjtcbiAgfVxuICBnZXQgdGFza3MoKTogVGFza0NvbmZpZ3VyYXRpb25bXSB7XG4gICAgcmV0dXJuIFsuLi50aGlzLl9lbmdpbmVIb3N0LnRhc2tzXTtcbiAgfVxuXG4gIHJlZ2lzdGVyQ29sbGVjdGlvbihjb2xsZWN0aW9uTmFtZTogc3RyaW5nLCBjb2xsZWN0aW9uUGF0aDogc3RyaW5nKSB7XG4gICAgdGhpcy5fZW5naW5lSG9zdC5yZWdpc3RlckNvbGxlY3Rpb24oY29sbGVjdGlvbk5hbWUsIGNvbGxlY3Rpb25QYXRoKTtcbiAgfVxuXG4gIGFzeW5jIHJ1blNjaGVtYXRpYzxTY2hlbWF0aWNTY2hlbWFUIGV4dGVuZHMgb2JqZWN0PihcbiAgICBzY2hlbWF0aWNOYW1lOiBzdHJpbmcsXG4gICAgb3B0cz86IFNjaGVtYXRpY1NjaGVtYVQsXG4gICAgdHJlZT86IFRyZWUsXG4gICk6IFByb21pc2U8VW5pdFRlc3RUcmVlPiB7XG4gICAgY29uc3Qgc2NoZW1hdGljID0gdGhpcy5fY29sbGVjdGlvbi5jcmVhdGVTY2hlbWF0aWMoc2NoZW1hdGljTmFtZSwgdHJ1ZSk7XG4gICAgY29uc3QgaG9zdCA9IG9ic2VydmFibGVPZih0cmVlIHx8IG5ldyBIb3N0VHJlZSgpKTtcbiAgICB0aGlzLl9lbmdpbmVIb3N0LmNsZWFyVGFza3MoKTtcblxuICAgIGNvbnN0IG5ld1RyZWUgPSBhd2FpdCBzY2hlbWF0aWMuY2FsbChvcHRzIHx8IHt9LCBob3N0LCB7IGxvZ2dlcjogdGhpcy5fbG9nZ2VyIH0pLnRvUHJvbWlzZSgpO1xuXG4gICAgcmV0dXJuIG5ldyBVbml0VGVzdFRyZWUobmV3VHJlZSk7XG4gIH1cblxuICAvKipcbiAgICogQGRlcHJlY2F0ZWQgc2luY2UgdmVyc2lvbiAxNS4xLiBVc2UgYHJ1blNjaGVtYXRpY2AgaW5zdGVhZC5cbiAgICovXG4gIHJ1blNjaGVtYXRpY0FzeW5jPFNjaGVtYXRpY1NjaGVtYVQgZXh0ZW5kcyBvYmplY3Q+KFxuICAgIHNjaGVtYXRpY05hbWU6IHN0cmluZyxcbiAgICBvcHRzPzogU2NoZW1hdGljU2NoZW1hVCxcbiAgICB0cmVlPzogVHJlZSxcbiAgKTogT2JzZXJ2YWJsZTxVbml0VGVzdFRyZWU+IHtcbiAgICByZXR1cm4gZnJvbSh0aGlzLnJ1blNjaGVtYXRpYyhzY2hlbWF0aWNOYW1lLCBvcHRzLCB0cmVlKSk7XG4gIH1cblxuICBhc3luYyBydW5FeHRlcm5hbFNjaGVtYXRpYzxTY2hlbWF0aWNTY2hlbWFUIGV4dGVuZHMgb2JqZWN0PihcbiAgICBjb2xsZWN0aW9uTmFtZTogc3RyaW5nLFxuICAgIHNjaGVtYXRpY05hbWU6IHN0cmluZyxcbiAgICBvcHRzPzogU2NoZW1hdGljU2NoZW1hVCxcbiAgICB0cmVlPzogVHJlZSxcbiAgKTogUHJvbWlzZTxVbml0VGVzdFRyZWU+IHtcbiAgICBjb25zdCBleHRlcm5hbENvbGxlY3Rpb24gPSB0aGlzLl9lbmdpbmUuY3JlYXRlQ29sbGVjdGlvbihjb2xsZWN0aW9uTmFtZSk7XG4gICAgY29uc3Qgc2NoZW1hdGljID0gZXh0ZXJuYWxDb2xsZWN0aW9uLmNyZWF0ZVNjaGVtYXRpYyhzY2hlbWF0aWNOYW1lLCB0cnVlKTtcbiAgICBjb25zdCBob3N0ID0gb2JzZXJ2YWJsZU9mKHRyZWUgfHwgbmV3IEhvc3RUcmVlKCkpO1xuICAgIHRoaXMuX2VuZ2luZUhvc3QuY2xlYXJUYXNrcygpO1xuXG4gICAgY29uc3QgbmV3VHJlZSA9IGF3YWl0IHNjaGVtYXRpYy5jYWxsKG9wdHMgfHwge30sIGhvc3QsIHsgbG9nZ2VyOiB0aGlzLl9sb2dnZXIgfSkudG9Qcm9taXNlKCk7XG5cbiAgICByZXR1cm4gbmV3IFVuaXRUZXN0VHJlZShuZXdUcmVlKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBAZGVwcmVjYXRlZCBzaW5jZSB2ZXJzaW9uIDE1LjEuIFVzZSBgcnVuRXh0ZXJuYWxTY2hlbWF0aWNgIGluc3RlYWQuXG4gICAqL1xuICBydW5FeHRlcm5hbFNjaGVtYXRpY0FzeW5jPFNjaGVtYXRpY1NjaGVtYVQgZXh0ZW5kcyBvYmplY3Q+KFxuICAgIGNvbGxlY3Rpb25OYW1lOiBzdHJpbmcsXG4gICAgc2NoZW1hdGljTmFtZTogc3RyaW5nLFxuICAgIG9wdHM/OiBTY2hlbWF0aWNTY2hlbWFULFxuICAgIHRyZWU/OiBUcmVlLFxuICApOiBPYnNlcnZhYmxlPFVuaXRUZXN0VHJlZT4ge1xuICAgIHJldHVybiBmcm9tKHRoaXMucnVuRXh0ZXJuYWxTY2hlbWF0aWMoY29sbGVjdGlvbk5hbWUsIHNjaGVtYXRpY05hbWUsIG9wdHMsIHRyZWUpKTtcbiAgfVxuXG4gIGNhbGxSdWxlKHJ1bGU6IFJ1bGUsIHRyZWU6IFRyZWUsIHBhcmVudENvbnRleHQ/OiBQYXJ0aWFsPFNjaGVtYXRpY0NvbnRleHQ+KTogT2JzZXJ2YWJsZTxUcmVlPiB7XG4gICAgY29uc3QgY29udGV4dCA9IHRoaXMuX2VuZ2luZS5jcmVhdGVDb250ZXh0KHt9IGFzIFNjaGVtYXRpYzx7fSwge30+LCBwYXJlbnRDb250ZXh0KTtcblxuICAgIHJldHVybiBjYWxsUnVsZShydWxlLCBvYnNlcnZhYmxlT2YodHJlZSksIGNvbnRleHQpO1xuICB9XG59XG4iXX0=