"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1049],{1049:(v,s,n)=>{n.r(s),n.d(s,{ion_avatar:()=>o,ion_badge:()=>e,ion_thumbnail:()=>u});var a=n(4363),d=n(611),l=n(333);const o=class{constructor(i){(0,a.r)(this,i)}render(){return(0,a.h)(a.H,{key:"f6014b524497bb18ae919ba6f6928407310d6870",class:(0,d.b)(this)},(0,a.h)("slot",{key:"192ff4a8e10c0b0a4a2ed795ff2675afa8b23449"}))}};o.style={ios:":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}",md:":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}"};const e=class{constructor(i){(0,a.r)(this,i),this.color=void 0}render(){const i=(0,d.b)(this);return(0,a.h)(a.H,{key:"22d41ceefb76f40dfbf739fd71483f1272a45858",class:(0,l.c)(this.color,{[i]:!0})},(0,a.h)("slot",{key:"e7e65463bac5903971a8f9f6be55515f42b81a83"}))}};e.style={ios:":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}",md:":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}"};const u=class{constructor(i){(0,a.r)(this,i)}render(){return(0,a.h)(a.H,{key:"d2667635930e4c0896805f452357e7dc9086bc72",class:(0,d.b)(this)},(0,a.h)("slot",{key:"66eb1487f3da4da2ef71b812a8d0f0fe884c7d81"}))}};u.style=":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}"},333:(v,s,n)=>{n.d(s,{c:()=>l,g:()=>p,h:()=>d,o:()=>g});var a=n(467);const d=(o,t)=>null!==t.closest(o),l=(o,t)=>"string"==typeof o&&o.length>0?Object.assign({"ion-color":!0,[`ion-color-${o}`]:!0},t):t,p=o=>{const t={};return(o=>void 0!==o?(Array.isArray(o)?o:o.split(" ")).filter(r=>null!=r).map(r=>r.trim()).filter(r=>""!==r):[])(o).forEach(r=>t[r]=!0),t},f=/^[a-z][a-z0-9+\-.]*:/,g=function(){var o=(0,a.A)(function*(t,r,b,c){if(null!=t&&"#"!==t[0]&&!f.test(t)){const e=document.querySelector("ion-router");if(e)return r?.preventDefault(),e.push(t,b,c)}return!1});return function(r,b,c,e){return o.apply(this,arguments)}}()}}]);