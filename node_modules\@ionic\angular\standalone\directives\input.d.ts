import { ChangeDetectorRef, ElementRef, EventEmitter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { InputInputEventDetail as IIonInputInputInputEventDetail, InputChangeEventDetail as IIonInputInputChangeEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonInput extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    handleIonInput(el: HTMLIonInputElement): void;
    registerOnChange(fn: (_: any) => void): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonInput, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonInput, "ion-input", never, { "accept": "accept"; "autocapitalize": "autocapitalize"; "autocomplete": "autocomplete"; "autocorrect": "autocorrect"; "autofocus": "autofocus"; "clearInput": "clearInput"; "clearOnEdit": "clearOnEdit"; "color": "color"; "counter": "counter"; "counterFormatter": "counterFormatter"; "debounce": "debounce"; "disabled": "disabled"; "enterkeyhint": "enterkeyhint"; "errorText": "errorText"; "fill": "fill"; "helperText": "helperText"; "inputmode": "inputmode"; "label": "label"; "labelPlacement": "labelPlacement"; "legacy": "legacy"; "max": "max"; "maxlength": "maxlength"; "min": "min"; "minlength": "minlength"; "mode": "mode"; "multiple": "multiple"; "name": "name"; "pattern": "pattern"; "placeholder": "placeholder"; "readonly": "readonly"; "required": "required"; "shape": "shape"; "size": "size"; "spellcheck": "spellcheck"; "step": "step"; "type": "type"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonInput extends Components.IonInput {
    /**
     * The `ionInput` event is fired each time the user modifies the input's value.
  Unlike the `ionChange` event, the `ionInput` event is fired for each alteration
  to the input's value. This typically happens for each keystroke as the user types.
  
  For elements that accept text input (`type=text`, `type=tel`, etc.), the interface
  is [`InputEvent`](https://developer.mozilla.org/en-US/docs/Web/API/InputEvent); for others,
  the interface is [`Event`](https://developer.mozilla.org/en-US/docs/Web/API/Event). If
  the input is cleared on edit, the type is `null`.
     */
    ionInput: EventEmitter<CustomEvent<IIonInputInputInputEventDetail>>;
    /**
     * The `ionChange` event is fired when the user modifies the input's value.
  Unlike the `ionInput` event, the `ionChange` event is only fired when changes
  are committed, not as the user types.
  
  Depending on the way the users interacts with the element, the `ionChange`
  event fires at a different moment:
  - When the user commits the change explicitly (e.g. by selecting a date
  from a date picker for `<ion-input type="date">`, pressing the "Enter" key, etc.).
  - When the element loses focus after its value has changed: for elements
  where the user's interaction is typing.
     */
    ionChange: EventEmitter<CustomEvent<IIonInputInputChangeEventDetail>>;
    /**
     * Emitted when the input loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<FocusEvent>>;
    /**
     * Emitted when the input has focus.
     */
    ionFocus: EventEmitter<CustomEvent<FocusEvent>>;
}
