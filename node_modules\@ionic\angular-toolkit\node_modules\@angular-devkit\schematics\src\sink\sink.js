"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleSinkBase = void 0;
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const exception_1 = require("../exception/exception");
const action_1 = require("../tree/action");
const Noop = function () { };
class SimpleSinkBase {
    constructor() {
        this.preCommitAction = Noop;
        this.postCommitAction = Noop;
        this.preCommit = Noop;
        this.postCommit = Noop;
    }
    _fileAlreadyExistException(path) {
        throw new exception_1.FileAlreadyExistException(path);
    }
    _fileDoesNotExistException(path) {
        throw new exception_1.FileDoesNotExistException(path);
    }
    _validateOverwriteAction(action) {
        return this._validateFileExists(action.path).pipe((0, operators_1.map)((b) => {
            if (!b) {
                this._fileDoesNotExistException(action.path);
            }
        }));
    }
    _validateCreateAction(action) {
        return this._validateFileExists(action.path).pipe((0, operators_1.map)((b) => {
            if (b) {
                this._fileAlreadyExistException(action.path);
            }
        }));
    }
    _validateRenameAction(action) {
        return this._validateFileExists(action.path).pipe((0, operators_1.map)((b) => {
            if (!b) {
                this._fileDoesNotExistException(action.path);
            }
        }), (0, operators_1.mergeMap)(() => this._validateFileExists(action.to)), (0, operators_1.map)((b) => {
            if (b) {
                this._fileAlreadyExistException(action.to);
            }
        }));
    }
    _validateDeleteAction(action) {
        return this._validateFileExists(action.path).pipe((0, operators_1.map)((b) => {
            if (!b) {
                this._fileDoesNotExistException(action.path);
            }
        }));
    }
    validateSingleAction(action) {
        switch (action.kind) {
            case 'o':
                return this._validateOverwriteAction(action);
            case 'c':
                return this._validateCreateAction(action);
            case 'r':
                return this._validateRenameAction(action);
            case 'd':
                return this._validateDeleteAction(action);
            default:
                throw new action_1.UnknownActionException(action);
        }
    }
    commitSingleAction(action) {
        return (0, rxjs_1.concat)(this.validateSingleAction(action), new rxjs_1.Observable((observer) => {
            let committed = null;
            switch (action.kind) {
                case 'o':
                    committed = this._overwriteFile(action.path, action.content);
                    break;
                case 'c':
                    committed = this._createFile(action.path, action.content);
                    break;
                case 'r':
                    committed = this._renameFile(action.path, action.to);
                    break;
                case 'd':
                    committed = this._deleteFile(action.path);
                    break;
            }
            if (committed) {
                committed.subscribe(observer);
            }
            else {
                observer.complete();
            }
        })).pipe((0, operators_1.ignoreElements)());
    }
    commit(tree) {
        const actions = (0, rxjs_1.from)(tree.actions);
        return (0, rxjs_1.concat)(this.preCommit() || (0, rxjs_1.of)(null), (0, rxjs_1.defer)(() => actions).pipe((0, operators_1.concatMap)((action) => {
            const maybeAction = this.preCommitAction(action);
            if ((0, rxjs_1.isObservable)(maybeAction) || isPromiseLike(maybeAction)) {
                return maybeAction;
            }
            return (0, rxjs_1.of)(maybeAction || action);
        }), (0, operators_1.concatMap)((action) => {
            return (0, rxjs_1.concat)(this.commitSingleAction(action).pipe((0, operators_1.ignoreElements)()), (0, rxjs_1.of)(action));
        }), (0, operators_1.concatMap)((action) => this.postCommitAction(action) || (0, rxjs_1.of)(null))), (0, rxjs_1.defer)(() => this._done()), (0, rxjs_1.defer)(() => this.postCommit() || (0, rxjs_1.of)(null))).pipe((0, operators_1.ignoreElements)());
    }
}
exports.SimpleSinkBase = SimpleSinkBase;
function isPromiseLike(value) {
    return !!value && typeof value.then === 'function';
}
//# sourceMappingURL=data:application/json;base64,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