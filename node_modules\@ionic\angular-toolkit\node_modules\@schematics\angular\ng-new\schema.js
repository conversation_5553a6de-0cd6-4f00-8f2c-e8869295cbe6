"use strict";
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN devkit-admin build (or bazel build ...).
Object.defineProperty(exports, "__esModule", { value: true });
exports.ViewEncapsulation = exports.Style = exports.PackageManager = void 0;
/**
 * The package manager used to install dependencies.
 */
var PackageManager;
(function (PackageManager) {
    PackageManager["Cnpm"] = "cnpm";
    PackageManager["Npm"] = "npm";
    PackageManager["Pnpm"] = "pnpm";
    PackageManager["Yarn"] = "yarn";
})(PackageManager = exports.PackageManager || (exports.PackageManager = {}));
/**
 * The file extension or preprocessor to use for style files.
 */
var Style;
(function (Style) {
    Style["Css"] = "css";
    Style["Less"] = "less";
    Style["Sass"] = "sass";
    Style["Scss"] = "scss";
})(Style = exports.Style || (exports.Style = {}));
/**
 * The view encapsulation strategy to use in the initial project.
 */
var ViewEncapsulation;
(function (ViewEncapsulation) {
    ViewEncapsulation["Emulated"] = "Emulated";
    ViewEncapsulation["None"] = "None";
    ViewEncapsulation["ShadowDom"] = "ShadowDom";
})(ViewEncapsulation = exports.ViewEncapsulation || (exports.ViewEncapsulation = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2NoZW1hLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvc2NoZW1hdGljcy9hbmd1bGFyL25nLW5ldy9zY2hlbWEudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG1GQUFtRjtBQUNuRixvRkFBb0Y7OztBQXNHcEY7O0dBRUc7QUFDSCxJQUFZLGNBS1g7QUFMRCxXQUFZLGNBQWM7SUFDdEIsK0JBQWEsQ0FBQTtJQUNiLDZCQUFXLENBQUE7SUFDWCwrQkFBYSxDQUFBO0lBQ2IsK0JBQWEsQ0FBQTtBQUNqQixDQUFDLEVBTFcsY0FBYyxHQUFkLHNCQUFjLEtBQWQsc0JBQWMsUUFLekI7QUFFRDs7R0FFRztBQUNILElBQVksS0FLWDtBQUxELFdBQVksS0FBSztJQUNiLG9CQUFXLENBQUE7SUFDWCxzQkFBYSxDQUFBO0lBQ2Isc0JBQWEsQ0FBQTtJQUNiLHNCQUFhLENBQUE7QUFDakIsQ0FBQyxFQUxXLEtBQUssR0FBTCxhQUFLLEtBQUwsYUFBSyxRQUtoQjtBQUVEOztHQUVHO0FBQ0gsSUFBWSxpQkFJWDtBQUpELFdBQVksaUJBQWlCO0lBQ3pCLDBDQUFxQixDQUFBO0lBQ3JCLGtDQUFhLENBQUE7SUFDYiw0Q0FBdUIsQ0FBQTtBQUMzQixDQUFDLEVBSlcsaUJBQWlCLEdBQWpCLHlCQUFpQixLQUFqQix5QkFBaUIsUUFJNUIiLCJzb3VyY2VzQ29udGVudCI6WyJcbi8vIFRISVMgRklMRSBJUyBBVVRPTUFUSUNBTExZIEdFTkVSQVRFRC4gVE8gVVBEQVRFIFRISVMgRklMRSBZT1UgTkVFRCBUTyBDSEFOR0UgVEhFXG4vLyBDT1JSRVNQT05ESU5HIEpTT04gU0NIRU1BIEZJTEUsIFRIRU4gUlVOIGRldmtpdC1hZG1pbiBidWlsZCAob3IgYmF6ZWwgYnVpbGQgLi4uKS5cblxuLyoqXG4gKiBDcmVhdGVzIGEgbmV3IHByb2plY3QgYnkgY29tYmluaW5nIHRoZSB3b3Jrc3BhY2UgYW5kIGFwcGxpY2F0aW9uIHNjaGVtYXRpY3MuXG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgU2NoZW1hIHtcbiAgICAvKipcbiAgICAgKiBJbml0aWFsIGdpdCByZXBvc2l0b3J5IGNvbW1pdCBpbmZvcm1hdGlvbi5cbiAgICAgKi9cbiAgICBjb21taXQ/OiBDb21taXRVbmlvbjtcbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSBuZXcgaW5pdGlhbCBhcHBsaWNhdGlvbiBwcm9qZWN0IGluIHRoZSAnc3JjJyBmb2xkZXIgb2YgdGhlIG5ldyB3b3Jrc3BhY2UuIFdoZW5cbiAgICAgKiBmYWxzZSwgY3JlYXRlcyBhbiBlbXB0eSB3b3Jrc3BhY2Ugd2l0aCBubyBpbml0aWFsIGFwcGxpY2F0aW9uLiBZb3UgY2FuIHRoZW4gdXNlIHRoZVxuICAgICAqIGdlbmVyYXRlIGFwcGxpY2F0aW9uIGNvbW1hbmQgc28gdGhhdCBhbGwgYXBwbGljYXRpb25zIGFyZSBjcmVhdGVkIGluIHRoZSBwcm9qZWN0cyBmb2xkZXIuXG4gICAgICovXG4gICAgY3JlYXRlQXBwbGljYXRpb24/OiBib29sZWFuO1xuICAgIC8qKlxuICAgICAqIFRoZSBkaXJlY3RvcnkgbmFtZSB0byBjcmVhdGUgdGhlIHdvcmtzcGFjZSBpbi5cbiAgICAgKi9cbiAgICBkaXJlY3Rvcnk/OiBzdHJpbmc7XG4gICAgLyoqXG4gICAgICogSW5jbHVkZSBzdHlsZXMgaW5saW5lIGluIHRoZSBjb21wb25lbnQgVFMgZmlsZS4gQnkgZGVmYXVsdCwgYW4gZXh0ZXJuYWwgc3R5bGVzIGZpbGUgaXNcbiAgICAgKiBjcmVhdGVkIGFuZCByZWZlcmVuY2VkIGluIHRoZSBjb21wb25lbnQgVHlwZVNjcmlwdCBmaWxlLlxuICAgICAqL1xuICAgIGlubGluZVN0eWxlPzogYm9vbGVhbjtcbiAgICAvKipcbiAgICAgKiBJbmNsdWRlIHRlbXBsYXRlIGlubGluZSBpbiB0aGUgY29tcG9uZW50IFRTIGZpbGUuIEJ5IGRlZmF1bHQsIGFuIGV4dGVybmFsIHRlbXBsYXRlIGZpbGVcbiAgICAgKiBpcyBjcmVhdGVkIGFuZCByZWZlcmVuY2VkIGluIHRoZSBjb21wb25lbnQgVHlwZVNjcmlwdCBmaWxlLlxuICAgICAqL1xuICAgIGlubGluZVRlbXBsYXRlPzogYm9vbGVhbjtcbiAgICAvKipcbiAgICAgKiBMaW5rIHRoZSBDTEkgdG8gdGhlIGdsb2JhbCB2ZXJzaW9uIChpbnRlcm5hbCBkZXZlbG9wbWVudCBvbmx5KS5cbiAgICAgKi9cbiAgICBsaW5rQ2xpPzogYm9vbGVhbjtcbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSB3b3Jrc3BhY2Ugd2l0aG91dCBhbnkgdGVzdGluZyBmcmFtZXdvcmtzLiAoVXNlIGZvciBsZWFybmluZyBwdXJwb3NlcyBvbmx5LilcbiAgICAgKi9cbiAgICBtaW5pbWFsPzogYm9vbGVhbjtcbiAgICAvKipcbiAgICAgKiBUaGUgbmFtZSBvZiB0aGUgbmV3IHdvcmtzcGFjZSBhbmQgaW5pdGlhbCBwcm9qZWN0LlxuICAgICAqL1xuICAgIG5hbWU6IHN0cmluZztcbiAgICAvKipcbiAgICAgKiBUaGUgcGF0aCB3aGVyZSBuZXcgcHJvamVjdHMgd2lsbCBiZSBjcmVhdGVkLCByZWxhdGl2ZSB0byB0aGUgbmV3IHdvcmtzcGFjZSByb290LlxuICAgICAqL1xuICAgIG5ld1Byb2plY3RSb290Pzogc3RyaW5nO1xuICAgIC8qKlxuICAgICAqIFRoZSBwYWNrYWdlIG1hbmFnZXIgdXNlZCB0byBpbnN0YWxsIGRlcGVuZGVuY2llcy5cbiAgICAgKi9cbiAgICBwYWNrYWdlTWFuYWdlcj86IFBhY2thZ2VNYW5hZ2VyO1xuICAgIC8qKlxuICAgICAqIFRoZSBwcmVmaXggdG8gYXBwbHkgdG8gZ2VuZXJhdGVkIHNlbGVjdG9ycyBmb3IgdGhlIGluaXRpYWwgcHJvamVjdC5cbiAgICAgKi9cbiAgICBwcmVmaXg/OiBzdHJpbmc7XG4gICAgLyoqXG4gICAgICogR2VuZXJhdGUgYSByb3V0aW5nIG1vZHVsZSBmb3IgdGhlIGluaXRpYWwgcHJvamVjdC5cbiAgICAgKi9cbiAgICByb3V0aW5nPzogYm9vbGVhbjtcbiAgICAvKipcbiAgICAgKiBEbyBub3QgaW5pdGlhbGl6ZSBhIGdpdCByZXBvc2l0b3J5LlxuICAgICAqL1xuICAgIHNraXBHaXQ/OiBib29sZWFuO1xuICAgIC8qKlxuICAgICAqIERvIG5vdCBpbnN0YWxsIGRlcGVuZGVuY3kgcGFja2FnZXMuXG4gICAgICovXG4gICAgc2tpcEluc3RhbGw/OiBib29sZWFuO1xuICAgIC8qKlxuICAgICAqIERvIG5vdCBnZW5lcmF0ZSBcInNwZWMudHNcIiB0ZXN0IGZpbGVzIGZvciB0aGUgbmV3IHByb2plY3QuXG4gICAgICovXG4gICAgc2tpcFRlc3RzPzogYm9vbGVhbjtcbiAgICAvKipcbiAgICAgKiBDcmVhdGVzIGEgd29ya3NwYWNlIHdpdGggc3RyaWN0ZXIgdHlwZSBjaGVja2luZyBhbmQgc3RyaWN0ZXIgYnVuZGxlIGJ1ZGdldHMgc2V0dGluZ3MuXG4gICAgICogVGhpcyBzZXR0aW5nIGhlbHBzIGltcHJvdmUgbWFpbnRhaW5hYmlsaXR5IGFuZCBjYXRjaCBidWdzIGFoZWFkIG9mIHRpbWUuIEZvciBtb3JlXG4gICAgICogaW5mb3JtYXRpb24sIHNlZSBodHRwczovL2FuZ3VsYXIuaW8vZ3VpZGUvc3RyaWN0LW1vZGVcbiAgICAgKi9cbiAgICBzdHJpY3Q/OiBib29sZWFuO1xuICAgIC8qKlxuICAgICAqIFRoZSBmaWxlIGV4dGVuc2lvbiBvciBwcmVwcm9jZXNzb3IgdG8gdXNlIGZvciBzdHlsZSBmaWxlcy5cbiAgICAgKi9cbiAgICBzdHlsZT86IFN0eWxlO1xuICAgIC8qKlxuICAgICAqIFRoZSB2ZXJzaW9uIG9mIHRoZSBBbmd1bGFyIENMSSB0byB1c2UuXG4gICAgICovXG4gICAgdmVyc2lvbjogc3RyaW5nO1xuICAgIC8qKlxuICAgICAqIFRoZSB2aWV3IGVuY2Fwc3VsYXRpb24gc3RyYXRlZ3kgdG8gdXNlIGluIHRoZSBpbml0aWFsIHByb2plY3QuXG4gICAgICovXG4gICAgdmlld0VuY2Fwc3VsYXRpb24/OiBWaWV3RW5jYXBzdWxhdGlvbjtcbn1cblxuLyoqXG4gKiBJbml0aWFsIGdpdCByZXBvc2l0b3J5IGNvbW1pdCBpbmZvcm1hdGlvbi5cbiAqL1xuZXhwb3J0IHR5cGUgQ29tbWl0VW5pb24gPSBib29sZWFuIHwgQ29tbWl0T2JqZWN0O1xuXG5leHBvcnQgaW50ZXJmYWNlIENvbW1pdE9iamVjdCB7XG4gICAgZW1haWw6ICAgIHN0cmluZztcbiAgICBtZXNzYWdlPzogc3RyaW5nO1xuICAgIG5hbWU6ICAgICBzdHJpbmc7XG4gICAgW3Byb3BlcnR5OiBzdHJpbmddOiBhbnk7XG59XG5cbi8qKlxuICogVGhlIHBhY2thZ2UgbWFuYWdlciB1c2VkIHRvIGluc3RhbGwgZGVwZW5kZW5jaWVzLlxuICovXG5leHBvcnQgZW51bSBQYWNrYWdlTWFuYWdlciB7XG4gICAgQ25wbSA9IFwiY25wbVwiLFxuICAgIE5wbSA9IFwibnBtXCIsXG4gICAgUG5wbSA9IFwicG5wbVwiLFxuICAgIFlhcm4gPSBcInlhcm5cIixcbn1cblxuLyoqXG4gKiBUaGUgZmlsZSBleHRlbnNpb24gb3IgcHJlcHJvY2Vzc29yIHRvIHVzZSBmb3Igc3R5bGUgZmlsZXMuXG4gKi9cbmV4cG9ydCBlbnVtIFN0eWxlIHtcbiAgICBDc3MgPSBcImNzc1wiLFxuICAgIExlc3MgPSBcImxlc3NcIixcbiAgICBTYXNzID0gXCJzYXNzXCIsXG4gICAgU2NzcyA9IFwic2Nzc1wiLFxufVxuXG4vKipcbiAqIFRoZSB2aWV3IGVuY2Fwc3VsYXRpb24gc3RyYXRlZ3kgdG8gdXNlIGluIHRoZSBpbml0aWFsIHByb2plY3QuXG4gKi9cbmV4cG9ydCBlbnVtIFZpZXdFbmNhcHN1bGF0aW9uIHtcbiAgICBFbXVsYXRlZCA9IFwiRW11bGF0ZWRcIixcbiAgICBOb25lID0gXCJOb25lXCIsXG4gICAgU2hhZG93RG9tID0gXCJTaGFkb3dEb21cIixcbn1cbiJdfQ==