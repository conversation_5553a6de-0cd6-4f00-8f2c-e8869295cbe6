/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;

if (i === 1 && v === 0)
    return 1;
return 5;
}
    global.ng.common.locales['hi-latn'] = ["hi-Latn",[["a","p"],["AM","PM"],u],[["am","pm"],u,["AM","PM"]],[["ra","so","ma","bu","gu","su","sa"],["ravi","som","mangal","budh","guru","shukra","shani"],["ravivaar","somvaar","mangalvaar","budhvaar","guruvaar","shukravaar","shanivaar"],["Su","Mo","<PERSON>","We","Th","Fr","Sa"]],u,[["<PERSON>","F","M","A","M","<PERSON>","<PERSON>","A","S","O","N","D"],["<PERSON>","<PERSON>","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[0,0],["dd/MM/y","dd-MMM-y","d MMMM y","EEEE, d MMMM, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,"{1}, {0}",u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##,##0.###","#,##,##0%","¤#,##,##0.00","[#E0]"],"INR","₹","Indian Rupee",{"JPY":["JP¥","¥"]},"ltr", plural, [[["mi","in the morning","in the afternoon","in the evening","at night"],["midnight","in the morning","in the afternoon","in the evening","at night"],["aadhi raat","subah","dopahar","shaam","raat"]],[["midnight","morning","afternoon","evening","night"],u,["aadhi raat","subah","dopahar","shaam","raat"]],["00:00",["04:00","12:00"],["12:00","16:00"],["16:00","20:00"],["20:00","04:00"]]]];
  })(globalThis);
    