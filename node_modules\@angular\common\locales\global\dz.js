/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['dz'] = ["dz",[["སྔ་ཆ་","ཕྱི་ཆ་"],u,u],u,[["ཟླ","མིར","ལྷག","ཕུར","སངྶ","སྤེན","ཉི"],["ཟླ་","མིར་","ལྷག་","ཕུར་","སངས་","སྤེན་","ཉི་"],["གཟའ་ཟླ་བ་","གཟའ་མིག་དམར་","གཟའ་ལྷག་པ་","གཟའ་ཕུར་བུ་","གཟའ་པ་སངས་","གཟའ་སྤེན་པ་","གཟའ་ཉི་མ་"],["ཟླ་","མིར་","ལྷག་","ཕུར་","སངས་","སྤེན་","ཉི་"]],u,[["༡","༢","༣","4","༥","༦","༧","༨","9","༡༠","༡༡","༡༢"],["༡","༢","༣","༤","༥","༦","༧","༨","༩","༡༠","༡༡","12"],["ཟླ་དངཔ་","ཟླ་གཉིས་པ་","ཟླ་གསུམ་པ་","ཟླ་བཞི་པ་","ཟླ་ལྔ་པ་","ཟླ་དྲུག་པ","ཟླ་བདུན་པ་","ཟླ་བརྒྱད་པ་","ཟླ་དགུ་པ་","ཟླ་བཅུ་པ་","ཟླ་བཅུ་གཅིག་པ་","ཟླ་བཅུ་གཉིས་པ་"]],[["༡","༢","༣","༤","༥","༦","༧","༨","༩","༡༠","༡༡","༡༢"],["ཟླ་༡","ཟླ་༢","ཟླ་༣","ཟླ་༤","ཟླ་༥","ཟླ་༦","ཟླ་༧","ཟླ་༨","ཟླ་༩","ཟླ་༡༠","ཟླ་༡༡","ཟླ་༡༢"],["སྤྱི་ཟླ་དངཔ་","སྤྱི་ཟླ་གཉིས་པ་","སྤྱི་ཟླ་གསུམ་པ་","སྤྱི་ཟླ་བཞི་པ","སྤྱི་ཟླ་ལྔ་པ་","སྤྱི་ཟླ་དྲུག་པ","སྤྱི་ཟླ་བདུན་པ་","སྤྱི་ཟླ་བརྒྱད་པ་","སྤྱི་ཟླ་དགུ་པ་","སྤྱི་ཟླ་བཅུ་པ་","སྤྱི་ཟླ་བཅུ་གཅིག་པ་","སྤྱི་ཟླ་བཅུ་གཉིས་པ་"]],[["BCE","CE"],u,u],0,[6,0],["y-MM-dd","སྤྱི་ལོ་y ཟླ་MMM ཚེས་dd","སྤྱི་ལོ་y MMMM ཚེས་ dd","EEEE, སྤྱི་ལོ་y MMMM ཚེས་dd"],["ཆུ་ཚོད་ h སྐར་མ་ mm a","ཆུ་ཚོད་h:mm:ss a","ཆུ་ཚོད་ h སྐར་མ་ mm:ss a z","ཆུ་ཚོད་ h སྐར་མ་ mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##,##0.###","#,##,##0 %","¤#,##,##0.00","#E0"],"INR","₹","རྒྱ་གར་གྱི་དངུལ་ རུ་པི",{"AUD":["AU$","$"],"BTN":["Nu."],"ILS":[u,"₪"],"JPY":["JP¥","¥"],"KRW":["KR₩","₩"],"THB":["TH฿","฿"],"USD":["US$","$"],"XAF":[]},"ltr", plural, []];
  })(globalThis);
    