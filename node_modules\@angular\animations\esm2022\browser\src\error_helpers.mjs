/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ɵRuntimeError as RuntimeError } from '@angular/core';
const LINE_START = '\n - ';
export function invalidTimingValue(exp) {
    return new RuntimeError(3000 /* RuntimeErrorCode.INVALID_TIMING_VALUE */, ngDevMode && `The provided timing value "${exp}" is invalid.`);
}
export function negativeStepValue() {
    return new RuntimeError(3100 /* RuntimeErrorCode.NEGATIVE_STEP_VALUE */, ngDevMode && 'Duration values below 0 are not allowed for this animation step.');
}
export function negativeDelayValue() {
    return new RuntimeError(3101 /* RuntimeErrorCode.NEGATIVE_DELAY_VALUE */, ngDevMode && 'Delay values below 0 are not allowed for this animation step.');
}
export function invalidStyleParams(varName) {
    return new RuntimeError(3001 /* RuntimeErrorCode.INVALID_STYLE_PARAMS */, ngDevMode &&
        `Unable to resolve the local animation param ${varName} in the given list of values`);
}
export function invalidParamValue(varName) {
    return new RuntimeError(3003 /* RuntimeErrorCode.INVALID_PARAM_VALUE */, ngDevMode && `Please provide a value for the animation param ${varName}`);
}
export function invalidNodeType(nodeType) {
    return new RuntimeError(3004 /* RuntimeErrorCode.INVALID_NODE_TYPE */, ngDevMode && `Unable to resolve animation metadata node #${nodeType}`);
}
export function invalidCssUnitValue(userProvidedProperty, value) {
    return new RuntimeError(3005 /* RuntimeErrorCode.INVALID_CSS_UNIT_VALUE */, ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`);
}
export function invalidTrigger() {
    return new RuntimeError(3006 /* RuntimeErrorCode.INVALID_TRIGGER */, ngDevMode &&
        "animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))");
}
export function invalidDefinition() {
    return new RuntimeError(3007 /* RuntimeErrorCode.INVALID_DEFINITION */, ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()');
}
export function invalidState(metadataName, missingSubs) {
    return new RuntimeError(3008 /* RuntimeErrorCode.INVALID_STATE */, ngDevMode &&
        `state("${metadataName}", ...) must define default values for all the following style substitutions: ${missingSubs.join(', ')}`);
}
export function invalidStyleValue(value) {
    return new RuntimeError(3002 /* RuntimeErrorCode.INVALID_STYLE_VALUE */, ngDevMode && `The provided style string value ${value} is not allowed.`);
}
export function invalidProperty(prop) {
    return new RuntimeError(3009 /* RuntimeErrorCode.INVALID_PROPERTY */, ngDevMode &&
        `The provided animation property "${prop}" is not a supported CSS property for animations`);
}
export function invalidParallelAnimation(prop, firstStart, firstEnd, secondStart, secondEnd) {
    return new RuntimeError(3010 /* RuntimeErrorCode.INVALID_PARALLEL_ANIMATION */, ngDevMode &&
        `The CSS property "${prop}" that exists between the times of "${firstStart}ms" and "${firstEnd}ms" is also being animated in a parallel animation between the times of "${secondStart}ms" and "${secondEnd}ms"`);
}
export function invalidKeyframes() {
    return new RuntimeError(3011 /* RuntimeErrorCode.INVALID_KEYFRAMES */, ngDevMode && `keyframes() must be placed inside of a call to animate()`);
}
export function invalidOffset() {
    return new RuntimeError(3012 /* RuntimeErrorCode.INVALID_OFFSET */, ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`);
}
export function keyframeOffsetsOutOfOrder() {
    return new RuntimeError(3200 /* RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER */, ngDevMode && `Please ensure that all keyframe offsets are in order`);
}
export function keyframesMissingOffsets() {
    return new RuntimeError(3202 /* RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS */, ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`);
}
export function invalidStagger() {
    return new RuntimeError(3013 /* RuntimeErrorCode.INVALID_STAGGER */, ngDevMode && `stagger() can only be used inside of query()`);
}
export function invalidQuery(selector) {
    return new RuntimeError(3014 /* RuntimeErrorCode.INVALID_QUERY */, ngDevMode &&
        `\`query("${selector}")\` returned zero elements. (Use \`query("${selector}", { optional: true })\` if you wish to allow this.)`);
}
export function invalidExpression(expr) {
    return new RuntimeError(3015 /* RuntimeErrorCode.INVALID_EXPRESSION */, ngDevMode && `The provided transition expression "${expr}" is not supported`);
}
export function invalidTransitionAlias(alias) {
    return new RuntimeError(3016 /* RuntimeErrorCode.INVALID_TRANSITION_ALIAS */, ngDevMode && `The transition alias value "${alias}" is not supported`);
}
export function validationFailed(errors) {
    return new RuntimeError(3500 /* RuntimeErrorCode.VALIDATION_FAILED */, ngDevMode && `animation validation failed:\n${errors.map((err) => err.message).join('\n')}`);
}
export function buildingFailed(errors) {
    return new RuntimeError(3501 /* RuntimeErrorCode.BUILDING_FAILED */, ngDevMode && `animation building failed:\n${errors.map((err) => err.message).join('\n')}`);
}
export function triggerBuildFailed(name, errors) {
    return new RuntimeError(3404 /* RuntimeErrorCode.TRIGGER_BUILD_FAILED */, ngDevMode &&
        `The animation trigger "${name}" has failed to build due to the following errors:\n - ${errors
            .map((err) => err.message)
            .join('\n - ')}`);
}
export function animationFailed(errors) {
    return new RuntimeError(3502 /* RuntimeErrorCode.ANIMATION_FAILED */, ngDevMode &&
        `Unable to animate due to the following errors:${LINE_START}${errors
            .map((err) => err.message)
            .join(LINE_START)}`);
}
export function registerFailed(errors) {
    return new RuntimeError(3503 /* RuntimeErrorCode.REGISTRATION_FAILED */, ngDevMode &&
        `Unable to build the animation due to the following errors: ${errors
            .map((err) => err.message)
            .join('\n')}`);
}
export function missingOrDestroyedAnimation() {
    return new RuntimeError(3300 /* RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION */, ngDevMode && "The requested animation doesn't exist or has already been destroyed");
}
export function createAnimationFailed(errors) {
    return new RuntimeError(3504 /* RuntimeErrorCode.CREATE_ANIMATION_FAILED */, ngDevMode &&
        `Unable to create the animation due to the following errors:${errors
            .map((err) => err.message)
            .join('\n')}`);
}
export function missingPlayer(id) {
    return new RuntimeError(3301 /* RuntimeErrorCode.MISSING_PLAYER */, ngDevMode && `Unable to find the timeline player referenced by ${id}`);
}
export function missingTrigger(phase, name) {
    return new RuntimeError(3302 /* RuntimeErrorCode.MISSING_TRIGGER */, ngDevMode &&
        `Unable to listen on the animation trigger event "${phase}" because the animation trigger "${name}" doesn\'t exist!`);
}
export function missingEvent(name) {
    return new RuntimeError(3303 /* RuntimeErrorCode.MISSING_EVENT */, ngDevMode &&
        `Unable to listen on the animation trigger "${name}" because the provided event is undefined!`);
}
export function unsupportedTriggerEvent(phase, name) {
    return new RuntimeError(3400 /* RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT */, ngDevMode &&
        `The provided animation trigger event "${phase}" for the animation trigger "${name}" is not supported!`);
}
export function unregisteredTrigger(name) {
    return new RuntimeError(3401 /* RuntimeErrorCode.UNREGISTERED_TRIGGER */, ngDevMode && `The provided animation trigger "${name}" has not been registered!`);
}
export function triggerTransitionsFailed(errors) {
    return new RuntimeError(3402 /* RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED */, ngDevMode &&
        `Unable to process animations due to the following failed trigger transitions\n ${errors
            .map((err) => err.message)
            .join('\n')}`);
}
export function triggerParsingFailed(name, errors) {
    return new RuntimeError(3403 /* RuntimeErrorCode.TRIGGER_PARSING_FAILED */, ngDevMode &&
        `Animation parsing for the ${name} trigger have failed:${LINE_START}${errors
            .map((err) => err.message)
            .join(LINE_START)}`);
}
export function transitionFailed(name, errors) {
    return new RuntimeError(3505 /* RuntimeErrorCode.TRANSITION_FAILED */, ngDevMode && `@${name} has failed due to:\n ${errors.map((err) => err.message).join('\n- ')}`);
}
//# sourceMappingURL=data:application/json;base64,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