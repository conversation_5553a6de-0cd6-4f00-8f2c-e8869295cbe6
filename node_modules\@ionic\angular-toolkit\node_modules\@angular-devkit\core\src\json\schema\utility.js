"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTypesOfSchema = void 0;
const utils_1 = require("../utils");
const allTypes = ['string', 'integer', 'number', 'object', 'array', 'boolean', 'null'];
function getTypesOfSchema(schema) {
    if (!schema) {
        return new Set();
    }
    if (schema === true) {
        return new Set(allTypes);
    }
    let potentials;
    if (typeof schema.type === 'string') {
        potentials = new Set([schema.type]);
    }
    else if (Array.isArray(schema.type)) {
        potentials = new Set(schema.type);
    }
    else if ((0, utils_1.isJsonArray)(schema.enum)) {
        potentials = new Set();
        // Gather the type of each enum values, and use that as a starter for potential types.
        for (const v of schema.enum) {
            switch (typeof v) {
                case 'string':
                case 'number':
                case 'boolean':
                    potentials.add(typeof v);
                    break;
                case 'object':
                    if (Array.isArray(v)) {
                        potentials.add('array');
                    }
                    else if (v === null) {
                        potentials.add('null');
                    }
                    else {
                        potentials.add('object');
                    }
                    break;
            }
        }
    }
    else {
        potentials = new Set(allTypes);
    }
    if ((0, utils_1.isJsonObject)(schema.not)) {
        const notTypes = getTypesOfSchema(schema.not);
        potentials = new Set([...potentials].filter((p) => !notTypes.has(p)));
    }
    if (Array.isArray(schema.allOf)) {
        for (const sub of schema.allOf) {
            const types = getTypesOfSchema(sub);
            potentials = new Set([...types].filter((t) => potentials.has(t)));
        }
    }
    if (Array.isArray(schema.oneOf)) {
        let options = new Set();
        for (const sub of schema.oneOf) {
            const types = getTypesOfSchema(sub);
            options = new Set([...options, ...types]);
        }
        potentials = new Set([...options].filter((o) => potentials.has(o)));
    }
    if (Array.isArray(schema.anyOf)) {
        let options = new Set();
        for (const sub of schema.anyOf) {
            const types = getTypesOfSchema(sub);
            options = new Set([...options, ...types]);
        }
        potentials = new Set([...options].filter((o) => potentials.has(o)));
    }
    if (schema.properties) {
        potentials.add('object');
    }
    else if (schema.items) {
        potentials.add('array');
    }
    return potentials;
}
exports.getTypesOfSchema = getTypesOfSchema;
//# sourceMappingURL=data:application/json;base64,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