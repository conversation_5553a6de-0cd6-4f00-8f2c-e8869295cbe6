"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateRecorderBom = exports.UpdateRecorderBase = void 0;
const exception_1 = require("../exception/exception");
const update_buffer_1 = require("../utility/update-buffer");
class UpdateRecorderBase {
    constructor(entry) {
        this._original = Buffer.from(entry.content);
        this._content = update_buffer_1.UpdateBufferBase.create(entry.content);
        this._path = entry.path;
    }
    static createFromFileEntry(entry) {
        const c0 = entry.content.byteLength > 0 && entry.content.readUInt8(0);
        const c1 = entry.content.byteLength > 1 && entry.content.readUInt8(1);
        const c2 = entry.content.byteLength > 2 && entry.content.readUInt8(2);
        // Check if we're BOM.
        if (c0 == 0xef && c1 == 0xbb && c2 == 0xbf) {
            return new UpdateRecorderBom(entry);
        }
        else if (c0 === 0xff && c1 == 0xfe) {
            return new UpdateRecorderBom(entry);
        }
        else if (c0 === 0xfe && c1 == 0xff) {
            return new UpdateRecorderBom(entry);
        }
        return new UpdateRecorderBase(entry);
    }
    get path() {
        return this._path;
    }
    // These just record changes.
    insertLeft(index, content) {
        this._content.insertLeft(index, typeof content == 'string' ? Buffer.from(content) : content);
        return this;
    }
    insertRight(index, content) {
        this._content.insertRight(index, typeof content == 'string' ? Buffer.from(content) : content);
        return this;
    }
    remove(index, length) {
        this._content.remove(index, length);
        return this;
    }
    apply(content) {
        if (!content.equals(this._content.original)) {
            throw new exception_1.ContentHasMutatedException(this.path);
        }
        return this._content.generate();
    }
}
exports.UpdateRecorderBase = UpdateRecorderBase;
class UpdateRecorderBom extends UpdateRecorderBase {
    constructor(entry, _delta = 1) {
        super(entry);
        this._delta = _delta;
    }
    insertLeft(index, content) {
        return super.insertLeft(index + this._delta, content);
    }
    insertRight(index, content) {
        return super.insertRight(index + this._delta, content);
    }
    remove(index, length) {
        return super.remove(index + this._delta, length);
    }
}
exports.UpdateRecorderBom = UpdateRecorderBom;
//# sourceMappingURL=data:application/json;base64,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