/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['guz'] = ["guz",[["Ma","<PERSON>"],u,["<PERSON><PERSON><PERSON>","Mog"]],[["Ma","<PERSON>"],u,u],[["C","C","C","C","A","I","E"],["Cpr","Ctt","Cmn","Cmt","Ars","Icm","Est"],["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["Cpr","Ctt","Cmn","Cmt","Ars","Icm","Est"]],u,[["C","F","M","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>t","<PERSON>b","<PERSON><PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","Chulai","Agosti","Septemba","Okitoba","Nobemba","Disemba"]],u,[["YA","YK"],u,["Yeso ataiborwa","Yeso kaiboirwe"]],0,[6,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"KES","Ksh","Shilingi ya Kenya",{"JPY":["JP¥","¥"],"KES":["Ksh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    