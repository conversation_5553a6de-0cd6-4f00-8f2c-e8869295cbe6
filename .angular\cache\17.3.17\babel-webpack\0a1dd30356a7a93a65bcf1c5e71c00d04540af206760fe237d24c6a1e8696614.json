{"ast": null, "code": "import _asyncToGenerator from \"D:/2025/agmuicon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst NAMESPACE = 'ionic';\nconst BUILD = /* ionic */{\n  allRenderFn: false,\n  appendChildSlotFix: false,\n  asyncLoading: true,\n  asyncQueue: false,\n  attachStyles: true,\n  cloneNodeFix: false,\n  cmpDidLoad: true,\n  cmpDidRender: true,\n  cmpDidUnload: false,\n  cmpDidUpdate: true,\n  cmpShouldUpdate: false,\n  cmpWillLoad: true,\n  cmpWillRender: true,\n  cmpWillUpdate: false,\n  connectedCallback: true,\n  constructableCSS: true,\n  cssAnnotations: true,\n  devTools: false,\n  disconnectedCallback: true,\n  element: false,\n  event: true,\n  experimentalScopedSlotChanges: false,\n  experimentalSlotFixes: false,\n  formAssociated: false,\n  hasRenderFn: true,\n  hostListener: true,\n  hostListenerTarget: true,\n  hostListenerTargetBody: true,\n  hostListenerTargetDocument: true,\n  hostListenerTargetParent: false,\n  hostListenerTargetWindow: true,\n  hotModuleReplacement: false,\n  hydrateClientSide: true,\n  hydrateServerSide: false,\n  hydratedAttribute: false,\n  hydratedClass: true,\n  initializeNextTick: false,\n  invisiblePrehydration: true,\n  isDebug: false,\n  isDev: false,\n  isTesting: false,\n  lazyLoad: true,\n  lifecycle: true,\n  lifecycleDOMEvents: false,\n  member: true,\n  method: true,\n  mode: true,\n  observeAttribute: true,\n  profile: false,\n  prop: true,\n  propBoolean: true,\n  propMutable: true,\n  propNumber: true,\n  propString: true,\n  reflect: true,\n  scoped: true,\n  scopedSlotTextContentFix: false,\n  scriptDataOpts: false,\n  shadowDelegatesFocus: true,\n  shadowDom: true,\n  slot: true,\n  slotChildNodesFix: false,\n  slotRelocation: true,\n  state: true,\n  style: true,\n  svg: true,\n  taskQueue: true,\n  transformTagName: false,\n  updatable: true,\n  vdomAttribute: true,\n  vdomClass: true,\n  vdomFunctional: true,\n  vdomKey: true,\n  vdomListener: true,\n  vdomPropOrAttr: true,\n  vdomRef: true,\n  vdomRender: true,\n  vdomStyle: true,\n  vdomText: true,\n  vdomXlink: true,\n  watchCallback: true\n};\n\n/**\n * Virtual DOM patching algorithm based on Snabbdom by\n * Simon Friis Vindum (@paldepind)\n * Licensed under the MIT License\n * https://github.com/snabbdom/snabbdom/blob/master/LICENSE\n *\n * Modified for Stencil's renderer and slot projection\n */\nlet scopeId;\nlet contentRef;\nlet hostTagName;\nlet useNativeShadowDom = false;\nlet checkSlotFallbackVisibility = false;\nlet checkSlotRelocate = false;\nlet isSvgMode = false;\nlet queuePending = false;\nconst Build = {\n  isDev: false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: false\n};\nconst getAssetPath = path => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nconst createTime = (fnName, tagName = '') => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nconst uniqueTime = (key, measureText) => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nconst CONTENT_REF_ID = 'r';\nconst ORG_LOCATION_ID = 'o';\nconst SLOT_NODE_ID = 's';\nconst TEXT_NODE_ID = 't';\nconst HYDRATE_ID = 's-id';\nconst HYDRATED_STYLE_ID = 'sty-id';\nconst HYDRATE_CHILD_ID = 'c-id';\nconst HYDRATED_CSS = '{visibility:hidden}.hydrated{visibility:inherit}';\n/**\n * Constant for styles to be globally applied to `slot-fb` elements for pseudo-slot behavior.\n *\n * Two cascading rules must be used instead of a `:not()` selector due to Stencil browser\n * support as of Stencil v4.\n */\nconst SLOT_FB_CSS = 'slot-fb{display:contents}slot-fb[hidden]{display:none}';\nconst XLINK_NS = 'http://www.w3.org/1999/xlink';\n/**\n * Default style mode id\n */\n/**\n * Reusable empty obj/array\n * Don't add values to these!!\n */\nconst EMPTY_OBJ = {};\n/**\n * Namespaces\n */\nconst SVG_NS = 'http://www.w3.org/2000/svg';\nconst HTML_NS = 'http://www.w3.org/1999/xhtml';\nconst isDef = v => v != null;\n/**\n * Check whether a value is a 'complex type', defined here as an object or a\n * function.\n *\n * @param o the value to check\n * @returns whether it's a complex type or not\n */\nconst isComplexType = o => {\n  // https://jsperf.com/typeof-fn-object/5\n  o = typeof o;\n  return o === 'object' || o === 'function';\n};\n/**\n * Helper method for querying a `meta` tag that contains a nonce value\n * out of a DOM's head.\n *\n * @param doc The DOM containing the `head` to query against\n * @returns The content of the meta tag representing the nonce value, or `undefined` if no tag\n * exists or the tag has no content.\n */\nfunction queryNonceMetaTagContent(doc) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc.head) === null || _a === void 0 ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) === null || _b === void 0 ? void 0 : _b.getAttribute('content')) !== null && _c !== void 0 ? _c : undefined;\n}\n/**\n * Production h() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, child?: d.ChildType): d.VNode;\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, ...children: d.ChildType[]): d.VNode;\nconst h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i = 0; i < c.length; i++) {\n      child = c[i];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== 'boolean') {\n        if (simple = typeof nodeName !== 'function' && !isComplexType(child)) {\n          child = String(child);\n        }\n        if (simple && lastSimple) {\n          // If the previous child was simple (string), we merge both\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          // Append a new vNode, if it's text, we create a text vNode\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    // normalize class / className attributes\n    {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== 'object' ? classData : Object.keys(classData).filter(k => classData[k]).join(' ');\n      }\n    }\n  }\n  if (typeof nodeName === 'function') {\n    // nodeName is a functional component\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  {\n    vnode.$key$ = key;\n  }\n  {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\n/**\n * A utility function for creating a virtual DOM node from a tag and some\n * possible text content.\n *\n * @param tag the tag for this element\n * @param text possible text content for the node\n * @returns a newly-minted virtual DOM node\n */\nconst newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  {\n    vnode.$attrs$ = null;\n  }\n  {\n    vnode.$key$ = null;\n  }\n  {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nconst Host = {};\n/**\n * Check whether a given node is a Host node or not\n *\n * @param node the virtual DOM node to check\n * @returns whether it's a Host node or not\n */\nconst isHost = node => node && node.$tag$ === Host;\n/**\n * Implementation of {@link d.FunctionalUtilities} for Stencil's VDom.\n *\n * Note that these functions convert from {@link d.VNode} to\n * {@link d.ChildNode} to give functional component developers a friendly\n * interface.\n */\nconst vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\n/**\n * Convert a {@link d.VNode} to a {@link d.ChildNode} in order to present a\n * friendlier public interface (hence, 'convertToPublic').\n *\n * @param node the virtual DOM node to convert\n * @returns a converted child node\n */\nconst convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\n/**\n * Convert a {@link d.ChildNode} back to an equivalent {@link d.VNode} in\n * order to use the resulting object in the virtual DOM. The initial object was\n * likely created as part of presenting a public API, so converting it back\n * involved making it 'private' again (hence, `convertToPrivate`).\n *\n * @param node the child node to convert\n * @returns a converted virtual DOM node\n */\nconst convertToPrivate = node => {\n  if (typeof node.vtag === 'function') {\n    const vnodeData = Object.assign({}, node.vattrs);\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\n/**\n * Entrypoint of the client-side hydration process. Facilitates calls to hydrate the\n * document and all its nodes.\n *\n * This process will also reconstruct the shadow root and slot DOM nodes for components using shadow DOM.\n *\n * @param hostElm The element to hydrate.\n * @param tagName The element's tag name.\n * @param hostId The host ID assigned to the element by the server.\n * @param hostRef The host reference for the element.\n */\nconst initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime('hydrateClient', tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map(c => {\n    const orgLocationId = c.$hostId$ + '.' + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    // Put the node back in its original location since the native Shadow DOM\n    // can handle rendering it its correct location now\n    if (orgLocationNode && supportsShadow && orgLocationNode['s-en'] === '') {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node['s-hn'] = tagName;\n      if (orgLocationNode) {\n        node['s-ol'] = orgLocationNode;\n        node['s-ol']['s-nr'] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (shadowRoot) {\n    shadowRootNodes.map(shadowRootNode => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\n/**\n * Recursively constructs the virtual node tree for a host element and its children.\n * The tree is constructed by parsing the annotations set on the nodes by the server.\n *\n * In addition to constructing the vNode tree, we also track information about the node's\n * descendants like which are slots, which should exist in the shadow root, and which\n * are nodes that should be rendered as children of the parent node.\n *\n * @param parentVNode The vNode representing the parent node.\n * @param childRenderNodes An array of all child nodes in the parent's node tree.\n * @param slotNodes An array of all slot nodes in the parent's node tree.\n * @param shadowRootNodes An array all nodes that should be rendered in the shadow root in the parent's node tree.\n * @param hostElm The parent element.\n * @param node The node to construct the vNode tree for.\n * @param hostId The host ID assigned to the element by the server.\n */\nconst clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i;\n  if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      // got the node data from the element's attribute\n      // `${hostId}.${nodeId}.${depth}.${index}`\n      childIdSplt = childNodeType.split('.');\n      if (childIdSplt[0] === hostId || childIdSplt[0] === '0') {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        // this is a new child vnode\n        // so ensure its parent vnode has the vchildren array\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        // add our child vnode to a specific index of the vnode's children\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        // this is now the new parent vnode for all the next child checks\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === '0') {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    // recursively drill down, end to start so we can remove nodes\n    for (i = node.childNodes.length - 1; i >= 0; i--) {\n      clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i], hostId);\n    }\n    if (node.shadowRoot) {\n      // keep drilling down through the shadow root nodes\n      for (i = node.shadowRoot.childNodes.length - 1; i >= 0; i--) {\n        clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i], hostId);\n      }\n    }\n  } else if (node.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n    // `${COMMENT_TYPE}.${hostId}.${nodeId}.${depth}.${index}`\n    childIdSplt = node.nodeValue.split('.');\n    if (childIdSplt[1] === hostId || childIdSplt[1] === '0') {\n      // comment node for either the host id or a 0 host id\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* NODE_TYPE.TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          // remove the text comment since it's no longer needed\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === '0') {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        // this comment node is specifically for this host id\n        if (childNodeType === SLOT_NODE_ID) {\n          // `${SLOT_NODE_ID}.${hostId}.${nodeId}.${depth}.${index}.${slotName}`;\n          childVNode.$tag$ = 'slot';\n          if (childIdSplt[5]) {\n            node['s-sn'] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node['s-sn'] = '';\n          }\n          node['s-sr'] = true;\n          if (shadowRootNodes) {\n            // browser support shadowRoot and this is a shadow dom component\n            // create an actual slot element\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              // add the slot name attribute\n              childVNode.$elm$.setAttribute('name', childVNode.$name$);\n            }\n            // insert the new slot element before the slot comment\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            // remove the slot comment since it's not needed for shadow\n            node.remove();\n            if (childVNode.$depth$ === '0') {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          // `${CONTENT_REF_ID}.${hostId}`;\n          if (shadowRootNodes) {\n            // remove the content ref comment since it's not needed for shadow\n            node.remove();\n          } else {\n            hostElm['s-cr'] = node;\n            node['s-cn'] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === 'style') {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = '0';\n    parentVNode.$children$ = [vnode];\n  }\n};\n/**\n * Recursively locate any comments representing an original location for a node in a node's\n * children or shadowRoot children.\n *\n * @param node The node to search.\n * @param orgLocNodes A map of the original location annotation and the current node being searched.\n */\nconst initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n    let i = 0;\n    for (; i < node.childNodes.length; i++) {\n      initializeDocumentHydrate(node.childNodes[i], orgLocNodes);\n    }\n    if (node.shadowRoot) {\n      for (i = 0; i < node.shadowRoot.childNodes.length; i++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i], orgLocNodes);\n      }\n    }\n  } else if (node.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n    const childIdSplt = node.nodeValue.split('.');\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + '.' + childIdSplt[2], node);\n      node.nodeValue = '';\n      // useful to know if the original location is\n      // the root light-dom of a shadow dom component\n      node['s-en'] = childIdSplt[3];\n    }\n  }\n};\n// Private\nconst computeMode = elm => modeResolutionChain.map(h => h(elm)).find(m => !!m);\n// Public\nconst setMode = handler => modeResolutionChain.push(handler);\nconst getMode = ref => getHostRef(ref).$modeName$;\n/**\n * Parse a new property value for a given property type.\n *\n * While the prop value can reasonably be expected to be of `any` type as far as TypeScript's type checker is concerned,\n * it is not safe to assume that the string returned by evaluating `typeof propValue` matches:\n *   1. `any`, the type given to `propValue` in the function signature\n *   2. the type stored from `propType`.\n *\n * This function provides the capability to parse/coerce a property's value to potentially any other JavaScript type.\n *\n * Property values represented in TSX preserve their type information. In the example below, the number 0 is passed to\n * a component. This `propValue` will preserve its type information (`typeof propValue === 'number'`). Note that is\n * based on the type of the value being passed in, not the type declared of the class member decorated with `@Prop`.\n * ```tsx\n * <my-cmp prop-val={0}></my-cmp>\n * ```\n *\n * HTML prop values on the other hand, will always a string\n *\n * @param propValue the new value to coerce to some type\n * @param propType the type of the prop, expressed as a binary number\n * @returns the parsed/coerced value\n */\nconst parsePropertyValue = (propValue, propType) => {\n  // ensure this value is of the correct prop type\n  if (propValue != null && !isComplexType(propValue)) {\n    if (propType & 4 /* MEMBER_FLAGS.Boolean */) {\n      // per the HTML spec, any string value means it is a boolean true value\n      // but we'll cheat here and say that the string \"false\" is the boolean false\n      return propValue === 'false' ? false : propValue === '' || !!propValue;\n    }\n    if (propType & 2 /* MEMBER_FLAGS.Number */) {\n      // force it to be a number\n      return parseFloat(propValue);\n    }\n    if (propType & 1 /* MEMBER_FLAGS.String */) {\n      // could have been passed as a number or boolean\n      // but we still want it as a string\n      return String(propValue);\n    }\n    // redundant return here for better minification\n    return propValue;\n  }\n  // not sure exactly what type we want\n  // so no need to change to a different type\n  return propValue;\n};\nconst getElement = ref => getHostRef(ref).$hostElement$;\nconst createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* EVENT_FLAGS.Bubbles */),\n        composed: !!(flags & 2 /* EVENT_FLAGS.Composed */),\n        cancelable: !!(flags & 1 /* EVENT_FLAGS.Cancellable */),\n        detail\n      });\n    }\n  };\n};\n/**\n * Helper function to create & dispatch a custom Event on a provided target\n * @param elm the target of the Event\n * @param name the name to give the custom Event\n * @param opts options for configuring a custom Event\n * @returns the custom Event\n */\nconst emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\nconst rootAppliedStyles = /*@__PURE__*/new WeakMap();\nconst registerStyle = (scopeId, cssText, allowCS) => {\n  let style = styles.get(scopeId);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === 'string') {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId, style);\n};\nconst addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId);\n  // if an element is NOT connected then getRootNode() will return the wrong root node\n  // so the fallback is to always use the document for the root node in those cases\n  styleContainerNode = styleContainerNode.nodeType === 11 /* NODE_TYPE.DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === 'string') {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = new Set());\n      }\n      if (!appliedStyles.has(scopeId)) {\n        if (styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId}\"]`))) {\n          // This is only happening on native shadow-dom, do not needs CSS var shim\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement('style');\n          styleElm.innerHTML = style;\n          // Apply CSP nonce to the style tag if it exists\n          const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute('nonce', nonce);\n          }\n          styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector('link'));\n        }\n        // Add styles for `slot-fb` elements if we're using slots outside the Shadow DOM\n        if (cmpMeta.$flags$ & 4 /* CMP_FLAGS.hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId);\n        }\n      }\n    } else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId;\n};\nconst attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime('attachStyles', cmpMeta.$tagName$);\n  const scopeId = addStyle(elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n  if (flags & 10 /* CMP_FLAGS.needsScopedEncapsulation */) {\n    // only required when we're NOT using native shadow dom (slot)\n    // or this browser doesn't support native shadow dom\n    // and this host element was NOT created with SSR\n    // let's pick out the inner content for slot projection\n    // create a node to represent where the original\n    // content was first placed, which is useful later on\n    // DOM WRITE!!\n    elm['s-sc'] = scopeId;\n    elm.classList.add(scopeId + '-h');\n    if (flags & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n      elm.classList.add(scopeId + '-s');\n    }\n  }\n  endAttachStyles();\n};\nconst getScopeId = (cmp, mode) => 'sc-' + (mode && cmp.$flags$ & 32 /* CMP_FLAGS.hasMode */ ? cmp.$tagName$ + '-' + mode : cmp.$tagName$);\nconst convertScopedToShadow = css => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, '$1{');\n/**\n * Production setAccessor() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n/**\n * When running a VDom render set properties present on a VDom node onto the\n * corresponding HTML element.\n *\n * Note that this function has special functionality for the `class`,\n * `style`, `key`, and `ref` attributes, as well as event handlers (like\n * `onClick`, etc). All others are just passed through as-is.\n *\n * @param elm the HTMLElement onto which attributes should be set\n * @param memberName the name of the attribute to set\n * @param oldValue the old value for the attribute\n * @param newValue the new value for the attribute\n * @param isSvg whether we're in an svg context or not\n * @param flags bitflags for Vdom variables\n */\nconst setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (memberName === 'class') {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    } else if (memberName === 'style') {\n      // update style attribute, css properties and values\n      {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (prop.includes('-')) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = '';\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (prop.includes('-')) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (memberName === 'key') ;else if (memberName === 'ref') {\n      // minifier will clean this up\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (!isProp && memberName[0] === 'o' && memberName[1] === 'n') {\n      // Event Handlers\n      // so if the member name starts with \"on\" and the 3rd characters is\n      // a capital letter, and it's not already a member on the element,\n      // then we're assuming it's an event listener\n      if (memberName[2] === '-') {\n        // on- prefixed events\n        // allows to be explicit about the dom event to listen without any magic\n        // under the hood:\n        // <my-cmp on-click> // listens for \"click\"\n        // <my-cmp on-Click> // listens for \"Click\"\n        // <my-cmp on-ionChange> // listens for \"ionChange\"\n        // <my-cmp on-EVENTS> // listens for \"EVENTS\"\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        // standard event\n        // the JSX attribute could have been \"onMouseOver\" and the\n        // member name \"onmouseover\" is on the window's prototype\n        // so let's add the listener \"mouseover\", which is all lowercased\n        memberName = ln.slice(2);\n      } else {\n        // custom event\n        // the JSX attribute could have been \"onMyCustomEvent\"\n        // so let's trim off the \"on\" prefix and lowercase the first character\n        // and add the listener \"myCustomEvent\"\n        // except for the first character, we keep the event name case\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        // Need to account for \"capture\" events.\n        // If the event name ends with \"Capture\", we'll update the name to remove\n        // the \"Capture\" suffix and make sure the event listener is setup to handle the capture event.\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        // Make sure we only replace the last instance of \"Capture\"\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, '');\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else {\n      // Set property if it exists and it's not a SVG\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes('-')) {\n            const n = newValue == null ? '' : newValue;\n            // Workaround for Safari, moving the <input> caret when re-assigning the same valued\n            if (memberName === 'list') {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {\n          /**\n           * in case someone tries to set a read-only property, e.g. \"namespaceURI\", we just ignore it\n           */\n        }\n      }\n      /**\n       * Need to manually update attribute if:\n       * - memberName is not an attribute\n       * - if we are rendering the host element in order to reflect attribute\n       * - if it's a SVG, since properties might not work in <svg>\n       * - if the newValue is null/undefined or 'false'.\n       */\n      let xlink = false;\n      {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, ''))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === '') {\n          if (xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* VNODE_FLAGS.isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? '' : newValue;\n        if (xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nconst parseClassListRegex = /\\s/;\n/**\n * Parsed a string of classnames into an array\n * @param value className string, e.g. \"foo bar baz\"\n * @returns list of classes, e.g. [\"foo\", \"bar\", \"baz\"]\n */\nconst parseClassList = value => !value ? [] : value.split(parseClassListRegex);\nconst CAPTURE_EVENT_SUFFIX = 'Capture';\nconst CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + '$');\nconst updateElement = (oldVnode, newVnode, isSvgMode, memberName) => {\n  // if the element passed in is a shadow root, which is a document fragment\n  // then we want to be adding attrs/props to the shadow root's \"host\" element\n  // if it's not a shadow root, then we add attrs/props to the same element\n  const elm = newVnode.$elm$.nodeType === 11 /* NODE_TYPE.DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  {\n    // remove attributes no longer present on the vnode by setting them to undefined\n    for (memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], undefined, isSvgMode, newVnode.$flags$);\n      }\n    }\n  }\n  // add new & update changed attributes\n  for (memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode, newVnode.$flags$);\n  }\n};\n/**\n * Sort a list of attribute names to ensure that all the attribute names which\n * are _not_ `\"ref\"` come before `\"ref\"`. Preserve the order of the non-ref\n * attributes.\n *\n * **Note**: if the supplied attributes do not include `'ref'` then the same\n * (by reference) array will be returned without modification.\n *\n * @param attrNames attribute names to sort\n * @returns a list of attribute names, sorted if they include `\"ref\"`\n */\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes('ref') ?\n  // we need to sort these to ensure that `'ref'` is the last attr\n  [...attrNames.filter(attr => attr !== 'ref'), 'ref'] :\n  // no need to sort, return the original array\n  attrNames;\n}\n/**\n * Create a DOM Node corresponding to one of the children of a given VNode.\n *\n * @param oldParentVNode the parent VNode from the previous render\n * @param newParentVNode the parent VNode from the current render\n * @param childIndex the index of the VNode, in the _new_ parent node's\n * children, for which we will create a new DOM node\n * @param parentElm the parent DOM node which our new node will be a child of\n * @returns the newly created node\n */\nconst createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  // tslint:disable-next-line: prefer-const\n  const newVNode = newParentVNode.$children$[childIndex];\n  let i = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (!useNativeShadowDom) {\n    // remember for later we need to check to relocate nodes\n    checkSlotRelocate = true;\n    if (newVNode.$tag$ === 'slot') {\n      if (scopeId) {\n        // scoped css needs to add its scoped id to the parent element\n        parentElm.classList.add(scopeId + '-s');\n      }\n      newVNode.$flags$ |= newVNode.$children$ ?\n      // slot element has fallback content\n      2 /* VNODE_FLAGS.isSlotFallback */ :\n      // slot element does not have fallback content\n      1 /* VNODE_FLAGS.isSlotReference */;\n    }\n  }\n  if (newVNode.$text$ !== null) {\n    // create text node\n    elm = newVNode.$elm$ = doc.createTextNode(newVNode.$text$);\n  } else if (newVNode.$flags$ & 1 /* VNODE_FLAGS.isSlotReference */) {\n    // create a slot reference node\n    elm = newVNode.$elm$ = doc.createTextNode('');\n  } else {\n    if (!isSvgMode) {\n      isSvgMode = newVNode.$tag$ === 'svg';\n    }\n    // create element\n    elm = newVNode.$elm$ = doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, newVNode.$flags$ & 2 /* VNODE_FLAGS.isSlotFallback */ ? 'slot-fb' : newVNode.$tag$);\n    if (isSvgMode && newVNode.$tag$ === 'foreignObject') {\n      isSvgMode = false;\n    }\n    // add css classes, attrs, props, listeners, etc.\n    {\n      updateElement(null, newVNode, isSvgMode);\n    }\n    if (isDef(scopeId) && elm['s-si'] !== scopeId) {\n      // if there is a scopeId and this is the initial render\n      // then let's add the scopeId as a css class\n      elm.classList.add(elm['s-si'] = scopeId);\n    }\n    if (newVNode.$children$) {\n      for (i = 0; i < newVNode.$children$.length; ++i) {\n        // create the node\n        childNode = createElm(oldParentVNode, newVNode, i, elm);\n        // return node could have been null\n        if (childNode) {\n          // append our new node\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    {\n      if (newVNode.$tag$ === 'svg') {\n        // Only reset the SVG context when we're exiting <svg> element\n        isSvgMode = false;\n      } else if (elm.tagName === 'foreignObject') {\n        // Reenter SVG context when we're exiting <foreignObject> element\n        isSvgMode = true;\n      }\n    }\n  }\n  // This needs to always happen so we can hide nodes that are projected\n  // to another component but don't end up in a slot\n  elm['s-hn'] = hostTagName;\n  {\n    if (newVNode.$flags$ & (2 /* VNODE_FLAGS.isSlotFallback */ | 1 /* VNODE_FLAGS.isSlotReference */)) {\n      // remember the content reference comment\n      elm['s-sr'] = true;\n      // remember the content reference comment\n      elm['s-cr'] = contentRef;\n      // remember the slot name, or empty string for default slot\n      elm['s-sn'] = newVNode.$name$ || '';\n      // remember the ref callback function\n      elm['s-rf'] = (_a = newVNode.$attrs$) === null || _a === void 0 ? void 0 : _a.ref;\n      // check if we've got an old vnode for this slot\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode.$tag$ && oldParentVNode.$elm$) {\n        {\n          // we've got an old slot vnode and the wrapper is being replaced\n          // so let's move the old slot content back to its original location\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n    }\n  }\n  return elm;\n};\nconst putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm['s-sr'] && BUILD.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node['s-sn'] === parentElm['s-sn'] && node['s-sh'] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i = oldSlotChildNodes.length - 1; i >= 0; i--) {\n    const childNode = oldSlotChildNodes[i];\n    if (childNode['s-hn'] !== hostTagName && childNode['s-ol']) {\n      // and relocate it back to it's original location\n      parentReferenceNode(childNode).insertBefore(childNode, referenceNode(childNode));\n      // remove the old original location comment entirely\n      // later on the patch function will know what to do\n      // and move this to the correct spot if need be\n      childNode['s-ol'].remove();\n      childNode['s-ol'] = undefined;\n      // Reset so we can correctly move the node around again.\n      childNode['s-sh'] = undefined;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n};\n/**\n * Create DOM nodes corresponding to a list of {@link d.Vnode} objects and\n * add them to the DOM in the appropriate place.\n *\n * @param parentElm the DOM node which should be used as a parent for the new\n * DOM nodes\n * @param before a child of the `parentElm` which the new children should be\n * inserted before (optional)\n * @param parentVNode the parent virtual DOM node\n * @param vnodes the new child virtual DOM nodes to produce DOM nodes for\n * @param startIdx the index in the child virtual DOM nodes at which to start\n * creating DOM nodes (inclusive)\n * @param endIdx the index in the child virtual DOM nodes at which to stop\n * creating DOM nodes (inclusive)\n */\nconst addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = parentElm['s-cr'] && parentElm['s-cr'].parentNode || parentElm;\n  let childNode;\n  if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        containerElm.insertBefore(childNode, referenceNode(before));\n      }\n    }\n  }\n};\n/**\n * Remove the DOM elements corresponding to a list of {@link d.VNode} objects.\n * This can be used to, for instance, clean up after a list of children which\n * should no longer be shown.\n *\n * This function also handles some of Stencil's slot relocation logic.\n *\n * @param vnodes a list of virtual DOM nodes to remove\n * @param startIdx the index at which to start removing nodes (inclusive)\n * @param endIdx the index at which to stop removing nodes (inclusive)\n */\nconst removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        {\n          // we're removing this element\n          // so it's possible we need to show slot fallback content now\n          checkSlotFallbackVisibility = true;\n          if (elm['s-ol']) {\n            // remove the original location comment\n            elm['s-ol'].remove();\n          } else {\n            // it's possible that child nodes of the node\n            // that's being removed are slot nodes\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        // remove the vnode's element from the dom\n        elm.remove();\n      }\n    }\n  }\n};\n/**\n * Reconcile the children of a new VNode with the children of an old VNode by\n * traversing the two collections of children, identifying nodes that are\n * conserved or changed, calling out to `patch` to make any necessary\n * updates to the DOM, and rearranging DOM nodes as needed.\n *\n * The algorithm for reconciling children works by analyzing two 'windows' onto\n * the two arrays of children (`oldCh` and `newCh`). We keep track of the\n * 'windows' by storing start and end indices and references to the\n * corresponding array entries. Initially the two 'windows' are basically equal\n * to the entire array, but we progressively narrow the windows until there are\n * no children left to update by doing the following:\n *\n * 1. Skip any `null` entries at the beginning or end of the two arrays, so\n *    that if we have an initial array like the following we'll end up dealing\n *    only with a window bounded by the highlighted elements:\n *\n *    [null, null, VNode1 , ... , VNode2, null, null]\n *                 ^^^^^^         ^^^^^^\n *\n * 2. Check to see if the elements at the head and tail positions are equal\n *    across the windows. This will basically detect elements which haven't\n *    been added, removed, or changed position, i.e. if you had the following\n *    VNode elements (represented as HTML):\n *\n *    oldVNode: `<div><p><span>HEY</span></p></div>`\n *    newVNode: `<div><p><span>THERE</span></p></div>`\n *\n *    Then when comparing the children of the `<div>` tag we check the equality\n *    of the VNodes corresponding to the `<p>` tags and, since they are the\n *    same tag in the same position, we'd be able to avoid completely\n *    re-rendering the subtree under them with a new DOM element and would just\n *    call out to `patch` to handle reconciling their children and so on.\n *\n * 3. Check, for both windows, to see if the element at the beginning of the\n *    window corresponds to the element at the end of the other window. This is\n *    a heuristic which will let us identify _some_ situations in which\n *    elements have changed position, for instance it _should_ detect that the\n *    children nodes themselves have not changed but merely moved in the\n *    following example:\n *\n *    oldVNode: `<div><element-one /><element-two /></div>`\n *    newVNode: `<div><element-two /><element-one /></div>`\n *\n *    If we find cases like this then we also need to move the concrete DOM\n *    elements corresponding to the moved children to write the re-order to the\n *    DOM.\n *\n * 4. Finally, if VNodes have the `key` attribute set on them we check for any\n *    nodes in the old children which have the same key as the first element in\n *    our window on the new children. If we find such a node we handle calling\n *    out to `patch`, moving relevant DOM nodes, and so on, in accordance with\n *    what we find.\n *\n * Finally, once we've narrowed our 'windows' to the point that either of them\n * collapse (i.e. they have length 0) we then handle any remaining VNode\n * insertion or deletion that needs to happen to get a DOM state that correctly\n * reflects the new child VNodes. If, for instance, after our window on the old\n * children has collapsed we still have more nodes on the new children that\n * we haven't dealt with yet then we need to add them, or if the new children\n * collapse but we still have unhandled _old_ children then we need to make\n * sure the corresponding DOM nodes are removed.\n *\n * @param parentElm the node into which the parent VNode is rendered\n * @param oldCh the old children of the parent node\n * @param newVNode the new VNode which will replace the parent\n * @param newCh the new children of the parent node\n * @param isInitialRender whether or not this is the first render of the vdom\n */\nconst updateChildren = (parentElm, oldCh, newVNode, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      // VNode might have been moved left\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      // if the start nodes are the same then we should patch the new VNode\n      // onto the old one, and increment our `newStartIdx` and `oldStartIdx`\n      // indices to reflect that. We don't need to move any DOM Nodes around\n      // since things are matched up in order.\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      // likewise, if the end nodes are the same we patch new onto old and\n      // decrement our end indices, and also likewise in this case we don't\n      // need to move any DOM Nodes.\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      // case: \"Vnode moved right\"\n      //\n      // We've found that the last node in our window on the new children is\n      // the same VNode as the _first_ node in our window on the old children\n      // we're dealing with now. Visually, this is the layout of these two\n      // nodes:\n      //\n      // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n      //                                    ^^^^^^^^^^^\n      // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n      //              ^^^^^^^^^^^^^\n      //\n      // In this situation we need to patch `newEndVnode` onto `oldStartVnode`\n      // and move the DOM element for `oldStartVnode`.\n      if (oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot') {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      // We need to move the element for `oldStartVnode` into a position which\n      // will be appropriate for `newEndVnode`. For this we can use\n      // `.insertBefore` and `oldEndVnode.$elm$.nextSibling`. If there is a\n      // sibling for `oldEndVnode.$elm$` then we want to move the DOM node for\n      // `oldStartVnode` between `oldEndVnode` and it's sibling, like so:\n      //\n      // <old-start-node />\n      // <some-intervening-node />\n      // <old-end-node />\n      // <!-- ->              <-- `oldStartVnode.$elm$` should be inserted here\n      // <next-sibling />\n      //\n      // If instead `oldEndVnode.$elm$` has no sibling then we just want to put\n      // the node for `oldStartVnode` at the end of the children of\n      // `parentElm`. Luckily, `Node.nextSibling` will return `null` if there\n      // aren't any siblings, and passing `null` to `Node.insertBefore` will\n      // append it to the children of the parent element.\n      parentElm.insertBefore(oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      // case: \"Vnode moved left\"\n      //\n      // We've found that the first node in our window on the new children is\n      // the same VNode as the _last_ node in our window on the old children.\n      // Visually, this is the layout of these two nodes:\n      //\n      // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n      //              ^^^^^^^^^^^^^\n      // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n      //                                    ^^^^^^^^^^^\n      //\n      // In this situation we need to patch `newStartVnode` onto `oldEndVnode`\n      // (which will handle updating any changed attributes, reconciling their\n      // children etc) but we also need to move the DOM node to which\n      // `oldEndVnode` corresponds.\n      if (oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot') {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      // We've already checked above if `oldStartVnode` and `newStartVnode` are\n      // the same node, so since we're here we know that they are not. Thus we\n      // can move the element for `oldEndVnode` _before_ the element for\n      // `oldStartVnode`, leaving `oldStartVnode` to be reconciled in the\n      // future.\n      parentElm.insertBefore(oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      // Here we do some checks to match up old and new nodes based on the\n      // `$key$` attribute, which is set by putting a `key=\"my-key\"` attribute\n      // in the JSX for a DOM element in the implementation of a Stencil\n      // component.\n      //\n      // First we check to see if there are any nodes in the array of old\n      // children which have the same key as the first node in the new\n      // children.\n      idxInOld = -1;\n      {\n        for (i = oldStartIdx; i <= oldEndIdx; ++i) {\n          if (oldCh[i] && oldCh[i].$key$ !== null && oldCh[i].$key$ === newStartVnode.$key$) {\n            idxInOld = i;\n            break;\n          }\n        }\n      }\n      if (idxInOld >= 0) {\n        // We found a node in the old children which matches up with the first\n        // node in the new children! So let's deal with that\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          // the tag doesn't match so we'll need a new DOM element\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          // invalidate the matching old node so that we won't try to update it\n          // again later on\n          oldCh[idxInOld] = undefined;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        // We either didn't find an element in the old children that matches\n        // the key of the first new child OR the build is not using `key`\n        // attributes at all. In either case we need to create a new element\n        // for the new node.\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        // if we created a new node then handle inserting it to the DOM\n        {\n          parentReferenceNode(oldStartVnode.$elm$).insertBefore(node, referenceNode(oldStartVnode.$elm$));\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    // we have some more new nodes to add which don't match up with old nodes\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode, newCh, newStartIdx, newEndIdx);\n  } else if (newStartIdx > newEndIdx) {\n    // there are nodes in the `oldCh` array which no longer correspond to nodes\n    // in the new array, so lets remove them (which entails cleaning up the\n    // relevant DOM nodes)\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\n/**\n * Compare two VNodes to determine if they are the same\n *\n * **NB**: This function is an equality _heuristic_ based on the available\n * information set on the two VNodes and can be misleading under certain\n * circumstances. In particular, if the two nodes do not have `key` attrs\n * (available under `$key$` on VNodes) then the function falls back on merely\n * checking that they have the same tag.\n *\n * So, in other words, if `key` attrs are not set on VNodes which may be\n * changing order within a `children` array or something along those lines then\n * we could obtain a false negative and then have to do needless re-rendering\n * (i.e. we'd say two VNodes aren't equal when in fact they should be).\n *\n * @param leftVNode the first VNode to check\n * @param rightVNode the second VNode to check\n * @param isInitialRender whether or not this is the first render of the vdom\n * @returns whether they're equal or not\n */\nconst isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  // compare if two vnode to see if they're \"technically\" the same\n  // need to have the same element tag, and same key to be the same\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (leftVNode.$tag$ === 'slot') {\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    // this will be set if JSX tags in the build have `key` attrs set on them\n    // we only want to check this if we're not on the first render since on\n    // first render `leftVNode.$key$` will always be `null`, so we can be led\n    // astray and, for instance, accidentally delete a DOM node that we want to\n    // keep around.\n    if (!isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nconst referenceNode = node => {\n  // this node was relocated to a new location in the dom\n  // because of some other component's slot\n  // but we still have an html comment in place of where\n  // it's original location was according to it's original vdom\n  return node && node['s-ol'] || node;\n};\nconst parentReferenceNode = node => (node['s-ol'] ? node['s-ol'] : node).parentNode;\n/**\n * Handle reconciling an outdated VNode with a new one which corresponds to\n * it. This function handles flushing updates to the DOM and reconciling the\n * children of the two nodes (if any).\n *\n * @param oldVNode an old VNode whose DOM element and children we want to update\n * @param newVNode a new VNode representing an updated version of the old one\n * @param isInitialRender whether or not this is the first render of the vdom\n */\nconst patch = (oldVNode, newVNode, isInitialRender = false) => {\n  const elm = newVNode.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode.$children$;\n  const tag = newVNode.$tag$;\n  const text = newVNode.$text$;\n  let defaultHolder;\n  if (text === null) {\n    {\n      // test if we're rendering an svg element, or still rendering nodes inside of one\n      // only add this to the when the compiler sees we're using an svg somewhere\n      isSvgMode = tag === 'svg' ? true : tag === 'foreignObject' ? false : isSvgMode;\n    }\n    {\n      if (tag === 'slot' && !useNativeShadowDom) ;else {\n        // either this is the first render of an element OR it's an update\n        // AND we already know it's possible it could have changed\n        // this updates the element's css classes, attrs, props, listeners, etc.\n        updateElement(oldVNode, newVNode, isSvgMode);\n      }\n    }\n    if (oldChildren !== null && newChildren !== null) {\n      // looks like there's child vnodes for both the old and new vnodes\n      // so we need to call `updateChildren` to reconcile them\n      updateChildren(elm, oldChildren, newVNode, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      // no old child vnodes, but there are new child vnodes to add\n      if (oldVNode.$text$ !== null) {\n        // the old vnode was text, so be sure to clear it out\n        elm.textContent = '';\n      }\n      // add the new vnode children\n      addVnodes(elm, null, newVNode, newChildren, 0, newChildren.length - 1);\n    } else if (oldChildren !== null) {\n      // no new child vnodes, but there are old child vnodes to remove\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (isSvgMode && tag === 'svg') {\n      isSvgMode = false;\n    }\n  } else if (defaultHolder = elm['s-cr']) {\n    // this element has slotted content\n    defaultHolder.parentNode.textContent = text;\n  } else if (oldVNode.$text$ !== text) {\n    // update the text content for the text only vnode\n    // and also only if the text is different than before\n    elm.data = text;\n  }\n};\n/**\n * Adjust the `.hidden` property as-needed on any nodes in a DOM subtree which\n * are slot fallbacks nodes.\n *\n * A slot fallback node should be visible by default. Then, it should be\n * conditionally hidden if:\n *\n * - it has a sibling with a `slot` property set to its slot name or if\n * - it is a default fallback slot node, in which case we hide if it has any\n *   content\n *\n * @param elm the element of interest\n */\nconst updateFallbackSlotVisibility = elm => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n      if (childNode['s-sr']) {\n        // this is a slot fallback node\n        // get the slot name for this slot reference node\n        const slotName = childNode['s-sn'];\n        // by default always show a fallback slot node\n        // then hide it if there are other slots in the light dom\n        childNode.hidden = false;\n        // we need to check all of its sibling nodes in order to see if\n        // `childNode` should be hidden\n        for (const siblingNode of childNodes) {\n          // Don't check the node against itself\n          if (siblingNode !== childNode) {\n            if (siblingNode['s-hn'] !== childNode['s-hn'] || slotName !== '') {\n              // this sibling node is from a different component OR is a named\n              // fallback slot node\n              if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ && (slotName === siblingNode.getAttribute('slot') || slotName === siblingNode['s-sn'])) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              // this is a default fallback slot node\n              // any element or text node (with content)\n              // should hide the default fallback slot node\n              if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ || siblingNode.nodeType === 3 /* NODE_TYPE.TextNode */ && siblingNode.textContent.trim() !== '') {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      // keep drilling down\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\n/**\n * Component-global information about nodes which are either currently being\n * relocated or will be shortly.\n */\nconst relocateNodes = [];\n/**\n * Mark the contents of a slot for relocation via adding references to them to\n * the {@link relocateNodes} data structure. The actual work of relocating them\n * will then be handled in {@link renderVdom}.\n *\n * @param elm a render node whose child nodes need to be relocated\n */\nconst markSlotContentForRelocation = elm => {\n  // tslint:disable-next-line: prefer-const\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    // we need to find child nodes which are slot references so we can then try\n    // to match them up with nodes that need to be relocated\n    if (childNode['s-sr'] && (node = childNode['s-cr']) && node.parentNode) {\n      // first get the content reference comment node ('s-cr'), then we get\n      // its parent, which is where all the host content is now\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode['s-sn'];\n      // iterate through all the nodes under the location where the host was\n      // originally rendered\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        // check that the node is not a content reference node or a node\n        // reference and then check that the host name does not match that of\n        // childNode.\n        // In addition, check that the slot either has not already been relocated, or\n        // that its current location's host is not childNode's host. This is essentially\n        // a check so that we don't try to relocate (and then hide) a node that is already\n        // where it should be.\n        if (!node['s-cn'] && !node['s-nr'] && node['s-hn'] !== childNode['s-hn'] && !BUILD.experimentalSlotFixes) {\n          // if `node` is located in the slot that `childNode` refers to (via the\n          // `'s-sn'` property) then we need to relocate it from it's current spot\n          // (under the host element parent) to the right slot location\n          if (isNodeLocatedInSlot(node, slotName)) {\n            // it's possible we've already decided to relocate this node\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            // made some changes to slots\n            // let's make sure we also double check\n            // fallbacks are correctly hidden or shown\n            checkSlotFallbackVisibility = true;\n            // ensure that the slot-name attr is correct\n            node['s-sn'] = node['s-sn'] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$['s-sh'] = childNode['s-hn'];\n              // we marked this node for relocation previously but didn't find\n              // out the slot reference node to which it needs to be relocated\n              // so write it down now!\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node['s-sh'] = childNode['s-hn'];\n              // add to our list of nodes to relocate\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node['s-sr']) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node['s-sn'])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            // the node is not found within the slot (`childNode`) that we're\n            // currently looking at, so we stick it into `relocateNodes` to\n            // handle later. If we never find a home for this element then\n            // we'll need to hide it\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    // if we're dealing with any type of element (capable of itself being a\n    // slot reference or containing one) then we recur\n    if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\n/**\n * Check whether a node is located in a given named slot.\n *\n * @param nodeToRelocate the node of interest\n * @param slotName the slot name to check\n * @returns whether the node is located in the slot or not\n */\nconst isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n    if (nodeToRelocate.getAttribute('slot') === null && slotName === '') {\n      // if the node doesn't have a slot attribute, and the slot we're checking\n      // is not a named slot, then we assume the node should be within the slot\n      return true;\n    }\n    if (nodeToRelocate.getAttribute('slot') === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate['s-sn'] === slotName) {\n    return true;\n  }\n  return slotName === '';\n};\n/**\n * 'Nullify' any VDom `ref` callbacks on a VDom node or its children by calling\n * them with `null`. This signals that the DOM element corresponding to the VDom\n * node has been removed from the DOM.\n *\n * @param vNode a virtual DOM node\n */\nconst nullifyVNodeRefs = vNode => {\n  {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\n/**\n * The main entry point for Stencil's virtual DOM-based rendering engine\n *\n * Given a {@link d.HostRef} container and some virtual DOM nodes, this\n * function will handle creating a virtual DOM tree with a single root, patching\n * the current virtual DOM tree onto an old one (if any), dealing with slot\n * relocation, and reflecting attributes.\n *\n * @param hostRef data needed to root and render the virtual DOM tree, such as\n * the DOM node into which it should be rendered.\n * @param renderFnResults the virtual DOM nodes to be rendered\n * @param isInitialLoad whether or not this is the first call after page load\n */\nconst renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  // if `renderFnResults` is a Host node then we can use it directly. If not,\n  // we need to call `h` again to wrap the children of our component in a\n  // 'dummy' Host node (well, an empty vnode) since `renderVdom` assumes\n  // implicitly that the top-level vdom node is 1) an only child and 2)\n  // contains attrs that need to be set on the host element.\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  // On the first render and *only* on the first render we want to check for\n  // any attributes set on the host element which are also set on the vdom\n  // node. If we find them, we override the value on the VDom node attrs with\n  // the value from the host element, which allows developers building apps\n  // with Stencil components to override e.g. the `role` attribute on a\n  // component even if it's already set on the `Host`.\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      // We have a special implementation in `setAccessor` for `style` and\n      // `class` which reconciles values coming from the VDom with values\n      // already present on the DOM element, so we don't want to override those\n      // attributes on the VDom tree with values from the host element if they\n      // are present.\n      //\n      // Likewise, `ref` and `key` are special internal values for the Stencil\n      // runtime and we don't want to override those either.\n      if (hostElm.hasAttribute(key) && !['key', 'ref', 'style', 'class'].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* VNODE_FLAGS.isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = hostElm.shadowRoot || hostElm;\n  {\n    scopeId = hostElm['s-sc'];\n  }\n  useNativeShadowDom = (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) !== 0;\n  {\n    contentRef = hostElm['s-cr'];\n    // always reset\n    checkSlotFallbackVisibility = false;\n  }\n  // synchronous patch\n  patch(oldVNode, rootVnode, isInitialLoad);\n  {\n    // while we're moving nodes around existing nodes, temporarily disable\n    // the disconnectCallback from working\n    plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate['s-ol']) {\n          // add a reference node marking this node's original location\n          // keep a reference to this node for later lookups\n          const orgLocationNode = doc.createTextNode('');\n          orgLocationNode['s-nr'] = nodeToRelocate;\n          nodeToRelocate.parentNode.insertBefore(nodeToRelocate['s-ol'] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          // When determining where to insert content, the most simple case would be\n          // to relocate the node immediately following the slot reference node. We do this\n          // by getting a reference to the node immediately following the slot reference node\n          // since we will use `insertBefore` to manipulate the DOM.\n          //\n          // If there is no node immediately following the slot reference node, then we will just\n          // end up appending the node as the last child of the parent.\n          let insertBeforeNode = slotRefNode.nextSibling;\n          // If the node we're currently planning on inserting the new node before is an element,\n          // we need to do some additional checks to make sure we're inserting the node in the correct order.\n          // The use case here would be that we have multiple nodes being relocated to the same slot. So, we want\n          // to make sure they get inserted into their new home in the same order they were declared in their original location.\n          //\n          // TODO(STENCIL-914): Remove `experimentalSlotFixes` check\n          {\n            let orgLocationNode = (_a = nodeToRelocate['s-ol']) === null || _a === void 0 ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode['s-nr']) !== null && _b !== void 0 ? _b : null;\n              if (refNode && refNode['s-sn'] === nodeToRelocate['s-sn'] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                // If the refNode is the same node to be relocated or another element's slot reference, keep searching to find the\n                // correct relocation target\n                while (refNode === nodeToRelocate || (refNode === null || refNode === void 0 ? void 0 : refNode['s-sr'])) {\n                  refNode = refNode === null || refNode === void 0 ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode['s-nr']) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            // we've checked that it's worth while to relocate\n            // since that the node to relocate\n            // has a different next sibling or parent relocated\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!nodeToRelocate['s-hn'] && nodeToRelocate['s-ol']) {\n                // probably a component in the index.html that doesn't have its hostname set\n                nodeToRelocate['s-hn'] = nodeToRelocate['s-ol'].parentNode.nodeName;\n              }\n              // Add it back to the dom but in its new home\n              // If we get to this point and `insertBeforeNode` is `null`, that means\n              // we're just going to append the node as the last child of the parent. Passing\n              // `null` as the second arg here will trigger that behavior.\n              parentNodeRef.insertBefore(nodeToRelocate, insertBeforeNode);\n              // Reset the `hidden` value back to what it was defined as originally\n              // This solves a problem where a `slot` is dynamically rendered and `hidden` may have\n              // been set on content originally, but now it has a slot to go to so it should have\n              // the value it was defined as having in the DOM, not what we overrode it to.\n              if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate['s-ih']) !== null && _c !== void 0 ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode['s-rf'] === 'function' && slotRefNode['s-rf'](nodeToRelocate);\n        } else {\n          // this node doesn't have a slot home to go to, so let's hide it\n          if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n            // Store the initial value of `hidden` so we can reset it later when\n            // moving nodes around.\n            if (isInitialLoad) {\n              nodeToRelocate['s-ih'] = (_d = nodeToRelocate.hidden) !== null && _d !== void 0 ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    // done moving nodes around\n    // allow the disconnect callback to work again\n    plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    // always reset\n    relocateNodes.length = 0;\n  }\n  // Clear the content ref so we don't create a memory leak\n  contentRef = undefined;\n};\nconst attachToAncestor = (hostRef, ancestorComponent) => {\n  if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent['s-p']) {\n    ancestorComponent['s-p'].push(new Promise(r => hostRef.$onRenderResolve$ = r));\n  }\n};\nconst scheduleUpdate = (hostRef, isInitialLoad) => {\n  {\n    hostRef.$flags$ |= 16 /* HOST_FLAGS.isQueuedForUpdate */;\n  }\n  if (hostRef.$flags$ & 4 /* HOST_FLAGS.isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* HOST_FLAGS.needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  // there is no ancestor component or the ancestor component\n  // has already fired off its lifecycle update then\n  // fire off the initial update\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return writeTask(dispatch);\n};\n/**\n * Dispatch initial-render and update lifecycle hooks, enqueuing calls to\n * component lifecycle methods like `componentWillLoad` as well as\n * {@link updateComponent}, which will kick off the virtual DOM re-render.\n *\n * @param hostRef a reference to a host DOM node\n * @param isInitialLoad whether we're on the initial load or not\n * @returns an empty Promise which is used to enqueue a series of operations for\n * the component\n */\nconst dispatchHooks = (hostRef, isInitialLoad) => {\n  const endSchedule = createTime('scheduleUpdate', hostRef.$cmpMeta$.$tagName$);\n  const instance = hostRef.$lazyInstance$;\n  // We're going to use this variable together with `enqueue` to implement a\n  // little promise-based queue. We start out with it `undefined`. When we add\n  // the first function to the queue we'll set this variable to be that\n  // function's return value. When we attempt to add subsequent values to the\n  // queue we'll check that value and, if it was a `Promise`, we'll then chain\n  // the new function off of that `Promise` using `.then()`. This will give our\n  // queue two nice properties:\n  //\n  // 1. If all functions added to the queue are synchronous they'll be called\n  //    synchronously right away.\n  // 2. If all functions added to the queue are asynchronous they'll all be\n  //    called in order after `dispatchHooks` exits.\n  let maybePromise;\n  if (isInitialLoad) {\n    {\n      hostRef.$flags$ |= 256 /* HOST_FLAGS.isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = undefined;\n      }\n    }\n    {\n      // If `componentWillLoad` returns a `Promise` then we want to wait on\n      // whatever's going on in that `Promise` before we launch into\n      // rendering the component, doing other lifecycle stuff, etc. So\n      // in that case we assign the returned promise to the variable we\n      // declared above to hold a possible 'queueing' Promise\n      maybePromise = safeCall(instance, 'componentWillLoad');\n    }\n  }\n  {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, 'componentWillRender'));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\n/**\n * This function uses a Promise to implement a simple first-in, first-out queue\n * of functions to be called.\n *\n * The queue is ordered on the basis of the first argument. If it's\n * `undefined`, then nothing is on the queue yet, so the provided function can\n * be called synchronously (although note that this function may return a\n * `Promise`). The idea is that then the return value of that enqueueing\n * operation is kept around, so that if it was a `Promise` then subsequent\n * functions can be enqueued by calling this function again with that `Promise`\n * as the first argument.\n *\n * @param maybePromise either a `Promise` which should resolve before the next function is called or an 'empty' sentinel\n * @param fn a function to enqueue\n * @returns either a `Promise` or the return value of the provided function\n */\nconst enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn) : fn();\n/**\n * Check that a value is a `Promise`. To check, we first see if the value is an\n * instance of the `Promise` global. In a few circumstances, in particular if\n * the global has been overwritten, this is could be misleading, so we also do\n * a little 'duck typing' check to see if the `.then` property of the value is\n * defined and a function.\n *\n * @param maybePromise it might be a promise!\n * @returns whether it is or not\n */\nconst isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === 'function';\n/**\n * Update a component given reference to its host elements and so on.\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param isInitialLoad whether or not this function is being called as part of\n * the first render cycle\n */\nconst updateComponent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (hostRef, instance, isInitialLoad) {\n    var _a;\n    const elm = hostRef.$hostElement$;\n    const endUpdate = createTime('update', hostRef.$cmpMeta$.$tagName$);\n    const rc = elm['s-rc'];\n    if (isInitialLoad) {\n      // DOM WRITE!\n      attachStyles(hostRef);\n    }\n    const endRender = createTime('render', hostRef.$cmpMeta$.$tagName$);\n    {\n      callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    if (rc) {\n      // ok, so turns out there are some child host elements\n      // waiting on this parent element to load\n      // let's fire off all update callbacks waiting\n      rc.map(cb => cb());\n      elm['s-rc'] = undefined;\n    }\n    endRender();\n    endUpdate();\n    {\n      const childrenPromises = (_a = elm['s-p']) !== null && _a !== void 0 ? _a : [];\n      const postUpdate = () => postUpdateComponent(hostRef);\n      if (childrenPromises.length === 0) {\n        postUpdate();\n      } else {\n        Promise.all(childrenPromises).then(postUpdate);\n        hostRef.$flags$ |= 4 /* HOST_FLAGS.isWaitingForChildren */;\n        childrenPromises.length = 0;\n      }\n    }\n  });\n  return function updateComponent(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * Handle making the call to the VDom renderer with the proper context given\n * various build variables\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param elm the Host element for the component\n * @param isInitialLoad whether or not this function is being called as part of\n * @returns an empty promise\n */\nconst callRender = (hostRef, instance, elm, isInitialLoad) => {\n  try {\n    /**\n     * minification optimization: `allRenderFn` is `true` if all components have a `render`\n     * method, so we can call the method immediately. If not, check before calling it.\n     */\n    instance = instance.render && instance.render();\n    {\n      hostRef.$flags$ &= ~16 /* HOST_FLAGS.isQueuedForUpdate */;\n    }\n    {\n      hostRef.$flags$ |= 2 /* HOST_FLAGS.hasRendered */;\n    }\n    {\n      {\n        // looks like we've got child nodes to render into this host element\n        // or we need to update the css class/attrs on the host element\n        // DOM WRITE!\n        {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  return null;\n};\nconst postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime('postUpdate', tagName);\n  const instance = hostRef.$lazyInstance$;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  {\n    safeCall(instance, 'componentDidRender');\n  }\n  if (!(hostRef.$flags$ & 64 /* HOST_FLAGS.hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* HOST_FLAGS.hasLoadedComponent */;\n    {\n      // DOM WRITE!\n      addHydratedFlag(elm);\n    }\n    {\n      safeCall(instance, 'componentDidLoad');\n    }\n    endPostUpdate();\n    {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad();\n      }\n    }\n  } else {\n    {\n      safeCall(instance, 'componentDidUpdate');\n    }\n    endPostUpdate();\n  }\n  {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  // load events fire from bottom to top\n  // the deepest elements load first then bubbles up\n  {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = undefined;\n    }\n    if (hostRef.$flags$ & 512 /* HOST_FLAGS.needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* HOST_FLAGS.isWaitingForChildren */ | 512 /* HOST_FLAGS.needsRerender */);\n  }\n  // ( •_•)\n  // ( •_•)>⌐■-■\n  // (⌐■_■)\n};\nconst forceUpdate = ref => {\n  {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    // Returns \"true\" when the forced update was successfully scheduled\n    return isConnected;\n  }\n};\nconst appDidLoad = who => {\n  // on appload\n  // we have finish the first big initial render\n  {\n    addHydratedFlag(doc.documentElement);\n  }\n  nextTick(() => emitEvent(win, 'appload', {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n};\n/**\n * Allows to safely call a method, e.g. `componentDidLoad`, on an instance,\n * e.g. custom element node. If a build figures out that e.g. no component\n * has a `componentDidLoad` method, the instance method gets removed from the\n * output bundle and this function returns `undefined`.\n * @param instance any object that may or may not contain methods\n * @param method method name\n * @param arg single arbitrary argument\n * @returns result of method call if it exists, otherwise `undefined`\n */\nconst safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return undefined;\n};\nconst addHydratedFlag = elm => elm.classList.add('hydrated');\nconst getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nconst setValue = (ref, propName, newVal, cmpMeta) => {\n  // check our new property value against our internal value\n  const hostRef = getHostRef(ref);\n  const elm = hostRef.$hostElement$;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = hostRef.$lazyInstance$;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  // explicitly check for NaN on both sides, as `NaN === NaN` is always false\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!(flags & 8 /* HOST_FLAGS.isConstructingInstance */) || oldVal === undefined) && didValueChange) {\n    // gadzooks! the property's value has changed!!\n    // set our new value!\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (instance) {\n      // get an array of method names of watch functions to call\n      if (cmpMeta.$watchers$ && flags & 128 /* HOST_FLAGS.isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          // this instance is watching for when this property changed\n          watchMethods.map(watchMethodName => {\n            try {\n              // fire off each of the watch methods that are watching this property\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if ((flags & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n        // looks like this value actually changed, so we've got work to do!\n        // but only if we've already rendered, otherwise just chill out\n        // queue that we need to do an update, but don't worry about queuing\n        // up millions cuz this function ensures it only runs once\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n/**\n * Attach a series of runtime constructs to a compiled Stencil component\n * constructor, including getters and setters for the `@Prop` and `@State`\n * decorators, callbacks for when attributes change, and so on.\n *\n * @param Cstr the constructor for a component that we need to process\n * @param cmpMeta metadata collected previously about the component\n * @param flags a number used to store a series of bit flags\n * @returns a reference to the same constructor passed in (but now mutated)\n */\nconst proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a;\n  const prototype = Cstr.prototype;\n  if (cmpMeta.$members$) {\n    if (Cstr.watchers) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    // It's better to have a const than two Object.entries()\n    const members = Object.entries(cmpMeta.$members$);\n    members.map(([memberName, [memberFlags]]) => {\n      if (memberFlags & 31 /* MEMBER_FLAGS.Prop */ || flags & 2 /* PROXY_FLAGS.proxyState */ && memberFlags & 32 /* MEMBER_FLAGS.State */) {\n        // proxyComponent - prop\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            // proxyComponent, get value\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            // proxyComponent, set value\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (flags & 1 /* PROXY_FLAGS.isElementConstructor */ && memberFlags & 64 /* MEMBER_FLAGS.Method */) {\n        // proxyComponent - method\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a;\n            const ref = getHostRef(this);\n            return (_a = ref === null || ref === void 0 ? void 0 : ref.$onInstancePromise$) === null || _a === void 0 ? void 0 : _a.then(() => {\n              var _a;\n              return (_a = ref.$lazyInstance$) === null || _a === void 0 ? void 0 : _a[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (flags & 1 /* PROXY_FLAGS.isElementConstructor */) {\n      const attrNameToPropName = new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a;\n          const propName = attrNameToPropName.get(attrName);\n          //  In a web component lifecycle the attributeChangedCallback runs prior to connectedCallback\n          //  in the case where an attribute was set inline.\n          //  ```html\n          //    <my-component some-attribute=\"some-value\"></my-component>\n          //  ```\n          //\n          //  There is an edge case where a developer sets the attribute inline on a custom element and then\n          //  programmatically changes it before it has been upgraded as shown below:\n          //\n          //  ```html\n          //    <!-- this component has _not_ been upgraded yet -->\n          //    <my-component id=\"test\" some-attribute=\"some-value\"></my-component>\n          //    <script>\n          //      // grab non-upgraded component\n          //      el = document.querySelector(\"#test\");\n          //      el.someAttribute = \"another-value\";\n          //      // upgrade component\n          //      customElements.define('my-component', MyComponent);\n          //    </script>\n          //  ```\n          //  In this case if we do not un-shadow here and use the value of the shadowing property, attributeChangedCallback\n          //  will be called with `newValue = \"some-value\"` and will set the shadowed property (this.someAttribute = \"another-value\")\n          //  to the value that was set inline i.e. \"some-value\" from above example. When\n          //  the connectedCallback attempts to un-shadow it will use \"some-value\" as the initial value rather than \"another-value\"\n          //\n          //  The case where the attribute was NOT set inline but was not set programmatically shall be handled/un-shadowed\n          //  by connectedCallback as this attributeChangedCallback will not fire.\n          //\n          //  https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n          //\n          //  TODO(STENCIL-16) we should think about whether or not we actually want to be reflecting the attributes to\n          //  properties here given that this goes against best practices outlined here\n          //  https://developers.google.com/web/fundamentals/web-components/best-practices#avoid-reentrancy\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === 'number' && this[propName] == newValue) {\n            // if the propName exists on the prototype of `Cstr`, this update may be a result of Stencil using native\n            // APIs to reflect props as attributes. Calls to `setAttribute(someElement, propName)` will result in\n            // `propName` to be converted to a `DOMString`, which may not be what we want for other primitive props.\n            return;\n          } else if (propName == null) {\n            // At this point we should know this is not a \"member\", so we can treat it like watching an attribute\n            // on a vanilla web component\n            const hostRef = getHostRef(this);\n            const flags = hostRef === null || hostRef === void 0 ? void 0 : hostRef.$flags$;\n            // We only want to trigger the callback(s) if:\n            // 1. The instance is ready\n            // 2. The watchers are ready\n            // 3. The value has changed\n            if (flags && !(flags & 8 /* HOST_FLAGS.isConstructingInstance */) && flags & 128 /* HOST_FLAGS.isWatchReady */ && newValue !== oldValue) {\n              const instance = hostRef.$lazyInstance$;\n              const entry = (_a = cmpMeta.$watchers$) === null || _a === void 0 ? void 0 : _a[attrName];\n              entry === null || entry === void 0 ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === 'boolean' ? false : newValue;\n        });\n      };\n      // Create an array of attributes to observe\n      // This list in comprised of all strings used within a `@Watch()` decorator\n      // on a component as well as any Stencil-specific \"members\" (`@Prop()`s and `@State()`s).\n      // As such, there is no way to guarantee type-safety here that a user hasn't entered\n      // an invalid attribute.\n      Cstr.observedAttributes = Array.from(new Set([...Object.keys((_a = cmpMeta.$watchers$) !== null && _a !== void 0 ? _a : {}), ...members.filter(([_, m]) => m[0] & 15 /* MEMBER_FLAGS.HasAttribute */).map(([propName, m]) => {\n        var _a;\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (m[0] & 512 /* MEMBER_FLAGS.ReflectAttr */) {\n          (_a = cmpMeta.$attrsToReflect$) === null || _a === void 0 ? void 0 : _a.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n/**\n * Initialize a Stencil component given a reference to its host element, its\n * runtime bookkeeping data structure, runtime metadata about the component,\n * and (optionally) an HMR version ID.\n *\n * @param elm a host element\n * @param hostRef the element's runtime bookkeeping object\n * @param cmpMeta runtime metadata for the Stencil component\n * @param hmrVersionId an (optional) HMR version ID\n */\nconst initializeComponent = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (elm, hostRef, cmpMeta, hmrVersionId) {\n    let Cstr;\n    // initializeComponent\n    if ((hostRef.$flags$ & 32 /* HOST_FLAGS.hasInitializedComponent */) === 0) {\n      // Let the runtime know that the component has been initialized\n      hostRef.$flags$ |= 32 /* HOST_FLAGS.hasInitializedComponent */;\n      const bundleId = cmpMeta.$lazyBundleId$;\n      if (bundleId) {\n        // lazy loaded components\n        // request the component's implementation to be\n        // wired up with the host element\n        Cstr = loadModule(cmpMeta);\n        if (Cstr.then) {\n          // Await creates a micro-task avoid if possible\n          const endLoad = uniqueTime();\n          Cstr = yield Cstr;\n          endLoad();\n        }\n        if (!Cstr.isProxied) {\n          // we've never proxied this Constructor before\n          // let's add the getters/setters to its prototype before\n          // the first time we create an instance of the implementation\n          {\n            cmpMeta.$watchers$ = Cstr.watchers;\n          }\n          proxyComponent(Cstr, cmpMeta, 2 /* PROXY_FLAGS.proxyState */);\n          Cstr.isProxied = true;\n        }\n        const endNewInstance = createTime('createInstance', cmpMeta.$tagName$);\n        // ok, time to construct the instance\n        // but let's keep track of when we start and stop\n        // so that the getters/setters don't incorrectly step on data\n        {\n          hostRef.$flags$ |= 8 /* HOST_FLAGS.isConstructingInstance */;\n        }\n        // construct the lazy-loaded component implementation\n        // passing the hostRef is very important during\n        // construction in order to directly wire together the\n        // host element and the lazy-loaded instance\n        try {\n          new Cstr(hostRef);\n        } catch (e) {\n          consoleError(e);\n        }\n        {\n          hostRef.$flags$ &= ~8 /* HOST_FLAGS.isConstructingInstance */;\n        }\n        {\n          hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */;\n        }\n        endNewInstance();\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else {\n        // sync constructor component\n        Cstr = elm.constructor;\n        // wait for the CustomElementRegistry to mark the component as ready before setting `isWatchReady`. Otherwise,\n        // watchers may fire prematurely if `customElements.get()`/`customElements.whenDefined()` resolves _before_\n        // Stencil has completed instantiating the component.\n        customElements.whenDefined(cmpMeta.$tagName$).then(() => hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */);\n      }\n      if (Cstr.style) {\n        // this component has styles but we haven't registered them yet\n        let style = Cstr.style;\n        if (typeof style !== 'string') {\n          style = style[hostRef.$modeName$ = computeMode(elm)];\n        }\n        const scopeId = getScopeId(cmpMeta, hostRef.$modeName$);\n        if (!styles.has(scopeId)) {\n          const endRegisterStyles = createTime('registerStyles', cmpMeta.$tagName$);\n          registerStyle(scopeId, style, !!(cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */));\n          endRegisterStyles();\n        }\n      }\n    }\n    // we've successfully created a lazy instance\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    const schedule = () => scheduleUpdate(hostRef, true);\n    if (ancestorComponent && ancestorComponent['s-rc']) {\n      // this is the initial load and this component it has an ancestor component\n      // but the ancestor component has NOT fired its will update lifecycle yet\n      // so let's just cool our jets and wait for the ancestor to continue first\n      // this will get fired off when the ancestor component\n      // finally gets around to rendering its lazy self\n      // fire off the initial update\n      ancestorComponent['s-rc'].push(schedule);\n    } else {\n      schedule();\n    }\n  });\n  return function initializeComponent(_x4, _x5, _x6, _x7) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nconst fireConnectedCallback = instance => {\n  {\n    safeCall(instance, 'connectedCallback');\n  }\n};\nconst connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime('connectedCallback', cmpMeta.$tagName$);\n    if (!(hostRef.$flags$ & 1 /* HOST_FLAGS.hasConnected */)) {\n      // first time this component has connected\n      hostRef.$flags$ |= 1 /* HOST_FLAGS.hasConnected */;\n      let hostId;\n      {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n            const scopeId = addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute('s-mode'));\n            elm.classList.remove(scopeId + '-h', scopeId + '-s');\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (!hostId) {\n        // initUpdate\n        // if the slot polyfill is required we'll need to put some nodes\n        // in here to act as original content anchors as we move nodes around\n        // host element has been connected to the DOM\n        if (\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* CMP_FLAGS.hasSlotRelocation */ | 8 /* CMP_FLAGS.needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      {\n        // find the first ancestor component (if there is one) and register\n        // this component as one of the actively loading child components for its ancestor\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          // climb up the ancestors looking for the first\n          // component that hasn't finished its lifecycle update yet\n          if (ancestorComponent.nodeType === 1 /* NODE_TYPE.ElementNode */ && ancestorComponent.hasAttribute('s-id') && ancestorComponent['s-p'] || ancestorComponent['s-p']) {\n            // we found this components first ancestor component\n            // keep a reference to this component's ancestor component\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      // Lazy properties\n      // https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n      if (cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* MEMBER_FLAGS.Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      // not the first time this has connected\n      // reattach any event listeners to the host\n      // since they would have been removed when disconnected\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);\n      // fire off connectedCallback() on component instance\n      if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nconst setContentReference = elm => {\n  // only required when we're NOT using native shadow dom (slot)\n  // or this browser doesn't support native shadow dom\n  // and this host element was NOT created with SSR\n  // let's pick out the inner content for slot projection\n  // create a node to represent where the original\n  // content was first placed, which is useful later on\n  const contentRefElm = elm['s-cr'] = doc.createComment('');\n  contentRefElm['s-cn'] = true;\n  elm.insertBefore(contentRefElm, elm.firstChild);\n};\nconst disconnectInstance = instance => {\n  {\n    safeCall(instance, 'disconnectedCallback');\n  }\n};\nconst disconnectedCallback = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (elm) {\n    if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n      const hostRef = getHostRef(elm);\n      {\n        if (hostRef.$rmListeners$) {\n          hostRef.$rmListeners$.map(rmListener => rmListener());\n          hostRef.$rmListeners$ = undefined;\n        }\n      }\n      if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n        disconnectInstance(hostRef.$lazyInstance$);\n      } else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n      }\n    }\n  });\n  return function disconnectedCallback(_x8) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nconst bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  const endBootstrap = createTime();\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements = win.customElements;\n  const head = doc.head;\n  const metaCharset = /*@__PURE__*/head.querySelector('meta[charset]');\n  const dataStyles = /*@__PURE__*/doc.createElement('style');\n  const deferredConnectedCallbacks = [];\n  const styles = /*@__PURE__*/doc.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n  let appLoadFallback;\n  let isBootstrapping = true;\n  let i = 0;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || './', doc.baseURI).href;\n  {\n    // If the app is already hydrated there is not point to disable the\n    // async queue. This will improve the first input delay\n    plt.$flags$ |= 2 /* PLATFORM_FLAGS.appLoaded */;\n  }\n  {\n    for (; i < styles.length; i++) {\n      registerStyle(styles[i].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles[i].innerHTML), true);\n    }\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      // Check if we are using slots outside the shadow DOM in this component.\n      // We'll use this information later to add styles for `slot-fb` elements\n      if (cmpMeta.$flags$ & 4 /* CMP_FLAGS.hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      {\n        cmpMeta.$watchers$ = (_a = compactMeta[4]) !== null && _a !== void 0 ? _a : {};\n      }\n      const tagName = cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          // @ts-ignore\n          super(self);\n          self = this;\n          registerHost(self, cmpMeta);\n          if (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n            // this component is using shadow dom\n            // and this browser supports shadow dom\n            // add the read-only property \"shadowRoot\" to the host element\n            // adding the shadow root build conditionals to minimize runtime\n            {\n              {\n                self.attachShadow({\n                  mode: 'open',\n                  delegatesFocus: !!(cmpMeta.$flags$ & 16 /* CMP_FLAGS.shadowDelegatesFocus */)\n                });\n              }\n            }\n          }\n        }\n        connectedCallback() {\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            // connectedCallback will be processed once all components have been registered\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* PROXY_FLAGS.isElementConstructor */));\n      }\n    });\n  });\n  // Only bother generating CSS if we have components\n  // TODO(STENCIL-1118): Add test cases for CSS content based on conditionals\n  if (cmpTags.length > 0) {\n    // Add styles for `slot-fb` elements if any of our components are using slots outside the Shadow DOM\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    // Add hydration styles\n    {\n      dataStyles.textContent += cmpTags + HYDRATED_CSS;\n    }\n    // If we have styles, add them to the DOM\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute('data-styles', '');\n      // Apply CSP nonce to the style tag if it exists\n      const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute('nonce', nonce);\n      }\n      // Insert the styles into the document head\n      // NOTE: this _needs_ to happen last so we can ensure the nonce (and other attributes) are applied\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  // Process deferred connectedCallbacks now all components have been registered\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  // Fallback appLoad event\n  endBootstrap();\n};\nconst addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (listeners) {\n    listeners.map(([flags, name, method]) => {\n      const target = getHostListenerTarget(elm, flags);\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nconst hostListenerProxy = (hostRef, methodName) => ev => {\n  try {\n    {\n      if (hostRef.$flags$ & 256 /* HOST_FLAGS.isListenReady */) {\n        // instance is ready, let's call it's member method for this event\n        hostRef.$lazyInstance$[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nconst getHostListenerTarget = (elm, flags) => {\n  if (flags & 4 /* LISTENER_FLAGS.TargetDocument */) return doc;\n  if (flags & 8 /* LISTENER_FLAGS.TargetWindow */) return win;\n  if (flags & 16 /* LISTENER_FLAGS.TargetBody */) return doc.body;\n  return elm;\n};\n// prettier-ignore\nconst hostListenerOpts = flags => supportsListenerOptions ? {\n  passive: (flags & 1 /* LISTENER_FLAGS.Passive */) !== 0,\n  capture: (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0\n} : (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0;\n/**\n * Assigns the given value to the nonce property on the runtime platform object.\n * During runtime, this value is used to set the nonce attribute on all dynamically created script and style tags.\n * @param nonce The value to be assigned to the platform nonce property.\n * @returns void\n */\nconst setNonce = nonce => plt.$nonce$ = nonce;\n/**\n * A WeakMap mapping runtime component references to their corresponding host reference\n * instances.\n *\n * **Note**: If we're in an HMR context we need to store a reference to this\n * value on `window` in order to maintain the mapping of {@link d.RuntimeRef}\n * to {@link d.HostRef} across HMR updates.\n *\n * This is necessary because when HMR updates for a component are processed by\n * the browser-side dev server client the JS bundle for that component is\n * re-fetched. Since the module containing {@link hostRefs} is included in\n * that bundle, if we do not store a reference to it the new iteration of the\n * component will not have access to the previous hostRef map, leading to a\n * bug where the new version of the component cannot properly initialize.\n */\nconst hostRefs = new WeakMap();\n/**\n * Given a {@link d.RuntimeRef} retrieve the corresponding {@link d.HostRef}\n *\n * @param ref the runtime ref of interest\n * @returns the Host reference (if found) or undefined\n */\nconst getHostRef = ref => hostRefs.get(ref);\n/**\n * Register a lazy instance with the {@link hostRefs} object so it's\n * corresponding {@link d.HostRef} can be retrieved later.\n *\n * @param lazyInstance the lazy instance of interest\n * @param hostRef that instances `HostRef` object\n * @returns a reference to the host ref WeakMap\n */\nconst registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\n/**\n * Register a host element for a Stencil component, setting up various metadata\n * and callbacks based on {@link BUILD} flags as well as the component's runtime\n * metadata.\n *\n * @param hostElement the host element to register\n * @param cmpMeta runtime metadata for that component\n * @returns a reference to the host ref WeakMap\n */\nconst registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: new Map()\n  };\n  {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement['s-p'] = [];\n    hostElement['s-rc'] = [];\n  }\n  addHostEventListeners(hostElement, hostRef, cmpMeta.$listeners$);\n  return hostRefs.set(hostElement, hostRef);\n};\nconst isMemberInElement = (elm, memberName) => memberName in elm;\nconst consoleError = (e, el) => (0, console.error)(e, el);\nconst cmpModules = /*@__PURE__*/new Map();\nconst loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  // loadModuleImport\n  const exportName = cmpMeta.$tagName$.replace(/-/g, '_');\n  const bundleId = cmpMeta.$lazyBundleId$;\n  const module = cmpModules.get(bundleId);\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(/* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${''}`).then(importedModule => {\n    {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\nconst styles = /*@__PURE__*/new Map();\nconst modeResolutionChain = [];\nconst win = typeof window !== 'undefined' ? window : {};\nconst doc = win.document || {\n  head: {}\n};\nconst plt = {\n  $flags$: 0,\n  $resourcesUrl$: '',\n  jmp: h => h(),\n  raf: h => requestAnimationFrame(h),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nconst setPlatformHelpers = helpers => {\n  Object.assign(plt, helpers);\n};\nconst supportsShadow =\n// TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\ntrue;\nconst supportsListenerOptions = /*@__PURE__*/(() => {\n  let supportsListenerOptions = false;\n  try {\n    doc.addEventListener('e', null, Object.defineProperty({}, 'passive', {\n      get() {\n        supportsListenerOptions = true;\n      }\n    }));\n  } catch (e) {}\n  return supportsListenerOptions;\n})();\nconst promiseResolve = v => Promise.resolve(v);\nconst supportsConstructableStylesheets = /*@__PURE__*/(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === 'function';\n  } catch (e) {}\n  return false;\n})();\nconst queueDomReads = [];\nconst queueDomWrites = [];\nconst queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* PLATFORM_FLAGS.queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nconst consume = queue => {\n  for (let i = 0; i < queue.length; i++) {\n    try {\n      queue[i](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nconst flush = () => {\n  // always force a bunch of medium callbacks to run, but still have\n  // a throttle on how many can run in a certain time\n  // DOM READS!!!\n  consume(queueDomReads);\n  // DOM WRITES!!!\n  {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      // still more to do yet, but we've run out of time\n      // let's let this thing cool off and try again in the next tick\n      plt.raf(flush);\n    }\n  }\n};\nconst nextTick = cb => promiseResolve().then(cb);\nconst readTask = /*@__PURE__*/queueTask(queueDomReads, false);\nconst writeTask = /*@__PURE__*/queueTask(queueDomWrites, true);\nexport { Build as B, Host as H, setPlatformHelpers as a, bootstrapLazy as b, setMode as c, createEvent as d, readTask as e, getElement as f, getMode as g, h, forceUpdate as i, getAssetPath as j, promiseResolve as p, registerInstance as r, setNonce as s, writeTask as w };", "map": {"version": 3, "names": ["NAMESPACE", "BUILD", "allRenderFn", "appendChildSlotFix", "asyncLoading", "asyncQueue", "attachStyles", "cloneNodeFix", "cmpDidLoad", "cmpDidRender", "cmpDidUnload", "cmpDidUpdate", "cmpShouldUpdate", "cmpWillLoad", "cmpWillRender", "cmpWillUpdate", "connectedCallback", "constructableCSS", "cssAnnotations", "devTools", "disconnectedCallback", "element", "event", "experimentalScopedSlotChanges", "experimentalSlotFixes", "formAssociated", "hasRenderFn", "hostListener", "hostListenerTarget", "hostListenerTargetBody", "hostListenerTargetDocument", "hostListenerTargetParent", "hostListenerTargetWindow", "hotModuleReplacement", "hydrateClientSide", "hydrateServerSide", "hydratedAttribute", "hydratedClass", "initializeNextTick", "invisiblePrehydration", "isDebug", "isDev", "isTesting", "lazyLoad", "lifecycle", "lifecycleDOMEvents", "member", "method", "mode", "observeAttribute", "profile", "prop", "propBoolean", "propMutable", "propNumber", "propString", "reflect", "scoped", "scopedSlotTextContentFix", "scriptDataOpts", "shadowDelegatesFocus", "shadowDom", "slot", "slotChildNodesFix", "slotRelocation", "state", "style", "svg", "taskQueue", "transformTagName", "updatable", "vdomAttribute", "vdomClass", "vdomFunctional", "vdomKey", "vdomListener", "vdomPropOrAttr", "vdomRef", "v<PERSON><PERSON><PERSON>", "vdomStyle", "vdomText", "vdomXlink", "watchCallback", "scopeId", "contentRef", "hostTagName", "useNativeShadowDom", "checkSlotFallbackVisibility", "checkSlotRelocate", "isSvgMode", "queuePending", "Build", "<PERSON><PERSON><PERSON><PERSON>", "isServer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "assetUrl", "URL", "plt", "$resourcesUrl$", "origin", "win", "location", "href", "pathname", "createTime", "fnName", "tagName", "uniqueTime", "key", "measureText", "CONTENT_REF_ID", "ORG_LOCATION_ID", "SLOT_NODE_ID", "TEXT_NODE_ID", "HYDRATE_ID", "HYDRATED_STYLE_ID", "HYDRATE_CHILD_ID", "HYDRATED_CSS", "SLOT_FB_CSS", "XLINK_NS", "EMPTY_OBJ", "SVG_NS", "HTML_NS", "isDef", "v", "isComplexType", "o", "queryNonceMetaTagContent", "doc", "_a", "_b", "_c", "head", "querySelector", "getAttribute", "undefined", "h", "nodeName", "vnodeData", "children", "child", "slotName", "simple", "lastSimple", "vNodeChildren", "walk", "c", "i", "length", "Array", "isArray", "String", "$text$", "push", "newVNode", "name", "classData", "className", "class", "Object", "keys", "filter", "k", "join", "vdomFnUtils", "vnode", "$attrs$", "$children$", "$key$", "$name$", "tag", "text", "$flags$", "$tag$", "$elm$", "Host", "isHost", "node", "for<PERSON>ach", "cb", "map", "convertToPublic", "convertToPrivate", "vattrs", "vchildren", "vkey", "vname", "vtag", "vtext", "assign", "initializeClientHydrate", "hostElm", "hostId", "hostRef", "endHydrate", "shadowRoot", "childRenderNodes", "slotNodes", "shadowRootNodes", "$vnode$", "$orgLocNodes$", "initializeDocumentHydrate", "body", "Map", "removeAttribute", "clientHydrate", "orgLocationId", "$hostId$", "$nodeId$", "orgLocationNode", "get", "supportsShadow", "parentNode", "insertBefore", "nextS<PERSON>ling", "delete", "shadowRootNode", "append<PERSON><PERSON><PERSON>", "parentVNode", "childNodeType", "childIdSplt", "childVNode", "nodeType", "split", "$depth$", "$index$", "toLowerCase", "childNodes", "nodeValue", "textContent", "remove", "createElement", "setAttribute", "orgLocNodes", "set", "computeMode", "elm", "modeResolutionChain", "find", "m", "setMode", "handler", "getMode", "ref", "getHostRef", "$modeName$", "parsePropertyValue", "propValue", "propType", "parseFloat", "getElement", "$hostElement$", "createEvent", "flags", "emit", "detail", "emitEvent", "bubbles", "composed", "cancelable", "opts", "ev", "ce", "dispatchEvent", "rootAppliedStyles", "WeakMap", "registerStyle", "cssText", "allowCS", "styles", "supportsConstructableStylesheets", "CSSStyleSheet", "replaceSync", "addStyle", "styleContainerNode", "cmpMeta", "getScopeId", "appliedStyles", "styleElm", "Set", "has", "host", "innerHTML", "nonce", "$nonce$", "add", "adoptedStyleSheets", "includes", "$cmpMeta$", "endAttachStyles", "$tagName$", "getRootNode", "classList", "cmp", "convertScopedToShadow", "css", "replace", "setAccessor", "memberName", "oldValue", "newValue", "isSvg", "isProp", "isMemberInElement", "ln", "oldClasses", "parseClassList", "newClasses", "removeProperty", "setProperty", "slice", "capture", "endsWith", "CAPTURE_EVENT_SUFFIX", "CAPTURE_EVENT_REGEX", "rel", "ael", "isComplex", "n", "e", "xlink", "removeAttributeNS", "setAttributeNS", "parseClassListRegex", "value", "RegExp", "updateElement", "oldVnode", "newVnode", "oldVnodeAttrs", "newVnodeAttrs", "sortedAttrNames", "attrNames", "attr", "createElm", "oldParentVNode", "newParentVNode", "childIndex", "parentElm", "childNode", "oldVNode", "createTextNode", "createElementNS", "putBackInOriginalLocation", "recursive", "oldSlotChildNodes", "from", "parentReferenceNode", "referenceNode", "addVnodes", "before", "vnodes", "startIdx", "endIdx", "containerElm", "removeVnodes", "index", "nullifyVNodeRefs", "update<PERSON><PERSON><PERSON>n", "oldCh", "newCh", "isInitialRender", "oldStartIdx", "newStartIdx", "idxInOld", "oldEndIdx", "oldStartVnode", "oldEndVnode", "newEndIdx", "newStartVnode", "newEndVnode", "elmToMove", "isSameVnode", "patch", "leftVNode", "rightVNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultHolder", "data", "updateFallbackSlotVisibility", "hidden", "siblingNode", "trim", "relocateNodes", "markSlotContentForRelocation", "hostContentNodes", "j", "isNodeLocatedInSlot", "relocateNodeData", "r", "$nodeToRelocate$", "$slotRefNode$", "relocateNode", "some", "nodeToRelocate", "vNode", "renderVdom", "renderFnResults", "isInitialLoad", "_d", "rootVnode", "$attrsToReflect$", "propName", "attribute", "hasAttribute", "relocateData", "slotRefNode", "parentNodeRef", "insertBeforeNode", "previousSibling", "refNode", "attachToAncestor", "ancestorComponent", "$onRenderResolve$", "Promise", "scheduleUpdate", "$ancestorComponent$", "dispatch", "dispatchHooks", "writeTask", "endSchedule", "instance", "$lazyInstance$", "<PERSON><PERSON><PERSON><PERSON>", "$queuedListeners$", "methodName", "safeCall", "enqueue", "updateComponent", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "_ref", "_asyncToGenerator", "endUpdate", "rc", "endRender", "callRender", "childrenPromises", "postUpdate", "postUpdateComponent", "all", "_x", "_x2", "_x3", "apply", "arguments", "render", "consoleError", "endPostUpdate", "addHydratedFlag", "$onReadyResolve$", "appDidLoad", "$onInstanceResolve$", "nextTick", "forceUpdate", "isConnected", "who", "documentElement", "namespace", "arg", "getValue", "$instanceValues$", "setValue", "newVal", "oldVal", "$members$", "areBothNaN", "Number", "isNaN", "didValueChange", "$watchers$", "watchMethods", "watchMethodName", "proxyComponent", "Cstr", "prototype", "watchers", "members", "entries", "memberFlags", "defineProperty", "configurable", "enumerable", "args", "$onInstancePromise$", "attrNameToPropName", "attributeChangedCallback", "attrName", "jmp", "hasOwnProperty", "entry", "callback<PERSON><PERSON>", "call", "observedAttributes", "_", "initializeComponent", "_ref2", "hmrVersionId", "bundleId", "$lazyBundleId$", "loadModule", "endLoad", "isProxied", "endNewInstance", "fireConnectedCallback", "constructor", "customElements", "whenDefined", "endRegisterStyles", "schedule", "_x4", "_x5", "_x6", "_x7", "endConnected", "setContentReference", "addHostEventListeners", "$listeners$", "$onReadyPromise$", "contentRefElm", "createComment", "<PERSON><PERSON><PERSON><PERSON>", "disconnectInstance", "_ref3", "$rmListeners$", "rmListener", "_x8", "bootstrapLazy", "lazyB<PERSON>les", "options", "endBootstrap", "cmpTags", "exclude", "metaCharset", "dataStyles", "deferredConnectedCallbacks", "querySelectorAll", "appLoadFallback", "isBootstrapping", "resourcesUrl", "baseURI", "hasSlotRelocation", "lazyBundle", "compactMeta", "HostElement", "HTMLElement", "self", "registerHost", "attachShadow", "delegatesFocus", "clearTimeout", "componentOnReady", "define", "setTimeout", "listeners", "attachParentListeners", "target", "getHostListenerTarget", "hostListenerProxy", "hostListenerOpts", "supportsListenerOptions", "passive", "setNonce", "hostRefs", "registerInstance", "lazyInstance", "hostElement", "el", "console", "error", "cmpModules", "exportName", "module", "importedModule", "window", "document", "raf", "requestAnimationFrame", "eventName", "listener", "addEventListener", "removeEventListener", "CustomEvent", "setPlatformHelpers", "helpers", "promiseResolve", "resolve", "queueDomReads", "queueDomWrites", "queueTask", "queue", "write", "flush", "consume", "performance", "now", "readTask", "B", "H", "a", "b", "d", "f", "g", "p", "s", "w"], "sources": ["D:/2025/agmuicon/node_modules/@ionic/core/dist/esm/index-a1a47f01.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst NAMESPACE = 'ionic';\nconst BUILD = /* ionic */ { allRenderFn: false, appendChildSlotFix: false, asyncLoading: true, asyncQueue: false, attachStyles: true, cloneNodeFix: false, cmpDidLoad: true, cmpDidRender: true, cmpDidUnload: false, cmpDidUpdate: true, cmpShouldUpdate: false, cmpWillLoad: true, cmpWillRender: true, cmpWillUpdate: false, connectedCallback: true, constructableCSS: true, cssAnnotations: true, devTools: false, disconnectedCallback: true, element: false, event: true, experimentalScopedSlotChanges: false, experimentalSlotFixes: false, formAssociated: false, hasRenderFn: true, hostListener: true, hostListenerTarget: true, hostListenerTargetBody: true, hostListenerTargetDocument: true, hostListenerTargetParent: false, hostListenerTargetWindow: true, hotModuleReplacement: false, hydrateClientSide: true, hydrateServerSide: false, hydratedAttribute: false, hydratedClass: true, initializeNextTick: false, invisiblePrehydration: true, isDebug: false, isDev: false, isTesting: false, lazyLoad: true, lifecycle: true, lifecycleDOMEvents: false, member: true, method: true, mode: true, observeAttribute: true, profile: false, prop: true, propBoolean: true, propMutable: true, propNumber: true, propString: true, reflect: true, scoped: true, scopedSlotTextContentFix: false, scriptDataOpts: false, shadowDelegatesFocus: true, shadowDom: true, slot: true, slotChildNodesFix: false, slotRelocation: true, state: true, style: true, svg: true, taskQueue: true, transformTagName: false, updatable: true, vdomAttribute: true, vdomClass: true, vdomFunctional: true, vdomKey: true, vdomListener: true, vdomPropOrAttr: true, vdomRef: true, vdomRender: true, vdomStyle: true, vdomText: true, vdomXlink: true, watchCallback: true };\n\n/**\n * Virtual DOM patching algorithm based on Snabbdom by\n * Simon Friis Vindum (@paldepind)\n * Licensed under the MIT License\n * https://github.com/snabbdom/snabbdom/blob/master/LICENSE\n *\n * Modified for Stencil's renderer and slot projection\n */\nlet scopeId;\nlet contentRef;\nlet hostTagName;\nlet useNativeShadowDom = false;\nlet checkSlotFallbackVisibility = false;\nlet checkSlotRelocate = false;\nlet isSvgMode = false;\nlet queuePending = false;\nconst Build = {\n    isDev: false,\n    isBrowser: true,\n    isServer: false,\n    isTesting: false,\n};\nconst getAssetPath = (path) => {\n    const assetUrl = new URL(path, plt.$resourcesUrl$);\n    return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nconst createTime = (fnName, tagName = '') => {\n    {\n        return () => {\n            return;\n        };\n    }\n};\nconst uniqueTime = (key, measureText) => {\n    {\n        return () => {\n            return;\n        };\n    }\n};\nconst CONTENT_REF_ID = 'r';\nconst ORG_LOCATION_ID = 'o';\nconst SLOT_NODE_ID = 's';\nconst TEXT_NODE_ID = 't';\nconst HYDRATE_ID = 's-id';\nconst HYDRATED_STYLE_ID = 'sty-id';\nconst HYDRATE_CHILD_ID = 'c-id';\nconst HYDRATED_CSS = '{visibility:hidden}.hydrated{visibility:inherit}';\n/**\n * Constant for styles to be globally applied to `slot-fb` elements for pseudo-slot behavior.\n *\n * Two cascading rules must be used instead of a `:not()` selector due to Stencil browser\n * support as of Stencil v4.\n */\nconst SLOT_FB_CSS = 'slot-fb{display:contents}slot-fb[hidden]{display:none}';\nconst XLINK_NS = 'http://www.w3.org/1999/xlink';\n/**\n * Default style mode id\n */\n/**\n * Reusable empty obj/array\n * Don't add values to these!!\n */\nconst EMPTY_OBJ = {};\n/**\n * Namespaces\n */\nconst SVG_NS = 'http://www.w3.org/2000/svg';\nconst HTML_NS = 'http://www.w3.org/1999/xhtml';\nconst isDef = (v) => v != null;\n/**\n * Check whether a value is a 'complex type', defined here as an object or a\n * function.\n *\n * @param o the value to check\n * @returns whether it's a complex type or not\n */\nconst isComplexType = (o) => {\n    // https://jsperf.com/typeof-fn-object/5\n    o = typeof o;\n    return o === 'object' || o === 'function';\n};\n/**\n * Helper method for querying a `meta` tag that contains a nonce value\n * out of a DOM's head.\n *\n * @param doc The DOM containing the `head` to query against\n * @returns The content of the meta tag representing the nonce value, or `undefined` if no tag\n * exists or the tag has no content.\n */\nfunction queryNonceMetaTagContent(doc) {\n    var _a, _b, _c;\n    return (_c = (_b = (_a = doc.head) === null || _a === void 0 ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) === null || _b === void 0 ? void 0 : _b.getAttribute('content')) !== null && _c !== void 0 ? _c : undefined;\n}\n/**\n * Production h() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, child?: d.ChildType): d.VNode;\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, ...children: d.ChildType[]): d.VNode;\nconst h = (nodeName, vnodeData, ...children) => {\n    let child = null;\n    let key = null;\n    let slotName = null;\n    let simple = false;\n    let lastSimple = false;\n    const vNodeChildren = [];\n    const walk = (c) => {\n        for (let i = 0; i < c.length; i++) {\n            child = c[i];\n            if (Array.isArray(child)) {\n                walk(child);\n            }\n            else if (child != null && typeof child !== 'boolean') {\n                if ((simple = typeof nodeName !== 'function' && !isComplexType(child))) {\n                    child = String(child);\n                }\n                if (simple && lastSimple) {\n                    // If the previous child was simple (string), we merge both\n                    vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n                }\n                else {\n                    // Append a new vNode, if it's text, we create a text vNode\n                    vNodeChildren.push(simple ? newVNode(null, child) : child);\n                }\n                lastSimple = simple;\n            }\n        }\n    };\n    walk(children);\n    if (vnodeData) {\n        if (vnodeData.key) {\n            key = vnodeData.key;\n        }\n        if (vnodeData.name) {\n            slotName = vnodeData.name;\n        }\n        // normalize class / className attributes\n        {\n            const classData = vnodeData.className || vnodeData.class;\n            if (classData) {\n                vnodeData.class =\n                    typeof classData !== 'object'\n                        ? classData\n                        : Object.keys(classData)\n                            .filter((k) => classData[k])\n                            .join(' ');\n            }\n        }\n    }\n    if (typeof nodeName === 'function') {\n        // nodeName is a functional component\n        return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n    }\n    const vnode = newVNode(nodeName, null);\n    vnode.$attrs$ = vnodeData;\n    if (vNodeChildren.length > 0) {\n        vnode.$children$ = vNodeChildren;\n    }\n    {\n        vnode.$key$ = key;\n    }\n    {\n        vnode.$name$ = slotName;\n    }\n    return vnode;\n};\n/**\n * A utility function for creating a virtual DOM node from a tag and some\n * possible text content.\n *\n * @param tag the tag for this element\n * @param text possible text content for the node\n * @returns a newly-minted virtual DOM node\n */\nconst newVNode = (tag, text) => {\n    const vnode = {\n        $flags$: 0,\n        $tag$: tag,\n        $text$: text,\n        $elm$: null,\n        $children$: null,\n    };\n    {\n        vnode.$attrs$ = null;\n    }\n    {\n        vnode.$key$ = null;\n    }\n    {\n        vnode.$name$ = null;\n    }\n    return vnode;\n};\nconst Host = {};\n/**\n * Check whether a given node is a Host node or not\n *\n * @param node the virtual DOM node to check\n * @returns whether it's a Host node or not\n */\nconst isHost = (node) => node && node.$tag$ === Host;\n/**\n * Implementation of {@link d.FunctionalUtilities} for Stencil's VDom.\n *\n * Note that these functions convert from {@link d.VNode} to\n * {@link d.ChildNode} to give functional component developers a friendly\n * interface.\n */\nconst vdomFnUtils = {\n    forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n    map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate),\n};\n/**\n * Convert a {@link d.VNode} to a {@link d.ChildNode} in order to present a\n * friendlier public interface (hence, 'convertToPublic').\n *\n * @param node the virtual DOM node to convert\n * @returns a converted child node\n */\nconst convertToPublic = (node) => ({\n    vattrs: node.$attrs$,\n    vchildren: node.$children$,\n    vkey: node.$key$,\n    vname: node.$name$,\n    vtag: node.$tag$,\n    vtext: node.$text$,\n});\n/**\n * Convert a {@link d.ChildNode} back to an equivalent {@link d.VNode} in\n * order to use the resulting object in the virtual DOM. The initial object was\n * likely created as part of presenting a public API, so converting it back\n * involved making it 'private' again (hence, `convertToPrivate`).\n *\n * @param node the child node to convert\n * @returns a converted virtual DOM node\n */\nconst convertToPrivate = (node) => {\n    if (typeof node.vtag === 'function') {\n        const vnodeData = Object.assign({}, node.vattrs);\n        if (node.vkey) {\n            vnodeData.key = node.vkey;\n        }\n        if (node.vname) {\n            vnodeData.name = node.vname;\n        }\n        return h(node.vtag, vnodeData, ...(node.vchildren || []));\n    }\n    const vnode = newVNode(node.vtag, node.vtext);\n    vnode.$attrs$ = node.vattrs;\n    vnode.$children$ = node.vchildren;\n    vnode.$key$ = node.vkey;\n    vnode.$name$ = node.vname;\n    return vnode;\n};\n/**\n * Entrypoint of the client-side hydration process. Facilitates calls to hydrate the\n * document and all its nodes.\n *\n * This process will also reconstruct the shadow root and slot DOM nodes for components using shadow DOM.\n *\n * @param hostElm The element to hydrate.\n * @param tagName The element's tag name.\n * @param hostId The host ID assigned to the element by the server.\n * @param hostRef The host reference for the element.\n */\nconst initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n    const endHydrate = createTime('hydrateClient', tagName);\n    const shadowRoot = hostElm.shadowRoot;\n    const childRenderNodes = [];\n    const slotNodes = [];\n    const shadowRootNodes = shadowRoot ? [] : null;\n    const vnode = (hostRef.$vnode$ = newVNode(tagName, null));\n    if (!plt.$orgLocNodes$) {\n        initializeDocumentHydrate(doc.body, (plt.$orgLocNodes$ = new Map()));\n    }\n    hostElm[HYDRATE_ID] = hostId;\n    hostElm.removeAttribute(HYDRATE_ID);\n    clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n    childRenderNodes.map((c) => {\n        const orgLocationId = c.$hostId$ + '.' + c.$nodeId$;\n        const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n        const node = c.$elm$;\n        // Put the node back in its original location since the native Shadow DOM\n        // can handle rendering it its correct location now\n        if (orgLocationNode && supportsShadow && orgLocationNode['s-en'] === '') {\n            orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n        }\n        if (!shadowRoot) {\n            node['s-hn'] = tagName;\n            if (orgLocationNode) {\n                node['s-ol'] = orgLocationNode;\n                node['s-ol']['s-nr'] = node;\n            }\n        }\n        plt.$orgLocNodes$.delete(orgLocationId);\n    });\n    if (shadowRoot) {\n        shadowRootNodes.map((shadowRootNode) => {\n            if (shadowRootNode) {\n                shadowRoot.appendChild(shadowRootNode);\n            }\n        });\n    }\n    endHydrate();\n};\n/**\n * Recursively constructs the virtual node tree for a host element and its children.\n * The tree is constructed by parsing the annotations set on the nodes by the server.\n *\n * In addition to constructing the vNode tree, we also track information about the node's\n * descendants like which are slots, which should exist in the shadow root, and which\n * are nodes that should be rendered as children of the parent node.\n *\n * @param parentVNode The vNode representing the parent node.\n * @param childRenderNodes An array of all child nodes in the parent's node tree.\n * @param slotNodes An array of all slot nodes in the parent's node tree.\n * @param shadowRootNodes An array all nodes that should be rendered in the shadow root in the parent's node tree.\n * @param hostElm The parent element.\n * @param node The node to construct the vNode tree for.\n * @param hostId The host ID assigned to the element by the server.\n */\nconst clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n    let childNodeType;\n    let childIdSplt;\n    let childVNode;\n    let i;\n    if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n        childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n        if (childNodeType) {\n            // got the node data from the element's attribute\n            // `${hostId}.${nodeId}.${depth}.${index}`\n            childIdSplt = childNodeType.split('.');\n            if (childIdSplt[0] === hostId || childIdSplt[0] === '0') {\n                childVNode = {\n                    $flags$: 0,\n                    $hostId$: childIdSplt[0],\n                    $nodeId$: childIdSplt[1],\n                    $depth$: childIdSplt[2],\n                    $index$: childIdSplt[3],\n                    $tag$: node.tagName.toLowerCase(),\n                    $elm$: node,\n                    $attrs$: null,\n                    $children$: null,\n                    $key$: null,\n                    $name$: null,\n                    $text$: null,\n                };\n                childRenderNodes.push(childVNode);\n                node.removeAttribute(HYDRATE_CHILD_ID);\n                // this is a new child vnode\n                // so ensure its parent vnode has the vchildren array\n                if (!parentVNode.$children$) {\n                    parentVNode.$children$ = [];\n                }\n                // add our child vnode to a specific index of the vnode's children\n                parentVNode.$children$[childVNode.$index$] = childVNode;\n                // this is now the new parent vnode for all the next child checks\n                parentVNode = childVNode;\n                if (shadowRootNodes && childVNode.$depth$ === '0') {\n                    shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n                }\n            }\n        }\n        // recursively drill down, end to start so we can remove nodes\n        for (i = node.childNodes.length - 1; i >= 0; i--) {\n            clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i], hostId);\n        }\n        if (node.shadowRoot) {\n            // keep drilling down through the shadow root nodes\n            for (i = node.shadowRoot.childNodes.length - 1; i >= 0; i--) {\n                clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i], hostId);\n            }\n        }\n    }\n    else if (node.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n        // `${COMMENT_TYPE}.${hostId}.${nodeId}.${depth}.${index}`\n        childIdSplt = node.nodeValue.split('.');\n        if (childIdSplt[1] === hostId || childIdSplt[1] === '0') {\n            // comment node for either the host id or a 0 host id\n            childNodeType = childIdSplt[0];\n            childVNode = {\n                $flags$: 0,\n                $hostId$: childIdSplt[1],\n                $nodeId$: childIdSplt[2],\n                $depth$: childIdSplt[3],\n                $index$: childIdSplt[4],\n                $elm$: node,\n                $attrs$: null,\n                $children$: null,\n                $key$: null,\n                $name$: null,\n                $tag$: null,\n                $text$: null,\n            };\n            if (childNodeType === TEXT_NODE_ID) {\n                childVNode.$elm$ = node.nextSibling;\n                if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* NODE_TYPE.TextNode */) {\n                    childVNode.$text$ = childVNode.$elm$.textContent;\n                    childRenderNodes.push(childVNode);\n                    // remove the text comment since it's no longer needed\n                    node.remove();\n                    if (!parentVNode.$children$) {\n                        parentVNode.$children$ = [];\n                    }\n                    parentVNode.$children$[childVNode.$index$] = childVNode;\n                    if (shadowRootNodes && childVNode.$depth$ === '0') {\n                        shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n                    }\n                }\n            }\n            else if (childVNode.$hostId$ === hostId) {\n                // this comment node is specifically for this host id\n                if (childNodeType === SLOT_NODE_ID) {\n                    // `${SLOT_NODE_ID}.${hostId}.${nodeId}.${depth}.${index}.${slotName}`;\n                    childVNode.$tag$ = 'slot';\n                    if (childIdSplt[5]) {\n                        node['s-sn'] = childVNode.$name$ = childIdSplt[5];\n                    }\n                    else {\n                        node['s-sn'] = '';\n                    }\n                    node['s-sr'] = true;\n                    if (shadowRootNodes) {\n                        // browser support shadowRoot and this is a shadow dom component\n                        // create an actual slot element\n                        childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n                        if (childVNode.$name$) {\n                            // add the slot name attribute\n                            childVNode.$elm$.setAttribute('name', childVNode.$name$);\n                        }\n                        // insert the new slot element before the slot comment\n                        node.parentNode.insertBefore(childVNode.$elm$, node);\n                        // remove the slot comment since it's not needed for shadow\n                        node.remove();\n                        if (childVNode.$depth$ === '0') {\n                            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n                        }\n                    }\n                    slotNodes.push(childVNode);\n                    if (!parentVNode.$children$) {\n                        parentVNode.$children$ = [];\n                    }\n                    parentVNode.$children$[childVNode.$index$] = childVNode;\n                }\n                else if (childNodeType === CONTENT_REF_ID) {\n                    // `${CONTENT_REF_ID}.${hostId}`;\n                    if (shadowRootNodes) {\n                        // remove the content ref comment since it's not needed for shadow\n                        node.remove();\n                    }\n                    else {\n                        hostElm['s-cr'] = node;\n                        node['s-cn'] = true;\n                    }\n                }\n            }\n        }\n    }\n    else if (parentVNode && parentVNode.$tag$ === 'style') {\n        const vnode = newVNode(null, node.textContent);\n        vnode.$elm$ = node;\n        vnode.$index$ = '0';\n        parentVNode.$children$ = [vnode];\n    }\n};\n/**\n * Recursively locate any comments representing an original location for a node in a node's\n * children or shadowRoot children.\n *\n * @param node The node to search.\n * @param orgLocNodes A map of the original location annotation and the current node being searched.\n */\nconst initializeDocumentHydrate = (node, orgLocNodes) => {\n    if (node.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n        let i = 0;\n        for (; i < node.childNodes.length; i++) {\n            initializeDocumentHydrate(node.childNodes[i], orgLocNodes);\n        }\n        if (node.shadowRoot) {\n            for (i = 0; i < node.shadowRoot.childNodes.length; i++) {\n                initializeDocumentHydrate(node.shadowRoot.childNodes[i], orgLocNodes);\n            }\n        }\n    }\n    else if (node.nodeType === 8 /* NODE_TYPE.CommentNode */) {\n        const childIdSplt = node.nodeValue.split('.');\n        if (childIdSplt[0] === ORG_LOCATION_ID) {\n            orgLocNodes.set(childIdSplt[1] + '.' + childIdSplt[2], node);\n            node.nodeValue = '';\n            // useful to know if the original location is\n            // the root light-dom of a shadow dom component\n            node['s-en'] = childIdSplt[3];\n        }\n    }\n};\n// Private\nconst computeMode = (elm) => modeResolutionChain.map((h) => h(elm)).find((m) => !!m);\n// Public\nconst setMode = (handler) => modeResolutionChain.push(handler);\nconst getMode = (ref) => getHostRef(ref).$modeName$;\n/**\n * Parse a new property value for a given property type.\n *\n * While the prop value can reasonably be expected to be of `any` type as far as TypeScript's type checker is concerned,\n * it is not safe to assume that the string returned by evaluating `typeof propValue` matches:\n *   1. `any`, the type given to `propValue` in the function signature\n *   2. the type stored from `propType`.\n *\n * This function provides the capability to parse/coerce a property's value to potentially any other JavaScript type.\n *\n * Property values represented in TSX preserve their type information. In the example below, the number 0 is passed to\n * a component. This `propValue` will preserve its type information (`typeof propValue === 'number'`). Note that is\n * based on the type of the value being passed in, not the type declared of the class member decorated with `@Prop`.\n * ```tsx\n * <my-cmp prop-val={0}></my-cmp>\n * ```\n *\n * HTML prop values on the other hand, will always a string\n *\n * @param propValue the new value to coerce to some type\n * @param propType the type of the prop, expressed as a binary number\n * @returns the parsed/coerced value\n */\nconst parsePropertyValue = (propValue, propType) => {\n    // ensure this value is of the correct prop type\n    if (propValue != null && !isComplexType(propValue)) {\n        if (propType & 4 /* MEMBER_FLAGS.Boolean */) {\n            // per the HTML spec, any string value means it is a boolean true value\n            // but we'll cheat here and say that the string \"false\" is the boolean false\n            return propValue === 'false' ? false : propValue === '' || !!propValue;\n        }\n        if (propType & 2 /* MEMBER_FLAGS.Number */) {\n            // force it to be a number\n            return parseFloat(propValue);\n        }\n        if (propType & 1 /* MEMBER_FLAGS.String */) {\n            // could have been passed as a number or boolean\n            // but we still want it as a string\n            return String(propValue);\n        }\n        // redundant return here for better minification\n        return propValue;\n    }\n    // not sure exactly what type we want\n    // so no need to change to a different type\n    return propValue;\n};\nconst getElement = (ref) => (getHostRef(ref).$hostElement$ );\nconst createEvent = (ref, name, flags) => {\n    const elm = getElement(ref);\n    return {\n        emit: (detail) => {\n            return emitEvent(elm, name, {\n                bubbles: !!(flags & 4 /* EVENT_FLAGS.Bubbles */),\n                composed: !!(flags & 2 /* EVENT_FLAGS.Composed */),\n                cancelable: !!(flags & 1 /* EVENT_FLAGS.Cancellable */),\n                detail,\n            });\n        },\n    };\n};\n/**\n * Helper function to create & dispatch a custom Event on a provided target\n * @param elm the target of the Event\n * @param name the name to give the custom Event\n * @param opts options for configuring a custom Event\n * @returns the custom Event\n */\nconst emitEvent = (elm, name, opts) => {\n    const ev = plt.ce(name, opts);\n    elm.dispatchEvent(ev);\n    return ev;\n};\nconst rootAppliedStyles = /*@__PURE__*/ new WeakMap();\nconst registerStyle = (scopeId, cssText, allowCS) => {\n    let style = styles.get(scopeId);\n    if (supportsConstructableStylesheets && allowCS) {\n        style = (style || new CSSStyleSheet());\n        if (typeof style === 'string') {\n            style = cssText;\n        }\n        else {\n            style.replaceSync(cssText);\n        }\n    }\n    else {\n        style = cssText;\n    }\n    styles.set(scopeId, style);\n};\nconst addStyle = (styleContainerNode, cmpMeta, mode) => {\n    var _a;\n    const scopeId = getScopeId(cmpMeta, mode);\n    const style = styles.get(scopeId);\n    // if an element is NOT connected then getRootNode() will return the wrong root node\n    // so the fallback is to always use the document for the root node in those cases\n    styleContainerNode = styleContainerNode.nodeType === 11 /* NODE_TYPE.DocumentFragment */ ? styleContainerNode : doc;\n    if (style) {\n        if (typeof style === 'string') {\n            styleContainerNode = styleContainerNode.head || styleContainerNode;\n            let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n            let styleElm;\n            if (!appliedStyles) {\n                rootAppliedStyles.set(styleContainerNode, (appliedStyles = new Set()));\n            }\n            if (!appliedStyles.has(scopeId)) {\n                if (styleContainerNode.host &&\n                    (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId}\"]`))) {\n                    // This is only happening on native shadow-dom, do not needs CSS var shim\n                    styleElm.innerHTML = style;\n                }\n                else {\n                    styleElm = doc.createElement('style');\n                    styleElm.innerHTML = style;\n                    // Apply CSP nonce to the style tag if it exists\n                    const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n                    if (nonce != null) {\n                        styleElm.setAttribute('nonce', nonce);\n                    }\n                    styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector('link'));\n                }\n                // Add styles for `slot-fb` elements if we're using slots outside the Shadow DOM\n                if (cmpMeta.$flags$ & 4 /* CMP_FLAGS.hasSlotRelocation */) {\n                    styleElm.innerHTML += SLOT_FB_CSS;\n                }\n                if (appliedStyles) {\n                    appliedStyles.add(scopeId);\n                }\n            }\n        }\n        else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {\n            styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n        }\n    }\n    return scopeId;\n};\nconst attachStyles = (hostRef) => {\n    const cmpMeta = hostRef.$cmpMeta$;\n    const elm = hostRef.$hostElement$;\n    const flags = cmpMeta.$flags$;\n    const endAttachStyles = createTime('attachStyles', cmpMeta.$tagName$);\n    const scopeId = addStyle(elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n    if (flags & 10 /* CMP_FLAGS.needsScopedEncapsulation */) {\n        // only required when we're NOT using native shadow dom (slot)\n        // or this browser doesn't support native shadow dom\n        // and this host element was NOT created with SSR\n        // let's pick out the inner content for slot projection\n        // create a node to represent where the original\n        // content was first placed, which is useful later on\n        // DOM WRITE!!\n        elm['s-sc'] = scopeId;\n        elm.classList.add(scopeId + '-h');\n        if (flags & 2 /* CMP_FLAGS.scopedCssEncapsulation */) {\n            elm.classList.add(scopeId + '-s');\n        }\n    }\n    endAttachStyles();\n};\nconst getScopeId = (cmp, mode) => 'sc-' + (mode && cmp.$flags$ & 32 /* CMP_FLAGS.hasMode */ ? cmp.$tagName$ + '-' + mode : cmp.$tagName$);\nconst convertScopedToShadow = (css) => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, '$1{');\n/**\n * Production setAccessor() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n/**\n * When running a VDom render set properties present on a VDom node onto the\n * corresponding HTML element.\n *\n * Note that this function has special functionality for the `class`,\n * `style`, `key`, and `ref` attributes, as well as event handlers (like\n * `onClick`, etc). All others are just passed through as-is.\n *\n * @param elm the HTMLElement onto which attributes should be set\n * @param memberName the name of the attribute to set\n * @param oldValue the old value for the attribute\n * @param newValue the new value for the attribute\n * @param isSvg whether we're in an svg context or not\n * @param flags bitflags for Vdom variables\n */\nconst setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n    if (oldValue !== newValue) {\n        let isProp = isMemberInElement(elm, memberName);\n        let ln = memberName.toLowerCase();\n        if (memberName === 'class') {\n            const classList = elm.classList;\n            const oldClasses = parseClassList(oldValue);\n            const newClasses = parseClassList(newValue);\n            classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));\n            classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));\n        }\n        else if (memberName === 'style') {\n            // update style attribute, css properties and values\n            {\n                for (const prop in oldValue) {\n                    if (!newValue || newValue[prop] == null) {\n                        if (prop.includes('-')) {\n                            elm.style.removeProperty(prop);\n                        }\n                        else {\n                            elm.style[prop] = '';\n                        }\n                    }\n                }\n            }\n            for (const prop in newValue) {\n                if (!oldValue || newValue[prop] !== oldValue[prop]) {\n                    if (prop.includes('-')) {\n                        elm.style.setProperty(prop, newValue[prop]);\n                    }\n                    else {\n                        elm.style[prop] = newValue[prop];\n                    }\n                }\n            }\n        }\n        else if (memberName === 'key')\n            ;\n        else if (memberName === 'ref') {\n            // minifier will clean this up\n            if (newValue) {\n                newValue(elm);\n            }\n        }\n        else if ((!isProp ) &&\n            memberName[0] === 'o' &&\n            memberName[1] === 'n') {\n            // Event Handlers\n            // so if the member name starts with \"on\" and the 3rd characters is\n            // a capital letter, and it's not already a member on the element,\n            // then we're assuming it's an event listener\n            if (memberName[2] === '-') {\n                // on- prefixed events\n                // allows to be explicit about the dom event to listen without any magic\n                // under the hood:\n                // <my-cmp on-click> // listens for \"click\"\n                // <my-cmp on-Click> // listens for \"Click\"\n                // <my-cmp on-ionChange> // listens for \"ionChange\"\n                // <my-cmp on-EVENTS> // listens for \"EVENTS\"\n                memberName = memberName.slice(3);\n            }\n            else if (isMemberInElement(win, ln)) {\n                // standard event\n                // the JSX attribute could have been \"onMouseOver\" and the\n                // member name \"onmouseover\" is on the window's prototype\n                // so let's add the listener \"mouseover\", which is all lowercased\n                memberName = ln.slice(2);\n            }\n            else {\n                // custom event\n                // the JSX attribute could have been \"onMyCustomEvent\"\n                // so let's trim off the \"on\" prefix and lowercase the first character\n                // and add the listener \"myCustomEvent\"\n                // except for the first character, we keep the event name case\n                memberName = ln[2] + memberName.slice(3);\n            }\n            if (oldValue || newValue) {\n                // Need to account for \"capture\" events.\n                // If the event name ends with \"Capture\", we'll update the name to remove\n                // the \"Capture\" suffix and make sure the event listener is setup to handle the capture event.\n                const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n                // Make sure we only replace the last instance of \"Capture\"\n                memberName = memberName.replace(CAPTURE_EVENT_REGEX, '');\n                if (oldValue) {\n                    plt.rel(elm, memberName, oldValue, capture);\n                }\n                if (newValue) {\n                    plt.ael(elm, memberName, newValue, capture);\n                }\n            }\n        }\n        else {\n            // Set property if it exists and it's not a SVG\n            const isComplex = isComplexType(newValue);\n            if ((isProp || (isComplex && newValue !== null)) && !isSvg) {\n                try {\n                    if (!elm.tagName.includes('-')) {\n                        const n = newValue == null ? '' : newValue;\n                        // Workaround for Safari, moving the <input> caret when re-assigning the same valued\n                        if (memberName === 'list') {\n                            isProp = false;\n                        }\n                        else if (oldValue == null || elm[memberName] != n) {\n                            elm[memberName] = n;\n                        }\n                    }\n                    else {\n                        elm[memberName] = newValue;\n                    }\n                }\n                catch (e) {\n                    /**\n                     * in case someone tries to set a read-only property, e.g. \"namespaceURI\", we just ignore it\n                     */\n                }\n            }\n            /**\n             * Need to manually update attribute if:\n             * - memberName is not an attribute\n             * - if we are rendering the host element in order to reflect attribute\n             * - if it's a SVG, since properties might not work in <svg>\n             * - if the newValue is null/undefined or 'false'.\n             */\n            let xlink = false;\n            {\n                if (ln !== (ln = ln.replace(/^xlink\\:?/, ''))) {\n                    memberName = ln;\n                    xlink = true;\n                }\n            }\n            if (newValue == null || newValue === false) {\n                if (newValue !== false || elm.getAttribute(memberName) === '') {\n                    if (xlink) {\n                        elm.removeAttributeNS(XLINK_NS, memberName);\n                    }\n                    else {\n                        elm.removeAttribute(memberName);\n                    }\n                }\n            }\n            else if ((!isProp || flags & 4 /* VNODE_FLAGS.isHost */ || isSvg) && !isComplex) {\n                newValue = newValue === true ? '' : newValue;\n                if (xlink) {\n                    elm.setAttributeNS(XLINK_NS, memberName, newValue);\n                }\n                else {\n                    elm.setAttribute(memberName, newValue);\n                }\n            }\n        }\n    }\n};\nconst parseClassListRegex = /\\s/;\n/**\n * Parsed a string of classnames into an array\n * @param value className string, e.g. \"foo bar baz\"\n * @returns list of classes, e.g. [\"foo\", \"bar\", \"baz\"]\n */\nconst parseClassList = (value) => (!value ? [] : value.split(parseClassListRegex));\nconst CAPTURE_EVENT_SUFFIX = 'Capture';\nconst CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + '$');\nconst updateElement = (oldVnode, newVnode, isSvgMode, memberName) => {\n    // if the element passed in is a shadow root, which is a document fragment\n    // then we want to be adding attrs/props to the shadow root's \"host\" element\n    // if it's not a shadow root, then we add attrs/props to the same element\n    const elm = newVnode.$elm$.nodeType === 11 /* NODE_TYPE.DocumentFragment */ && newVnode.$elm$.host\n        ? newVnode.$elm$.host\n        : newVnode.$elm$;\n    const oldVnodeAttrs = (oldVnode && oldVnode.$attrs$) || EMPTY_OBJ;\n    const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n    {\n        // remove attributes no longer present on the vnode by setting them to undefined\n        for (memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n            if (!(memberName in newVnodeAttrs)) {\n                setAccessor(elm, memberName, oldVnodeAttrs[memberName], undefined, isSvgMode, newVnode.$flags$);\n            }\n        }\n    }\n    // add new & update changed attributes\n    for (memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode, newVnode.$flags$);\n    }\n};\n/**\n * Sort a list of attribute names to ensure that all the attribute names which\n * are _not_ `\"ref\"` come before `\"ref\"`. Preserve the order of the non-ref\n * attributes.\n *\n * **Note**: if the supplied attributes do not include `'ref'` then the same\n * (by reference) array will be returned without modification.\n *\n * @param attrNames attribute names to sort\n * @returns a list of attribute names, sorted if they include `\"ref\"`\n */\nfunction sortedAttrNames(attrNames) {\n    return attrNames.includes('ref')\n        ? // we need to sort these to ensure that `'ref'` is the last attr\n            [...attrNames.filter((attr) => attr !== 'ref'), 'ref']\n        : // no need to sort, return the original array\n            attrNames;\n}\n/**\n * Create a DOM Node corresponding to one of the children of a given VNode.\n *\n * @param oldParentVNode the parent VNode from the previous render\n * @param newParentVNode the parent VNode from the current render\n * @param childIndex the index of the VNode, in the _new_ parent node's\n * children, for which we will create a new DOM node\n * @param parentElm the parent DOM node which our new node will be a child of\n * @returns the newly created node\n */\nconst createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n    var _a;\n    // tslint:disable-next-line: prefer-const\n    const newVNode = newParentVNode.$children$[childIndex];\n    let i = 0;\n    let elm;\n    let childNode;\n    let oldVNode;\n    if (!useNativeShadowDom) {\n        // remember for later we need to check to relocate nodes\n        checkSlotRelocate = true;\n        if (newVNode.$tag$ === 'slot') {\n            if (scopeId) {\n                // scoped css needs to add its scoped id to the parent element\n                parentElm.classList.add(scopeId + '-s');\n            }\n            newVNode.$flags$ |= newVNode.$children$\n                ? // slot element has fallback content\n                    2 /* VNODE_FLAGS.isSlotFallback */\n                : // slot element does not have fallback content\n                    1 /* VNODE_FLAGS.isSlotReference */;\n        }\n    }\n    if (newVNode.$text$ !== null) {\n        // create text node\n        elm = newVNode.$elm$ = doc.createTextNode(newVNode.$text$);\n    }\n    else if (newVNode.$flags$ & 1 /* VNODE_FLAGS.isSlotReference */) {\n        // create a slot reference node\n        elm = newVNode.$elm$ =\n            doc.createTextNode('');\n    }\n    else {\n        if (!isSvgMode) {\n            isSvgMode = newVNode.$tag$ === 'svg';\n        }\n        // create element\n        elm = newVNode.$elm$ = (doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, newVNode.$flags$ & 2 /* VNODE_FLAGS.isSlotFallback */\n                ? 'slot-fb'\n                : newVNode.$tag$)\n            );\n        if (isSvgMode && newVNode.$tag$ === 'foreignObject') {\n            isSvgMode = false;\n        }\n        // add css classes, attrs, props, listeners, etc.\n        {\n            updateElement(null, newVNode, isSvgMode);\n        }\n        if (isDef(scopeId) && elm['s-si'] !== scopeId) {\n            // if there is a scopeId and this is the initial render\n            // then let's add the scopeId as a css class\n            elm.classList.add((elm['s-si'] = scopeId));\n        }\n        if (newVNode.$children$) {\n            for (i = 0; i < newVNode.$children$.length; ++i) {\n                // create the node\n                childNode = createElm(oldParentVNode, newVNode, i, elm);\n                // return node could have been null\n                if (childNode) {\n                    // append our new node\n                    elm.appendChild(childNode);\n                }\n            }\n        }\n        {\n            if (newVNode.$tag$ === 'svg') {\n                // Only reset the SVG context when we're exiting <svg> element\n                isSvgMode = false;\n            }\n            else if (elm.tagName === 'foreignObject') {\n                // Reenter SVG context when we're exiting <foreignObject> element\n                isSvgMode = true;\n            }\n        }\n    }\n    // This needs to always happen so we can hide nodes that are projected\n    // to another component but don't end up in a slot\n    elm['s-hn'] = hostTagName;\n    {\n        if (newVNode.$flags$ & (2 /* VNODE_FLAGS.isSlotFallback */ | 1 /* VNODE_FLAGS.isSlotReference */)) {\n            // remember the content reference comment\n            elm['s-sr'] = true;\n            // remember the content reference comment\n            elm['s-cr'] = contentRef;\n            // remember the slot name, or empty string for default slot\n            elm['s-sn'] = newVNode.$name$ || '';\n            // remember the ref callback function\n            elm['s-rf'] = (_a = newVNode.$attrs$) === null || _a === void 0 ? void 0 : _a.ref;\n            // check if we've got an old vnode for this slot\n            oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n            if (oldVNode && oldVNode.$tag$ === newVNode.$tag$ && oldParentVNode.$elm$) {\n                {\n                    // we've got an old slot vnode and the wrapper is being replaced\n                    // so let's move the old slot content back to its original location\n                    putBackInOriginalLocation(oldParentVNode.$elm$, false);\n                }\n            }\n        }\n    }\n    return elm;\n};\nconst putBackInOriginalLocation = (parentElm, recursive) => {\n    plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    const oldSlotChildNodes = Array.from(parentElm.childNodes);\n    if (parentElm['s-sr'] && BUILD.experimentalSlotFixes) {\n        let node = parentElm;\n        while ((node = node.nextSibling)) {\n            if (node && node['s-sn'] === parentElm['s-sn'] && node['s-sh'] === hostTagName) {\n                oldSlotChildNodes.push(node);\n            }\n        }\n    }\n    for (let i = oldSlotChildNodes.length - 1; i >= 0; i--) {\n        const childNode = oldSlotChildNodes[i];\n        if (childNode['s-hn'] !== hostTagName && childNode['s-ol']) {\n            // and relocate it back to it's original location\n            parentReferenceNode(childNode).insertBefore(childNode, referenceNode(childNode));\n            // remove the old original location comment entirely\n            // later on the patch function will know what to do\n            // and move this to the correct spot if need be\n            childNode['s-ol'].remove();\n            childNode['s-ol'] = undefined;\n            // Reset so we can correctly move the node around again.\n            childNode['s-sh'] = undefined;\n            checkSlotRelocate = true;\n        }\n        if (recursive) {\n            putBackInOriginalLocation(childNode, recursive);\n        }\n    }\n    plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n};\n/**\n * Create DOM nodes corresponding to a list of {@link d.Vnode} objects and\n * add them to the DOM in the appropriate place.\n *\n * @param parentElm the DOM node which should be used as a parent for the new\n * DOM nodes\n * @param before a child of the `parentElm` which the new children should be\n * inserted before (optional)\n * @param parentVNode the parent virtual DOM node\n * @param vnodes the new child virtual DOM nodes to produce DOM nodes for\n * @param startIdx the index in the child virtual DOM nodes at which to start\n * creating DOM nodes (inclusive)\n * @param endIdx the index in the child virtual DOM nodes at which to stop\n * creating DOM nodes (inclusive)\n */\nconst addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n    let containerElm = ((parentElm['s-cr'] && parentElm['s-cr'].parentNode) || parentElm);\n    let childNode;\n    if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n        containerElm = containerElm.shadowRoot;\n    }\n    for (; startIdx <= endIdx; ++startIdx) {\n        if (vnodes[startIdx]) {\n            childNode = createElm(null, parentVNode, startIdx, parentElm);\n            if (childNode) {\n                vnodes[startIdx].$elm$ = childNode;\n                containerElm.insertBefore(childNode, referenceNode(before) );\n            }\n        }\n    }\n};\n/**\n * Remove the DOM elements corresponding to a list of {@link d.VNode} objects.\n * This can be used to, for instance, clean up after a list of children which\n * should no longer be shown.\n *\n * This function also handles some of Stencil's slot relocation logic.\n *\n * @param vnodes a list of virtual DOM nodes to remove\n * @param startIdx the index at which to start removing nodes (inclusive)\n * @param endIdx the index at which to stop removing nodes (inclusive)\n */\nconst removeVnodes = (vnodes, startIdx, endIdx) => {\n    for (let index = startIdx; index <= endIdx; ++index) {\n        const vnode = vnodes[index];\n        if (vnode) {\n            const elm = vnode.$elm$;\n            nullifyVNodeRefs(vnode);\n            if (elm) {\n                {\n                    // we're removing this element\n                    // so it's possible we need to show slot fallback content now\n                    checkSlotFallbackVisibility = true;\n                    if (elm['s-ol']) {\n                        // remove the original location comment\n                        elm['s-ol'].remove();\n                    }\n                    else {\n                        // it's possible that child nodes of the node\n                        // that's being removed are slot nodes\n                        putBackInOriginalLocation(elm, true);\n                    }\n                }\n                // remove the vnode's element from the dom\n                elm.remove();\n            }\n        }\n    }\n};\n/**\n * Reconcile the children of a new VNode with the children of an old VNode by\n * traversing the two collections of children, identifying nodes that are\n * conserved or changed, calling out to `patch` to make any necessary\n * updates to the DOM, and rearranging DOM nodes as needed.\n *\n * The algorithm for reconciling children works by analyzing two 'windows' onto\n * the two arrays of children (`oldCh` and `newCh`). We keep track of the\n * 'windows' by storing start and end indices and references to the\n * corresponding array entries. Initially the two 'windows' are basically equal\n * to the entire array, but we progressively narrow the windows until there are\n * no children left to update by doing the following:\n *\n * 1. Skip any `null` entries at the beginning or end of the two arrays, so\n *    that if we have an initial array like the following we'll end up dealing\n *    only with a window bounded by the highlighted elements:\n *\n *    [null, null, VNode1 , ... , VNode2, null, null]\n *                 ^^^^^^         ^^^^^^\n *\n * 2. Check to see if the elements at the head and tail positions are equal\n *    across the windows. This will basically detect elements which haven't\n *    been added, removed, or changed position, i.e. if you had the following\n *    VNode elements (represented as HTML):\n *\n *    oldVNode: `<div><p><span>HEY</span></p></div>`\n *    newVNode: `<div><p><span>THERE</span></p></div>`\n *\n *    Then when comparing the children of the `<div>` tag we check the equality\n *    of the VNodes corresponding to the `<p>` tags and, since they are the\n *    same tag in the same position, we'd be able to avoid completely\n *    re-rendering the subtree under them with a new DOM element and would just\n *    call out to `patch` to handle reconciling their children and so on.\n *\n * 3. Check, for both windows, to see if the element at the beginning of the\n *    window corresponds to the element at the end of the other window. This is\n *    a heuristic which will let us identify _some_ situations in which\n *    elements have changed position, for instance it _should_ detect that the\n *    children nodes themselves have not changed but merely moved in the\n *    following example:\n *\n *    oldVNode: `<div><element-one /><element-two /></div>`\n *    newVNode: `<div><element-two /><element-one /></div>`\n *\n *    If we find cases like this then we also need to move the concrete DOM\n *    elements corresponding to the moved children to write the re-order to the\n *    DOM.\n *\n * 4. Finally, if VNodes have the `key` attribute set on them we check for any\n *    nodes in the old children which have the same key as the first element in\n *    our window on the new children. If we find such a node we handle calling\n *    out to `patch`, moving relevant DOM nodes, and so on, in accordance with\n *    what we find.\n *\n * Finally, once we've narrowed our 'windows' to the point that either of them\n * collapse (i.e. they have length 0) we then handle any remaining VNode\n * insertion or deletion that needs to happen to get a DOM state that correctly\n * reflects the new child VNodes. If, for instance, after our window on the old\n * children has collapsed we still have more nodes on the new children that\n * we haven't dealt with yet then we need to add them, or if the new children\n * collapse but we still have unhandled _old_ children then we need to make\n * sure the corresponding DOM nodes are removed.\n *\n * @param parentElm the node into which the parent VNode is rendered\n * @param oldCh the old children of the parent node\n * @param newVNode the new VNode which will replace the parent\n * @param newCh the new children of the parent node\n * @param isInitialRender whether or not this is the first render of the vdom\n */\nconst updateChildren = (parentElm, oldCh, newVNode, newCh, isInitialRender = false) => {\n    let oldStartIdx = 0;\n    let newStartIdx = 0;\n    let idxInOld = 0;\n    let i = 0;\n    let oldEndIdx = oldCh.length - 1;\n    let oldStartVnode = oldCh[0];\n    let oldEndVnode = oldCh[oldEndIdx];\n    let newEndIdx = newCh.length - 1;\n    let newStartVnode = newCh[0];\n    let newEndVnode = newCh[newEndIdx];\n    let node;\n    let elmToMove;\n    while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n        if (oldStartVnode == null) {\n            // VNode might have been moved left\n            oldStartVnode = oldCh[++oldStartIdx];\n        }\n        else if (oldEndVnode == null) {\n            oldEndVnode = oldCh[--oldEndIdx];\n        }\n        else if (newStartVnode == null) {\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (newEndVnode == null) {\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n            // if the start nodes are the same then we should patch the new VNode\n            // onto the old one, and increment our `newStartIdx` and `oldStartIdx`\n            // indices to reflect that. We don't need to move any DOM Nodes around\n            // since things are matched up in order.\n            patch(oldStartVnode, newStartVnode, isInitialRender);\n            oldStartVnode = oldCh[++oldStartIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n            // likewise, if the end nodes are the same we patch new onto old and\n            // decrement our end indices, and also likewise in this case we don't\n            // need to move any DOM Nodes.\n            patch(oldEndVnode, newEndVnode, isInitialRender);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n            // case: \"Vnode moved right\"\n            //\n            // We've found that the last node in our window on the new children is\n            // the same VNode as the _first_ node in our window on the old children\n            // we're dealing with now. Visually, this is the layout of these two\n            // nodes:\n            //\n            // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n            //                                    ^^^^^^^^^^^\n            // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n            //              ^^^^^^^^^^^^^\n            //\n            // In this situation we need to patch `newEndVnode` onto `oldStartVnode`\n            // and move the DOM element for `oldStartVnode`.\n            if ((oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot')) {\n                putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n            }\n            patch(oldStartVnode, newEndVnode, isInitialRender);\n            // We need to move the element for `oldStartVnode` into a position which\n            // will be appropriate for `newEndVnode`. For this we can use\n            // `.insertBefore` and `oldEndVnode.$elm$.nextSibling`. If there is a\n            // sibling for `oldEndVnode.$elm$` then we want to move the DOM node for\n            // `oldStartVnode` between `oldEndVnode` and it's sibling, like so:\n            //\n            // <old-start-node />\n            // <some-intervening-node />\n            // <old-end-node />\n            // <!-- ->              <-- `oldStartVnode.$elm$` should be inserted here\n            // <next-sibling />\n            //\n            // If instead `oldEndVnode.$elm$` has no sibling then we just want to put\n            // the node for `oldStartVnode` at the end of the children of\n            // `parentElm`. Luckily, `Node.nextSibling` will return `null` if there\n            // aren't any siblings, and passing `null` to `Node.insertBefore` will\n            // append it to the children of the parent element.\n            parentElm.insertBefore(oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n            oldStartVnode = oldCh[++oldStartIdx];\n            newEndVnode = newCh[--newEndIdx];\n        }\n        else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n            // case: \"Vnode moved left\"\n            //\n            // We've found that the first node in our window on the new children is\n            // the same VNode as the _last_ node in our window on the old children.\n            // Visually, this is the layout of these two nodes:\n            //\n            // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n            //              ^^^^^^^^^^^^^\n            // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n            //                                    ^^^^^^^^^^^\n            //\n            // In this situation we need to patch `newStartVnode` onto `oldEndVnode`\n            // (which will handle updating any changed attributes, reconciling their\n            // children etc) but we also need to move the DOM node to which\n            // `oldEndVnode` corresponds.\n            if ((oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot')) {\n                putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n            }\n            patch(oldEndVnode, newStartVnode, isInitialRender);\n            // We've already checked above if `oldStartVnode` and `newStartVnode` are\n            // the same node, so since we're here we know that they are not. Thus we\n            // can move the element for `oldEndVnode` _before_ the element for\n            // `oldStartVnode`, leaving `oldStartVnode` to be reconciled in the\n            // future.\n            parentElm.insertBefore(oldEndVnode.$elm$, oldStartVnode.$elm$);\n            oldEndVnode = oldCh[--oldEndIdx];\n            newStartVnode = newCh[++newStartIdx];\n        }\n        else {\n            // Here we do some checks to match up old and new nodes based on the\n            // `$key$` attribute, which is set by putting a `key=\"my-key\"` attribute\n            // in the JSX for a DOM element in the implementation of a Stencil\n            // component.\n            //\n            // First we check to see if there are any nodes in the array of old\n            // children which have the same key as the first node in the new\n            // children.\n            idxInOld = -1;\n            {\n                for (i = oldStartIdx; i <= oldEndIdx; ++i) {\n                    if (oldCh[i] && oldCh[i].$key$ !== null && oldCh[i].$key$ === newStartVnode.$key$) {\n                        idxInOld = i;\n                        break;\n                    }\n                }\n            }\n            if (idxInOld >= 0) {\n                // We found a node in the old children which matches up with the first\n                // node in the new children! So let's deal with that\n                elmToMove = oldCh[idxInOld];\n                if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n                    // the tag doesn't match so we'll need a new DOM element\n                    node = createElm(oldCh && oldCh[newStartIdx], newVNode, idxInOld, parentElm);\n                }\n                else {\n                    patch(elmToMove, newStartVnode, isInitialRender);\n                    // invalidate the matching old node so that we won't try to update it\n                    // again later on\n                    oldCh[idxInOld] = undefined;\n                    node = elmToMove.$elm$;\n                }\n                newStartVnode = newCh[++newStartIdx];\n            }\n            else {\n                // We either didn't find an element in the old children that matches\n                // the key of the first new child OR the build is not using `key`\n                // attributes at all. In either case we need to create a new element\n                // for the new node.\n                node = createElm(oldCh && oldCh[newStartIdx], newVNode, newStartIdx, parentElm);\n                newStartVnode = newCh[++newStartIdx];\n            }\n            if (node) {\n                // if we created a new node then handle inserting it to the DOM\n                {\n                    parentReferenceNode(oldStartVnode.$elm$).insertBefore(node, referenceNode(oldStartVnode.$elm$));\n                }\n            }\n        }\n    }\n    if (oldStartIdx > oldEndIdx) {\n        // we have some more new nodes to add which don't match up with old nodes\n        addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode, newCh, newStartIdx, newEndIdx);\n    }\n    else if (newStartIdx > newEndIdx) {\n        // there are nodes in the `oldCh` array which no longer correspond to nodes\n        // in the new array, so lets remove them (which entails cleaning up the\n        // relevant DOM nodes)\n        removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n    }\n};\n/**\n * Compare two VNodes to determine if they are the same\n *\n * **NB**: This function is an equality _heuristic_ based on the available\n * information set on the two VNodes and can be misleading under certain\n * circumstances. In particular, if the two nodes do not have `key` attrs\n * (available under `$key$` on VNodes) then the function falls back on merely\n * checking that they have the same tag.\n *\n * So, in other words, if `key` attrs are not set on VNodes which may be\n * changing order within a `children` array or something along those lines then\n * we could obtain a false negative and then have to do needless re-rendering\n * (i.e. we'd say two VNodes aren't equal when in fact they should be).\n *\n * @param leftVNode the first VNode to check\n * @param rightVNode the second VNode to check\n * @param isInitialRender whether or not this is the first render of the vdom\n * @returns whether they're equal or not\n */\nconst isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n    // compare if two vnode to see if they're \"technically\" the same\n    // need to have the same element tag, and same key to be the same\n    if (leftVNode.$tag$ === rightVNode.$tag$) {\n        if (leftVNode.$tag$ === 'slot') {\n            return leftVNode.$name$ === rightVNode.$name$;\n        }\n        // this will be set if JSX tags in the build have `key` attrs set on them\n        // we only want to check this if we're not on the first render since on\n        // first render `leftVNode.$key$` will always be `null`, so we can be led\n        // astray and, for instance, accidentally delete a DOM node that we want to\n        // keep around.\n        if (!isInitialRender) {\n            return leftVNode.$key$ === rightVNode.$key$;\n        }\n        return true;\n    }\n    return false;\n};\nconst referenceNode = (node) => {\n    // this node was relocated to a new location in the dom\n    // because of some other component's slot\n    // but we still have an html comment in place of where\n    // it's original location was according to it's original vdom\n    return (node && node['s-ol']) || node;\n};\nconst parentReferenceNode = (node) => (node['s-ol'] ? node['s-ol'] : node).parentNode;\n/**\n * Handle reconciling an outdated VNode with a new one which corresponds to\n * it. This function handles flushing updates to the DOM and reconciling the\n * children of the two nodes (if any).\n *\n * @param oldVNode an old VNode whose DOM element and children we want to update\n * @param newVNode a new VNode representing an updated version of the old one\n * @param isInitialRender whether or not this is the first render of the vdom\n */\nconst patch = (oldVNode, newVNode, isInitialRender = false) => {\n    const elm = (newVNode.$elm$ = oldVNode.$elm$);\n    const oldChildren = oldVNode.$children$;\n    const newChildren = newVNode.$children$;\n    const tag = newVNode.$tag$;\n    const text = newVNode.$text$;\n    let defaultHolder;\n    if (text === null) {\n        {\n            // test if we're rendering an svg element, or still rendering nodes inside of one\n            // only add this to the when the compiler sees we're using an svg somewhere\n            isSvgMode = tag === 'svg' ? true : tag === 'foreignObject' ? false : isSvgMode;\n        }\n        {\n            if (tag === 'slot' && !useNativeShadowDom) ;\n            else {\n                // either this is the first render of an element OR it's an update\n                // AND we already know it's possible it could have changed\n                // this updates the element's css classes, attrs, props, listeners, etc.\n                updateElement(oldVNode, newVNode, isSvgMode);\n            }\n        }\n        if (oldChildren !== null && newChildren !== null) {\n            // looks like there's child vnodes for both the old and new vnodes\n            // so we need to call `updateChildren` to reconcile them\n            updateChildren(elm, oldChildren, newVNode, newChildren, isInitialRender);\n        }\n        else if (newChildren !== null) {\n            // no old child vnodes, but there are new child vnodes to add\n            if (oldVNode.$text$ !== null) {\n                // the old vnode was text, so be sure to clear it out\n                elm.textContent = '';\n            }\n            // add the new vnode children\n            addVnodes(elm, null, newVNode, newChildren, 0, newChildren.length - 1);\n        }\n        else if (oldChildren !== null) {\n            // no new child vnodes, but there are old child vnodes to remove\n            removeVnodes(oldChildren, 0, oldChildren.length - 1);\n        }\n        if (isSvgMode && tag === 'svg') {\n            isSvgMode = false;\n        }\n    }\n    else if ((defaultHolder = elm['s-cr'])) {\n        // this element has slotted content\n        defaultHolder.parentNode.textContent = text;\n    }\n    else if (oldVNode.$text$ !== text) {\n        // update the text content for the text only vnode\n        // and also only if the text is different than before\n        elm.data = text;\n    }\n};\n/**\n * Adjust the `.hidden` property as-needed on any nodes in a DOM subtree which\n * are slot fallbacks nodes.\n *\n * A slot fallback node should be visible by default. Then, it should be\n * conditionally hidden if:\n *\n * - it has a sibling with a `slot` property set to its slot name or if\n * - it is a default fallback slot node, in which case we hide if it has any\n *   content\n *\n * @param elm the element of interest\n */\nconst updateFallbackSlotVisibility = (elm) => {\n    const childNodes = elm.childNodes;\n    for (const childNode of childNodes) {\n        if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n            if (childNode['s-sr']) {\n                // this is a slot fallback node\n                // get the slot name for this slot reference node\n                const slotName = childNode['s-sn'];\n                // by default always show a fallback slot node\n                // then hide it if there are other slots in the light dom\n                childNode.hidden = false;\n                // we need to check all of its sibling nodes in order to see if\n                // `childNode` should be hidden\n                for (const siblingNode of childNodes) {\n                    // Don't check the node against itself\n                    if (siblingNode !== childNode) {\n                        if (siblingNode['s-hn'] !== childNode['s-hn'] || slotName !== '') {\n                            // this sibling node is from a different component OR is a named\n                            // fallback slot node\n                            if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ &&\n                                (slotName === siblingNode.getAttribute('slot') || slotName === siblingNode['s-sn'])) {\n                                childNode.hidden = true;\n                                break;\n                            }\n                        }\n                        else {\n                            // this is a default fallback slot node\n                            // any element or text node (with content)\n                            // should hide the default fallback slot node\n                            if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ ||\n                                (siblingNode.nodeType === 3 /* NODE_TYPE.TextNode */ && siblingNode.textContent.trim() !== '')) {\n                                childNode.hidden = true;\n                                break;\n                            }\n                        }\n                    }\n                }\n            }\n            // keep drilling down\n            updateFallbackSlotVisibility(childNode);\n        }\n    }\n};\n/**\n * Component-global information about nodes which are either currently being\n * relocated or will be shortly.\n */\nconst relocateNodes = [];\n/**\n * Mark the contents of a slot for relocation via adding references to them to\n * the {@link relocateNodes} data structure. The actual work of relocating them\n * will then be handled in {@link renderVdom}.\n *\n * @param elm a render node whose child nodes need to be relocated\n */\nconst markSlotContentForRelocation = (elm) => {\n    // tslint:disable-next-line: prefer-const\n    let node;\n    let hostContentNodes;\n    let j;\n    for (const childNode of elm.childNodes) {\n        // we need to find child nodes which are slot references so we can then try\n        // to match them up with nodes that need to be relocated\n        if (childNode['s-sr'] && (node = childNode['s-cr']) && node.parentNode) {\n            // first get the content reference comment node ('s-cr'), then we get\n            // its parent, which is where all the host content is now\n            hostContentNodes = node.parentNode.childNodes;\n            const slotName = childNode['s-sn'];\n            // iterate through all the nodes under the location where the host was\n            // originally rendered\n            for (j = hostContentNodes.length - 1; j >= 0; j--) {\n                node = hostContentNodes[j];\n                // check that the node is not a content reference node or a node\n                // reference and then check that the host name does not match that of\n                // childNode.\n                // In addition, check that the slot either has not already been relocated, or\n                // that its current location's host is not childNode's host. This is essentially\n                // a check so that we don't try to relocate (and then hide) a node that is already\n                // where it should be.\n                if (!node['s-cn'] &&\n                    !node['s-nr'] &&\n                    node['s-hn'] !== childNode['s-hn'] &&\n                    (!BUILD.experimentalSlotFixes  )) {\n                    // if `node` is located in the slot that `childNode` refers to (via the\n                    // `'s-sn'` property) then we need to relocate it from it's current spot\n                    // (under the host element parent) to the right slot location\n                    if (isNodeLocatedInSlot(node, slotName)) {\n                        // it's possible we've already decided to relocate this node\n                        let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                        // made some changes to slots\n                        // let's make sure we also double check\n                        // fallbacks are correctly hidden or shown\n                        checkSlotFallbackVisibility = true;\n                        // ensure that the slot-name attr is correct\n                        node['s-sn'] = node['s-sn'] || slotName;\n                        if (relocateNodeData) {\n                            relocateNodeData.$nodeToRelocate$['s-sh'] = childNode['s-hn'];\n                            // we marked this node for relocation previously but didn't find\n                            // out the slot reference node to which it needs to be relocated\n                            // so write it down now!\n                            relocateNodeData.$slotRefNode$ = childNode;\n                        }\n                        else {\n                            node['s-sh'] = childNode['s-hn'];\n                            // add to our list of nodes to relocate\n                            relocateNodes.push({\n                                $slotRefNode$: childNode,\n                                $nodeToRelocate$: node,\n                            });\n                        }\n                        if (node['s-sr']) {\n                            relocateNodes.map((relocateNode) => {\n                                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node['s-sn'])) {\n                                    relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                                    if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                                        relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                                    }\n                                }\n                            });\n                        }\n                    }\n                    else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {\n                        // the node is not found within the slot (`childNode`) that we're\n                        // currently looking at, so we stick it into `relocateNodes` to\n                        // handle later. If we never find a home for this element then\n                        // we'll need to hide it\n                        relocateNodes.push({\n                            $nodeToRelocate$: node,\n                        });\n                    }\n                }\n            }\n        }\n        // if we're dealing with any type of element (capable of itself being a\n        // slot reference or containing one) then we recur\n        if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n            markSlotContentForRelocation(childNode);\n        }\n    }\n};\n/**\n * Check whether a node is located in a given named slot.\n *\n * @param nodeToRelocate the node of interest\n * @param slotName the slot name to check\n * @returns whether the node is located in the slot or not\n */\nconst isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n    if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n        if (nodeToRelocate.getAttribute('slot') === null && slotName === '') {\n            // if the node doesn't have a slot attribute, and the slot we're checking\n            // is not a named slot, then we assume the node should be within the slot\n            return true;\n        }\n        if (nodeToRelocate.getAttribute('slot') === slotName) {\n            return true;\n        }\n        return false;\n    }\n    if (nodeToRelocate['s-sn'] === slotName) {\n        return true;\n    }\n    return slotName === '';\n};\n/**\n * 'Nullify' any VDom `ref` callbacks on a VDom node or its children by calling\n * them with `null`. This signals that the DOM element corresponding to the VDom\n * node has been removed from the DOM.\n *\n * @param vNode a virtual DOM node\n */\nconst nullifyVNodeRefs = (vNode) => {\n    {\n        vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n        vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n    }\n};\n/**\n * The main entry point for Stencil's virtual DOM-based rendering engine\n *\n * Given a {@link d.HostRef} container and some virtual DOM nodes, this\n * function will handle creating a virtual DOM tree with a single root, patching\n * the current virtual DOM tree onto an old one (if any), dealing with slot\n * relocation, and reflecting attributes.\n *\n * @param hostRef data needed to root and render the virtual DOM tree, such as\n * the DOM node into which it should be rendered.\n * @param renderFnResults the virtual DOM nodes to be rendered\n * @param isInitialLoad whether or not this is the first call after page load\n */\nconst renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n    var _a, _b, _c, _d;\n    const hostElm = hostRef.$hostElement$;\n    const cmpMeta = hostRef.$cmpMeta$;\n    const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n    // if `renderFnResults` is a Host node then we can use it directly. If not,\n    // we need to call `h` again to wrap the children of our component in a\n    // 'dummy' Host node (well, an empty vnode) since `renderVdom` assumes\n    // implicitly that the top-level vdom node is 1) an only child and 2)\n    // contains attrs that need to be set on the host element.\n    const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n    hostTagName = hostElm.tagName;\n    if (cmpMeta.$attrsToReflect$) {\n        rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n        cmpMeta.$attrsToReflect$.map(([propName, attribute]) => (rootVnode.$attrs$[attribute] = hostElm[propName]));\n    }\n    // On the first render and *only* on the first render we want to check for\n    // any attributes set on the host element which are also set on the vdom\n    // node. If we find them, we override the value on the VDom node attrs with\n    // the value from the host element, which allows developers building apps\n    // with Stencil components to override e.g. the `role` attribute on a\n    // component even if it's already set on the `Host`.\n    if (isInitialLoad && rootVnode.$attrs$) {\n        for (const key of Object.keys(rootVnode.$attrs$)) {\n            // We have a special implementation in `setAccessor` for `style` and\n            // `class` which reconciles values coming from the VDom with values\n            // already present on the DOM element, so we don't want to override those\n            // attributes on the VDom tree with values from the host element if they\n            // are present.\n            //\n            // Likewise, `ref` and `key` are special internal values for the Stencil\n            // runtime and we don't want to override those either.\n            if (hostElm.hasAttribute(key) && !['key', 'ref', 'style', 'class'].includes(key)) {\n                rootVnode.$attrs$[key] = hostElm[key];\n            }\n        }\n    }\n    rootVnode.$tag$ = null;\n    rootVnode.$flags$ |= 4 /* VNODE_FLAGS.isHost */;\n    hostRef.$vnode$ = rootVnode;\n    rootVnode.$elm$ = oldVNode.$elm$ = (hostElm.shadowRoot || hostElm );\n    {\n        scopeId = hostElm['s-sc'];\n    }\n    useNativeShadowDom = (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) !== 0;\n    {\n        contentRef = hostElm['s-cr'];\n        // always reset\n        checkSlotFallbackVisibility = false;\n    }\n    // synchronous patch\n    patch(oldVNode, rootVnode, isInitialLoad);\n    {\n        // while we're moving nodes around existing nodes, temporarily disable\n        // the disconnectCallback from working\n        plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n        if (checkSlotRelocate) {\n            markSlotContentForRelocation(rootVnode.$elm$);\n            for (const relocateData of relocateNodes) {\n                const nodeToRelocate = relocateData.$nodeToRelocate$;\n                if (!nodeToRelocate['s-ol']) {\n                    // add a reference node marking this node's original location\n                    // keep a reference to this node for later lookups\n                    const orgLocationNode = doc.createTextNode('');\n                    orgLocationNode['s-nr'] = nodeToRelocate;\n                    nodeToRelocate.parentNode.insertBefore((nodeToRelocate['s-ol'] = orgLocationNode), nodeToRelocate);\n                }\n            }\n            for (const relocateData of relocateNodes) {\n                const nodeToRelocate = relocateData.$nodeToRelocate$;\n                const slotRefNode = relocateData.$slotRefNode$;\n                if (slotRefNode) {\n                    const parentNodeRef = slotRefNode.parentNode;\n                    // When determining where to insert content, the most simple case would be\n                    // to relocate the node immediately following the slot reference node. We do this\n                    // by getting a reference to the node immediately following the slot reference node\n                    // since we will use `insertBefore` to manipulate the DOM.\n                    //\n                    // If there is no node immediately following the slot reference node, then we will just\n                    // end up appending the node as the last child of the parent.\n                    let insertBeforeNode = slotRefNode.nextSibling;\n                    // If the node we're currently planning on inserting the new node before is an element,\n                    // we need to do some additional checks to make sure we're inserting the node in the correct order.\n                    // The use case here would be that we have multiple nodes being relocated to the same slot. So, we want\n                    // to make sure they get inserted into their new home in the same order they were declared in their original location.\n                    //\n                    // TODO(STENCIL-914): Remove `experimentalSlotFixes` check\n                    {\n                        let orgLocationNode = (_a = nodeToRelocate['s-ol']) === null || _a === void 0 ? void 0 : _a.previousSibling;\n                        while (orgLocationNode) {\n                            let refNode = (_b = orgLocationNode['s-nr']) !== null && _b !== void 0 ? _b : null;\n                            if (refNode && refNode['s-sn'] === nodeToRelocate['s-sn'] && parentNodeRef === refNode.parentNode) {\n                                refNode = refNode.nextSibling;\n                                // If the refNode is the same node to be relocated or another element's slot reference, keep searching to find the\n                                // correct relocation target\n                                while (refNode === nodeToRelocate || (refNode === null || refNode === void 0 ? void 0 : refNode['s-sr'])) {\n                                    refNode = refNode === null || refNode === void 0 ? void 0 : refNode.nextSibling;\n                                }\n                                if (!refNode || !refNode['s-nr']) {\n                                    insertBeforeNode = refNode;\n                                    break;\n                                }\n                            }\n                            orgLocationNode = orgLocationNode.previousSibling;\n                        }\n                    }\n                    if ((!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode) ||\n                        nodeToRelocate.nextSibling !== insertBeforeNode) {\n                        // we've checked that it's worth while to relocate\n                        // since that the node to relocate\n                        // has a different next sibling or parent relocated\n                        if (nodeToRelocate !== insertBeforeNode) {\n                            if (!nodeToRelocate['s-hn'] && nodeToRelocate['s-ol']) {\n                                // probably a component in the index.html that doesn't have its hostname set\n                                nodeToRelocate['s-hn'] = nodeToRelocate['s-ol'].parentNode.nodeName;\n                            }\n                            // Add it back to the dom but in its new home\n                            // If we get to this point and `insertBeforeNode` is `null`, that means\n                            // we're just going to append the node as the last child of the parent. Passing\n                            // `null` as the second arg here will trigger that behavior.\n                            parentNodeRef.insertBefore(nodeToRelocate, insertBeforeNode);\n                            // Reset the `hidden` value back to what it was defined as originally\n                            // This solves a problem where a `slot` is dynamically rendered and `hidden` may have\n                            // been set on content originally, but now it has a slot to go to so it should have\n                            // the value it was defined as having in the DOM, not what we overrode it to.\n                            if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n                                nodeToRelocate.hidden = (_c = nodeToRelocate['s-ih']) !== null && _c !== void 0 ? _c : false;\n                            }\n                        }\n                    }\n                    nodeToRelocate && typeof slotRefNode['s-rf'] === 'function' && slotRefNode['s-rf'](nodeToRelocate);\n                }\n                else {\n                    // this node doesn't have a slot home to go to, so let's hide it\n                    if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n                        // Store the initial value of `hidden` so we can reset it later when\n                        // moving nodes around.\n                        if (isInitialLoad) {\n                            nodeToRelocate['s-ih'] = (_d = nodeToRelocate.hidden) !== null && _d !== void 0 ? _d : false;\n                        }\n                        nodeToRelocate.hidden = true;\n                    }\n                }\n            }\n        }\n        if (checkSlotFallbackVisibility) {\n            updateFallbackSlotVisibility(rootVnode.$elm$);\n        }\n        // done moving nodes around\n        // allow the disconnect callback to work again\n        plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n        // always reset\n        relocateNodes.length = 0;\n    }\n    // Clear the content ref so we don't create a memory leak\n    contentRef = undefined;\n};\nconst attachToAncestor = (hostRef, ancestorComponent) => {\n    if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent['s-p']) {\n        ancestorComponent['s-p'].push(new Promise((r) => (hostRef.$onRenderResolve$ = r)));\n    }\n};\nconst scheduleUpdate = (hostRef, isInitialLoad) => {\n    {\n        hostRef.$flags$ |= 16 /* HOST_FLAGS.isQueuedForUpdate */;\n    }\n    if (hostRef.$flags$ & 4 /* HOST_FLAGS.isWaitingForChildren */) {\n        hostRef.$flags$ |= 512 /* HOST_FLAGS.needsRerender */;\n        return;\n    }\n    attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n    // there is no ancestor component or the ancestor component\n    // has already fired off its lifecycle update then\n    // fire off the initial update\n    const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n    return writeTask(dispatch) ;\n};\n/**\n * Dispatch initial-render and update lifecycle hooks, enqueuing calls to\n * component lifecycle methods like `componentWillLoad` as well as\n * {@link updateComponent}, which will kick off the virtual DOM re-render.\n *\n * @param hostRef a reference to a host DOM node\n * @param isInitialLoad whether we're on the initial load or not\n * @returns an empty Promise which is used to enqueue a series of operations for\n * the component\n */\nconst dispatchHooks = (hostRef, isInitialLoad) => {\n    const endSchedule = createTime('scheduleUpdate', hostRef.$cmpMeta$.$tagName$);\n    const instance = hostRef.$lazyInstance$ ;\n    // We're going to use this variable together with `enqueue` to implement a\n    // little promise-based queue. We start out with it `undefined`. When we add\n    // the first function to the queue we'll set this variable to be that\n    // function's return value. When we attempt to add subsequent values to the\n    // queue we'll check that value and, if it was a `Promise`, we'll then chain\n    // the new function off of that `Promise` using `.then()`. This will give our\n    // queue two nice properties:\n    //\n    // 1. If all functions added to the queue are synchronous they'll be called\n    //    synchronously right away.\n    // 2. If all functions added to the queue are asynchronous they'll all be\n    //    called in order after `dispatchHooks` exits.\n    let maybePromise;\n    if (isInitialLoad) {\n        {\n            hostRef.$flags$ |= 256 /* HOST_FLAGS.isListenReady */;\n            if (hostRef.$queuedListeners$) {\n                hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n                hostRef.$queuedListeners$ = undefined;\n            }\n        }\n        {\n            // If `componentWillLoad` returns a `Promise` then we want to wait on\n            // whatever's going on in that `Promise` before we launch into\n            // rendering the component, doing other lifecycle stuff, etc. So\n            // in that case we assign the returned promise to the variable we\n            // declared above to hold a possible 'queueing' Promise\n            maybePromise = safeCall(instance, 'componentWillLoad');\n        }\n    }\n    {\n        maybePromise = enqueue(maybePromise, () => safeCall(instance, 'componentWillRender'));\n    }\n    endSchedule();\n    return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\n/**\n * This function uses a Promise to implement a simple first-in, first-out queue\n * of functions to be called.\n *\n * The queue is ordered on the basis of the first argument. If it's\n * `undefined`, then nothing is on the queue yet, so the provided function can\n * be called synchronously (although note that this function may return a\n * `Promise`). The idea is that then the return value of that enqueueing\n * operation is kept around, so that if it was a `Promise` then subsequent\n * functions can be enqueued by calling this function again with that `Promise`\n * as the first argument.\n *\n * @param maybePromise either a `Promise` which should resolve before the next function is called or an 'empty' sentinel\n * @param fn a function to enqueue\n * @returns either a `Promise` or the return value of the provided function\n */\nconst enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn) : fn();\n/**\n * Check that a value is a `Promise`. To check, we first see if the value is an\n * instance of the `Promise` global. In a few circumstances, in particular if\n * the global has been overwritten, this is could be misleading, so we also do\n * a little 'duck typing' check to see if the `.then` property of the value is\n * defined and a function.\n *\n * @param maybePromise it might be a promise!\n * @returns whether it is or not\n */\nconst isPromisey = (maybePromise) => maybePromise instanceof Promise ||\n    (maybePromise && maybePromise.then && typeof maybePromise.then === 'function');\n/**\n * Update a component given reference to its host elements and so on.\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param isInitialLoad whether or not this function is being called as part of\n * the first render cycle\n */\nconst updateComponent = async (hostRef, instance, isInitialLoad) => {\n    var _a;\n    const elm = hostRef.$hostElement$;\n    const endUpdate = createTime('update', hostRef.$cmpMeta$.$tagName$);\n    const rc = elm['s-rc'];\n    if (isInitialLoad) {\n        // DOM WRITE!\n        attachStyles(hostRef);\n    }\n    const endRender = createTime('render', hostRef.$cmpMeta$.$tagName$);\n    {\n        callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    if (rc) {\n        // ok, so turns out there are some child host elements\n        // waiting on this parent element to load\n        // let's fire off all update callbacks waiting\n        rc.map((cb) => cb());\n        elm['s-rc'] = undefined;\n    }\n    endRender();\n    endUpdate();\n    {\n        const childrenPromises = (_a = elm['s-p']) !== null && _a !== void 0 ? _a : [];\n        const postUpdate = () => postUpdateComponent(hostRef);\n        if (childrenPromises.length === 0) {\n            postUpdate();\n        }\n        else {\n            Promise.all(childrenPromises).then(postUpdate);\n            hostRef.$flags$ |= 4 /* HOST_FLAGS.isWaitingForChildren */;\n            childrenPromises.length = 0;\n        }\n    }\n};\n/**\n * Handle making the call to the VDom renderer with the proper context given\n * various build variables\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param elm the Host element for the component\n * @param isInitialLoad whether or not this function is being called as part of\n * @returns an empty promise\n */\nconst callRender = (hostRef, instance, elm, isInitialLoad) => {\n    try {\n        /**\n         * minification optimization: `allRenderFn` is `true` if all components have a `render`\n         * method, so we can call the method immediately. If not, check before calling it.\n         */\n        instance = instance.render && instance.render();\n        {\n            hostRef.$flags$ &= ~16 /* HOST_FLAGS.isQueuedForUpdate */;\n        }\n        {\n            hostRef.$flags$ |= 2 /* HOST_FLAGS.hasRendered */;\n        }\n        {\n            {\n                // looks like we've got child nodes to render into this host element\n                // or we need to update the css class/attrs on the host element\n                // DOM WRITE!\n                {\n                    renderVdom(hostRef, instance, isInitialLoad);\n                }\n            }\n        }\n    }\n    catch (e) {\n        consoleError(e, hostRef.$hostElement$);\n    }\n    return null;\n};\nconst postUpdateComponent = (hostRef) => {\n    const tagName = hostRef.$cmpMeta$.$tagName$;\n    const elm = hostRef.$hostElement$;\n    const endPostUpdate = createTime('postUpdate', tagName);\n    const instance = hostRef.$lazyInstance$ ;\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    {\n        safeCall(instance, 'componentDidRender');\n    }\n    if (!(hostRef.$flags$ & 64 /* HOST_FLAGS.hasLoadedComponent */)) {\n        hostRef.$flags$ |= 64 /* HOST_FLAGS.hasLoadedComponent */;\n        {\n            // DOM WRITE!\n            addHydratedFlag(elm);\n        }\n        {\n            safeCall(instance, 'componentDidLoad');\n        }\n        endPostUpdate();\n        {\n            hostRef.$onReadyResolve$(elm);\n            if (!ancestorComponent) {\n                appDidLoad();\n            }\n        }\n    }\n    else {\n        {\n            safeCall(instance, 'componentDidUpdate');\n        }\n        endPostUpdate();\n    }\n    {\n        hostRef.$onInstanceResolve$(elm);\n    }\n    // load events fire from bottom to top\n    // the deepest elements load first then bubbles up\n    {\n        if (hostRef.$onRenderResolve$) {\n            hostRef.$onRenderResolve$();\n            hostRef.$onRenderResolve$ = undefined;\n        }\n        if (hostRef.$flags$ & 512 /* HOST_FLAGS.needsRerender */) {\n            nextTick(() => scheduleUpdate(hostRef, false));\n        }\n        hostRef.$flags$ &= ~(4 /* HOST_FLAGS.isWaitingForChildren */ | 512 /* HOST_FLAGS.needsRerender */);\n    }\n    // ( •_•)\n    // ( •_•)>⌐■-■\n    // (⌐■_■)\n};\nconst forceUpdate = (ref) => {\n    {\n        const hostRef = getHostRef(ref);\n        const isConnected = hostRef.$hostElement$.isConnected;\n        if (isConnected &&\n            (hostRef.$flags$ & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n            scheduleUpdate(hostRef, false);\n        }\n        // Returns \"true\" when the forced update was successfully scheduled\n        return isConnected;\n    }\n};\nconst appDidLoad = (who) => {\n    // on appload\n    // we have finish the first big initial render\n    {\n        addHydratedFlag(doc.documentElement);\n    }\n    nextTick(() => emitEvent(win, 'appload', { detail: { namespace: NAMESPACE } }));\n};\n/**\n * Allows to safely call a method, e.g. `componentDidLoad`, on an instance,\n * e.g. custom element node. If a build figures out that e.g. no component\n * has a `componentDidLoad` method, the instance method gets removed from the\n * output bundle and this function returns `undefined`.\n * @param instance any object that may or may not contain methods\n * @param method method name\n * @param arg single arbitrary argument\n * @returns result of method call if it exists, otherwise `undefined`\n */\nconst safeCall = (instance, method, arg) => {\n    if (instance && instance[method]) {\n        try {\n            return instance[method](arg);\n        }\n        catch (e) {\n            consoleError(e);\n        }\n    }\n    return undefined;\n};\nconst addHydratedFlag = (elm) => elm.classList.add('hydrated')\n    ;\nconst getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nconst setValue = (ref, propName, newVal, cmpMeta) => {\n    // check our new property value against our internal value\n    const hostRef = getHostRef(ref);\n    const elm = hostRef.$hostElement$ ;\n    const oldVal = hostRef.$instanceValues$.get(propName);\n    const flags = hostRef.$flags$;\n    const instance = hostRef.$lazyInstance$ ;\n    newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n    // explicitly check for NaN on both sides, as `NaN === NaN` is always false\n    const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n    const didValueChange = newVal !== oldVal && !areBothNaN;\n    if ((!(flags & 8 /* HOST_FLAGS.isConstructingInstance */) || oldVal === undefined) && didValueChange) {\n        // gadzooks! the property's value has changed!!\n        // set our new value!\n        hostRef.$instanceValues$.set(propName, newVal);\n        if (instance) {\n            // get an array of method names of watch functions to call\n            if (cmpMeta.$watchers$ && flags & 128 /* HOST_FLAGS.isWatchReady */) {\n                const watchMethods = cmpMeta.$watchers$[propName];\n                if (watchMethods) {\n                    // this instance is watching for when this property changed\n                    watchMethods.map((watchMethodName) => {\n                        try {\n                            // fire off each of the watch methods that are watching this property\n                            instance[watchMethodName](newVal, oldVal, propName);\n                        }\n                        catch (e) {\n                            consoleError(e, elm);\n                        }\n                    });\n                }\n            }\n            if ((flags & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n                // looks like this value actually changed, so we've got work to do!\n                // but only if we've already rendered, otherwise just chill out\n                // queue that we need to do an update, but don't worry about queuing\n                // up millions cuz this function ensures it only runs once\n                scheduleUpdate(hostRef, false);\n            }\n        }\n    }\n};\n/**\n * Attach a series of runtime constructs to a compiled Stencil component\n * constructor, including getters and setters for the `@Prop` and `@State`\n * decorators, callbacks for when attributes change, and so on.\n *\n * @param Cstr the constructor for a component that we need to process\n * @param cmpMeta metadata collected previously about the component\n * @param flags a number used to store a series of bit flags\n * @returns a reference to the same constructor passed in (but now mutated)\n */\nconst proxyComponent = (Cstr, cmpMeta, flags) => {\n    var _a;\n    const prototype = Cstr.prototype;\n    if (cmpMeta.$members$) {\n        if (Cstr.watchers) {\n            cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        // It's better to have a const than two Object.entries()\n        const members = Object.entries(cmpMeta.$members$);\n        members.map(([memberName, [memberFlags]]) => {\n            if ((memberFlags & 31 /* MEMBER_FLAGS.Prop */ ||\n                    ((flags & 2 /* PROXY_FLAGS.proxyState */) && memberFlags & 32 /* MEMBER_FLAGS.State */))) {\n                // proxyComponent - prop\n                Object.defineProperty(prototype, memberName, {\n                    get() {\n                        // proxyComponent, get value\n                        return getValue(this, memberName);\n                    },\n                    set(newValue) {\n                        // proxyComponent, set value\n                        setValue(this, memberName, newValue, cmpMeta);\n                    },\n                    configurable: true,\n                    enumerable: true,\n                });\n            }\n            else if (flags & 1 /* PROXY_FLAGS.isElementConstructor */ &&\n                memberFlags & 64 /* MEMBER_FLAGS.Method */) {\n                // proxyComponent - method\n                Object.defineProperty(prototype, memberName, {\n                    value(...args) {\n                        var _a;\n                        const ref = getHostRef(this);\n                        return (_a = ref === null || ref === void 0 ? void 0 : ref.$onInstancePromise$) === null || _a === void 0 ? void 0 : _a.then(() => { var _a; return (_a = ref.$lazyInstance$) === null || _a === void 0 ? void 0 : _a[memberName](...args); });\n                    },\n                });\n            }\n        });\n        if ((flags & 1 /* PROXY_FLAGS.isElementConstructor */)) {\n            const attrNameToPropName = new Map();\n            prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n                plt.jmp(() => {\n                    var _a;\n                    const propName = attrNameToPropName.get(attrName);\n                    //  In a web component lifecycle the attributeChangedCallback runs prior to connectedCallback\n                    //  in the case where an attribute was set inline.\n                    //  ```html\n                    //    <my-component some-attribute=\"some-value\"></my-component>\n                    //  ```\n                    //\n                    //  There is an edge case where a developer sets the attribute inline on a custom element and then\n                    //  programmatically changes it before it has been upgraded as shown below:\n                    //\n                    //  ```html\n                    //    <!-- this component has _not_ been upgraded yet -->\n                    //    <my-component id=\"test\" some-attribute=\"some-value\"></my-component>\n                    //    <script>\n                    //      // grab non-upgraded component\n                    //      el = document.querySelector(\"#test\");\n                    //      el.someAttribute = \"another-value\";\n                    //      // upgrade component\n                    //      customElements.define('my-component', MyComponent);\n                    //    </script>\n                    //  ```\n                    //  In this case if we do not un-shadow here and use the value of the shadowing property, attributeChangedCallback\n                    //  will be called with `newValue = \"some-value\"` and will set the shadowed property (this.someAttribute = \"another-value\")\n                    //  to the value that was set inline i.e. \"some-value\" from above example. When\n                    //  the connectedCallback attempts to un-shadow it will use \"some-value\" as the initial value rather than \"another-value\"\n                    //\n                    //  The case where the attribute was NOT set inline but was not set programmatically shall be handled/un-shadowed\n                    //  by connectedCallback as this attributeChangedCallback will not fire.\n                    //\n                    //  https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n                    //\n                    //  TODO(STENCIL-16) we should think about whether or not we actually want to be reflecting the attributes to\n                    //  properties here given that this goes against best practices outlined here\n                    //  https://developers.google.com/web/fundamentals/web-components/best-practices#avoid-reentrancy\n                    if (this.hasOwnProperty(propName)) {\n                        newValue = this[propName];\n                        delete this[propName];\n                    }\n                    else if (prototype.hasOwnProperty(propName) &&\n                        typeof this[propName] === 'number' &&\n                        this[propName] == newValue) {\n                        // if the propName exists on the prototype of `Cstr`, this update may be a result of Stencil using native\n                        // APIs to reflect props as attributes. Calls to `setAttribute(someElement, propName)` will result in\n                        // `propName` to be converted to a `DOMString`, which may not be what we want for other primitive props.\n                        return;\n                    }\n                    else if (propName == null) {\n                        // At this point we should know this is not a \"member\", so we can treat it like watching an attribute\n                        // on a vanilla web component\n                        const hostRef = getHostRef(this);\n                        const flags = hostRef === null || hostRef === void 0 ? void 0 : hostRef.$flags$;\n                        // We only want to trigger the callback(s) if:\n                        // 1. The instance is ready\n                        // 2. The watchers are ready\n                        // 3. The value has changed\n                        if (flags &&\n                            !(flags & 8 /* HOST_FLAGS.isConstructingInstance */) &&\n                            flags & 128 /* HOST_FLAGS.isWatchReady */ &&\n                            newValue !== oldValue) {\n                            const instance = hostRef.$lazyInstance$ ;\n                            const entry = (_a = cmpMeta.$watchers$) === null || _a === void 0 ? void 0 : _a[attrName];\n                            entry === null || entry === void 0 ? void 0 : entry.forEach((callbackName) => {\n                                if (instance[callbackName] != null) {\n                                    instance[callbackName].call(instance, newValue, oldValue, attrName);\n                                }\n                            });\n                        }\n                        return;\n                    }\n                    this[propName] = newValue === null && typeof this[propName] === 'boolean' ? false : newValue;\n                });\n            };\n            // Create an array of attributes to observe\n            // This list in comprised of all strings used within a `@Watch()` decorator\n            // on a component as well as any Stencil-specific \"members\" (`@Prop()`s and `@State()`s).\n            // As such, there is no way to guarantee type-safety here that a user hasn't entered\n            // an invalid attribute.\n            Cstr.observedAttributes = Array.from(new Set([\n                ...Object.keys((_a = cmpMeta.$watchers$) !== null && _a !== void 0 ? _a : {}),\n                ...members\n                    .filter(([_, m]) => m[0] & 15 /* MEMBER_FLAGS.HasAttribute */)\n                    .map(([propName, m]) => {\n                    var _a;\n                    const attrName = m[1] || propName;\n                    attrNameToPropName.set(attrName, propName);\n                    if (m[0] & 512 /* MEMBER_FLAGS.ReflectAttr */) {\n                        (_a = cmpMeta.$attrsToReflect$) === null || _a === void 0 ? void 0 : _a.push([propName, attrName]);\n                    }\n                    return attrName;\n                }),\n            ]));\n        }\n    }\n    return Cstr;\n};\n/**\n * Initialize a Stencil component given a reference to its host element, its\n * runtime bookkeeping data structure, runtime metadata about the component,\n * and (optionally) an HMR version ID.\n *\n * @param elm a host element\n * @param hostRef the element's runtime bookkeeping object\n * @param cmpMeta runtime metadata for the Stencil component\n * @param hmrVersionId an (optional) HMR version ID\n */\nconst initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n    let Cstr;\n    // initializeComponent\n    if ((hostRef.$flags$ & 32 /* HOST_FLAGS.hasInitializedComponent */) === 0) {\n        // Let the runtime know that the component has been initialized\n        hostRef.$flags$ |= 32 /* HOST_FLAGS.hasInitializedComponent */;\n        const bundleId = cmpMeta.$lazyBundleId$;\n        if (bundleId) {\n            // lazy loaded components\n            // request the component's implementation to be\n            // wired up with the host element\n            Cstr = loadModule(cmpMeta);\n            if (Cstr.then) {\n                // Await creates a micro-task avoid if possible\n                const endLoad = uniqueTime();\n                Cstr = await Cstr;\n                endLoad();\n            }\n            if (!Cstr.isProxied) {\n                // we've never proxied this Constructor before\n                // let's add the getters/setters to its prototype before\n                // the first time we create an instance of the implementation\n                {\n                    cmpMeta.$watchers$ = Cstr.watchers;\n                }\n                proxyComponent(Cstr, cmpMeta, 2 /* PROXY_FLAGS.proxyState */);\n                Cstr.isProxied = true;\n            }\n            const endNewInstance = createTime('createInstance', cmpMeta.$tagName$);\n            // ok, time to construct the instance\n            // but let's keep track of when we start and stop\n            // so that the getters/setters don't incorrectly step on data\n            {\n                hostRef.$flags$ |= 8 /* HOST_FLAGS.isConstructingInstance */;\n            }\n            // construct the lazy-loaded component implementation\n            // passing the hostRef is very important during\n            // construction in order to directly wire together the\n            // host element and the lazy-loaded instance\n            try {\n                new Cstr(hostRef);\n            }\n            catch (e) {\n                consoleError(e);\n            }\n            {\n                hostRef.$flags$ &= ~8 /* HOST_FLAGS.isConstructingInstance */;\n            }\n            {\n                hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */;\n            }\n            endNewInstance();\n            fireConnectedCallback(hostRef.$lazyInstance$);\n        }\n        else {\n            // sync constructor component\n            Cstr = elm.constructor;\n            // wait for the CustomElementRegistry to mark the component as ready before setting `isWatchReady`. Otherwise,\n            // watchers may fire prematurely if `customElements.get()`/`customElements.whenDefined()` resolves _before_\n            // Stencil has completed instantiating the component.\n            customElements.whenDefined(cmpMeta.$tagName$).then(() => (hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */));\n        }\n        if (Cstr.style) {\n            // this component has styles but we haven't registered them yet\n            let style = Cstr.style;\n            if (typeof style !== 'string') {\n                style = style[(hostRef.$modeName$ = computeMode(elm))];\n            }\n            const scopeId = getScopeId(cmpMeta, hostRef.$modeName$);\n            if (!styles.has(scopeId)) {\n                const endRegisterStyles = createTime('registerStyles', cmpMeta.$tagName$);\n                registerStyle(scopeId, style, !!(cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */));\n                endRegisterStyles();\n            }\n        }\n    }\n    // we've successfully created a lazy instance\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    const schedule = () => scheduleUpdate(hostRef, true);\n    if (ancestorComponent && ancestorComponent['s-rc']) {\n        // this is the initial load and this component it has an ancestor component\n        // but the ancestor component has NOT fired its will update lifecycle yet\n        // so let's just cool our jets and wait for the ancestor to continue first\n        // this will get fired off when the ancestor component\n        // finally gets around to rendering its lazy self\n        // fire off the initial update\n        ancestorComponent['s-rc'].push(schedule);\n    }\n    else {\n        schedule();\n    }\n};\nconst fireConnectedCallback = (instance) => {\n    {\n        safeCall(instance, 'connectedCallback');\n    }\n};\nconst connectedCallback = (elm) => {\n    if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n        const hostRef = getHostRef(elm);\n        const cmpMeta = hostRef.$cmpMeta$;\n        const endConnected = createTime('connectedCallback', cmpMeta.$tagName$);\n        if (!(hostRef.$flags$ & 1 /* HOST_FLAGS.hasConnected */)) {\n            // first time this component has connected\n            hostRef.$flags$ |= 1 /* HOST_FLAGS.hasConnected */;\n            let hostId;\n            {\n                hostId = elm.getAttribute(HYDRATE_ID);\n                if (hostId) {\n                    if (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n                        const scopeId = addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute('s-mode'))\n                            ;\n                        elm.classList.remove(scopeId + '-h', scopeId + '-s');\n                    }\n                    initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n                }\n            }\n            if (!hostId) {\n                // initUpdate\n                // if the slot polyfill is required we'll need to put some nodes\n                // in here to act as original content anchors as we move nodes around\n                // host element has been connected to the DOM\n                if ((// TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n                        cmpMeta.$flags$ & (4 /* CMP_FLAGS.hasSlotRelocation */ | 8 /* CMP_FLAGS.needsShadowDomShim */))) {\n                    setContentReference(elm);\n                }\n            }\n            {\n                // find the first ancestor component (if there is one) and register\n                // this component as one of the actively loading child components for its ancestor\n                let ancestorComponent = elm;\n                while ((ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host)) {\n                    // climb up the ancestors looking for the first\n                    // component that hasn't finished its lifecycle update yet\n                    if ((ancestorComponent.nodeType === 1 /* NODE_TYPE.ElementNode */ &&\n                        ancestorComponent.hasAttribute('s-id') &&\n                        ancestorComponent['s-p']) ||\n                        ancestorComponent['s-p']) {\n                        // we found this components first ancestor component\n                        // keep a reference to this component's ancestor component\n                        attachToAncestor(hostRef, (hostRef.$ancestorComponent$ = ancestorComponent));\n                        break;\n                    }\n                }\n            }\n            // Lazy properties\n            // https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n            if (cmpMeta.$members$) {\n                Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n                    if (memberFlags & 31 /* MEMBER_FLAGS.Prop */ && elm.hasOwnProperty(memberName)) {\n                        const value = elm[memberName];\n                        delete elm[memberName];\n                        elm[memberName] = value;\n                    }\n                });\n            }\n            {\n                initializeComponent(elm, hostRef, cmpMeta);\n            }\n        }\n        else {\n            // not the first time this has connected\n            // reattach any event listeners to the host\n            // since they would have been removed when disconnected\n            addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);\n            // fire off connectedCallback() on component instance\n            if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n                fireConnectedCallback(hostRef.$lazyInstance$);\n            }\n            else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n                hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n            }\n        }\n        endConnected();\n    }\n};\nconst setContentReference = (elm) => {\n    // only required when we're NOT using native shadow dom (slot)\n    // or this browser doesn't support native shadow dom\n    // and this host element was NOT created with SSR\n    // let's pick out the inner content for slot projection\n    // create a node to represent where the original\n    // content was first placed, which is useful later on\n    const contentRefElm = (elm['s-cr'] = doc.createComment(''));\n    contentRefElm['s-cn'] = true;\n    elm.insertBefore(contentRefElm, elm.firstChild);\n};\nconst disconnectInstance = (instance) => {\n    {\n        safeCall(instance, 'disconnectedCallback');\n    }\n};\nconst disconnectedCallback = async (elm) => {\n    if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n        const hostRef = getHostRef(elm);\n        {\n            if (hostRef.$rmListeners$) {\n                hostRef.$rmListeners$.map((rmListener) => rmListener());\n                hostRef.$rmListeners$ = undefined;\n            }\n        }\n        if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n            disconnectInstance(hostRef.$lazyInstance$);\n        }\n        else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n            hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n        }\n    }\n};\nconst bootstrapLazy = (lazyBundles, options = {}) => {\n    var _a;\n    const endBootstrap = createTime();\n    const cmpTags = [];\n    const exclude = options.exclude || [];\n    const customElements = win.customElements;\n    const head = doc.head;\n    const metaCharset = /*@__PURE__*/ head.querySelector('meta[charset]');\n    const dataStyles = /*@__PURE__*/ doc.createElement('style');\n    const deferredConnectedCallbacks = [];\n    const styles = /*@__PURE__*/ doc.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n    let appLoadFallback;\n    let isBootstrapping = true;\n    let i = 0;\n    Object.assign(plt, options);\n    plt.$resourcesUrl$ = new URL(options.resourcesUrl || './', doc.baseURI).href;\n    {\n        // If the app is already hydrated there is not point to disable the\n        // async queue. This will improve the first input delay\n        plt.$flags$ |= 2 /* PLATFORM_FLAGS.appLoaded */;\n    }\n    {\n        for (; i < styles.length; i++) {\n            registerStyle(styles[i].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles[i].innerHTML), true);\n        }\n    }\n    let hasSlotRelocation = false;\n    lazyBundles.map((lazyBundle) => {\n        lazyBundle[1].map((compactMeta) => {\n            var _a;\n            const cmpMeta = {\n                $flags$: compactMeta[0],\n                $tagName$: compactMeta[1],\n                $members$: compactMeta[2],\n                $listeners$: compactMeta[3],\n            };\n            // Check if we are using slots outside the shadow DOM in this component.\n            // We'll use this information later to add styles for `slot-fb` elements\n            if (cmpMeta.$flags$ & 4 /* CMP_FLAGS.hasSlotRelocation */) {\n                hasSlotRelocation = true;\n            }\n            {\n                cmpMeta.$members$ = compactMeta[2];\n            }\n            {\n                cmpMeta.$listeners$ = compactMeta[3];\n            }\n            {\n                cmpMeta.$attrsToReflect$ = [];\n            }\n            {\n                cmpMeta.$watchers$ = (_a = compactMeta[4]) !== null && _a !== void 0 ? _a : {};\n            }\n            const tagName = cmpMeta.$tagName$;\n            const HostElement = class extends HTMLElement {\n                // StencilLazyHost\n                constructor(self) {\n                    // @ts-ignore\n                    super(self);\n                    self = this;\n                    registerHost(self, cmpMeta);\n                    if (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n                        // this component is using shadow dom\n                        // and this browser supports shadow dom\n                        // add the read-only property \"shadowRoot\" to the host element\n                        // adding the shadow root build conditionals to minimize runtime\n                        {\n                            {\n                                self.attachShadow({\n                                    mode: 'open',\n                                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* CMP_FLAGS.shadowDelegatesFocus */),\n                                });\n                            }\n                        }\n                    }\n                }\n                connectedCallback() {\n                    if (appLoadFallback) {\n                        clearTimeout(appLoadFallback);\n                        appLoadFallback = null;\n                    }\n                    if (isBootstrapping) {\n                        // connectedCallback will be processed once all components have been registered\n                        deferredConnectedCallbacks.push(this);\n                    }\n                    else {\n                        plt.jmp(() => connectedCallback(this));\n                    }\n                }\n                disconnectedCallback() {\n                    plt.jmp(() => disconnectedCallback(this));\n                }\n                componentOnReady() {\n                    return getHostRef(this).$onReadyPromise$;\n                }\n            };\n            cmpMeta.$lazyBundleId$ = lazyBundle[0];\n            if (!exclude.includes(tagName) && !customElements.get(tagName)) {\n                cmpTags.push(tagName);\n                customElements.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* PROXY_FLAGS.isElementConstructor */));\n            }\n        });\n    });\n    // Only bother generating CSS if we have components\n    // TODO(STENCIL-1118): Add test cases for CSS content based on conditionals\n    if (cmpTags.length > 0) {\n        // Add styles for `slot-fb` elements if any of our components are using slots outside the Shadow DOM\n        if (hasSlotRelocation) {\n            dataStyles.textContent += SLOT_FB_CSS;\n        }\n        // Add hydration styles\n        {\n            dataStyles.textContent += cmpTags + HYDRATED_CSS;\n        }\n        // If we have styles, add them to the DOM\n        if (dataStyles.innerHTML.length) {\n            dataStyles.setAttribute('data-styles', '');\n            // Apply CSP nonce to the style tag if it exists\n            const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n            if (nonce != null) {\n                dataStyles.setAttribute('nonce', nonce);\n            }\n            // Insert the styles into the document head\n            // NOTE: this _needs_ to happen last so we can ensure the nonce (and other attributes) are applied\n            head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n        }\n    }\n    // Process deferred connectedCallbacks now all components have been registered\n    isBootstrapping = false;\n    if (deferredConnectedCallbacks.length) {\n        deferredConnectedCallbacks.map((host) => host.connectedCallback());\n    }\n    else {\n        {\n            plt.jmp(() => (appLoadFallback = setTimeout(appDidLoad, 30)));\n        }\n    }\n    // Fallback appLoad event\n    endBootstrap();\n};\nconst addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n    if (listeners) {\n        listeners.map(([flags, name, method]) => {\n            const target = getHostListenerTarget(elm, flags) ;\n            const handler = hostListenerProxy(hostRef, method);\n            const opts = hostListenerOpts(flags);\n            plt.ael(target, name, handler, opts);\n            (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n        });\n    }\n};\nconst hostListenerProxy = (hostRef, methodName) => (ev) => {\n    try {\n        {\n            if (hostRef.$flags$ & 256 /* HOST_FLAGS.isListenReady */) {\n                // instance is ready, let's call it's member method for this event\n                hostRef.$lazyInstance$[methodName](ev);\n            }\n            else {\n                (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n            }\n        }\n    }\n    catch (e) {\n        consoleError(e);\n    }\n};\nconst getHostListenerTarget = (elm, flags) => {\n    if (flags & 4 /* LISTENER_FLAGS.TargetDocument */)\n        return doc;\n    if (flags & 8 /* LISTENER_FLAGS.TargetWindow */)\n        return win;\n    if (flags & 16 /* LISTENER_FLAGS.TargetBody */)\n        return doc.body;\n    return elm;\n};\n// prettier-ignore\nconst hostListenerOpts = (flags) => supportsListenerOptions\n    ? ({\n        passive: (flags & 1 /* LISTENER_FLAGS.Passive */) !== 0,\n        capture: (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0,\n    })\n    : (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0;\n/**\n * Assigns the given value to the nonce property on the runtime platform object.\n * During runtime, this value is used to set the nonce attribute on all dynamically created script and style tags.\n * @param nonce The value to be assigned to the platform nonce property.\n * @returns void\n */\nconst setNonce = (nonce) => (plt.$nonce$ = nonce);\n/**\n * A WeakMap mapping runtime component references to their corresponding host reference\n * instances.\n *\n * **Note**: If we're in an HMR context we need to store a reference to this\n * value on `window` in order to maintain the mapping of {@link d.RuntimeRef}\n * to {@link d.HostRef} across HMR updates.\n *\n * This is necessary because when HMR updates for a component are processed by\n * the browser-side dev server client the JS bundle for that component is\n * re-fetched. Since the module containing {@link hostRefs} is included in\n * that bundle, if we do not store a reference to it the new iteration of the\n * component will not have access to the previous hostRef map, leading to a\n * bug where the new version of the component cannot properly initialize.\n */\nconst hostRefs = new WeakMap();\n/**\n * Given a {@link d.RuntimeRef} retrieve the corresponding {@link d.HostRef}\n *\n * @param ref the runtime ref of interest\n * @returns the Host reference (if found) or undefined\n */\nconst getHostRef = (ref) => hostRefs.get(ref);\n/**\n * Register a lazy instance with the {@link hostRefs} object so it's\n * corresponding {@link d.HostRef} can be retrieved later.\n *\n * @param lazyInstance the lazy instance of interest\n * @param hostRef that instances `HostRef` object\n * @returns a reference to the host ref WeakMap\n */\nconst registerInstance = (lazyInstance, hostRef) => hostRefs.set((hostRef.$lazyInstance$ = lazyInstance), hostRef);\n/**\n * Register a host element for a Stencil component, setting up various metadata\n * and callbacks based on {@link BUILD} flags as well as the component's runtime\n * metadata.\n *\n * @param hostElement the host element to register\n * @param cmpMeta runtime metadata for that component\n * @returns a reference to the host ref WeakMap\n */\nconst registerHost = (hostElement, cmpMeta) => {\n    const hostRef = {\n        $flags$: 0,\n        $hostElement$: hostElement,\n        $cmpMeta$: cmpMeta,\n        $instanceValues$: new Map(),\n    };\n    {\n        hostRef.$onInstancePromise$ = new Promise((r) => (hostRef.$onInstanceResolve$ = r));\n    }\n    {\n        hostRef.$onReadyPromise$ = new Promise((r) => (hostRef.$onReadyResolve$ = r));\n        hostElement['s-p'] = [];\n        hostElement['s-rc'] = [];\n    }\n    addHostEventListeners(hostElement, hostRef, cmpMeta.$listeners$);\n    return hostRefs.set(hostElement, hostRef);\n};\nconst isMemberInElement = (elm, memberName) => memberName in elm;\nconst consoleError = (e, el) => (0, console.error)(e, el);\nconst cmpModules = /*@__PURE__*/ new Map();\nconst loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n    // loadModuleImport\n    const exportName = cmpMeta.$tagName$.replace(/-/g, '_');\n    const bundleId = cmpMeta.$lazyBundleId$;\n    const module = cmpModules.get(bundleId) ;\n    if (module) {\n        return module[exportName];\n    }\n    /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n    return import(\n    /* @vite-ignore */\n    /* webpackInclude: /\\.entry\\.js$/ */\n    /* webpackExclude: /\\.system\\.entry\\.js$/ */\n    /* webpackMode: \"lazy\" */\n    `./${bundleId}.entry.js${''}`).then((importedModule) => {\n        {\n            cmpModules.set(bundleId, importedModule);\n        }\n        return importedModule[exportName];\n    }, consoleError);\n};\nconst styles = /*@__PURE__*/ new Map();\nconst modeResolutionChain = [];\nconst win = typeof window !== 'undefined' ? window : {};\nconst doc = win.document || { head: {} };\nconst plt = {\n    $flags$: 0,\n    $resourcesUrl$: '',\n    jmp: (h) => h(),\n    raf: (h) => requestAnimationFrame(h),\n    ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n    rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n    ce: (eventName, opts) => new CustomEvent(eventName, opts),\n};\nconst setPlatformHelpers = (helpers) => {\n    Object.assign(plt, helpers);\n};\nconst supportsShadow = \n// TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\ntrue;\nconst supportsListenerOptions = /*@__PURE__*/ (() => {\n    let supportsListenerOptions = false;\n    try {\n        doc.addEventListener('e', null, Object.defineProperty({}, 'passive', {\n            get() {\n                supportsListenerOptions = true;\n            },\n        }));\n    }\n    catch (e) { }\n    return supportsListenerOptions;\n})();\nconst promiseResolve = (v) => Promise.resolve(v);\nconst supportsConstructableStylesheets = /*@__PURE__*/ (() => {\n        try {\n            new CSSStyleSheet();\n            return typeof new CSSStyleSheet().replaceSync === 'function';\n        }\n        catch (e) { }\n        return false;\n    })()\n    ;\nconst queueDomReads = [];\nconst queueDomWrites = [];\nconst queueTask = (queue, write) => (cb) => {\n    queue.push(cb);\n    if (!queuePending) {\n        queuePending = true;\n        if (write && plt.$flags$ & 4 /* PLATFORM_FLAGS.queueSync */) {\n            nextTick(flush);\n        }\n        else {\n            plt.raf(flush);\n        }\n    }\n};\nconst consume = (queue) => {\n    for (let i = 0; i < queue.length; i++) {\n        try {\n            queue[i](performance.now());\n        }\n        catch (e) {\n            consoleError(e);\n        }\n    }\n    queue.length = 0;\n};\nconst flush = () => {\n    // always force a bunch of medium callbacks to run, but still have\n    // a throttle on how many can run in a certain time\n    // DOM READS!!!\n    consume(queueDomReads);\n    // DOM WRITES!!!\n    {\n        consume(queueDomWrites);\n        if ((queuePending = queueDomReads.length > 0)) {\n            // still more to do yet, but we've run out of time\n            // let's let this thing cool off and try again in the next tick\n            plt.raf(flush);\n        }\n    }\n};\nconst nextTick = (cb) => promiseResolve().then(cb);\nconst readTask = /*@__PURE__*/ queueTask(queueDomReads, false);\nconst writeTask = /*@__PURE__*/ queueTask(queueDomWrites, true);\n\nexport { Build as B, Host as H, setPlatformHelpers as a, bootstrapLazy as b, setMode as c, createEvent as d, readTask as e, getElement as f, getMode as g, h, forceUpdate as i, getAssetPath as j, promiseResolve as p, registerInstance as r, setNonce as s, writeTask as w };\n"], "mappings": ";AAAA;AACA;AACA;AACA,MAAMA,SAAS,GAAG,OAAO;AACzB,MAAMC,KAAK,GAAG,WAAY;EAAEC,WAAW,EAAE,KAAK;EAAEC,kBAAkB,EAAE,KAAK;EAAEC,YAAY,EAAE,IAAI;EAAEC,UAAU,EAAE,KAAK;EAAEC,YAAY,EAAE,IAAI;EAAEC,YAAY,EAAE,KAAK;EAAEC,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,YAAY,EAAE,KAAK;EAAEC,YAAY,EAAE,IAAI;EAAEC,eAAe,EAAE,KAAK;EAAEC,WAAW,EAAE,IAAI;EAAEC,aAAa,EAAE,IAAI;EAAEC,aAAa,EAAE,KAAK;EAAEC,iBAAiB,EAAE,IAAI;EAAEC,gBAAgB,EAAE,IAAI;EAAEC,cAAc,EAAE,IAAI;EAAEC,QAAQ,EAAE,KAAK;EAAEC,oBAAoB,EAAE,IAAI;EAAEC,OAAO,EAAE,KAAK;EAAEC,KAAK,EAAE,IAAI;EAAEC,6BAA6B,EAAE,KAAK;EAAEC,qBAAqB,EAAE,KAAK;EAAEC,cAAc,EAAE,KAAK;EAAEC,WAAW,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,kBAAkB,EAAE,IAAI;EAAEC,sBAAsB,EAAE,IAAI;EAAEC,0BAA0B,EAAE,IAAI;EAAEC,wBAAwB,EAAE,KAAK;EAAEC,wBAAwB,EAAE,IAAI;EAAEC,oBAAoB,EAAE,KAAK;EAAEC,iBAAiB,EAAE,IAAI;EAAEC,iBAAiB,EAAE,KAAK;EAAEC,iBAAiB,EAAE,KAAK;EAAEC,aAAa,EAAE,IAAI;EAAEC,kBAAkB,EAAE,KAAK;EAAEC,qBAAqB,EAAE,IAAI;EAAEC,OAAO,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,SAAS,EAAE,KAAK;EAAEC,QAAQ,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,kBAAkB,EAAE,KAAK;EAAEC,MAAM,EAAE,IAAI;EAAEC,MAAM,EAAE,IAAI;EAAEC,IAAI,EAAE,IAAI;EAAEC,gBAAgB,EAAE,IAAI;EAAEC,OAAO,EAAE,KAAK;EAAEC,IAAI,EAAE,IAAI;EAAEC,WAAW,EAAE,IAAI;EAAEC,WAAW,EAAE,IAAI;EAAEC,UAAU,EAAE,IAAI;EAAEC,UAAU,EAAE,IAAI;EAAEC,OAAO,EAAE,IAAI;EAAEC,MAAM,EAAE,IAAI;EAAEC,wBAAwB,EAAE,KAAK;EAAEC,cAAc,EAAE,KAAK;EAAEC,oBAAoB,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,IAAI,EAAE,IAAI;EAAEC,iBAAiB,EAAE,KAAK;EAAEC,cAAc,EAAE,IAAI;EAAEC,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE,IAAI;EAAEC,GAAG,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,gBAAgB,EAAE,KAAK;EAAEC,SAAS,EAAE,IAAI;EAAEC,aAAa,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,IAAI;EAAEC,UAAU,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,aAAa,EAAE;AAAK,CAAC;;AAElrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO;AACX,IAAIC,UAAU;AACd,IAAIC,WAAW;AACf,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,2BAA2B,GAAG,KAAK;AACvC,IAAIC,iBAAiB,GAAG,KAAK;AAC7B,IAAIC,SAAS,GAAG,KAAK;AACrB,IAAIC,YAAY,GAAG,KAAK;AACxB,MAAMC,KAAK,GAAG;EACVlD,KAAK,EAAE,KAAK;EACZmD,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,KAAK;EACfnD,SAAS,EAAE;AACf,CAAC;AACD,MAAMoD,YAAY,GAAIC,IAAI,IAAK;EAC3B,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAACF,IAAI,EAAEG,GAAG,CAACC,cAAc,CAAC;EAClD,OAAOH,QAAQ,CAACI,MAAM,KAAKC,GAAG,CAACC,QAAQ,CAACF,MAAM,GAAGJ,QAAQ,CAACO,IAAI,GAAGP,QAAQ,CAACQ,QAAQ;AACtF,CAAC;AACD,MAAMC,UAAU,GAAGA,CAACC,MAAM,EAAEC,OAAO,GAAG,EAAE,KAAK;EACzC;IACI,OAAO,MAAM;MACT;IACJ,CAAC;EACL;AACJ,CAAC;AACD,MAAMC,UAAU,GAAGA,CAACC,GAAG,EAAEC,WAAW,KAAK;EACrC;IACI,OAAO,MAAM;MACT;IACJ,CAAC;EACL;AACJ,CAAC;AACD,MAAMC,cAAc,GAAG,GAAG;AAC1B,MAAMC,eAAe,GAAG,GAAG;AAC3B,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAMC,YAAY,GAAG,GAAG;AACxB,MAAMC,UAAU,GAAG,MAAM;AACzB,MAAMC,iBAAiB,GAAG,QAAQ;AAClC,MAAMC,gBAAgB,GAAG,MAAM;AAC/B,MAAMC,YAAY,GAAG,kDAAkD;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,wDAAwD;AAC5E,MAAMC,QAAQ,GAAG,8BAA8B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,CAAC,CAAC;AACpB;AACA;AACA;AACA,MAAMC,MAAM,GAAG,4BAA4B;AAC3C,MAAMC,OAAO,GAAG,8BAA8B;AAC9C,MAAMC,KAAK,GAAIC,CAAC,IAAKA,CAAC,IAAI,IAAI;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAIC,CAAC,IAAK;EACzB;EACAA,CAAC,GAAG,OAAOA,CAAC;EACZ,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,UAAU;AAC7C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,GAAG,EAAE;EACnC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGD,GAAG,CAACI,IAAI,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,aAAa,CAAC,wBAAwB,CAAC,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,YAAY,CAAC,SAAS,CAAC,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGI,SAAS;AACnO;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,CAAC,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAE,GAAGC,QAAQ,KAAK;EAC5C,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIhC,GAAG,GAAG,IAAI;EACd,IAAIiC,QAAQ,GAAG,IAAI;EACnB,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,UAAU,GAAG,KAAK;EACtB,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,IAAI,GAAIC,CAAC,IAAK;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/BP,KAAK,GAAGM,CAAC,CAACC,CAAC,CAAC;MACZ,IAAIE,KAAK,CAACC,OAAO,CAACV,KAAK,CAAC,EAAE;QACtBK,IAAI,CAACL,KAAK,CAAC;MACf,CAAC,MACI,IAAIA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;QAClD,IAAKE,MAAM,GAAG,OAAOL,QAAQ,KAAK,UAAU,IAAI,CAACZ,aAAa,CAACe,KAAK,CAAC,EAAG;UACpEA,KAAK,GAAGW,MAAM,CAACX,KAAK,CAAC;QACzB;QACA,IAAIE,MAAM,IAAIC,UAAU,EAAE;UACtB;UACAC,aAAa,CAACA,aAAa,CAACI,MAAM,GAAG,CAAC,CAAC,CAACI,MAAM,IAAIZ,KAAK;QAC3D,CAAC,MACI;UACD;UACAI,aAAa,CAACS,IAAI,CAACX,MAAM,GAAGY,QAAQ,CAAC,IAAI,EAAEd,KAAK,CAAC,GAAGA,KAAK,CAAC;QAC9D;QACAG,UAAU,GAAGD,MAAM;MACvB;IACJ;EACJ,CAAC;EACDG,IAAI,CAACN,QAAQ,CAAC;EACd,IAAID,SAAS,EAAE;IACX,IAAIA,SAAS,CAAC9B,GAAG,EAAE;MACfA,GAAG,GAAG8B,SAAS,CAAC9B,GAAG;IACvB;IACA,IAAI8B,SAAS,CAACiB,IAAI,EAAE;MAChBd,QAAQ,GAAGH,SAAS,CAACiB,IAAI;IAC7B;IACA;IACA;MACI,MAAMC,SAAS,GAAGlB,SAAS,CAACmB,SAAS,IAAInB,SAAS,CAACoB,KAAK;MACxD,IAAIF,SAAS,EAAE;QACXlB,SAAS,CAACoB,KAAK,GACX,OAAOF,SAAS,KAAK,QAAQ,GACvBA,SAAS,GACTG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CACnBK,MAAM,CAAEC,CAAC,IAAKN,SAAS,CAACM,CAAC,CAAC,CAAC,CAC3BC,IAAI,CAAC,GAAG,CAAC;MAC1B;IACJ;EACJ;EACA,IAAI,OAAO1B,QAAQ,KAAK,UAAU,EAAE;IAChC;IACA,OAAOA,QAAQ,CAACC,SAAS,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,SAAS,EAAEM,aAAa,EAAEoB,WAAW,CAAC;EACpF;EACA,MAAMC,KAAK,GAAGX,QAAQ,CAACjB,QAAQ,EAAE,IAAI,CAAC;EACtC4B,KAAK,CAACC,OAAO,GAAG5B,SAAS;EACzB,IAAIM,aAAa,CAACI,MAAM,GAAG,CAAC,EAAE;IAC1BiB,KAAK,CAACE,UAAU,GAAGvB,aAAa;EACpC;EACA;IACIqB,KAAK,CAACG,KAAK,GAAG5D,GAAG;EACrB;EACA;IACIyD,KAAK,CAACI,MAAM,GAAG5B,QAAQ;EAC3B;EACA,OAAOwB,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMX,QAAQ,GAAGA,CAACgB,GAAG,EAAEC,IAAI,KAAK;EAC5B,MAAMN,KAAK,GAAG;IACVO,OAAO,EAAE,CAAC;IACVC,KAAK,EAAEH,GAAG;IACVlB,MAAM,EAAEmB,IAAI;IACZG,KAAK,EAAE,IAAI;IACXP,UAAU,EAAE;EAChB,CAAC;EACD;IACIF,KAAK,CAACC,OAAO,GAAG,IAAI;EACxB;EACA;IACID,KAAK,CAACG,KAAK,GAAG,IAAI;EACtB;EACA;IACIH,KAAK,CAACI,MAAM,GAAG,IAAI;EACvB;EACA,OAAOJ,KAAK;AAChB,CAAC;AACD,MAAMU,IAAI,GAAG,CAAC,CAAC;AACf;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAIC,IAAI,IAAKA,IAAI,IAAIA,IAAI,CAACJ,KAAK,KAAKE,IAAI;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMX,WAAW,GAAG;EAChBc,OAAO,EAAEA,CAACvC,QAAQ,EAAEwC,EAAE,KAAKxC,QAAQ,CAACyC,GAAG,CAACC,eAAe,CAAC,CAACH,OAAO,CAACC,EAAE,CAAC;EACpEC,GAAG,EAAEA,CAACzC,QAAQ,EAAEwC,EAAE,KAAKxC,QAAQ,CAACyC,GAAG,CAACC,eAAe,CAAC,CAACD,GAAG,CAACD,EAAE,CAAC,CAACC,GAAG,CAACE,gBAAgB;AACrF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,eAAe,GAAIJ,IAAI,KAAM;EAC/BM,MAAM,EAAEN,IAAI,CAACX,OAAO;EACpBkB,SAAS,EAAEP,IAAI,CAACV,UAAU;EAC1BkB,IAAI,EAAER,IAAI,CAACT,KAAK;EAChBkB,KAAK,EAAET,IAAI,CAACR,MAAM;EAClBkB,IAAI,EAAEV,IAAI,CAACJ,KAAK;EAChBe,KAAK,EAAEX,IAAI,CAACzB;AAChB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8B,gBAAgB,GAAIL,IAAI,IAAK;EAC/B,IAAI,OAAOA,IAAI,CAACU,IAAI,KAAK,UAAU,EAAE;IACjC,MAAMjD,SAAS,GAAGqB,MAAM,CAAC8B,MAAM,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACM,MAAM,CAAC;IAChD,IAAIN,IAAI,CAACQ,IAAI,EAAE;MACX/C,SAAS,CAAC9B,GAAG,GAAGqE,IAAI,CAACQ,IAAI;IAC7B;IACA,IAAIR,IAAI,CAACS,KAAK,EAAE;MACZhD,SAAS,CAACiB,IAAI,GAAGsB,IAAI,CAACS,KAAK;IAC/B;IACA,OAAOlD,CAAC,CAACyC,IAAI,CAACU,IAAI,EAAEjD,SAAS,EAAE,IAAIuC,IAAI,CAACO,SAAS,IAAI,EAAE,CAAC,CAAC;EAC7D;EACA,MAAMnB,KAAK,GAAGX,QAAQ,CAACuB,IAAI,CAACU,IAAI,EAAEV,IAAI,CAACW,KAAK,CAAC;EAC7CvB,KAAK,CAACC,OAAO,GAAGW,IAAI,CAACM,MAAM;EAC3BlB,KAAK,CAACE,UAAU,GAAGU,IAAI,CAACO,SAAS;EACjCnB,KAAK,CAACG,KAAK,GAAGS,IAAI,CAACQ,IAAI;EACvBpB,KAAK,CAACI,MAAM,GAAGQ,IAAI,CAACS,KAAK;EACzB,OAAOrB,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyB,uBAAuB,GAAGA,CAACC,OAAO,EAAErF,OAAO,EAAEsF,MAAM,EAAEC,OAAO,KAAK;EACnE,MAAMC,UAAU,GAAG1F,UAAU,CAAC,eAAe,EAAEE,OAAO,CAAC;EACvD,MAAMyF,UAAU,GAAGJ,OAAO,CAACI,UAAU;EACrC,MAAMC,gBAAgB,GAAG,EAAE;EAC3B,MAAMC,SAAS,GAAG,EAAE;EACpB,MAAMC,eAAe,GAAGH,UAAU,GAAG,EAAE,GAAG,IAAI;EAC9C,MAAM9B,KAAK,GAAI4B,OAAO,CAACM,OAAO,GAAG7C,QAAQ,CAAChD,OAAO,EAAE,IAAI,CAAE;EACzD,IAAI,CAACT,GAAG,CAACuG,aAAa,EAAE;IACpBC,yBAAyB,CAACzE,GAAG,CAAC0E,IAAI,EAAGzG,GAAG,CAACuG,aAAa,GAAG,IAAIG,GAAG,CAAC,CAAE,CAAC;EACxE;EACAZ,OAAO,CAAC7E,UAAU,CAAC,GAAG8E,MAAM;EAC5BD,OAAO,CAACa,eAAe,CAAC1F,UAAU,CAAC;EACnC2F,aAAa,CAACxC,KAAK,EAAE+B,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEP,OAAO,EAAEA,OAAO,EAAEC,MAAM,CAAC;EAC5FI,gBAAgB,CAAChB,GAAG,CAAElC,CAAC,IAAK;IACxB,MAAM4D,aAAa,GAAG5D,CAAC,CAAC6D,QAAQ,GAAG,GAAG,GAAG7D,CAAC,CAAC8D,QAAQ;IACnD,MAAMC,eAAe,GAAGhH,GAAG,CAACuG,aAAa,CAACU,GAAG,CAACJ,aAAa,CAAC;IAC5D,MAAM7B,IAAI,GAAG/B,CAAC,CAAC4B,KAAK;IACpB;IACA;IACA,IAAImC,eAAe,IAAIE,cAAc,IAAIF,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;MACrEA,eAAe,CAACG,UAAU,CAACC,YAAY,CAACpC,IAAI,EAAEgC,eAAe,CAACK,WAAW,CAAC;IAC9E;IACA,IAAI,CAACnB,UAAU,EAAE;MACblB,IAAI,CAAC,MAAM,CAAC,GAAGvE,OAAO;MACtB,IAAIuG,eAAe,EAAE;QACjBhC,IAAI,CAAC,MAAM,CAAC,GAAGgC,eAAe;QAC9BhC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAGA,IAAI;MAC/B;IACJ;IACAhF,GAAG,CAACuG,aAAa,CAACe,MAAM,CAACT,aAAa,CAAC;EAC3C,CAAC,CAAC;EACF,IAAIX,UAAU,EAAE;IACZG,eAAe,CAAClB,GAAG,CAAEoC,cAAc,IAAK;MACpC,IAAIA,cAAc,EAAE;QAChBrB,UAAU,CAACsB,WAAW,CAACD,cAAc,CAAC;MAC1C;IACJ,CAAC,CAAC;EACN;EACAtB,UAAU,CAAC,CAAC;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,aAAa,GAAGA,CAACa,WAAW,EAAEtB,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEP,OAAO,EAAEd,IAAI,EAAEe,MAAM,KAAK;EACxG,IAAI2B,aAAa;EACjB,IAAIC,WAAW;EACf,IAAIC,UAAU;EACd,IAAI1E,CAAC;EACL,IAAI8B,IAAI,CAAC6C,QAAQ,KAAK,CAAC,CAAC,6BAA6B;IACjDH,aAAa,GAAG1C,IAAI,CAAC3C,YAAY,CAAClB,gBAAgB,CAAC;IACnD,IAAIuG,aAAa,EAAE;MACf;MACA;MACAC,WAAW,GAAGD,aAAa,CAACI,KAAK,CAAC,GAAG,CAAC;MACtC,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK5B,MAAM,IAAI4B,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACrDC,UAAU,GAAG;UACTjD,OAAO,EAAE,CAAC;UACVmC,QAAQ,EAAEa,WAAW,CAAC,CAAC,CAAC;UACxBZ,QAAQ,EAAEY,WAAW,CAAC,CAAC,CAAC;UACxBI,OAAO,EAAEJ,WAAW,CAAC,CAAC,CAAC;UACvBK,OAAO,EAAEL,WAAW,CAAC,CAAC,CAAC;UACvB/C,KAAK,EAAEI,IAAI,CAACvE,OAAO,CAACwH,WAAW,CAAC,CAAC;UACjCpD,KAAK,EAAEG,IAAI;UACXX,OAAO,EAAE,IAAI;UACbC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZjB,MAAM,EAAE;QACZ,CAAC;QACD4C,gBAAgB,CAAC3C,IAAI,CAACoE,UAAU,CAAC;QACjC5C,IAAI,CAAC2B,eAAe,CAACxF,gBAAgB,CAAC;QACtC;QACA;QACA,IAAI,CAACsG,WAAW,CAACnD,UAAU,EAAE;UACzBmD,WAAW,CAACnD,UAAU,GAAG,EAAE;QAC/B;QACA;QACAmD,WAAW,CAACnD,UAAU,CAACsD,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;QACvD;QACAH,WAAW,GAAGG,UAAU;QACxB,IAAIvB,eAAe,IAAIuB,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;UAC/C1B,eAAe,CAACuB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAAC/C,KAAK;QAC1D;MACJ;IACJ;IACA;IACA,KAAK3B,CAAC,GAAG8B,IAAI,CAACkD,UAAU,CAAC/E,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C0D,aAAa,CAACa,WAAW,EAAEtB,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEP,OAAO,EAAEd,IAAI,CAACkD,UAAU,CAAChF,CAAC,CAAC,EAAE6C,MAAM,CAAC;IACjH;IACA,IAAIf,IAAI,CAACkB,UAAU,EAAE;MACjB;MACA,KAAKhD,CAAC,GAAG8B,IAAI,CAACkB,UAAU,CAACgC,UAAU,CAAC/E,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzD0D,aAAa,CAACa,WAAW,EAAEtB,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEP,OAAO,EAAEd,IAAI,CAACkB,UAAU,CAACgC,UAAU,CAAChF,CAAC,CAAC,EAAE6C,MAAM,CAAC;MAC5H;IACJ;EACJ,CAAC,MACI,IAAIf,IAAI,CAAC6C,QAAQ,KAAK,CAAC,CAAC,6BAA6B;IACtD;IACAF,WAAW,GAAG3C,IAAI,CAACmD,SAAS,CAACL,KAAK,CAAC,GAAG,CAAC;IACvC,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK5B,MAAM,IAAI4B,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACrD;MACAD,aAAa,GAAGC,WAAW,CAAC,CAAC,CAAC;MAC9BC,UAAU,GAAG;QACTjD,OAAO,EAAE,CAAC;QACVmC,QAAQ,EAAEa,WAAW,CAAC,CAAC,CAAC;QACxBZ,QAAQ,EAAEY,WAAW,CAAC,CAAC,CAAC;QACxBI,OAAO,EAAEJ,WAAW,CAAC,CAAC,CAAC;QACvBK,OAAO,EAAEL,WAAW,CAAC,CAAC,CAAC;QACvB9C,KAAK,EAAEG,IAAI;QACXX,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZI,KAAK,EAAE,IAAI;QACXrB,MAAM,EAAE;MACZ,CAAC;MACD,IAAImE,aAAa,KAAK1G,YAAY,EAAE;QAChC4G,UAAU,CAAC/C,KAAK,GAAGG,IAAI,CAACqC,WAAW;QACnC,IAAIO,UAAU,CAAC/C,KAAK,IAAI+C,UAAU,CAAC/C,KAAK,CAACgD,QAAQ,KAAK,CAAC,CAAC,0BAA0B;UAC9ED,UAAU,CAACrE,MAAM,GAAGqE,UAAU,CAAC/C,KAAK,CAACuD,WAAW;UAChDjC,gBAAgB,CAAC3C,IAAI,CAACoE,UAAU,CAAC;UACjC;UACA5C,IAAI,CAACqD,MAAM,CAAC,CAAC;UACb,IAAI,CAACZ,WAAW,CAACnD,UAAU,EAAE;YACzBmD,WAAW,CAACnD,UAAU,GAAG,EAAE;UAC/B;UACAmD,WAAW,CAACnD,UAAU,CAACsD,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;UACvD,IAAIvB,eAAe,IAAIuB,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;YAC/C1B,eAAe,CAACuB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAAC/C,KAAK;UAC1D;QACJ;MACJ,CAAC,MACI,IAAI+C,UAAU,CAACd,QAAQ,KAAKf,MAAM,EAAE;QACrC;QACA,IAAI2B,aAAa,KAAK3G,YAAY,EAAE;UAChC;UACA6G,UAAU,CAAChD,KAAK,GAAG,MAAM;UACzB,IAAI+C,WAAW,CAAC,CAAC,CAAC,EAAE;YAChB3C,IAAI,CAAC,MAAM,CAAC,GAAG4C,UAAU,CAACpD,MAAM,GAAGmD,WAAW,CAAC,CAAC,CAAC;UACrD,CAAC,MACI;YACD3C,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;UACrB;UACAA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;UACnB,IAAIqB,eAAe,EAAE;YACjB;YACA;YACAuB,UAAU,CAAC/C,KAAK,GAAG9C,GAAG,CAACuG,aAAa,CAACV,UAAU,CAAChD,KAAK,CAAC;YACtD,IAAIgD,UAAU,CAACpD,MAAM,EAAE;cACnB;cACAoD,UAAU,CAAC/C,KAAK,CAAC0D,YAAY,CAAC,MAAM,EAAEX,UAAU,CAACpD,MAAM,CAAC;YAC5D;YACA;YACAQ,IAAI,CAACmC,UAAU,CAACC,YAAY,CAACQ,UAAU,CAAC/C,KAAK,EAAEG,IAAI,CAAC;YACpD;YACAA,IAAI,CAACqD,MAAM,CAAC,CAAC;YACb,IAAIT,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;cAC5B1B,eAAe,CAACuB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAAC/C,KAAK;YAC1D;UACJ;UACAuB,SAAS,CAAC5C,IAAI,CAACoE,UAAU,CAAC;UAC1B,IAAI,CAACH,WAAW,CAACnD,UAAU,EAAE;YACzBmD,WAAW,CAACnD,UAAU,GAAG,EAAE;UAC/B;UACAmD,WAAW,CAACnD,UAAU,CAACsD,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;QAC3D,CAAC,MACI,IAAIF,aAAa,KAAK7G,cAAc,EAAE;UACvC;UACA,IAAIwF,eAAe,EAAE;YACjB;YACArB,IAAI,CAACqD,MAAM,CAAC,CAAC;UACjB,CAAC,MACI;YACDvC,OAAO,CAAC,MAAM,CAAC,GAAGd,IAAI;YACtBA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;UACvB;QACJ;MACJ;IACJ;EACJ,CAAC,MACI,IAAIyC,WAAW,IAAIA,WAAW,CAAC7C,KAAK,KAAK,OAAO,EAAE;IACnD,MAAMR,KAAK,GAAGX,QAAQ,CAAC,IAAI,EAAEuB,IAAI,CAACoD,WAAW,CAAC;IAC9ChE,KAAK,CAACS,KAAK,GAAGG,IAAI;IAClBZ,KAAK,CAAC4D,OAAO,GAAG,GAAG;IACnBP,WAAW,CAACnD,UAAU,GAAG,CAACF,KAAK,CAAC;EACpC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,yBAAyB,GAAGA,CAACxB,IAAI,EAAEwD,WAAW,KAAK;EACrD,IAAIxD,IAAI,CAAC6C,QAAQ,KAAK,CAAC,CAAC,6BAA6B;IACjD,IAAI3E,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG8B,IAAI,CAACkD,UAAU,CAAC/E,MAAM,EAAED,CAAC,EAAE,EAAE;MACpCsD,yBAAyB,CAACxB,IAAI,CAACkD,UAAU,CAAChF,CAAC,CAAC,EAAEsF,WAAW,CAAC;IAC9D;IACA,IAAIxD,IAAI,CAACkB,UAAU,EAAE;MACjB,KAAKhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,IAAI,CAACkB,UAAU,CAACgC,UAAU,CAAC/E,MAAM,EAAED,CAAC,EAAE,EAAE;QACpDsD,yBAAyB,CAACxB,IAAI,CAACkB,UAAU,CAACgC,UAAU,CAAChF,CAAC,CAAC,EAAEsF,WAAW,CAAC;MACzE;IACJ;EACJ,CAAC,MACI,IAAIxD,IAAI,CAAC6C,QAAQ,KAAK,CAAC,CAAC,6BAA6B;IACtD,MAAMF,WAAW,GAAG3C,IAAI,CAACmD,SAAS,CAACL,KAAK,CAAC,GAAG,CAAC;IAC7C,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK7G,eAAe,EAAE;MACpC0H,WAAW,CAACC,GAAG,CAACd,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE3C,IAAI,CAAC;MAC5DA,IAAI,CAACmD,SAAS,GAAG,EAAE;MACnB;MACA;MACAnD,IAAI,CAAC,MAAM,CAAC,GAAG2C,WAAW,CAAC,CAAC,CAAC;IACjC;EACJ;AACJ,CAAC;AACD;AACA,MAAMe,WAAW,GAAIC,GAAG,IAAKC,mBAAmB,CAACzD,GAAG,CAAE5C,CAAC,IAAKA,CAAC,CAACoG,GAAG,CAAC,CAAC,CAACE,IAAI,CAAEC,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;AACpF;AACA,MAAMC,OAAO,GAAIC,OAAO,IAAKJ,mBAAmB,CAACpF,IAAI,CAACwF,OAAO,CAAC;AAC9D,MAAMC,OAAO,GAAIC,GAAG,IAAKC,UAAU,CAACD,GAAG,CAAC,CAACE,UAAU;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;EAChD;EACA,IAAID,SAAS,IAAI,IAAI,IAAI,CAAC1H,aAAa,CAAC0H,SAAS,CAAC,EAAE;IAChD,IAAIC,QAAQ,GAAG,CAAC,CAAC,4BAA4B;MACzC;MACA;MACA,OAAOD,SAAS,KAAK,OAAO,GAAG,KAAK,GAAGA,SAAS,KAAK,EAAE,IAAI,CAAC,CAACA,SAAS;IAC1E;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,2BAA2B;MACxC;MACA,OAAOC,UAAU,CAACF,SAAS,CAAC;IAChC;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,2BAA2B;MACxC;MACA;MACA,OAAOjG,MAAM,CAACgG,SAAS,CAAC;IAC5B;IACA;IACA,OAAOA,SAAS;EACpB;EACA;EACA;EACA,OAAOA,SAAS;AACpB,CAAC;AACD,MAAMG,UAAU,GAAIP,GAAG,IAAMC,UAAU,CAACD,GAAG,CAAC,CAACQ,aAAe;AAC5D,MAAMC,WAAW,GAAGA,CAACT,GAAG,EAAExF,IAAI,EAAEkG,KAAK,KAAK;EACtC,MAAMjB,GAAG,GAAGc,UAAU,CAACP,GAAG,CAAC;EAC3B,OAAO;IACHW,IAAI,EAAGC,MAAM,IAAK;MACd,OAAOC,SAAS,CAACpB,GAAG,EAAEjF,IAAI,EAAE;QACxBsG,OAAO,EAAE,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,0BAA0B;QAChDK,QAAQ,EAAE,CAAC,EAAEL,KAAK,GAAG,CAAC,CAAC,2BAA2B;QAClDM,UAAU,EAAE,CAAC,EAAEN,KAAK,GAAG,CAAC,CAAC,8BAA8B;QACvDE;MACJ,CAAC,CAAC;IACN;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAGA,CAACpB,GAAG,EAAEjF,IAAI,EAAEyG,IAAI,KAAK;EACnC,MAAMC,EAAE,GAAGpK,GAAG,CAACqK,EAAE,CAAC3G,IAAI,EAAEyG,IAAI,CAAC;EAC7BxB,GAAG,CAAC2B,aAAa,CAACF,EAAE,CAAC;EACrB,OAAOA,EAAE;AACb,CAAC;AACD,MAAMG,iBAAiB,GAAG,aAAc,IAAIC,OAAO,CAAC,CAAC;AACrD,MAAMC,aAAa,GAAGA,CAACxL,OAAO,EAAEyL,OAAO,EAAEC,OAAO,KAAK;EACjD,IAAI3M,KAAK,GAAG4M,MAAM,CAAC3D,GAAG,CAAChI,OAAO,CAAC;EAC/B,IAAI4L,gCAAgC,IAAIF,OAAO,EAAE;IAC7C3M,KAAK,GAAIA,KAAK,IAAI,IAAI8M,aAAa,CAAC,CAAE;IACtC,IAAI,OAAO9M,KAAK,KAAK,QAAQ,EAAE;MAC3BA,KAAK,GAAG0M,OAAO;IACnB,CAAC,MACI;MACD1M,KAAK,CAAC+M,WAAW,CAACL,OAAO,CAAC;IAC9B;EACJ,CAAC,MACI;IACD1M,KAAK,GAAG0M,OAAO;EACnB;EACAE,MAAM,CAACnC,GAAG,CAACxJ,OAAO,EAAEjB,KAAK,CAAC;AAC9B,CAAC;AACD,MAAMgN,QAAQ,GAAGA,CAACC,kBAAkB,EAAEC,OAAO,EAAEpO,IAAI,KAAK;EACpD,IAAIkF,EAAE;EACN,MAAM/C,OAAO,GAAGkM,UAAU,CAACD,OAAO,EAAEpO,IAAI,CAAC;EACzC,MAAMkB,KAAK,GAAG4M,MAAM,CAAC3D,GAAG,CAAChI,OAAO,CAAC;EACjC;EACA;EACAgM,kBAAkB,GAAGA,kBAAkB,CAACpD,QAAQ,KAAK,EAAE,CAAC,mCAAmCoD,kBAAkB,GAAGlJ,GAAG;EACnH,IAAI/D,KAAK,EAAE;IACP,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3BiN,kBAAkB,GAAGA,kBAAkB,CAAC9I,IAAI,IAAI8I,kBAAkB;MAClE,IAAIG,aAAa,GAAGb,iBAAiB,CAACtD,GAAG,CAACgE,kBAAkB,CAAC;MAC7D,IAAII,QAAQ;MACZ,IAAI,CAACD,aAAa,EAAE;QAChBb,iBAAiB,CAAC9B,GAAG,CAACwC,kBAAkB,EAAGG,aAAa,GAAG,IAAIE,GAAG,CAAC,CAAE,CAAC;MAC1E;MACA,IAAI,CAACF,aAAa,CAACG,GAAG,CAACtM,OAAO,CAAC,EAAE;QAC7B,IAAIgM,kBAAkB,CAACO,IAAI,KACtBH,QAAQ,GAAGJ,kBAAkB,CAAC7I,aAAa,CAAC,IAAIlB,iBAAiB,KAAKjC,OAAO,IAAI,CAAC,CAAC,EAAE;UACtF;UACAoM,QAAQ,CAACI,SAAS,GAAGzN,KAAK;QAC9B,CAAC,MACI;UACDqN,QAAQ,GAAGtJ,GAAG,CAACuG,aAAa,CAAC,OAAO,CAAC;UACrC+C,QAAQ,CAACI,SAAS,GAAGzN,KAAK;UAC1B;UACA,MAAM0N,KAAK,GAAG,CAAC1J,EAAE,GAAGhC,GAAG,CAAC2L,OAAO,MAAM,IAAI,IAAI3J,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGF,wBAAwB,CAACC,GAAG,CAAC;UAC/F,IAAI2J,KAAK,IAAI,IAAI,EAAE;YACfL,QAAQ,CAAC9C,YAAY,CAAC,OAAO,EAAEmD,KAAK,CAAC;UACzC;UACAT,kBAAkB,CAAC7D,YAAY,CAACiE,QAAQ,EAAEJ,kBAAkB,CAAC7I,aAAa,CAAC,MAAM,CAAC,CAAC;QACvF;QACA;QACA,IAAI8I,OAAO,CAACvG,OAAO,GAAG,CAAC,CAAC,mCAAmC;UACvD0G,QAAQ,CAACI,SAAS,IAAIpK,WAAW;QACrC;QACA,IAAI+J,aAAa,EAAE;UACfA,aAAa,CAACQ,GAAG,CAAC3M,OAAO,CAAC;QAC9B;MACJ;IACJ,CAAC,MACI,IAAI,CAACgM,kBAAkB,CAACY,kBAAkB,CAACC,QAAQ,CAAC9N,KAAK,CAAC,EAAE;MAC7DiN,kBAAkB,CAACY,kBAAkB,GAAG,CAAC,GAAGZ,kBAAkB,CAACY,kBAAkB,EAAE7N,KAAK,CAAC;IAC7F;EACJ;EACA,OAAOiB,OAAO;AAClB,CAAC;AACD,MAAM7E,YAAY,GAAI4L,OAAO,IAAK;EAC9B,MAAMkF,OAAO,GAAGlF,OAAO,CAAC+F,SAAS;EACjC,MAAMpD,GAAG,GAAG3C,OAAO,CAAC0D,aAAa;EACjC,MAAME,KAAK,GAAGsB,OAAO,CAACvG,OAAO;EAC7B,MAAMqH,eAAe,GAAGzL,UAAU,CAAC,cAAc,EAAE2K,OAAO,CAACe,SAAS,CAAC;EACrE,MAAMhN,OAAO,GAAG+L,QAAQ,CAACrC,GAAG,CAACzC,UAAU,GAAGyC,GAAG,CAACzC,UAAU,GAAGyC,GAAG,CAACuD,WAAW,CAAC,CAAC,EAAEhB,OAAO,EAAElF,OAAO,CAACoD,UAAU,CAAC;EAC1G,IAAIQ,KAAK,GAAG,EAAE,CAAC,0CAA0C;IACrD;IACA;IACA;IACA;IACA;IACA;IACA;IACAjB,GAAG,CAAC,MAAM,CAAC,GAAG1J,OAAO;IACrB0J,GAAG,CAACwD,SAAS,CAACP,GAAG,CAAC3M,OAAO,GAAG,IAAI,CAAC;IACjC,IAAI2K,KAAK,GAAG,CAAC,CAAC,wCAAwC;MAClDjB,GAAG,CAACwD,SAAS,CAACP,GAAG,CAAC3M,OAAO,GAAG,IAAI,CAAC;IACrC;EACJ;EACA+M,eAAe,CAAC,CAAC;AACrB,CAAC;AACD,MAAMb,UAAU,GAAGA,CAACiB,GAAG,EAAEtP,IAAI,KAAK,KAAK,IAAIA,IAAI,IAAIsP,GAAG,CAACzH,OAAO,GAAG,EAAE,CAAC,0BAA0ByH,GAAG,CAACH,SAAS,GAAG,GAAG,GAAGnP,IAAI,GAAGsP,GAAG,CAACH,SAAS,CAAC;AACzI,MAAMI,qBAAqB,GAAIC,GAAG,IAAKA,GAAG,CAACC,OAAO,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAC7D,GAAG,EAAE8D,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEhD,KAAK,KAAK;EACvE,IAAI8C,QAAQ,KAAKC,QAAQ,EAAE;IACvB,IAAIE,MAAM,GAAGC,iBAAiB,CAACnE,GAAG,EAAE8D,UAAU,CAAC;IAC/C,IAAIM,EAAE,GAAGN,UAAU,CAACxE,WAAW,CAAC,CAAC;IACjC,IAAIwE,UAAU,KAAK,OAAO,EAAE;MACxB,MAAMN,SAAS,GAAGxD,GAAG,CAACwD,SAAS;MAC/B,MAAMa,UAAU,GAAGC,cAAc,CAACP,QAAQ,CAAC;MAC3C,MAAMQ,UAAU,GAAGD,cAAc,CAACN,QAAQ,CAAC;MAC3CR,SAAS,CAAC9D,MAAM,CAAC,GAAG2E,UAAU,CAAChJ,MAAM,CAAEf,CAAC,IAAKA,CAAC,IAAI,CAACiK,UAAU,CAACpB,QAAQ,CAAC7I,CAAC,CAAC,CAAC,CAAC;MAC3EkJ,SAAS,CAACP,GAAG,CAAC,GAAGsB,UAAU,CAAClJ,MAAM,CAAEf,CAAC,IAAKA,CAAC,IAAI,CAAC+J,UAAU,CAAClB,QAAQ,CAAC7I,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC,MACI,IAAIwJ,UAAU,KAAK,OAAO,EAAE;MAC7B;MACA;QACI,KAAK,MAAMxP,IAAI,IAAIyP,QAAQ,EAAE;UACzB,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAC1P,IAAI,CAAC,IAAI,IAAI,EAAE;YACrC,IAAIA,IAAI,CAAC6O,QAAQ,CAAC,GAAG,CAAC,EAAE;cACpBnD,GAAG,CAAC3K,KAAK,CAACmP,cAAc,CAAClQ,IAAI,CAAC;YAClC,CAAC,MACI;cACD0L,GAAG,CAAC3K,KAAK,CAACf,IAAI,CAAC,GAAG,EAAE;YACxB;UACJ;QACJ;MACJ;MACA,KAAK,MAAMA,IAAI,IAAI0P,QAAQ,EAAE;QACzB,IAAI,CAACD,QAAQ,IAAIC,QAAQ,CAAC1P,IAAI,CAAC,KAAKyP,QAAQ,CAACzP,IAAI,CAAC,EAAE;UAChD,IAAIA,IAAI,CAAC6O,QAAQ,CAAC,GAAG,CAAC,EAAE;YACpBnD,GAAG,CAAC3K,KAAK,CAACoP,WAAW,CAACnQ,IAAI,EAAE0P,QAAQ,CAAC1P,IAAI,CAAC,CAAC;UAC/C,CAAC,MACI;YACD0L,GAAG,CAAC3K,KAAK,CAACf,IAAI,CAAC,GAAG0P,QAAQ,CAAC1P,IAAI,CAAC;UACpC;QACJ;MACJ;IACJ,CAAC,MACI,IAAIwP,UAAU,KAAK,KAAK,EACzB,CAAC,KACA,IAAIA,UAAU,KAAK,KAAK,EAAE;MAC3B;MACA,IAAIE,QAAQ,EAAE;QACVA,QAAQ,CAAChE,GAAG,CAAC;MACjB;IACJ,CAAC,MACI,IAAK,CAACkE,MAAM,IACbJ,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IACrBA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvB;MACA;MACA;MACA;MACA,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACAA,UAAU,GAAGA,UAAU,CAACY,KAAK,CAAC,CAAC,CAAC;MACpC,CAAC,MACI,IAAIP,iBAAiB,CAAC3M,GAAG,EAAE4M,EAAE,CAAC,EAAE;QACjC;QACA;QACA;QACA;QACAN,UAAU,GAAGM,EAAE,CAACM,KAAK,CAAC,CAAC,CAAC;MAC5B,CAAC,MACI;QACD;QACA;QACA;QACA;QACA;QACAZ,UAAU,GAAGM,EAAE,CAAC,CAAC,CAAC,GAAGN,UAAU,CAACY,KAAK,CAAC,CAAC,CAAC;MAC5C;MACA,IAAIX,QAAQ,IAAIC,QAAQ,EAAE;QACtB;QACA;QACA;QACA,MAAMW,OAAO,GAAGb,UAAU,CAACc,QAAQ,CAACC,oBAAoB,CAAC;QACzD;QACAf,UAAU,GAAGA,UAAU,CAACF,OAAO,CAACkB,mBAAmB,EAAE,EAAE,CAAC;QACxD,IAAIf,QAAQ,EAAE;UACV1M,GAAG,CAAC0N,GAAG,CAAC/E,GAAG,EAAE8D,UAAU,EAAEC,QAAQ,EAAEY,OAAO,CAAC;QAC/C;QACA,IAAIX,QAAQ,EAAE;UACV3M,GAAG,CAAC2N,GAAG,CAAChF,GAAG,EAAE8D,UAAU,EAAEE,QAAQ,EAAEW,OAAO,CAAC;QAC/C;MACJ;IACJ,CAAC,MACI;MACD;MACA,MAAMM,SAAS,GAAGhM,aAAa,CAAC+K,QAAQ,CAAC;MACzC,IAAI,CAACE,MAAM,IAAKe,SAAS,IAAIjB,QAAQ,KAAK,IAAK,KAAK,CAACC,KAAK,EAAE;QACxD,IAAI;UACA,IAAI,CAACjE,GAAG,CAAClI,OAAO,CAACqL,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC5B,MAAM+B,CAAC,GAAGlB,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ;YAC1C;YACA,IAAIF,UAAU,KAAK,MAAM,EAAE;cACvBI,MAAM,GAAG,KAAK;YAClB,CAAC,MACI,IAAIH,QAAQ,IAAI,IAAI,IAAI/D,GAAG,CAAC8D,UAAU,CAAC,IAAIoB,CAAC,EAAE;cAC/ClF,GAAG,CAAC8D,UAAU,CAAC,GAAGoB,CAAC;YACvB;UACJ,CAAC,MACI;YACDlF,GAAG,CAAC8D,UAAU,CAAC,GAAGE,QAAQ;UAC9B;QACJ,CAAC,CACD,OAAOmB,CAAC,EAAE;UACN;AACpB;AACA;QAFoB;MAIR;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAIC,KAAK,GAAG,KAAK;MACjB;QACI,IAAIhB,EAAE,MAAMA,EAAE,GAAGA,EAAE,CAACR,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE;UAC3CE,UAAU,GAAGM,EAAE;UACfgB,KAAK,GAAG,IAAI;QAChB;MACJ;MACA,IAAIpB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,KAAK,EAAE;QACxC,IAAIA,QAAQ,KAAK,KAAK,IAAIhE,GAAG,CAACtG,YAAY,CAACoK,UAAU,CAAC,KAAK,EAAE,EAAE;UAC3D,IAAIsB,KAAK,EAAE;YACPpF,GAAG,CAACqF,iBAAiB,CAAC1M,QAAQ,EAAEmL,UAAU,CAAC;UAC/C,CAAC,MACI;YACD9D,GAAG,CAAChC,eAAe,CAAC8F,UAAU,CAAC;UACnC;QACJ;MACJ,CAAC,MACI,IAAI,CAAC,CAACI,MAAM,IAAIjD,KAAK,GAAG,CAAC,CAAC,4BAA4BgD,KAAK,KAAK,CAACgB,SAAS,EAAE;QAC7EjB,QAAQ,GAAGA,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAGA,QAAQ;QAC5C,IAAIoB,KAAK,EAAE;UACPpF,GAAG,CAACsF,cAAc,CAAC3M,QAAQ,EAAEmL,UAAU,EAAEE,QAAQ,CAAC;QACtD,CAAC,MACI;UACDhE,GAAG,CAACJ,YAAY,CAACkE,UAAU,EAAEE,QAAQ,CAAC;QAC1C;MACJ;IACJ;EACJ;AACJ,CAAC;AACD,MAAMuB,mBAAmB,GAAG,IAAI;AAChC;AACA;AACA;AACA;AACA;AACA,MAAMjB,cAAc,GAAIkB,KAAK,IAAM,CAACA,KAAK,GAAG,EAAE,GAAGA,KAAK,CAACrG,KAAK,CAACoG,mBAAmB,CAAE;AAClF,MAAMV,oBAAoB,GAAG,SAAS;AACtC,MAAMC,mBAAmB,GAAG,IAAIW,MAAM,CAACZ,oBAAoB,GAAG,GAAG,CAAC;AAClE,MAAMa,aAAa,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEhP,SAAS,EAAEkN,UAAU,KAAK;EACjE;EACA;EACA;EACA,MAAM9D,GAAG,GAAG4F,QAAQ,CAAC1J,KAAK,CAACgD,QAAQ,KAAK,EAAE,CAAC,oCAAoC0G,QAAQ,CAAC1J,KAAK,CAAC2G,IAAI,GAC5F+C,QAAQ,CAAC1J,KAAK,CAAC2G,IAAI,GACnB+C,QAAQ,CAAC1J,KAAK;EACpB,MAAM2J,aAAa,GAAIF,QAAQ,IAAIA,QAAQ,CAACjK,OAAO,IAAK9C,SAAS;EACjE,MAAMkN,aAAa,GAAGF,QAAQ,CAAClK,OAAO,IAAI9C,SAAS;EACnD;IACI;IACA,KAAKkL,UAAU,IAAIiC,eAAe,CAAC5K,MAAM,CAACC,IAAI,CAACyK,aAAa,CAAC,CAAC,EAAE;MAC5D,IAAI,EAAE/B,UAAU,IAAIgC,aAAa,CAAC,EAAE;QAChCjC,WAAW,CAAC7D,GAAG,EAAE8D,UAAU,EAAE+B,aAAa,CAAC/B,UAAU,CAAC,EAAEnK,SAAS,EAAE/C,SAAS,EAAEgP,QAAQ,CAAC5J,OAAO,CAAC;MACnG;IACJ;EACJ;EACA;EACA,KAAK8H,UAAU,IAAIiC,eAAe,CAAC5K,MAAM,CAACC,IAAI,CAAC0K,aAAa,CAAC,CAAC,EAAE;IAC5DjC,WAAW,CAAC7D,GAAG,EAAE8D,UAAU,EAAE+B,aAAa,CAAC/B,UAAU,CAAC,EAAEgC,aAAa,CAAChC,UAAU,CAAC,EAAElN,SAAS,EAAEgP,QAAQ,CAAC5J,OAAO,CAAC;EACnH;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+J,eAAeA,CAACC,SAAS,EAAE;EAChC,OAAOA,SAAS,CAAC7C,QAAQ,CAAC,KAAK,CAAC;EAC1B;EACE,CAAC,GAAG6C,SAAS,CAAC3K,MAAM,CAAE4K,IAAI,IAAKA,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,CAAC;EACxD;EACED,SAAS;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,SAAS,GAAGA,CAACC,cAAc,EAAEC,cAAc,EAAEC,UAAU,EAAEC,SAAS,KAAK;EACzE,IAAIjN,EAAE;EACN;EACA,MAAMyB,QAAQ,GAAGsL,cAAc,CAACzK,UAAU,CAAC0K,UAAU,CAAC;EACtD,IAAI9L,CAAC,GAAG,CAAC;EACT,IAAIyF,GAAG;EACP,IAAIuG,SAAS;EACb,IAAIC,QAAQ;EACZ,IAAI,CAAC/P,kBAAkB,EAAE;IACrB;IACAE,iBAAiB,GAAG,IAAI;IACxB,IAAImE,QAAQ,CAACmB,KAAK,KAAK,MAAM,EAAE;MAC3B,IAAI3F,OAAO,EAAE;QACT;QACAgQ,SAAS,CAAC9C,SAAS,CAACP,GAAG,CAAC3M,OAAO,GAAG,IAAI,CAAC;MAC3C;MACAwE,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACa,UAAU;MACjC;MACE,CAAC,CAAC;MACJ;MACE,CAAC,CAAC;IACd;EACJ;EACA,IAAIb,QAAQ,CAACF,MAAM,KAAK,IAAI,EAAE;IAC1B;IACAoF,GAAG,GAAGlF,QAAQ,CAACoB,KAAK,GAAG9C,GAAG,CAACqN,cAAc,CAAC3L,QAAQ,CAACF,MAAM,CAAC;EAC9D,CAAC,MACI,IAAIE,QAAQ,CAACkB,OAAO,GAAG,CAAC,CAAC,mCAAmC;IAC7D;IACAgE,GAAG,GAAGlF,QAAQ,CAACoB,KAAK,GAChB9C,GAAG,CAACqN,cAAc,CAAC,EAAE,CAAC;EAC9B,CAAC,MACI;IACD,IAAI,CAAC7P,SAAS,EAAE;MACZA,SAAS,GAAGkE,QAAQ,CAACmB,KAAK,KAAK,KAAK;IACxC;IACA;IACA+D,GAAG,GAAGlF,QAAQ,CAACoB,KAAK,GAAI9C,GAAG,CAACsN,eAAe,CAAC9P,SAAS,GAAGiC,MAAM,GAAGC,OAAO,EAAEgC,QAAQ,CAACkB,OAAO,GAAG,CAAC,CAAC,mCACrF,SAAS,GACTlB,QAAQ,CAACmB,KAAK,CACnB;IACL,IAAIrF,SAAS,IAAIkE,QAAQ,CAACmB,KAAK,KAAK,eAAe,EAAE;MACjDrF,SAAS,GAAG,KAAK;IACrB;IACA;IACA;MACI8O,aAAa,CAAC,IAAI,EAAE5K,QAAQ,EAAElE,SAAS,CAAC;IAC5C;IACA,IAAImC,KAAK,CAACzC,OAAO,CAAC,IAAI0J,GAAG,CAAC,MAAM,CAAC,KAAK1J,OAAO,EAAE;MAC3C;MACA;MACA0J,GAAG,CAACwD,SAAS,CAACP,GAAG,CAAEjD,GAAG,CAAC,MAAM,CAAC,GAAG1J,OAAQ,CAAC;IAC9C;IACA,IAAIwE,QAAQ,CAACa,UAAU,EAAE;MACrB,KAAKpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,QAAQ,CAACa,UAAU,CAACnB,MAAM,EAAE,EAAED,CAAC,EAAE;QAC7C;QACAgM,SAAS,GAAGL,SAAS,CAACC,cAAc,EAAErL,QAAQ,EAAEP,CAAC,EAAEyF,GAAG,CAAC;QACvD;QACA,IAAIuG,SAAS,EAAE;UACX;UACAvG,GAAG,CAACnB,WAAW,CAAC0H,SAAS,CAAC;QAC9B;MACJ;IACJ;IACA;MACI,IAAIzL,QAAQ,CAACmB,KAAK,KAAK,KAAK,EAAE;QAC1B;QACArF,SAAS,GAAG,KAAK;MACrB,CAAC,MACI,IAAIoJ,GAAG,CAAClI,OAAO,KAAK,eAAe,EAAE;QACtC;QACAlB,SAAS,GAAG,IAAI;MACpB;IACJ;EACJ;EACA;EACA;EACAoJ,GAAG,CAAC,MAAM,CAAC,GAAGxJ,WAAW;EACzB;IACI,IAAIsE,QAAQ,CAACkB,OAAO,IAAI,CAAC,CAAC,mCAAmC,CAAC,CAAC,kCAAkC,EAAE;MAC/F;MACAgE,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI;MAClB;MACAA,GAAG,CAAC,MAAM,CAAC,GAAGzJ,UAAU;MACxB;MACAyJ,GAAG,CAAC,MAAM,CAAC,GAAGlF,QAAQ,CAACe,MAAM,IAAI,EAAE;MACnC;MACAmE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC3G,EAAE,GAAGyB,QAAQ,CAACY,OAAO,MAAM,IAAI,IAAIrC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkH,GAAG;MACjF;MACAiG,QAAQ,GAAGL,cAAc,IAAIA,cAAc,CAACxK,UAAU,IAAIwK,cAAc,CAACxK,UAAU,CAAC0K,UAAU,CAAC;MAC/F,IAAIG,QAAQ,IAAIA,QAAQ,CAACvK,KAAK,KAAKnB,QAAQ,CAACmB,KAAK,IAAIkK,cAAc,CAACjK,KAAK,EAAE;QACvE;UACI;UACA;UACAyK,yBAAyB,CAACR,cAAc,CAACjK,KAAK,EAAE,KAAK,CAAC;QAC1D;MACJ;IACJ;EACJ;EACA,OAAO8D,GAAG;AACd,CAAC;AACD,MAAM2G,yBAAyB,GAAGA,CAACL,SAAS,EAAEM,SAAS,KAAK;EACxDvP,GAAG,CAAC2E,OAAO,IAAI,CAAC,CAAC;EACjB,MAAM6K,iBAAiB,GAAGpM,KAAK,CAACqM,IAAI,CAACR,SAAS,CAAC/G,UAAU,CAAC;EAC1D,IAAI+G,SAAS,CAAC,MAAM,CAAC,IAAIlV,KAAK,CAACuB,qBAAqB,EAAE;IAClD,IAAI0J,IAAI,GAAGiK,SAAS;IACpB,OAAQjK,IAAI,GAAGA,IAAI,CAACqC,WAAW,EAAG;MAC9B,IAAIrC,IAAI,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAKiK,SAAS,CAAC,MAAM,CAAC,IAAIjK,IAAI,CAAC,MAAM,CAAC,KAAK7F,WAAW,EAAE;QAC5EqQ,iBAAiB,CAAChM,IAAI,CAACwB,IAAI,CAAC;MAChC;IACJ;EACJ;EACA,KAAK,IAAI9B,CAAC,GAAGsM,iBAAiB,CAACrM,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACpD,MAAMgM,SAAS,GAAGM,iBAAiB,CAACtM,CAAC,CAAC;IACtC,IAAIgM,SAAS,CAAC,MAAM,CAAC,KAAK/P,WAAW,IAAI+P,SAAS,CAAC,MAAM,CAAC,EAAE;MACxD;MACAQ,mBAAmB,CAACR,SAAS,CAAC,CAAC9H,YAAY,CAAC8H,SAAS,EAAES,aAAa,CAACT,SAAS,CAAC,CAAC;MAChF;MACA;MACA;MACAA,SAAS,CAAC,MAAM,CAAC,CAAC7G,MAAM,CAAC,CAAC;MAC1B6G,SAAS,CAAC,MAAM,CAAC,GAAG5M,SAAS;MAC7B;MACA4M,SAAS,CAAC,MAAM,CAAC,GAAG5M,SAAS;MAC7BhD,iBAAiB,GAAG,IAAI;IAC5B;IACA,IAAIiQ,SAAS,EAAE;MACXD,yBAAyB,CAACJ,SAAS,EAAEK,SAAS,CAAC;IACnD;EACJ;EACAvP,GAAG,CAAC2E,OAAO,IAAI,CAAC,CAAC,CAAC;AACtB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiL,SAAS,GAAGA,CAACX,SAAS,EAAEY,MAAM,EAAEpI,WAAW,EAAEqI,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC5E,IAAIC,YAAY,GAAKhB,SAAS,CAAC,MAAM,CAAC,IAAIA,SAAS,CAAC,MAAM,CAAC,CAAC9H,UAAU,IAAK8H,SAAU;EACrF,IAAIC,SAAS;EACb,IAAIe,YAAY,CAAC/J,UAAU,IAAI+J,YAAY,CAACxP,OAAO,KAAKtB,WAAW,EAAE;IACjE8Q,YAAY,GAAGA,YAAY,CAAC/J,UAAU;EAC1C;EACA,OAAO6J,QAAQ,IAAIC,MAAM,EAAE,EAAED,QAAQ,EAAE;IACnC,IAAID,MAAM,CAACC,QAAQ,CAAC,EAAE;MAClBb,SAAS,GAAGL,SAAS,CAAC,IAAI,EAAEpH,WAAW,EAAEsI,QAAQ,EAAEd,SAAS,CAAC;MAC7D,IAAIC,SAAS,EAAE;QACXY,MAAM,CAACC,QAAQ,CAAC,CAAClL,KAAK,GAAGqK,SAAS;QAClCe,YAAY,CAAC7I,YAAY,CAAC8H,SAAS,EAAES,aAAa,CAACE,MAAM,CAAE,CAAC;MAChE;IACJ;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,YAAY,GAAGA,CAACJ,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC/C,KAAK,IAAIG,KAAK,GAAGJ,QAAQ,EAAEI,KAAK,IAAIH,MAAM,EAAE,EAAEG,KAAK,EAAE;IACjD,MAAM/L,KAAK,GAAG0L,MAAM,CAACK,KAAK,CAAC;IAC3B,IAAI/L,KAAK,EAAE;MACP,MAAMuE,GAAG,GAAGvE,KAAK,CAACS,KAAK;MACvBuL,gBAAgB,CAAChM,KAAK,CAAC;MACvB,IAAIuE,GAAG,EAAE;QACL;UACI;UACA;UACAtJ,2BAA2B,GAAG,IAAI;UAClC,IAAIsJ,GAAG,CAAC,MAAM,CAAC,EAAE;YACb;YACAA,GAAG,CAAC,MAAM,CAAC,CAACN,MAAM,CAAC,CAAC;UACxB,CAAC,MACI;YACD;YACA;YACAiH,yBAAyB,CAAC3G,GAAG,EAAE,IAAI,CAAC;UACxC;QACJ;QACA;QACAA,GAAG,CAACN,MAAM,CAAC,CAAC;MAChB;IACJ;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgI,cAAc,GAAGA,CAACpB,SAAS,EAAEqB,KAAK,EAAE7M,QAAQ,EAAE8M,KAAK,EAAEC,eAAe,GAAG,KAAK,KAAK;EACnF,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIzN,CAAC,GAAG,CAAC;EACT,IAAI0N,SAAS,GAAGN,KAAK,CAACnN,MAAM,GAAG,CAAC;EAChC,IAAI0N,aAAa,GAAGP,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIQ,WAAW,GAAGR,KAAK,CAACM,SAAS,CAAC;EAClC,IAAIG,SAAS,GAAGR,KAAK,CAACpN,MAAM,GAAG,CAAC;EAChC,IAAI6N,aAAa,GAAGT,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIU,WAAW,GAAGV,KAAK,CAACQ,SAAS,CAAC;EAClC,IAAI/L,IAAI;EACR,IAAIkM,SAAS;EACb,OAAOT,WAAW,IAAIG,SAAS,IAAIF,WAAW,IAAIK,SAAS,EAAE;IACzD,IAAIF,aAAa,IAAI,IAAI,EAAE;MACvB;MACAA,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;IACxC,CAAC,MACI,IAAIK,WAAW,IAAI,IAAI,EAAE;MAC1BA,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;IACpC,CAAC,MACI,IAAII,aAAa,IAAI,IAAI,EAAE;MAC5BA,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACxC,CAAC,MACI,IAAIO,WAAW,IAAI,IAAI,EAAE;MAC1BA,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IACpC,CAAC,MACI,IAAII,WAAW,CAACN,aAAa,EAAEG,aAAa,EAAER,eAAe,CAAC,EAAE;MACjE;MACA;MACA;MACA;MACAY,KAAK,CAACP,aAAa,EAAEG,aAAa,EAAER,eAAe,CAAC;MACpDK,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;MACpCO,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACxC,CAAC,MACI,IAAIS,WAAW,CAACL,WAAW,EAAEG,WAAW,EAAET,eAAe,CAAC,EAAE;MAC7D;MACA;MACA;MACAY,KAAK,CAACN,WAAW,EAAEG,WAAW,EAAET,eAAe,CAAC;MAChDM,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;MAChCK,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IACpC,CAAC,MACI,IAAII,WAAW,CAACN,aAAa,EAAEI,WAAW,EAAET,eAAe,CAAC,EAAE;MAC/D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAKK,aAAa,CAACjM,KAAK,KAAK,MAAM,IAAIqM,WAAW,CAACrM,KAAK,KAAK,MAAM,EAAG;QAClE0K,yBAAyB,CAACuB,aAAa,CAAChM,KAAK,CAACsC,UAAU,EAAE,KAAK,CAAC;MACpE;MACAiK,KAAK,CAACP,aAAa,EAAEI,WAAW,EAAET,eAAe,CAAC;MAClD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAvB,SAAS,CAAC7H,YAAY,CAACyJ,aAAa,CAAChM,KAAK,EAAEiM,WAAW,CAACjM,KAAK,CAACwC,WAAW,CAAC;MAC1EwJ,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;MACpCQ,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IACpC,CAAC,MACI,IAAII,WAAW,CAACL,WAAW,EAAEE,aAAa,EAAER,eAAe,CAAC,EAAE;MAC/D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAKK,aAAa,CAACjM,KAAK,KAAK,MAAM,IAAIqM,WAAW,CAACrM,KAAK,KAAK,MAAM,EAAG;QAClE0K,yBAAyB,CAACwB,WAAW,CAACjM,KAAK,CAACsC,UAAU,EAAE,KAAK,CAAC;MAClE;MACAiK,KAAK,CAACN,WAAW,EAAEE,aAAa,EAAER,eAAe,CAAC;MAClD;MACA;MACA;MACA;MACA;MACAvB,SAAS,CAAC7H,YAAY,CAAC0J,WAAW,CAACjM,KAAK,EAAEgM,aAAa,CAAChM,KAAK,CAAC;MAC9DiM,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;MAChCI,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACxC,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,QAAQ,GAAG,CAAC,CAAC;MACb;QACI,KAAKzN,CAAC,GAAGuN,WAAW,EAAEvN,CAAC,IAAI0N,SAAS,EAAE,EAAE1N,CAAC,EAAE;UACvC,IAAIoN,KAAK,CAACpN,CAAC,CAAC,IAAIoN,KAAK,CAACpN,CAAC,CAAC,CAACqB,KAAK,KAAK,IAAI,IAAI+L,KAAK,CAACpN,CAAC,CAAC,CAACqB,KAAK,KAAKyM,aAAa,CAACzM,KAAK,EAAE;YAC/EoM,QAAQ,GAAGzN,CAAC;YACZ;UACJ;QACJ;MACJ;MACA,IAAIyN,QAAQ,IAAI,CAAC,EAAE;QACf;QACA;QACAO,SAAS,GAAGZ,KAAK,CAACK,QAAQ,CAAC;QAC3B,IAAIO,SAAS,CAACtM,KAAK,KAAKoM,aAAa,CAACpM,KAAK,EAAE;UACzC;UACAI,IAAI,GAAG6J,SAAS,CAACyB,KAAK,IAAIA,KAAK,CAACI,WAAW,CAAC,EAAEjN,QAAQ,EAAEkN,QAAQ,EAAE1B,SAAS,CAAC;QAChF,CAAC,MACI;UACDmC,KAAK,CAACF,SAAS,EAAEF,aAAa,EAAER,eAAe,CAAC;UAChD;UACA;UACAF,KAAK,CAACK,QAAQ,CAAC,GAAGrO,SAAS;UAC3B0C,IAAI,GAAGkM,SAAS,CAACrM,KAAK;QAC1B;QACAmM,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;MACxC,CAAC,MACI;QACD;QACA;QACA;QACA;QACA1L,IAAI,GAAG6J,SAAS,CAACyB,KAAK,IAAIA,KAAK,CAACI,WAAW,CAAC,EAAEjN,QAAQ,EAAEiN,WAAW,EAAEzB,SAAS,CAAC;QAC/E+B,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;MACxC;MACA,IAAI1L,IAAI,EAAE;QACN;QACA;UACI0K,mBAAmB,CAACmB,aAAa,CAAChM,KAAK,CAAC,CAACuC,YAAY,CAACpC,IAAI,EAAE2K,aAAa,CAACkB,aAAa,CAAChM,KAAK,CAAC,CAAC;QACnG;MACJ;IACJ;EACJ;EACA,IAAI4L,WAAW,GAAGG,SAAS,EAAE;IACzB;IACAhB,SAAS,CAACX,SAAS,EAAEsB,KAAK,CAACQ,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGR,KAAK,CAACQ,SAAS,GAAG,CAAC,CAAC,CAAClM,KAAK,EAAEpB,QAAQ,EAAE8M,KAAK,EAAEG,WAAW,EAAEK,SAAS,CAAC;EACnI,CAAC,MACI,IAAIL,WAAW,GAAGK,SAAS,EAAE;IAC9B;IACA;IACA;IACAb,YAAY,CAACI,KAAK,EAAEG,WAAW,EAAEG,SAAS,CAAC;EAC/C;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,WAAW,GAAGA,CAACE,SAAS,EAAEC,UAAU,EAAEd,eAAe,GAAG,KAAK,KAAK;EACpE;EACA;EACA,IAAIa,SAAS,CAACzM,KAAK,KAAK0M,UAAU,CAAC1M,KAAK,EAAE;IACtC,IAAIyM,SAAS,CAACzM,KAAK,KAAK,MAAM,EAAE;MAC5B,OAAOyM,SAAS,CAAC7M,MAAM,KAAK8M,UAAU,CAAC9M,MAAM;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACgM,eAAe,EAAE;MAClB,OAAOa,SAAS,CAAC9M,KAAK,KAAK+M,UAAU,CAAC/M,KAAK;IAC/C;IACA,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAMoL,aAAa,GAAI3K,IAAI,IAAK;EAC5B;EACA;EACA;EACA;EACA,OAAQA,IAAI,IAAIA,IAAI,CAAC,MAAM,CAAC,IAAKA,IAAI;AACzC,CAAC;AACD,MAAM0K,mBAAmB,GAAI1K,IAAI,IAAK,CAACA,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,EAAEmC,UAAU;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiK,KAAK,GAAGA,CAACjC,QAAQ,EAAE1L,QAAQ,EAAE+M,eAAe,GAAG,KAAK,KAAK;EAC3D,MAAM7H,GAAG,GAAIlF,QAAQ,CAACoB,KAAK,GAAGsK,QAAQ,CAACtK,KAAM;EAC7C,MAAM0M,WAAW,GAAGpC,QAAQ,CAAC7K,UAAU;EACvC,MAAMkN,WAAW,GAAG/N,QAAQ,CAACa,UAAU;EACvC,MAAMG,GAAG,GAAGhB,QAAQ,CAACmB,KAAK;EAC1B,MAAMF,IAAI,GAAGjB,QAAQ,CAACF,MAAM;EAC5B,IAAIkO,aAAa;EACjB,IAAI/M,IAAI,KAAK,IAAI,EAAE;IACf;MACI;MACA;MACAnF,SAAS,GAAGkF,GAAG,KAAK,KAAK,GAAG,IAAI,GAAGA,GAAG,KAAK,eAAe,GAAG,KAAK,GAAGlF,SAAS;IAClF;IACA;MACI,IAAIkF,GAAG,KAAK,MAAM,IAAI,CAACrF,kBAAkB,EAAE,CAAC,KACvC;QACD;QACA;QACA;QACAiP,aAAa,CAACc,QAAQ,EAAE1L,QAAQ,EAAElE,SAAS,CAAC;MAChD;IACJ;IACA,IAAIgS,WAAW,KAAK,IAAI,IAAIC,WAAW,KAAK,IAAI,EAAE;MAC9C;MACA;MACAnB,cAAc,CAAC1H,GAAG,EAAE4I,WAAW,EAAE9N,QAAQ,EAAE+N,WAAW,EAAEhB,eAAe,CAAC;IAC5E,CAAC,MACI,IAAIgB,WAAW,KAAK,IAAI,EAAE;MAC3B;MACA,IAAIrC,QAAQ,CAAC5L,MAAM,KAAK,IAAI,EAAE;QAC1B;QACAoF,GAAG,CAACP,WAAW,GAAG,EAAE;MACxB;MACA;MACAwH,SAAS,CAACjH,GAAG,EAAE,IAAI,EAAElF,QAAQ,EAAE+N,WAAW,EAAE,CAAC,EAAEA,WAAW,CAACrO,MAAM,GAAG,CAAC,CAAC;IAC1E,CAAC,MACI,IAAIoO,WAAW,KAAK,IAAI,EAAE;MAC3B;MACArB,YAAY,CAACqB,WAAW,EAAE,CAAC,EAAEA,WAAW,CAACpO,MAAM,GAAG,CAAC,CAAC;IACxD;IACA,IAAI5D,SAAS,IAAIkF,GAAG,KAAK,KAAK,EAAE;MAC5BlF,SAAS,GAAG,KAAK;IACrB;EACJ,CAAC,MACI,IAAKkS,aAAa,GAAG9I,GAAG,CAAC,MAAM,CAAC,EAAG;IACpC;IACA8I,aAAa,CAACtK,UAAU,CAACiB,WAAW,GAAG1D,IAAI;EAC/C,CAAC,MACI,IAAIyK,QAAQ,CAAC5L,MAAM,KAAKmB,IAAI,EAAE;IAC/B;IACA;IACAiE,GAAG,CAAC+I,IAAI,GAAGhN,IAAI;EACnB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiN,4BAA4B,GAAIhJ,GAAG,IAAK;EAC1C,MAAMT,UAAU,GAAGS,GAAG,CAACT,UAAU;EACjC,KAAK,MAAMgH,SAAS,IAAIhH,UAAU,EAAE;IAChC,IAAIgH,SAAS,CAACrH,QAAQ,KAAK,CAAC,CAAC,6BAA6B;MACtD,IAAIqH,SAAS,CAAC,MAAM,CAAC,EAAE;QACnB;QACA;QACA,MAAMtM,QAAQ,GAAGsM,SAAS,CAAC,MAAM,CAAC;QAClC;QACA;QACAA,SAAS,CAAC0C,MAAM,GAAG,KAAK;QACxB;QACA;QACA,KAAK,MAAMC,WAAW,IAAI3J,UAAU,EAAE;UAClC;UACA,IAAI2J,WAAW,KAAK3C,SAAS,EAAE;YAC3B,IAAI2C,WAAW,CAAC,MAAM,CAAC,KAAK3C,SAAS,CAAC,MAAM,CAAC,IAAItM,QAAQ,KAAK,EAAE,EAAE;cAC9D;cACA;cACA,IAAIiP,WAAW,CAAChK,QAAQ,KAAK,CAAC,CAAC,gCAC1BjF,QAAQ,KAAKiP,WAAW,CAACxP,YAAY,CAAC,MAAM,CAAC,IAAIO,QAAQ,KAAKiP,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE;gBACrF3C,SAAS,CAAC0C,MAAM,GAAG,IAAI;gBACvB;cACJ;YACJ,CAAC,MACI;cACD;cACA;cACA;cACA,IAAIC,WAAW,CAAChK,QAAQ,KAAK,CAAC,CAAC,+BAC1BgK,WAAW,CAAChK,QAAQ,KAAK,CAAC,CAAC,4BAA4BgK,WAAW,CAACzJ,WAAW,CAAC0J,IAAI,CAAC,CAAC,KAAK,EAAG,EAAE;gBAChG5C,SAAS,CAAC0C,MAAM,GAAG,IAAI;gBACvB;cACJ;YACJ;UACJ;QACJ;MACJ;MACA;MACAD,4BAA4B,CAACzC,SAAS,CAAC;IAC3C;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM6C,aAAa,GAAG,EAAE;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,GAAIrJ,GAAG,IAAK;EAC1C;EACA,IAAI3D,IAAI;EACR,IAAIiN,gBAAgB;EACpB,IAAIC,CAAC;EACL,KAAK,MAAMhD,SAAS,IAAIvG,GAAG,CAACT,UAAU,EAAE;IACpC;IACA;IACA,IAAIgH,SAAS,CAAC,MAAM,CAAC,KAAKlK,IAAI,GAAGkK,SAAS,CAAC,MAAM,CAAC,CAAC,IAAIlK,IAAI,CAACmC,UAAU,EAAE;MACpE;MACA;MACA8K,gBAAgB,GAAGjN,IAAI,CAACmC,UAAU,CAACe,UAAU;MAC7C,MAAMtF,QAAQ,GAAGsM,SAAS,CAAC,MAAM,CAAC;MAClC;MACA;MACA,KAAKgD,CAAC,GAAGD,gBAAgB,CAAC9O,MAAM,GAAG,CAAC,EAAE+O,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/ClN,IAAI,GAAGiN,gBAAgB,CAACC,CAAC,CAAC;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAClN,IAAI,CAAC,MAAM,CAAC,IACb,CAACA,IAAI,CAAC,MAAM,CAAC,IACbA,IAAI,CAAC,MAAM,CAAC,KAAKkK,SAAS,CAAC,MAAM,CAAC,IACjC,CAACnV,KAAK,CAACuB,qBAAwB,EAAE;UAClC;UACA;UACA;UACA,IAAI6W,mBAAmB,CAACnN,IAAI,EAAEpC,QAAQ,CAAC,EAAE;YACrC;YACA,IAAIwP,gBAAgB,GAAGL,aAAa,CAAClJ,IAAI,CAAEwJ,CAAC,IAAKA,CAAC,CAACC,gBAAgB,KAAKtN,IAAI,CAAC;YAC7E;YACA;YACA;YACA3F,2BAA2B,GAAG,IAAI;YAClC;YACA2F,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,CAAC,MAAM,CAAC,IAAIpC,QAAQ;YACvC,IAAIwP,gBAAgB,EAAE;cAClBA,gBAAgB,CAACE,gBAAgB,CAAC,MAAM,CAAC,GAAGpD,SAAS,CAAC,MAAM,CAAC;cAC7D;cACA;cACA;cACAkD,gBAAgB,CAACG,aAAa,GAAGrD,SAAS;YAC9C,CAAC,MACI;cACDlK,IAAI,CAAC,MAAM,CAAC,GAAGkK,SAAS,CAAC,MAAM,CAAC;cAChC;cACA6C,aAAa,CAACvO,IAAI,CAAC;gBACf+O,aAAa,EAAErD,SAAS;gBACxBoD,gBAAgB,EAAEtN;cACtB,CAAC,CAAC;YACN;YACA,IAAIA,IAAI,CAAC,MAAM,CAAC,EAAE;cACd+M,aAAa,CAAC5M,GAAG,CAAEqN,YAAY,IAAK;gBAChC,IAAIL,mBAAmB,CAACK,YAAY,CAACF,gBAAgB,EAAEtN,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;kBAClEoN,gBAAgB,GAAGL,aAAa,CAAClJ,IAAI,CAAEwJ,CAAC,IAAKA,CAAC,CAACC,gBAAgB,KAAKtN,IAAI,CAAC;kBACzE,IAAIoN,gBAAgB,IAAI,CAACI,YAAY,CAACD,aAAa,EAAE;oBACjDC,YAAY,CAACD,aAAa,GAAGH,gBAAgB,CAACG,aAAa;kBAC/D;gBACJ;cACJ,CAAC,CAAC;YACN;UACJ,CAAC,MACI,IAAI,CAACR,aAAa,CAACU,IAAI,CAAEJ,CAAC,IAAKA,CAAC,CAACC,gBAAgB,KAAKtN,IAAI,CAAC,EAAE;YAC9D;YACA;YACA;YACA;YACA+M,aAAa,CAACvO,IAAI,CAAC;cACf8O,gBAAgB,EAAEtN;YACtB,CAAC,CAAC;UACN;QACJ;MACJ;IACJ;IACA;IACA;IACA,IAAIkK,SAAS,CAACrH,QAAQ,KAAK,CAAC,CAAC,6BAA6B;MACtDmK,4BAA4B,CAAC9C,SAAS,CAAC;IAC3C;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiD,mBAAmB,GAAGA,CAACO,cAAc,EAAE9P,QAAQ,KAAK;EACtD,IAAI8P,cAAc,CAAC7K,QAAQ,KAAK,CAAC,CAAC,6BAA6B;IAC3D,IAAI6K,cAAc,CAACrQ,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,IAAIO,QAAQ,KAAK,EAAE,EAAE;MACjE;MACA;MACA,OAAO,IAAI;IACf;IACA,IAAI8P,cAAc,CAACrQ,YAAY,CAAC,MAAM,CAAC,KAAKO,QAAQ,EAAE;MAClD,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA,IAAI8P,cAAc,CAAC,MAAM,CAAC,KAAK9P,QAAQ,EAAE;IACrC,OAAO,IAAI;EACf;EACA,OAAOA,QAAQ,KAAK,EAAE;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwN,gBAAgB,GAAIuC,KAAK,IAAK;EAChC;IACIA,KAAK,CAACtO,OAAO,IAAIsO,KAAK,CAACtO,OAAO,CAAC6E,GAAG,IAAIyJ,KAAK,CAACtO,OAAO,CAAC6E,GAAG,CAAC,IAAI,CAAC;IAC7DyJ,KAAK,CAACrO,UAAU,IAAIqO,KAAK,CAACrO,UAAU,CAACa,GAAG,CAACiL,gBAAgB,CAAC;EAC9D;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwC,UAAU,GAAGA,CAAC5M,OAAO,EAAE6M,eAAe,EAAEC,aAAa,GAAG,KAAK,KAAK;EACpE,IAAI9Q,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE6Q,EAAE;EAClB,MAAMjN,OAAO,GAAGE,OAAO,CAAC0D,aAAa;EACrC,MAAMwB,OAAO,GAAGlF,OAAO,CAAC+F,SAAS;EACjC,MAAMoD,QAAQ,GAAGnJ,OAAO,CAACM,OAAO,IAAI7C,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;EACxD;EACA;EACA;EACA;EACA;EACA,MAAMuP,SAAS,GAAGjO,MAAM,CAAC8N,eAAe,CAAC,GAAGA,eAAe,GAAGtQ,CAAC,CAAC,IAAI,EAAE,IAAI,EAAEsQ,eAAe,CAAC;EAC5F1T,WAAW,GAAG2G,OAAO,CAACrF,OAAO;EAC7B,IAAIyK,OAAO,CAAC+H,gBAAgB,EAAE;IAC1BD,SAAS,CAAC3O,OAAO,GAAG2O,SAAS,CAAC3O,OAAO,IAAI,CAAC,CAAC;IAC3C6G,OAAO,CAAC+H,gBAAgB,CAAC9N,GAAG,CAAC,CAAC,CAAC+N,QAAQ,EAAEC,SAAS,CAAC,KAAMH,SAAS,CAAC3O,OAAO,CAAC8O,SAAS,CAAC,GAAGrN,OAAO,CAACoN,QAAQ,CAAE,CAAC;EAC/G;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIJ,aAAa,IAAIE,SAAS,CAAC3O,OAAO,EAAE;IACpC,KAAK,MAAM1D,GAAG,IAAImD,MAAM,CAACC,IAAI,CAACiP,SAAS,CAAC3O,OAAO,CAAC,EAAE;MAC9C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIyB,OAAO,CAACsN,YAAY,CAACzS,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAACmL,QAAQ,CAACnL,GAAG,CAAC,EAAE;QAC9EqS,SAAS,CAAC3O,OAAO,CAAC1D,GAAG,CAAC,GAAGmF,OAAO,CAACnF,GAAG,CAAC;MACzC;IACJ;EACJ;EACAqS,SAAS,CAACpO,KAAK,GAAG,IAAI;EACtBoO,SAAS,CAACrO,OAAO,IAAI,CAAC,CAAC;EACvBqB,OAAO,CAACM,OAAO,GAAG0M,SAAS;EAC3BA,SAAS,CAACnO,KAAK,GAAGsK,QAAQ,CAACtK,KAAK,GAAIiB,OAAO,CAACI,UAAU,IAAIJ,OAAS;EACnE;IACI7G,OAAO,GAAG6G,OAAO,CAAC,MAAM,CAAC;EAC7B;EACA1G,kBAAkB,GAAG,CAAC8L,OAAO,CAACvG,OAAO,GAAG,CAAC,CAAC,4CAA4C,CAAC;EACvF;IACIzF,UAAU,GAAG4G,OAAO,CAAC,MAAM,CAAC;IAC5B;IACAzG,2BAA2B,GAAG,KAAK;EACvC;EACA;EACA+R,KAAK,CAACjC,QAAQ,EAAE6D,SAAS,EAAEF,aAAa,CAAC;EACzC;IACI;IACA;IACA9S,GAAG,CAAC2E,OAAO,IAAI,CAAC,CAAC;IACjB,IAAIrF,iBAAiB,EAAE;MACnB0S,4BAA4B,CAACgB,SAAS,CAACnO,KAAK,CAAC;MAC7C,KAAK,MAAMwO,YAAY,IAAItB,aAAa,EAAE;QACtC,MAAMW,cAAc,GAAGW,YAAY,CAACf,gBAAgB;QACpD,IAAI,CAACI,cAAc,CAAC,MAAM,CAAC,EAAE;UACzB;UACA;UACA,MAAM1L,eAAe,GAAGjF,GAAG,CAACqN,cAAc,CAAC,EAAE,CAAC;UAC9CpI,eAAe,CAAC,MAAM,CAAC,GAAG0L,cAAc;UACxCA,cAAc,CAACvL,UAAU,CAACC,YAAY,CAAEsL,cAAc,CAAC,MAAM,CAAC,GAAG1L,eAAe,EAAG0L,cAAc,CAAC;QACtG;MACJ;MACA,KAAK,MAAMW,YAAY,IAAItB,aAAa,EAAE;QACtC,MAAMW,cAAc,GAAGW,YAAY,CAACf,gBAAgB;QACpD,MAAMgB,WAAW,GAAGD,YAAY,CAACd,aAAa;QAC9C,IAAIe,WAAW,EAAE;UACb,MAAMC,aAAa,GAAGD,WAAW,CAACnM,UAAU;UAC5C;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAIqM,gBAAgB,GAAGF,WAAW,CAACjM,WAAW;UAC9C;UACA;UACA;UACA;UACA;UACA;UACA;YACI,IAAIL,eAAe,GAAG,CAAChF,EAAE,GAAG0Q,cAAc,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI1Q,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyR,eAAe;YAC3G,OAAOzM,eAAe,EAAE;cACpB,IAAI0M,OAAO,GAAG,CAACzR,EAAE,GAAG+E,eAAe,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI/E,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;cAClF,IAAIyR,OAAO,IAAIA,OAAO,CAAC,MAAM,CAAC,KAAKhB,cAAc,CAAC,MAAM,CAAC,IAAIa,aAAa,KAAKG,OAAO,CAACvM,UAAU,EAAE;gBAC/FuM,OAAO,GAAGA,OAAO,CAACrM,WAAW;gBAC7B;gBACA;gBACA,OAAOqM,OAAO,KAAKhB,cAAc,KAAKgB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;kBACtGA,OAAO,GAAGA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACrM,WAAW;gBACnF;gBACA,IAAI,CAACqM,OAAO,IAAI,CAACA,OAAO,CAAC,MAAM,CAAC,EAAE;kBAC9BF,gBAAgB,GAAGE,OAAO;kBAC1B;gBACJ;cACJ;cACA1M,eAAe,GAAGA,eAAe,CAACyM,eAAe;YACrD;UACJ;UACA,IAAK,CAACD,gBAAgB,IAAID,aAAa,KAAKb,cAAc,CAACvL,UAAU,IACjEuL,cAAc,CAACrL,WAAW,KAAKmM,gBAAgB,EAAE;YACjD;YACA;YACA;YACA,IAAId,cAAc,KAAKc,gBAAgB,EAAE;cACrC,IAAI,CAACd,cAAc,CAAC,MAAM,CAAC,IAAIA,cAAc,CAAC,MAAM,CAAC,EAAE;gBACnD;gBACAA,cAAc,CAAC,MAAM,CAAC,GAAGA,cAAc,CAAC,MAAM,CAAC,CAACvL,UAAU,CAAC3E,QAAQ;cACvE;cACA;cACA;cACA;cACA;cACA+Q,aAAa,CAACnM,YAAY,CAACsL,cAAc,EAAEc,gBAAgB,CAAC;cAC5D;cACA;cACA;cACA;cACA,IAAId,cAAc,CAAC7K,QAAQ,KAAK,CAAC,CAAC,6BAA6B;gBAC3D6K,cAAc,CAACd,MAAM,GAAG,CAAC1P,EAAE,GAAGwQ,cAAc,CAAC,MAAM,CAAC,MAAM,IAAI,IAAIxQ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;cAChG;YACJ;UACJ;UACAwQ,cAAc,IAAI,OAAOY,WAAW,CAAC,MAAM,CAAC,KAAK,UAAU,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACZ,cAAc,CAAC;QACtG,CAAC,MACI;UACD;UACA,IAAIA,cAAc,CAAC7K,QAAQ,KAAK,CAAC,CAAC,6BAA6B;YAC3D;YACA;YACA,IAAIiL,aAAa,EAAE;cACfJ,cAAc,CAAC,MAAM,CAAC,GAAG,CAACK,EAAE,GAAGL,cAAc,CAACd,MAAM,MAAM,IAAI,IAAImB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;YAChG;YACAL,cAAc,CAACd,MAAM,GAAG,IAAI;UAChC;QACJ;MACJ;IACJ;IACA,IAAIvS,2BAA2B,EAAE;MAC7BsS,4BAA4B,CAACqB,SAAS,CAACnO,KAAK,CAAC;IACjD;IACA;IACA;IACA7E,GAAG,CAAC2E,OAAO,IAAI,CAAC,CAAC,CAAC;IAClB;IACAoN,aAAa,CAAC5O,MAAM,GAAG,CAAC;EAC5B;EACA;EACAjE,UAAU,GAAGoD,SAAS;AAC1B,CAAC;AACD,MAAMqR,gBAAgB,GAAGA,CAAC3N,OAAO,EAAE4N,iBAAiB,KAAK;EACrD,IAAIA,iBAAiB,IAAI,CAAC5N,OAAO,CAAC6N,iBAAiB,IAAID,iBAAiB,CAAC,KAAK,CAAC,EAAE;IAC7EA,iBAAiB,CAAC,KAAK,CAAC,CAACpQ,IAAI,CAAC,IAAIsQ,OAAO,CAAEzB,CAAC,IAAMrM,OAAO,CAAC6N,iBAAiB,GAAGxB,CAAE,CAAC,CAAC;EACtF;AACJ,CAAC;AACD,MAAM0B,cAAc,GAAGA,CAAC/N,OAAO,EAAE8M,aAAa,KAAK;EAC/C;IACI9M,OAAO,CAACrB,OAAO,IAAI,EAAE,CAAC;EAC1B;EACA,IAAIqB,OAAO,CAACrB,OAAO,GAAG,CAAC,CAAC,uCAAuC;IAC3DqB,OAAO,CAACrB,OAAO,IAAI,GAAG,CAAC;IACvB;EACJ;EACAgP,gBAAgB,CAAC3N,OAAO,EAAEA,OAAO,CAACgO,mBAAmB,CAAC;EACtD;EACA;EACA;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAMC,aAAa,CAAClO,OAAO,EAAE8M,aAAa,CAAC;EAC5D,OAAOqB,SAAS,CAACF,QAAQ,CAAC;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAAClO,OAAO,EAAE8M,aAAa,KAAK;EAC9C,MAAMsB,WAAW,GAAG7T,UAAU,CAAC,gBAAgB,EAAEyF,OAAO,CAAC+F,SAAS,CAACE,SAAS,CAAC;EAC7E,MAAMoI,QAAQ,GAAGrO,OAAO,CAACsO,cAAc;EACvC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,YAAY;EAChB,IAAIzB,aAAa,EAAE;IACf;MACI9M,OAAO,CAACrB,OAAO,IAAI,GAAG,CAAC;MACvB,IAAIqB,OAAO,CAACwO,iBAAiB,EAAE;QAC3BxO,OAAO,CAACwO,iBAAiB,CAACrP,GAAG,CAAC,CAAC,CAACsP,UAAU,EAAErZ,KAAK,CAAC,KAAKsZ,QAAQ,CAACL,QAAQ,EAAEI,UAAU,EAAErZ,KAAK,CAAC,CAAC;QAC7F4K,OAAO,CAACwO,iBAAiB,GAAGlS,SAAS;MACzC;IACJ;IACA;MACI;MACA;MACA;MACA;MACA;MACAiS,YAAY,GAAGG,QAAQ,CAACL,QAAQ,EAAE,mBAAmB,CAAC;IAC1D;EACJ;EACA;IACIE,YAAY,GAAGI,OAAO,CAACJ,YAAY,EAAE,MAAMG,QAAQ,CAACL,QAAQ,EAAE,qBAAqB,CAAC,CAAC;EACzF;EACAD,WAAW,CAAC,CAAC;EACb,OAAOO,OAAO,CAACJ,YAAY,EAAE,MAAMK,eAAe,CAAC5O,OAAO,EAAEqO,QAAQ,EAAEvB,aAAa,CAAC,CAAC;AACzF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6B,OAAO,GAAGA,CAACJ,YAAY,EAAEM,EAAE,KAAKC,UAAU,CAACP,YAAY,CAAC,GAAGA,YAAY,CAACQ,IAAI,CAACF,EAAE,CAAC,GAAGA,EAAE,CAAC,CAAC;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAIP,YAAY,IAAKA,YAAY,YAAYT,OAAO,IAC/DS,YAAY,IAAIA,YAAY,CAACQ,IAAI,IAAI,OAAOR,YAAY,CAACQ,IAAI,KAAK,UAAW;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMH,eAAe;EAAA,IAAAI,IAAA,GAAAC,iBAAA,CAAG,WAAOjP,OAAO,EAAEqO,QAAQ,EAAEvB,aAAa,EAAK;IAChE,IAAI9Q,EAAE;IACN,MAAM2G,GAAG,GAAG3C,OAAO,CAAC0D,aAAa;IACjC,MAAMwL,SAAS,GAAG3U,UAAU,CAAC,QAAQ,EAAEyF,OAAO,CAAC+F,SAAS,CAACE,SAAS,CAAC;IACnE,MAAMkJ,EAAE,GAAGxM,GAAG,CAAC,MAAM,CAAC;IACtB,IAAImK,aAAa,EAAE;MACf;MACA1Y,YAAY,CAAC4L,OAAO,CAAC;IACzB;IACA,MAAMoP,SAAS,GAAG7U,UAAU,CAAC,QAAQ,EAAEyF,OAAO,CAAC+F,SAAS,CAACE,SAAS,CAAC;IACnE;MACIoJ,UAAU,CAACrP,OAAO,EAAEqO,QAAQ,EAAE1L,GAAG,EAAEmK,aAAa,CAAC;IACrD;IACA,IAAIqC,EAAE,EAAE;MACJ;MACA;MACA;MACAA,EAAE,CAAChQ,GAAG,CAAED,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACpByD,GAAG,CAAC,MAAM,CAAC,GAAGrG,SAAS;IAC3B;IACA8S,SAAS,CAAC,CAAC;IACXF,SAAS,CAAC,CAAC;IACX;MACI,MAAMI,gBAAgB,GAAG,CAACtT,EAAE,GAAG2G,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI3G,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;MAC9E,MAAMuT,UAAU,GAAGA,CAAA,KAAMC,mBAAmB,CAACxP,OAAO,CAAC;MACrD,IAAIsP,gBAAgB,CAACnS,MAAM,KAAK,CAAC,EAAE;QAC/BoS,UAAU,CAAC,CAAC;MAChB,CAAC,MACI;QACDzB,OAAO,CAAC2B,GAAG,CAACH,gBAAgB,CAAC,CAACP,IAAI,CAACQ,UAAU,CAAC;QAC9CvP,OAAO,CAACrB,OAAO,IAAI,CAAC,CAAC;QACrB2Q,gBAAgB,CAACnS,MAAM,GAAG,CAAC;MAC/B;IACJ;EACJ,CAAC;EAAA,gBAlCKyR,eAAeA,CAAAc,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;EAAA;AAAA,GAkCpB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMT,UAAU,GAAGA,CAACrP,OAAO,EAAEqO,QAAQ,EAAE1L,GAAG,EAAEmK,aAAa,KAAK;EAC1D,IAAI;IACA;AACR;AACA;AACA;IACQuB,QAAQ,GAAGA,QAAQ,CAAC0B,MAAM,IAAI1B,QAAQ,CAAC0B,MAAM,CAAC,CAAC;IAC/C;MACI/P,OAAO,CAACrB,OAAO,IAAI,CAAC,EAAE,CAAC;IAC3B;IACA;MACIqB,OAAO,CAACrB,OAAO,IAAI,CAAC,CAAC;IACzB;IACA;MACI;QACI;QACA;QACA;QACA;UACIiO,UAAU,CAAC5M,OAAO,EAAEqO,QAAQ,EAAEvB,aAAa,CAAC;QAChD;MACJ;IACJ;EACJ,CAAC,CACD,OAAOhF,CAAC,EAAE;IACNkI,YAAY,CAAClI,CAAC,EAAE9H,OAAO,CAAC0D,aAAa,CAAC;EAC1C;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAM8L,mBAAmB,GAAIxP,OAAO,IAAK;EACrC,MAAMvF,OAAO,GAAGuF,OAAO,CAAC+F,SAAS,CAACE,SAAS;EAC3C,MAAMtD,GAAG,GAAG3C,OAAO,CAAC0D,aAAa;EACjC,MAAMuM,aAAa,GAAG1V,UAAU,CAAC,YAAY,EAAEE,OAAO,CAAC;EACvD,MAAM4T,QAAQ,GAAGrO,OAAO,CAACsO,cAAc;EACvC,MAAMV,iBAAiB,GAAG5N,OAAO,CAACgO,mBAAmB;EACrD;IACIU,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,CAAC;EAC5C;EACA,IAAI,EAAErO,OAAO,CAACrB,OAAO,GAAG,EAAE,CAAC,oCAAoC,EAAE;IAC7DqB,OAAO,CAACrB,OAAO,IAAI,EAAE,CAAC;IACtB;MACI;MACAuR,eAAe,CAACvN,GAAG,CAAC;IACxB;IACA;MACI+L,QAAQ,CAACL,QAAQ,EAAE,kBAAkB,CAAC;IAC1C;IACA4B,aAAa,CAAC,CAAC;IACf;MACIjQ,OAAO,CAACmQ,gBAAgB,CAACxN,GAAG,CAAC;MAC7B,IAAI,CAACiL,iBAAiB,EAAE;QACpBwC,UAAU,CAAC,CAAC;MAChB;IACJ;EACJ,CAAC,MACI;IACD;MACI1B,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,CAAC;IAC5C;IACA4B,aAAa,CAAC,CAAC;EACnB;EACA;IACIjQ,OAAO,CAACqQ,mBAAmB,CAAC1N,GAAG,CAAC;EACpC;EACA;EACA;EACA;IACI,IAAI3C,OAAO,CAAC6N,iBAAiB,EAAE;MAC3B7N,OAAO,CAAC6N,iBAAiB,CAAC,CAAC;MAC3B7N,OAAO,CAAC6N,iBAAiB,GAAGvR,SAAS;IACzC;IACA,IAAI0D,OAAO,CAACrB,OAAO,GAAG,GAAG,CAAC,gCAAgC;MACtD2R,QAAQ,CAAC,MAAMvC,cAAc,CAAC/N,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD;IACAA,OAAO,CAACrB,OAAO,IAAI,EAAE,CAAC,CAAC,wCAAwC,GAAG,CAAC,+BAA+B;EACtG;EACA;EACA;EACA;AACJ,CAAC;AACD,MAAM4R,WAAW,GAAIrN,GAAG,IAAK;EACzB;IACI,MAAMlD,OAAO,GAAGmD,UAAU,CAACD,GAAG,CAAC;IAC/B,MAAMsN,WAAW,GAAGxQ,OAAO,CAAC0D,aAAa,CAAC8M,WAAW;IACrD,IAAIA,WAAW,IACX,CAACxQ,OAAO,CAACrB,OAAO,IAAI,CAAC,CAAC,+BAA+B,EAAE,CAAC,mCAAmC,MAAM,CAAC,CAAC,8BAA8B;MACjIoP,cAAc,CAAC/N,OAAO,EAAE,KAAK,CAAC;IAClC;IACA;IACA,OAAOwQ,WAAW;EACtB;AACJ,CAAC;AACD,MAAMJ,UAAU,GAAIK,GAAG,IAAK;EACxB;EACA;EACA;IACIP,eAAe,CAACnU,GAAG,CAAC2U,eAAe,CAAC;EACxC;EACAJ,QAAQ,CAAC,MAAMvM,SAAS,CAAC5J,GAAG,EAAE,SAAS,EAAE;IAAE2J,MAAM,EAAE;MAAE6M,SAAS,EAAE7c;IAAU;EAAE,CAAC,CAAC,CAAC;AACnF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4a,QAAQ,GAAGA,CAACL,QAAQ,EAAExX,MAAM,EAAE+Z,GAAG,KAAK;EACxC,IAAIvC,QAAQ,IAAIA,QAAQ,CAACxX,MAAM,CAAC,EAAE;IAC9B,IAAI;MACA,OAAOwX,QAAQ,CAACxX,MAAM,CAAC,CAAC+Z,GAAG,CAAC;IAChC,CAAC,CACD,OAAO9I,CAAC,EAAE;MACNkI,YAAY,CAAClI,CAAC,CAAC;IACnB;EACJ;EACA,OAAOxL,SAAS;AACpB,CAAC;AACD,MAAM4T,eAAe,GAAIvN,GAAG,IAAKA,GAAG,CAACwD,SAAS,CAACP,GAAG,CAAC,UAAU,CAAC;AAE9D,MAAMiL,QAAQ,GAAGA,CAAC3N,GAAG,EAAEgK,QAAQ,KAAK/J,UAAU,CAACD,GAAG,CAAC,CAAC4N,gBAAgB,CAAC7P,GAAG,CAACiM,QAAQ,CAAC;AAClF,MAAM6D,QAAQ,GAAGA,CAAC7N,GAAG,EAAEgK,QAAQ,EAAE8D,MAAM,EAAE9L,OAAO,KAAK;EACjD;EACA,MAAMlF,OAAO,GAAGmD,UAAU,CAACD,GAAG,CAAC;EAC/B,MAAMP,GAAG,GAAG3C,OAAO,CAAC0D,aAAa;EACjC,MAAMuN,MAAM,GAAGjR,OAAO,CAAC8Q,gBAAgB,CAAC7P,GAAG,CAACiM,QAAQ,CAAC;EACrD,MAAMtJ,KAAK,GAAG5D,OAAO,CAACrB,OAAO;EAC7B,MAAM0P,QAAQ,GAAGrO,OAAO,CAACsO,cAAc;EACvC0C,MAAM,GAAG3N,kBAAkB,CAAC2N,MAAM,EAAE9L,OAAO,CAACgM,SAAS,CAAChE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE;EACA,MAAMiE,UAAU,GAAGC,MAAM,CAACC,KAAK,CAACJ,MAAM,CAAC,IAAIG,MAAM,CAACC,KAAK,CAACL,MAAM,CAAC;EAC/D,MAAMM,cAAc,GAAGN,MAAM,KAAKC,MAAM,IAAI,CAACE,UAAU;EACvD,IAAI,CAAC,EAAEvN,KAAK,GAAG,CAAC,CAAC,wCAAwC,IAAIqN,MAAM,KAAK3U,SAAS,KAAKgV,cAAc,EAAE;IAClG;IACA;IACAtR,OAAO,CAAC8Q,gBAAgB,CAACrO,GAAG,CAACyK,QAAQ,EAAE8D,MAAM,CAAC;IAC9C,IAAI3C,QAAQ,EAAE;MACV;MACA,IAAInJ,OAAO,CAACqM,UAAU,IAAI3N,KAAK,GAAG,GAAG,CAAC,+BAA+B;QACjE,MAAM4N,YAAY,GAAGtM,OAAO,CAACqM,UAAU,CAACrE,QAAQ,CAAC;QACjD,IAAIsE,YAAY,EAAE;UACd;UACAA,YAAY,CAACrS,GAAG,CAAEsS,eAAe,IAAK;YAClC,IAAI;cACA;cACApD,QAAQ,CAACoD,eAAe,CAAC,CAACT,MAAM,EAAEC,MAAM,EAAE/D,QAAQ,CAAC;YACvD,CAAC,CACD,OAAOpF,CAAC,EAAE;cACNkI,YAAY,CAAClI,CAAC,EAAEnF,GAAG,CAAC;YACxB;UACJ,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAACiB,KAAK,IAAI,CAAC,CAAC,+BAA+B,EAAE,CAAC,mCAAmC,MAAM,CAAC,CAAC,8BAA8B;QACvH;QACA;QACA;QACA;QACAmK,cAAc,CAAC/N,OAAO,EAAE,KAAK,CAAC;MAClC;IACJ;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0R,cAAc,GAAGA,CAACC,IAAI,EAAEzM,OAAO,EAAEtB,KAAK,KAAK;EAC7C,IAAI5H,EAAE;EACN,MAAM4V,SAAS,GAAGD,IAAI,CAACC,SAAS;EAChC,IAAI1M,OAAO,CAACgM,SAAS,EAAE;IACnB,IAAIS,IAAI,CAACE,QAAQ,EAAE;MACf3M,OAAO,CAACqM,UAAU,GAAGI,IAAI,CAACE,QAAQ;IACtC;IACA;IACA,MAAMC,OAAO,GAAGhU,MAAM,CAACiU,OAAO,CAAC7M,OAAO,CAACgM,SAAS,CAAC;IACjDY,OAAO,CAAC3S,GAAG,CAAC,CAAC,CAACsH,UAAU,EAAE,CAACuL,WAAW,CAAC,CAAC,KAAK;MACzC,IAAKA,WAAW,GAAG,EAAE,CAAC,2BACZpO,KAAK,GAAG,CAAC,CAAC,gCAAiCoO,WAAW,GAAG,EAAE,CAAC,wBAAyB,EAAG;QAC9F;QACAlU,MAAM,CAACmU,cAAc,CAACL,SAAS,EAAEnL,UAAU,EAAE;UACzCxF,GAAGA,CAAA,EAAG;YACF;YACA,OAAO4P,QAAQ,CAAC,IAAI,EAAEpK,UAAU,CAAC;UACrC,CAAC;UACDhE,GAAGA,CAACkE,QAAQ,EAAE;YACV;YACAoK,QAAQ,CAAC,IAAI,EAAEtK,UAAU,EAAEE,QAAQ,EAAEzB,OAAO,CAAC;UACjD,CAAC;UACDgN,YAAY,EAAE,IAAI;UAClBC,UAAU,EAAE;QAChB,CAAC,CAAC;MACN,CAAC,MACI,IAAIvO,KAAK,GAAG,CAAC,CAAC,0CACfoO,WAAW,GAAG,EAAE,CAAC,2BAA2B;QAC5C;QACAlU,MAAM,CAACmU,cAAc,CAACL,SAAS,EAAEnL,UAAU,EAAE;UACzC0B,KAAKA,CAAC,GAAGiK,IAAI,EAAE;YACX,IAAIpW,EAAE;YACN,MAAMkH,GAAG,GAAGC,UAAU,CAAC,IAAI,CAAC;YAC5B,OAAO,CAACnH,EAAE,GAAGkH,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACmP,mBAAmB,MAAM,IAAI,IAAIrW,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+S,IAAI,CAAC,MAAM;cAAE,IAAI/S,EAAE;cAAE,OAAO,CAACA,EAAE,GAAGkH,GAAG,CAACoL,cAAc,MAAM,IAAI,IAAItS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyK,UAAU,CAAC,CAAC,GAAG2L,IAAI,CAAC;YAAE,CAAC,CAAC;UAClP;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,IAAKxO,KAAK,GAAG,CAAC,CAAC,wCAAyC;MACpD,MAAM0O,kBAAkB,GAAG,IAAI5R,GAAG,CAAC,CAAC;MACpCkR,SAAS,CAACW,wBAAwB,GAAG,UAAUC,QAAQ,EAAE9L,QAAQ,EAAEC,QAAQ,EAAE;QACzE3M,GAAG,CAACyY,GAAG,CAAC,MAAM;UACV,IAAIzW,EAAE;UACN,MAAMkR,QAAQ,GAAGoF,kBAAkB,CAACrR,GAAG,CAACuR,QAAQ,CAAC;UACjD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,IAAI,CAACE,cAAc,CAACxF,QAAQ,CAAC,EAAE;YAC/BvG,QAAQ,GAAG,IAAI,CAACuG,QAAQ,CAAC;YACzB,OAAO,IAAI,CAACA,QAAQ,CAAC;UACzB,CAAC,MACI,IAAI0E,SAAS,CAACc,cAAc,CAACxF,QAAQ,CAAC,IACvC,OAAO,IAAI,CAACA,QAAQ,CAAC,KAAK,QAAQ,IAClC,IAAI,CAACA,QAAQ,CAAC,IAAIvG,QAAQ,EAAE;YAC5B;YACA;YACA;YACA;UACJ,CAAC,MACI,IAAIuG,QAAQ,IAAI,IAAI,EAAE;YACvB;YACA;YACA,MAAMlN,OAAO,GAAGmD,UAAU,CAAC,IAAI,CAAC;YAChC,MAAMS,KAAK,GAAG5D,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACrB,OAAO;YAC/E;YACA;YACA;YACA;YACA,IAAIiF,KAAK,IACL,EAAEA,KAAK,GAAG,CAAC,CAAC,wCAAwC,IACpDA,KAAK,GAAG,GAAG,CAAC,iCACZ+C,QAAQ,KAAKD,QAAQ,EAAE;cACvB,MAAM2H,QAAQ,GAAGrO,OAAO,CAACsO,cAAc;cACvC,MAAMqE,KAAK,GAAG,CAAC3W,EAAE,GAAGkJ,OAAO,CAACqM,UAAU,MAAM,IAAI,IAAIvV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwW,QAAQ,CAAC;cACzFG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC1T,OAAO,CAAE2T,YAAY,IAAK;gBAC1E,IAAIvE,QAAQ,CAACuE,YAAY,CAAC,IAAI,IAAI,EAAE;kBAChCvE,QAAQ,CAACuE,YAAY,CAAC,CAACC,IAAI,CAACxE,QAAQ,EAAE1H,QAAQ,EAAED,QAAQ,EAAE8L,QAAQ,CAAC;gBACvE;cACJ,CAAC,CAAC;YACN;YACA;UACJ;UACA,IAAI,CAACtF,QAAQ,CAAC,GAAGvG,QAAQ,KAAK,IAAI,IAAI,OAAO,IAAI,CAACuG,QAAQ,CAAC,KAAK,SAAS,GAAG,KAAK,GAAGvG,QAAQ;QAChG,CAAC,CAAC;MACN,CAAC;MACD;MACA;MACA;MACA;MACA;MACAgL,IAAI,CAACmB,kBAAkB,GAAG1V,KAAK,CAACqM,IAAI,CAAC,IAAInE,GAAG,CAAC,CACzC,GAAGxH,MAAM,CAACC,IAAI,CAAC,CAAC/B,EAAE,GAAGkJ,OAAO,CAACqM,UAAU,MAAM,IAAI,IAAIvV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,CAAC,EAC7E,GAAG8V,OAAO,CACL9T,MAAM,CAAC,CAAC,CAAC+U,CAAC,EAAEjQ,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,+BAA+B,CAAC,CAC7D3D,GAAG,CAAC,CAAC,CAAC+N,QAAQ,EAAEpK,CAAC,CAAC,KAAK;QACxB,IAAI9G,EAAE;QACN,MAAMwW,QAAQ,GAAG1P,CAAC,CAAC,CAAC,CAAC,IAAIoK,QAAQ;QACjCoF,kBAAkB,CAAC7P,GAAG,CAAC+P,QAAQ,EAAEtF,QAAQ,CAAC;QAC1C,IAAIpK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,gCAAgC;UAC3C,CAAC9G,EAAE,GAAGkJ,OAAO,CAAC+H,gBAAgB,MAAM,IAAI,IAAIjR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwB,IAAI,CAAC,CAAC0P,QAAQ,EAAEsF,QAAQ,CAAC,CAAC;QACtG;QACA,OAAOA,QAAQ;MACnB,CAAC,CAAC,CACL,CAAC,CAAC;IACP;EACJ;EACA,OAAOb,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,mBAAmB;EAAA,IAAAC,KAAA,GAAAhE,iBAAA,CAAG,WAAOtM,GAAG,EAAE3C,OAAO,EAAEkF,OAAO,EAAEgO,YAAY,EAAK;IACvE,IAAIvB,IAAI;IACR;IACA,IAAI,CAAC3R,OAAO,CAACrB,OAAO,GAAG,EAAE,CAAC,8CAA8C,CAAC,EAAE;MACvE;MACAqB,OAAO,CAACrB,OAAO,IAAI,EAAE,CAAC;MACtB,MAAMwU,QAAQ,GAAGjO,OAAO,CAACkO,cAAc;MACvC,IAAID,QAAQ,EAAE;QACV;QACA;QACA;QACAxB,IAAI,GAAG0B,UAAU,CAACnO,OAAO,CAAC;QAC1B,IAAIyM,IAAI,CAAC5C,IAAI,EAAE;UACX;UACA,MAAMuE,OAAO,GAAG5Y,UAAU,CAAC,CAAC;UAC5BiX,IAAI,SAASA,IAAI;UACjB2B,OAAO,CAAC,CAAC;QACb;QACA,IAAI,CAAC3B,IAAI,CAAC4B,SAAS,EAAE;UACjB;UACA;UACA;UACA;YACIrO,OAAO,CAACqM,UAAU,GAAGI,IAAI,CAACE,QAAQ;UACtC;UACAH,cAAc,CAACC,IAAI,EAAEzM,OAAO,EAAE,CAAC,CAAC,4BAA4B,CAAC;UAC7DyM,IAAI,CAAC4B,SAAS,GAAG,IAAI;QACzB;QACA,MAAMC,cAAc,GAAGjZ,UAAU,CAAC,gBAAgB,EAAE2K,OAAO,CAACe,SAAS,CAAC;QACtE;QACA;QACA;QACA;UACIjG,OAAO,CAACrB,OAAO,IAAI,CAAC,CAAC;QACzB;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,IAAIgT,IAAI,CAAC3R,OAAO,CAAC;QACrB,CAAC,CACD,OAAO8H,CAAC,EAAE;UACNkI,YAAY,CAAClI,CAAC,CAAC;QACnB;QACA;UACI9H,OAAO,CAACrB,OAAO,IAAI,CAAC,CAAC,CAAC;QAC1B;QACA;UACIqB,OAAO,CAACrB,OAAO,IAAI,GAAG,CAAC;QAC3B;QACA6U,cAAc,CAAC,CAAC;QAChBC,qBAAqB,CAACzT,OAAO,CAACsO,cAAc,CAAC;MACjD,CAAC,MACI;QACD;QACAqD,IAAI,GAAGhP,GAAG,CAAC+Q,WAAW;QACtB;QACA;QACA;QACAC,cAAc,CAACC,WAAW,CAAC1O,OAAO,CAACe,SAAS,CAAC,CAAC8I,IAAI,CAAC,MAAO/O,OAAO,CAACrB,OAAO,IAAI,GAAG,CAAC,6BAA8B,CAAC;MACpH;MACA,IAAIgT,IAAI,CAAC3Z,KAAK,EAAE;QACZ;QACA,IAAIA,KAAK,GAAG2Z,IAAI,CAAC3Z,KAAK;QACtB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC3BA,KAAK,GAAGA,KAAK,CAAEgI,OAAO,CAACoD,UAAU,GAAGV,WAAW,CAACC,GAAG,CAAC,CAAE;QAC1D;QACA,MAAM1J,OAAO,GAAGkM,UAAU,CAACD,OAAO,EAAElF,OAAO,CAACoD,UAAU,CAAC;QACvD,IAAI,CAACwB,MAAM,CAACW,GAAG,CAACtM,OAAO,CAAC,EAAE;UACtB,MAAM4a,iBAAiB,GAAGtZ,UAAU,CAAC,gBAAgB,EAAE2K,OAAO,CAACe,SAAS,CAAC;UACzExB,aAAa,CAACxL,OAAO,EAAEjB,KAAK,EAAE,CAAC,EAAEkN,OAAO,CAACvG,OAAO,GAAG,CAAC,CAAC,uCAAuC,CAAC;UAC7FkV,iBAAiB,CAAC,CAAC;QACvB;MACJ;IACJ;IACA;IACA,MAAMjG,iBAAiB,GAAG5N,OAAO,CAACgO,mBAAmB;IACrD,MAAM8F,QAAQ,GAAGA,CAAA,KAAM/F,cAAc,CAAC/N,OAAO,EAAE,IAAI,CAAC;IACpD,IAAI4N,iBAAiB,IAAIA,iBAAiB,CAAC,MAAM,CAAC,EAAE;MAChD;MACA;MACA;MACA;MACA;MACA;MACAA,iBAAiB,CAAC,MAAM,CAAC,CAACpQ,IAAI,CAACsW,QAAQ,CAAC;IAC5C,CAAC,MACI;MACDA,QAAQ,CAAC,CAAC;IACd;EACJ,CAAC;EAAA,gBA3FKd,mBAAmBA,CAAAe,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAjB,KAAA,CAAApD,KAAA,OAAAC,SAAA;EAAA;AAAA,GA2FxB;AACD,MAAM2D,qBAAqB,GAAIpF,QAAQ,IAAK;EACxC;IACIK,QAAQ,CAACL,QAAQ,EAAE,mBAAmB,CAAC;EAC3C;AACJ,CAAC;AACD,MAAMvZ,iBAAiB,GAAI6N,GAAG,IAAK;EAC/B,IAAI,CAAC3I,GAAG,CAAC2E,OAAO,GAAG,CAAC,CAAC,4CAA4C,CAAC,EAAE;IAChE,MAAMqB,OAAO,GAAGmD,UAAU,CAACR,GAAG,CAAC;IAC/B,MAAMuC,OAAO,GAAGlF,OAAO,CAAC+F,SAAS;IACjC,MAAMoO,YAAY,GAAG5Z,UAAU,CAAC,mBAAmB,EAAE2K,OAAO,CAACe,SAAS,CAAC;IACvE,IAAI,EAAEjG,OAAO,CAACrB,OAAO,GAAG,CAAC,CAAC,8BAA8B,EAAE;MACtD;MACAqB,OAAO,CAACrB,OAAO,IAAI,CAAC,CAAC;MACrB,IAAIoB,MAAM;MACV;QACIA,MAAM,GAAG4C,GAAG,CAACtG,YAAY,CAACpB,UAAU,CAAC;QACrC,IAAI8E,MAAM,EAAE;UACR,IAAImF,OAAO,CAACvG,OAAO,GAAG,CAAC,CAAC,wCAAwC;YAC5D,MAAM1F,OAAO,GAAG+L,QAAQ,CAACrC,GAAG,CAACzC,UAAU,EAAEgF,OAAO,EAAEvC,GAAG,CAACtG,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE7EsG,GAAG,CAACwD,SAAS,CAAC9D,MAAM,CAACpJ,OAAO,GAAG,IAAI,EAAEA,OAAO,GAAG,IAAI,CAAC;UACxD;UACA4G,uBAAuB,CAAC8C,GAAG,EAAEuC,OAAO,CAACe,SAAS,EAAElG,MAAM,EAAEC,OAAO,CAAC;QACpE;MACJ;MACA,IAAI,CAACD,MAAM,EAAE;QACT;QACA;QACA;QACA;QACA;QAAK;QACGmF,OAAO,CAACvG,OAAO,IAAI,CAAC,CAAC,oCAAoC,CAAC,CAAC,mCAAmC,EAAG;UACrGyV,mBAAmB,CAACzR,GAAG,CAAC;QAC5B;MACJ;MACA;QACI;QACA;QACA,IAAIiL,iBAAiB,GAAGjL,GAAG;QAC3B,OAAQiL,iBAAiB,GAAGA,iBAAiB,CAACzM,UAAU,IAAIyM,iBAAiB,CAACpI,IAAI,EAAG;UACjF;UACA;UACA,IAAKoI,iBAAiB,CAAC/L,QAAQ,KAAK,CAAC,CAAC,+BAClC+L,iBAAiB,CAACR,YAAY,CAAC,MAAM,CAAC,IACtCQ,iBAAiB,CAAC,KAAK,CAAC,IACxBA,iBAAiB,CAAC,KAAK,CAAC,EAAE;YAC1B;YACA;YACAD,gBAAgB,CAAC3N,OAAO,EAAGA,OAAO,CAACgO,mBAAmB,GAAGJ,iBAAkB,CAAC;YAC5E;UACJ;QACJ;MACJ;MACA;MACA;MACA,IAAI1I,OAAO,CAACgM,SAAS,EAAE;QACnBpT,MAAM,CAACiU,OAAO,CAAC7M,OAAO,CAACgM,SAAS,CAAC,CAAC/R,GAAG,CAAC,CAAC,CAACsH,UAAU,EAAE,CAACuL,WAAW,CAAC,CAAC,KAAK;UACnE,IAAIA,WAAW,GAAG,EAAE,CAAC,2BAA2BrP,GAAG,CAAC+P,cAAc,CAACjM,UAAU,CAAC,EAAE;YAC5E,MAAM0B,KAAK,GAAGxF,GAAG,CAAC8D,UAAU,CAAC;YAC7B,OAAO9D,GAAG,CAAC8D,UAAU,CAAC;YACtB9D,GAAG,CAAC8D,UAAU,CAAC,GAAG0B,KAAK;UAC3B;QACJ,CAAC,CAAC;MACN;MACA;QACI6K,mBAAmB,CAACrQ,GAAG,EAAE3C,OAAO,EAAEkF,OAAO,CAAC;MAC9C;IACJ,CAAC,MACI;MACD;MACA;MACA;MACAmP,qBAAqB,CAAC1R,GAAG,EAAE3C,OAAO,EAAEkF,OAAO,CAACoP,WAAW,CAAC;MACxD;MACA,IAAItU,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsO,cAAc,EAAE;QAC1EmF,qBAAqB,CAACzT,OAAO,CAACsO,cAAc,CAAC;MACjD,CAAC,MACI,IAAItO,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuU,gBAAgB,EAAE;QACjFvU,OAAO,CAACuU,gBAAgB,CAACxF,IAAI,CAAC,MAAM0E,qBAAqB,CAACzT,OAAO,CAACsO,cAAc,CAAC,CAAC;MACtF;IACJ;IACA6F,YAAY,CAAC,CAAC;EAClB;AACJ,CAAC;AACD,MAAMC,mBAAmB,GAAIzR,GAAG,IAAK;EACjC;EACA;EACA;EACA;EACA;EACA;EACA,MAAM6R,aAAa,GAAI7R,GAAG,CAAC,MAAM,CAAC,GAAG5G,GAAG,CAAC0Y,aAAa,CAAC,EAAE,CAAE;EAC3DD,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI;EAC5B7R,GAAG,CAACvB,YAAY,CAACoT,aAAa,EAAE7R,GAAG,CAAC+R,UAAU,CAAC;AACnD,CAAC;AACD,MAAMC,kBAAkB,GAAItG,QAAQ,IAAK;EACrC;IACIK,QAAQ,CAACL,QAAQ,EAAE,sBAAsB,CAAC;EAC9C;AACJ,CAAC;AACD,MAAMnZ,oBAAoB;EAAA,IAAA0f,KAAA,GAAA3F,iBAAA,CAAG,WAAOtM,GAAG,EAAK;IACxC,IAAI,CAAC3I,GAAG,CAAC2E,OAAO,GAAG,CAAC,CAAC,4CAA4C,CAAC,EAAE;MAChE,MAAMqB,OAAO,GAAGmD,UAAU,CAACR,GAAG,CAAC;MAC/B;QACI,IAAI3C,OAAO,CAAC6U,aAAa,EAAE;UACvB7U,OAAO,CAAC6U,aAAa,CAAC1V,GAAG,CAAE2V,UAAU,IAAKA,UAAU,CAAC,CAAC,CAAC;UACvD9U,OAAO,CAAC6U,aAAa,GAAGvY,SAAS;QACrC;MACJ;MACA,IAAI0D,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsO,cAAc,EAAE;QAC1EqG,kBAAkB,CAAC3U,OAAO,CAACsO,cAAc,CAAC;MAC9C,CAAC,MACI,IAAItO,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuU,gBAAgB,EAAE;QACjFvU,OAAO,CAACuU,gBAAgB,CAACxF,IAAI,CAAC,MAAM4F,kBAAkB,CAAC3U,OAAO,CAACsO,cAAc,CAAC,CAAC;MACnF;IACJ;EACJ,CAAC;EAAA,gBAhBKpZ,oBAAoBA,CAAA6f,GAAA;IAAA,OAAAH,KAAA,CAAA/E,KAAA,OAAAC,SAAA;EAAA;AAAA,GAgBzB;AACD,MAAMkF,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACjD,IAAIlZ,EAAE;EACN,MAAMmZ,YAAY,GAAG5a,UAAU,CAAC,CAAC;EACjC,MAAM6a,OAAO,GAAG,EAAE;EAClB,MAAMC,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,EAAE;EACrC,MAAM1B,cAAc,GAAGxZ,GAAG,CAACwZ,cAAc;EACzC,MAAMxX,IAAI,GAAGJ,GAAG,CAACI,IAAI;EACrB,MAAMmZ,WAAW,GAAG,aAAcnZ,IAAI,CAACC,aAAa,CAAC,eAAe,CAAC;EACrE,MAAMmZ,UAAU,GAAG,aAAcxZ,GAAG,CAACuG,aAAa,CAAC,OAAO,CAAC;EAC3D,MAAMkT,0BAA0B,GAAG,EAAE;EACrC,MAAM5Q,MAAM,GAAG,aAAc7I,GAAG,CAAC0Z,gBAAgB,CAAC,IAAIva,iBAAiB,GAAG,CAAC;EAC3E,IAAIwa,eAAe;EACnB,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIzY,CAAC,GAAG,CAAC;EACTY,MAAM,CAAC8B,MAAM,CAAC5F,GAAG,EAAEkb,OAAO,CAAC;EAC3Blb,GAAG,CAACC,cAAc,GAAG,IAAIF,GAAG,CAACmb,OAAO,CAACU,YAAY,IAAI,IAAI,EAAE7Z,GAAG,CAAC8Z,OAAO,CAAC,CAACxb,IAAI;EAC5E;IACI;IACA;IACAL,GAAG,CAAC2E,OAAO,IAAI,CAAC,CAAC;EACrB;EACA;IACI,OAAOzB,CAAC,GAAG0H,MAAM,CAACzH,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3BuH,aAAa,CAACG,MAAM,CAAC1H,CAAC,CAAC,CAACb,YAAY,CAACnB,iBAAiB,CAAC,EAAEmL,qBAAqB,CAACzB,MAAM,CAAC1H,CAAC,CAAC,CAACuI,SAAS,CAAC,EAAE,IAAI,CAAC;IAC9G;EACJ;EACA,IAAIqQ,iBAAiB,GAAG,KAAK;EAC7Bb,WAAW,CAAC9V,GAAG,CAAE4W,UAAU,IAAK;IAC5BA,UAAU,CAAC,CAAC,CAAC,CAAC5W,GAAG,CAAE6W,WAAW,IAAK;MAC/B,IAAIha,EAAE;MACN,MAAMkJ,OAAO,GAAG;QACZvG,OAAO,EAAEqX,WAAW,CAAC,CAAC,CAAC;QACvB/P,SAAS,EAAE+P,WAAW,CAAC,CAAC,CAAC;QACzB9E,SAAS,EAAE8E,WAAW,CAAC,CAAC,CAAC;QACzB1B,WAAW,EAAE0B,WAAW,CAAC,CAAC;MAC9B,CAAC;MACD;MACA;MACA,IAAI9Q,OAAO,CAACvG,OAAO,GAAG,CAAC,CAAC,mCAAmC;QACvDmX,iBAAiB,GAAG,IAAI;MAC5B;MACA;QACI5Q,OAAO,CAACgM,SAAS,GAAG8E,WAAW,CAAC,CAAC,CAAC;MACtC;MACA;QACI9Q,OAAO,CAACoP,WAAW,GAAG0B,WAAW,CAAC,CAAC,CAAC;MACxC;MACA;QACI9Q,OAAO,CAAC+H,gBAAgB,GAAG,EAAE;MACjC;MACA;QACI/H,OAAO,CAACqM,UAAU,GAAG,CAACvV,EAAE,GAAGga,WAAW,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIha,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;MAClF;MACA,MAAMvB,OAAO,GAAGyK,OAAO,CAACe,SAAS;MACjC,MAAMgQ,WAAW,GAAG,cAAcC,WAAW,CAAC;QAC1C;QACAxC,WAAWA,CAACyC,IAAI,EAAE;UACd;UACA,KAAK,CAACA,IAAI,CAAC;UACXA,IAAI,GAAG,IAAI;UACXC,YAAY,CAACD,IAAI,EAAEjR,OAAO,CAAC;UAC3B,IAAIA,OAAO,CAACvG,OAAO,GAAG,CAAC,CAAC,wCAAwC;YAC5D;YACA;YACA;YACA;YACA;cACI;gBACIwX,IAAI,CAACE,YAAY,CAAC;kBACdvf,IAAI,EAAE,MAAM;kBACZwf,cAAc,EAAE,CAAC,EAAEpR,OAAO,CAACvG,OAAO,GAAG,EAAE,CAAC;gBAC5C,CAAC,CAAC;cACN;YACJ;UACJ;QACJ;QACA7J,iBAAiBA,CAAA,EAAG;UAChB,IAAI4gB,eAAe,EAAE;YACjBa,YAAY,CAACb,eAAe,CAAC;YAC7BA,eAAe,GAAG,IAAI;UAC1B;UACA,IAAIC,eAAe,EAAE;YACjB;YACAH,0BAA0B,CAAChY,IAAI,CAAC,IAAI,CAAC;UACzC,CAAC,MACI;YACDxD,GAAG,CAACyY,GAAG,CAAC,MAAM3d,iBAAiB,CAAC,IAAI,CAAC,CAAC;UAC1C;QACJ;QACAI,oBAAoBA,CAAA,EAAG;UACnB8E,GAAG,CAACyY,GAAG,CAAC,MAAMvd,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC7C;QACAshB,gBAAgBA,CAAA,EAAG;UACf,OAAOrT,UAAU,CAAC,IAAI,CAAC,CAACoR,gBAAgB;QAC5C;MACJ,CAAC;MACDrP,OAAO,CAACkO,cAAc,GAAG2C,UAAU,CAAC,CAAC,CAAC;MACtC,IAAI,CAACV,OAAO,CAACvP,QAAQ,CAACrL,OAAO,CAAC,IAAI,CAACkZ,cAAc,CAAC1S,GAAG,CAACxG,OAAO,CAAC,EAAE;QAC5D2a,OAAO,CAAC5X,IAAI,CAAC/C,OAAO,CAAC;QACrBkZ,cAAc,CAAC8C,MAAM,CAAChc,OAAO,EAAEiX,cAAc,CAACuE,WAAW,EAAE/Q,OAAO,EAAE,CAAC,CAAC,sCAAsC,CAAC,CAAC;MAClH;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF;EACA;EACA,IAAIkQ,OAAO,CAACjY,MAAM,GAAG,CAAC,EAAE;IACpB;IACA,IAAI2Y,iBAAiB,EAAE;MACnBP,UAAU,CAACnT,WAAW,IAAI/G,WAAW;IACzC;IACA;IACA;MACIka,UAAU,CAACnT,WAAW,IAAIgT,OAAO,GAAGha,YAAY;IACpD;IACA;IACA,IAAIma,UAAU,CAAC9P,SAAS,CAACtI,MAAM,EAAE;MAC7BoY,UAAU,CAAChT,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;MAC1C;MACA,MAAMmD,KAAK,GAAG,CAAC1J,EAAE,GAAGhC,GAAG,CAAC2L,OAAO,MAAM,IAAI,IAAI3J,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGF,wBAAwB,CAACC,GAAG,CAAC;MAC/F,IAAI2J,KAAK,IAAI,IAAI,EAAE;QACf6P,UAAU,CAAChT,YAAY,CAAC,OAAO,EAAEmD,KAAK,CAAC;MAC3C;MACA;MACA;MACAvJ,IAAI,CAACiF,YAAY,CAACmU,UAAU,EAAED,WAAW,GAAGA,WAAW,CAACjU,WAAW,GAAGlF,IAAI,CAACuY,UAAU,CAAC;IAC1F;EACJ;EACA;EACAiB,eAAe,GAAG,KAAK;EACvB,IAAIH,0BAA0B,CAACrY,MAAM,EAAE;IACnCqY,0BAA0B,CAACrW,GAAG,CAAEqG,IAAI,IAAKA,IAAI,CAAC1Q,iBAAiB,CAAC,CAAC,CAAC;EACtE,CAAC,MACI;IACD;MACIkF,GAAG,CAACyY,GAAG,CAAC,MAAOiD,eAAe,GAAGgB,UAAU,CAACtG,UAAU,EAAE,EAAE,CAAE,CAAC;IACjE;EACJ;EACA;EACA+E,YAAY,CAAC,CAAC;AAClB,CAAC;AACD,MAAMd,qBAAqB,GAAGA,CAAC1R,GAAG,EAAE3C,OAAO,EAAE2W,SAAS,EAAEC,qBAAqB,KAAK;EAC9E,IAAID,SAAS,EAAE;IACXA,SAAS,CAACxX,GAAG,CAAC,CAAC,CAACyE,KAAK,EAAElG,IAAI,EAAE7G,MAAM,CAAC,KAAK;MACrC,MAAMggB,MAAM,GAAGC,qBAAqB,CAACnU,GAAG,EAAEiB,KAAK,CAAC;MAChD,MAAMZ,OAAO,GAAG+T,iBAAiB,CAAC/W,OAAO,EAAEnJ,MAAM,CAAC;MAClD,MAAMsN,IAAI,GAAG6S,gBAAgB,CAACpT,KAAK,CAAC;MACpC5J,GAAG,CAAC2N,GAAG,CAACkP,MAAM,EAAEnZ,IAAI,EAAEsF,OAAO,EAAEmB,IAAI,CAAC;MACpC,CAACnE,OAAO,CAAC6U,aAAa,GAAG7U,OAAO,CAAC6U,aAAa,IAAI,EAAE,EAAErX,IAAI,CAAC,MAAMxD,GAAG,CAAC0N,GAAG,CAACmP,MAAM,EAAEnZ,IAAI,EAAEsF,OAAO,EAAEmB,IAAI,CAAC,CAAC;IAC1G,CAAC,CAAC;EACN;AACJ,CAAC;AACD,MAAM4S,iBAAiB,GAAGA,CAAC/W,OAAO,EAAEyO,UAAU,KAAMrK,EAAE,IAAK;EACvD,IAAI;IACA;MACI,IAAIpE,OAAO,CAACrB,OAAO,GAAG,GAAG,CAAC,gCAAgC;QACtD;QACAqB,OAAO,CAACsO,cAAc,CAACG,UAAU,CAAC,CAACrK,EAAE,CAAC;MAC1C,CAAC,MACI;QACD,CAACpE,OAAO,CAACwO,iBAAiB,GAAGxO,OAAO,CAACwO,iBAAiB,IAAI,EAAE,EAAEhR,IAAI,CAAC,CAACiR,UAAU,EAAErK,EAAE,CAAC,CAAC;MACxF;IACJ;EACJ,CAAC,CACD,OAAO0D,CAAC,EAAE;IACNkI,YAAY,CAAClI,CAAC,CAAC;EACnB;AACJ,CAAC;AACD,MAAMgP,qBAAqB,GAAGA,CAACnU,GAAG,EAAEiB,KAAK,KAAK;EAC1C,IAAIA,KAAK,GAAG,CAAC,CAAC,qCACV,OAAO7H,GAAG;EACd,IAAI6H,KAAK,GAAG,CAAC,CAAC,mCACV,OAAOzJ,GAAG;EACd,IAAIyJ,KAAK,GAAG,EAAE,CAAC,iCACX,OAAO7H,GAAG,CAAC0E,IAAI;EACnB,OAAOkC,GAAG;AACd,CAAC;AACD;AACA,MAAMqU,gBAAgB,GAAIpT,KAAK,IAAKqT,uBAAuB,GACpD;EACCC,OAAO,EAAE,CAACtT,KAAK,GAAG,CAAC,CAAC,kCAAkC,CAAC;EACvD0D,OAAO,EAAE,CAAC1D,KAAK,GAAG,CAAC,CAAC,kCAAkC;AAC1D,CAAC,GACC,CAACA,KAAK,GAAG,CAAC,CAAC,kCAAkC,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuT,QAAQ,GAAIzR,KAAK,IAAM1L,GAAG,CAAC2L,OAAO,GAAGD,KAAM;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0R,QAAQ,GAAG,IAAI5S,OAAO,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMrB,UAAU,GAAID,GAAG,IAAKkU,QAAQ,CAACnW,GAAG,CAACiC,GAAG,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmU,gBAAgB,GAAGA,CAACC,YAAY,EAAEtX,OAAO,KAAKoX,QAAQ,CAAC3U,GAAG,CAAEzC,OAAO,CAACsO,cAAc,GAAGgJ,YAAY,EAAGtX,OAAO,CAAC;AAClH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoW,YAAY,GAAGA,CAACmB,WAAW,EAAErS,OAAO,KAAK;EAC3C,MAAMlF,OAAO,GAAG;IACZrB,OAAO,EAAE,CAAC;IACV+E,aAAa,EAAE6T,WAAW;IAC1BxR,SAAS,EAAEb,OAAO;IAClB4L,gBAAgB,EAAE,IAAIpQ,GAAG,CAAC;EAC9B,CAAC;EACD;IACIV,OAAO,CAACqS,mBAAmB,GAAG,IAAIvE,OAAO,CAAEzB,CAAC,IAAMrM,OAAO,CAACqQ,mBAAmB,GAAGhE,CAAE,CAAC;EACvF;EACA;IACIrM,OAAO,CAACuU,gBAAgB,GAAG,IAAIzG,OAAO,CAAEzB,CAAC,IAAMrM,OAAO,CAACmQ,gBAAgB,GAAG9D,CAAE,CAAC;IAC7EkL,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;IACvBA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE;EAC5B;EACAlD,qBAAqB,CAACkD,WAAW,EAAEvX,OAAO,EAAEkF,OAAO,CAACoP,WAAW,CAAC;EAChE,OAAO8C,QAAQ,CAAC3U,GAAG,CAAC8U,WAAW,EAAEvX,OAAO,CAAC;AAC7C,CAAC;AACD,MAAM8G,iBAAiB,GAAGA,CAACnE,GAAG,EAAE8D,UAAU,KAAKA,UAAU,IAAI9D,GAAG;AAChE,MAAMqN,YAAY,GAAGA,CAAClI,CAAC,EAAE0P,EAAE,KAAK,CAAC,CAAC,EAAEC,OAAO,CAACC,KAAK,EAAE5P,CAAC,EAAE0P,EAAE,CAAC;AACzD,MAAMG,UAAU,GAAG,aAAc,IAAIjX,GAAG,CAAC,CAAC;AAC1C,MAAM2S,UAAU,GAAGA,CAACnO,OAAO,EAAElF,OAAO,EAAEkT,YAAY,KAAK;EACnD;EACA,MAAM0E,UAAU,GAAG1S,OAAO,CAACe,SAAS,CAACM,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACvD,MAAM4M,QAAQ,GAAGjO,OAAO,CAACkO,cAAc;EACvC,MAAMyE,MAAM,GAAGF,UAAU,CAAC1W,GAAG,CAACkS,QAAQ,CAAC;EACvC,IAAI0E,MAAM,EAAE;IACR,OAAOA,MAAM,CAACD,UAAU,CAAC;EAC7B;EACA;EACA,OAAO,MAAM,CACb;EACA;EACA;EACA;EACA,KAAKzE,QAAQ,YAAY,EAAE,EAAE,CAAC,CAACpE,IAAI,CAAE+I,cAAc,IAAK;IACpD;MACIH,UAAU,CAAClV,GAAG,CAAC0Q,QAAQ,EAAE2E,cAAc,CAAC;IAC5C;IACA,OAAOA,cAAc,CAACF,UAAU,CAAC;EACrC,CAAC,EAAE5H,YAAY,CAAC;AACpB,CAAC;AACD,MAAMpL,MAAM,GAAG,aAAc,IAAIlE,GAAG,CAAC,CAAC;AACtC,MAAMkC,mBAAmB,GAAG,EAAE;AAC9B,MAAMzI,GAAG,GAAG,OAAO4d,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC;AACvD,MAAMhc,GAAG,GAAG5B,GAAG,CAAC6d,QAAQ,IAAI;EAAE7b,IAAI,EAAE,CAAC;AAAE,CAAC;AACxC,MAAMnC,GAAG,GAAG;EACR2E,OAAO,EAAE,CAAC;EACV1E,cAAc,EAAE,EAAE;EAClBwY,GAAG,EAAGlW,CAAC,IAAKA,CAAC,CAAC,CAAC;EACf0b,GAAG,EAAG1b,CAAC,IAAK2b,qBAAqB,CAAC3b,CAAC,CAAC;EACpCoL,GAAG,EAAEA,CAAC6P,EAAE,EAAEW,SAAS,EAAEC,QAAQ,EAAEjU,IAAI,KAAKqT,EAAE,CAACa,gBAAgB,CAACF,SAAS,EAAEC,QAAQ,EAAEjU,IAAI,CAAC;EACtFuD,GAAG,EAAEA,CAAC8P,EAAE,EAAEW,SAAS,EAAEC,QAAQ,EAAEjU,IAAI,KAAKqT,EAAE,CAACc,mBAAmB,CAACH,SAAS,EAAEC,QAAQ,EAAEjU,IAAI,CAAC;EACzFE,EAAE,EAAEA,CAAC8T,SAAS,EAAEhU,IAAI,KAAK,IAAIoU,WAAW,CAACJ,SAAS,EAAEhU,IAAI;AAC5D,CAAC;AACD,MAAMqU,kBAAkB,GAAIC,OAAO,IAAK;EACpC3a,MAAM,CAAC8B,MAAM,CAAC5F,GAAG,EAAEye,OAAO,CAAC;AAC/B,CAAC;AACD,MAAMvX,cAAc;AACpB;AACA,IAAI;AACJ,MAAM+V,uBAAuB,GAAG,aAAc,CAAC,MAAM;EACjD,IAAIA,uBAAuB,GAAG,KAAK;EACnC,IAAI;IACAlb,GAAG,CAACsc,gBAAgB,CAAC,GAAG,EAAE,IAAI,EAAEva,MAAM,CAACmU,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MACjEhR,GAAGA,CAAA,EAAG;QACFgW,uBAAuB,GAAG,IAAI;MAClC;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CACD,OAAOnP,CAAC,EAAE,CAAE;EACZ,OAAOmP,uBAAuB;AAClC,CAAC,EAAE,CAAC;AACJ,MAAMyB,cAAc,GAAI/c,CAAC,IAAKmS,OAAO,CAAC6K,OAAO,CAAChd,CAAC,CAAC;AAChD,MAAMkJ,gCAAgC,GAAG,aAAc,CAAC,MAAM;EACtD,IAAI;IACA,IAAIC,aAAa,CAAC,CAAC;IACnB,OAAO,OAAO,IAAIA,aAAa,CAAC,CAAC,CAACC,WAAW,KAAK,UAAU;EAChE,CAAC,CACD,OAAO+C,CAAC,EAAE,CAAE;EACZ,OAAO,KAAK;AAChB,CAAC,EAAE,CAAC;AAER,MAAM8Q,aAAa,GAAG,EAAE;AACxB,MAAMC,cAAc,GAAG,EAAE;AACzB,MAAMC,SAAS,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAM9Z,EAAE,IAAK;EACxC6Z,KAAK,CAACvb,IAAI,CAAC0B,EAAE,CAAC;EACd,IAAI,CAAC1F,YAAY,EAAE;IACfA,YAAY,GAAG,IAAI;IACnB,IAAIwf,KAAK,IAAIhf,GAAG,CAAC2E,OAAO,GAAG,CAAC,CAAC,gCAAgC;MACzD2R,QAAQ,CAAC2I,KAAK,CAAC;IACnB,CAAC,MACI;MACDjf,GAAG,CAACie,GAAG,CAACgB,KAAK,CAAC;IAClB;EACJ;AACJ,CAAC;AACD,MAAMC,OAAO,GAAIH,KAAK,IAAK;EACvB,KAAK,IAAI7b,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6b,KAAK,CAAC5b,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAI;MACA6b,KAAK,CAAC7b,CAAC,CAAC,CAACic,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC,CACD,OAAOtR,CAAC,EAAE;MACNkI,YAAY,CAAClI,CAAC,CAAC;IACnB;EACJ;EACAiR,KAAK,CAAC5b,MAAM,GAAG,CAAC;AACpB,CAAC;AACD,MAAM8b,KAAK,GAAGA,CAAA,KAAM;EAChB;EACA;EACA;EACAC,OAAO,CAACN,aAAa,CAAC;EACtB;EACA;IACIM,OAAO,CAACL,cAAc,CAAC;IACvB,IAAKrf,YAAY,GAAGof,aAAa,CAACzb,MAAM,GAAG,CAAC,EAAG;MAC3C;MACA;MACAnD,GAAG,CAACie,GAAG,CAACgB,KAAK,CAAC;IAClB;EACJ;AACJ,CAAC;AACD,MAAM3I,QAAQ,GAAIpR,EAAE,IAAKwZ,cAAc,CAAC,CAAC,CAAC3J,IAAI,CAAC7P,EAAE,CAAC;AAClD,MAAMma,QAAQ,GAAG,aAAcP,SAAS,CAACF,aAAa,EAAE,KAAK,CAAC;AAC9D,MAAMzK,SAAS,GAAG,aAAc2K,SAAS,CAACD,cAAc,EAAE,IAAI,CAAC;AAE/D,SAASpf,KAAK,IAAI6f,CAAC,EAAExa,IAAI,IAAIya,CAAC,EAAEf,kBAAkB,IAAIgB,CAAC,EAAExE,aAAa,IAAIyE,CAAC,EAAE1W,OAAO,IAAI9F,CAAC,EAAE0G,WAAW,IAAI+V,CAAC,EAAEL,QAAQ,IAAIvR,CAAC,EAAErE,UAAU,IAAIkW,CAAC,EAAE1W,OAAO,IAAI2W,CAAC,EAAErd,CAAC,EAAEgU,WAAW,IAAIrT,CAAC,EAAEtD,YAAY,IAAIsS,CAAC,EAAEwM,cAAc,IAAImB,CAAC,EAAExC,gBAAgB,IAAIhL,CAAC,EAAE8K,QAAQ,IAAI2C,CAAC,EAAE3L,SAAS,IAAI4L,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}