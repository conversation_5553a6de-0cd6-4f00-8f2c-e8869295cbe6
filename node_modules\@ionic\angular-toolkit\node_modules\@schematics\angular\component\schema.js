"use strict";
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN devkit-admin build (or bazel build ...).
Object.defineProperty(exports, "__esModule", { value: true });
exports.ViewEncapsulation = exports.Style = exports.ChangeDetection = void 0;
/**
 * The change detection strategy to use in the new component.
 */
var ChangeDetection;
(function (ChangeDetection) {
    ChangeDetection["Default"] = "Default";
    ChangeDetection["OnPush"] = "OnPush";
})(ChangeDetection = exports.ChangeDetection || (exports.ChangeDetection = {}));
/**
 * The file extension or preprocessor to use for style files, or 'none' to skip generating
 * the style file.
 */
var Style;
(function (Style) {
    Style["Css"] = "css";
    Style["Less"] = "less";
    Style["None"] = "none";
    Style["Sass"] = "sass";
    Style["Scss"] = "scss";
})(Style = exports.Style || (exports.Style = {}));
/**
 * The view encapsulation strategy to use in the new component.
 */
var ViewEncapsulation;
(function (ViewEncapsulation) {
    ViewEncapsulation["Emulated"] = "Emulated";
    ViewEncapsulation["None"] = "None";
    ViewEncapsulation["ShadowDom"] = "ShadowDom";
})(ViewEncapsulation = exports.ViewEncapsulation || (exports.ViewEncapsulation = {}));
//# sourceMappingURL=data:application/json;base64,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