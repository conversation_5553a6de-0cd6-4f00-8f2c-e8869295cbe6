"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleMemoryHost = void 0;
const rxjs_1 = require("rxjs");
const exception_1 = require("../../exception");
const path_1 = require("../path");
class SimpleMemoryHost {
    _newDirStats() {
        return {
            inspect() {
                return '<Directory>';
            },
            isFile() {
                return false;
            },
            isDirectory() {
                return true;
            },
            size: 0,
            atime: new Date(),
            ctime: new Date(),
            mtime: new Date(),
            birthtime: new Date(),
            content: null,
        };
    }
    _newFileStats(content, oldStats) {
        return {
            inspect() {
                return `<File size(${content.byteLength})>`;
            },
            isFile() {
                return true;
            },
            isDirectory() {
                return false;
            },
            size: content.byteLength,
            atime: oldStats ? oldStats.atime : new Date(),
            ctime: new Date(),
            mtime: new Date(),
            birthtime: oldStats ? oldStats.birthtime : new Date(),
            content,
        };
    }
    constructor() {
        this._cache = new Map();
        this._watchers = new Map();
        this._cache.set((0, path_1.normalize)('/'), this._newDirStats());
    }
    _toAbsolute(path) {
        return (0, path_1.isAbsolute)(path) ? path : (0, path_1.normalize)('/' + path);
    }
    _updateWatchers(path, type) {
        const time = new Date();
        let currentPath = path;
        let parent = null;
        if (this._watchers.size == 0) {
            // Nothing to do if there's no watchers.
            return;
        }
        const maybeWatcher = this._watchers.get(currentPath);
        if (maybeWatcher) {
            maybeWatcher.forEach((watcher) => {
                const [options, subject] = watcher;
                subject.next({ path, time, type });
                if (!options.persistent && type == 2 /* HostWatchEventType.Deleted */) {
                    subject.complete();
                    this._watchers.delete(currentPath);
                }
            });
        }
        do {
            currentPath = parent !== null ? parent : currentPath;
            parent = (0, path_1.dirname)(currentPath);
            const maybeWatcher = this._watchers.get(currentPath);
            if (maybeWatcher) {
                maybeWatcher.forEach((watcher) => {
                    const [options, subject] = watcher;
                    if (!options.recursive) {
                        return;
                    }
                    subject.next({ path, time, type });
                    if (!options.persistent && type == 2 /* HostWatchEventType.Deleted */) {
                        subject.complete();
                        this._watchers.delete(currentPath);
                    }
                });
            }
        } while (parent != currentPath);
    }
    get capabilities() {
        return { synchronous: true };
    }
    /**
     * List of protected methods that give direct access outside the observables to the cache
     * and internal states.
     */
    _write(path, content) {
        path = this._toAbsolute(path);
        const old = this._cache.get(path);
        if (old && old.isDirectory()) {
            throw new exception_1.PathIsDirectoryException(path);
        }
        // Update all directories. If we find a file we know it's an invalid write.
        const fragments = (0, path_1.split)(path);
        let curr = (0, path_1.normalize)('/');
        for (const fr of fragments) {
            curr = (0, path_1.join)(curr, fr);
            const maybeStats = this._cache.get(fr);
            if (maybeStats) {
                if (maybeStats.isFile()) {
                    throw new exception_1.PathIsFileException(curr);
                }
            }
            else {
                this._cache.set(curr, this._newDirStats());
            }
        }
        // Create the stats.
        const stats = this._newFileStats(content, old);
        this._cache.set(path, stats);
        this._updateWatchers(path, old ? 0 /* HostWatchEventType.Changed */ : 1 /* HostWatchEventType.Created */);
    }
    _read(path) {
        path = this._toAbsolute(path);
        const maybeStats = this._cache.get(path);
        if (!maybeStats) {
            throw new exception_1.FileDoesNotExistException(path);
        }
        else if (maybeStats.isDirectory()) {
            throw new exception_1.PathIsDirectoryException(path);
        }
        else if (!maybeStats.content) {
            throw new exception_1.PathIsDirectoryException(path);
        }
        else {
            return maybeStats.content;
        }
    }
    _delete(path) {
        path = this._toAbsolute(path);
        if (this._isDirectory(path)) {
            for (const [cachePath] of this._cache.entries()) {
                if (cachePath.startsWith(path + path_1.NormalizedSep) || cachePath === path) {
                    this._cache.delete(cachePath);
                }
            }
        }
        else {
            this._cache.delete(path);
        }
        this._updateWatchers(path, 2 /* HostWatchEventType.Deleted */);
    }
    _rename(from, to) {
        from = this._toAbsolute(from);
        to = this._toAbsolute(to);
        if (!this._cache.has(from)) {
            throw new exception_1.FileDoesNotExistException(from);
        }
        else if (this._cache.has(to)) {
            throw new exception_1.FileAlreadyExistException(to);
        }
        if (this._isDirectory(from)) {
            for (const path of this._cache.keys()) {
                if (path.startsWith(from + path_1.NormalizedSep)) {
                    const content = this._cache.get(path);
                    if (content) {
                        // We don't need to clone or extract the content, since we're moving files.
                        this._cache.set((0, path_1.join)(to, path_1.NormalizedSep, path.slice(from.length)), content);
                    }
                }
            }
        }
        else {
            const content = this._cache.get(from);
            if (content) {
                const fragments = (0, path_1.split)(to);
                const newDirectories = [];
                let curr = (0, path_1.normalize)('/');
                for (const fr of fragments) {
                    curr = (0, path_1.join)(curr, fr);
                    const maybeStats = this._cache.get(fr);
                    if (maybeStats) {
                        if (maybeStats.isFile()) {
                            throw new exception_1.PathIsFileException(curr);
                        }
                    }
                    else {
                        newDirectories.push(curr);
                    }
                }
                for (const newDirectory of newDirectories) {
                    this._cache.set(newDirectory, this._newDirStats());
                }
                this._cache.delete(from);
                this._cache.set(to, content);
            }
        }
        this._updateWatchers(from, 3 /* HostWatchEventType.Renamed */);
    }
    _list(path) {
        path = this._toAbsolute(path);
        if (this._isFile(path)) {
            throw new exception_1.PathIsFileException(path);
        }
        const fragments = (0, path_1.split)(path);
        const result = new Set();
        if (path !== path_1.NormalizedRoot) {
            for (const p of this._cache.keys()) {
                if (p.startsWith(path + path_1.NormalizedSep)) {
                    result.add((0, path_1.split)(p)[fragments.length]);
                }
            }
        }
        else {
            for (const p of this._cache.keys()) {
                if (p.startsWith(path_1.NormalizedSep) && p !== path_1.NormalizedRoot) {
                    result.add((0, path_1.split)(p)[1]);
                }
            }
        }
        return [...result];
    }
    _exists(path) {
        return !!this._cache.get(this._toAbsolute(path));
    }
    _isDirectory(path) {
        const maybeStats = this._cache.get(this._toAbsolute(path));
        return maybeStats ? maybeStats.isDirectory() : false;
    }
    _isFile(path) {
        const maybeStats = this._cache.get(this._toAbsolute(path));
        return maybeStats ? maybeStats.isFile() : false;
    }
    _stat(path) {
        const maybeStats = this._cache.get(this._toAbsolute(path));
        if (!maybeStats) {
            return null;
        }
        else {
            return maybeStats;
        }
    }
    _watch(path, options) {
        path = this._toAbsolute(path);
        const subject = new rxjs_1.Subject();
        let maybeWatcherArray = this._watchers.get(path);
        if (!maybeWatcherArray) {
            maybeWatcherArray = [];
            this._watchers.set(path, maybeWatcherArray);
        }
        maybeWatcherArray.push([options || {}, subject]);
        return subject.asObservable();
    }
    write(path, content) {
        return new rxjs_1.Observable((obs) => {
            this._write(path, content);
            obs.next();
            obs.complete();
        });
    }
    read(path) {
        return new rxjs_1.Observable((obs) => {
            const content = this._read(path);
            obs.next(content);
            obs.complete();
        });
    }
    delete(path) {
        return new rxjs_1.Observable((obs) => {
            this._delete(path);
            obs.next();
            obs.complete();
        });
    }
    rename(from, to) {
        return new rxjs_1.Observable((obs) => {
            this._rename(from, to);
            obs.next();
            obs.complete();
        });
    }
    list(path) {
        return new rxjs_1.Observable((obs) => {
            obs.next(this._list(path));
            obs.complete();
        });
    }
    exists(path) {
        return new rxjs_1.Observable((obs) => {
            obs.next(this._exists(path));
            obs.complete();
        });
    }
    isDirectory(path) {
        return new rxjs_1.Observable((obs) => {
            obs.next(this._isDirectory(path));
            obs.complete();
        });
    }
    isFile(path) {
        return new rxjs_1.Observable((obs) => {
            obs.next(this._isFile(path));
            obs.complete();
        });
    }
    // Some hosts may not support stat.
    stat(path) {
        return new rxjs_1.Observable((obs) => {
            obs.next(this._stat(path));
            obs.complete();
        });
    }
    watch(path, options) {
        return this._watch(path, options);
    }
    reset() {
        this._cache.clear();
        this._watchers.clear();
    }
}
exports.SimpleMemoryHost = SimpleMemoryHost;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWVtb3J5LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvYW5ndWxhcl9kZXZraXQvY29yZS9zcmMvdmlydHVhbC1mcy9ob3N0L21lbW9yeS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUE7Ozs7OztHQU1HOzs7QUFFSCwrQkFBMkM7QUFDM0MsK0NBS3lCO0FBQ3pCLGtDQVVpQjtBQWVqQixNQUFhLGdCQUFnQjtJQUlqQixZQUFZO1FBQ3BCLE9BQU87WUFDTCxPQUFPO2dCQUNMLE9BQU8sYUFBYSxDQUFDO1lBQ3ZCLENBQUM7WUFFRCxNQUFNO2dCQUNKLE9BQU8sS0FBSyxDQUFDO1lBQ2YsQ0FBQztZQUNELFdBQVc7Z0JBQ1QsT0FBTyxJQUFJLENBQUM7WUFDZCxDQUFDO1lBQ0QsSUFBSSxFQUFFLENBQUM7WUFFUCxLQUFLLEVBQUUsSUFBSSxJQUFJLEVBQUU7WUFDakIsS0FBSyxFQUFFLElBQUksSUFBSSxFQUFFO1lBQ2pCLEtBQUssRUFBRSxJQUFJLElBQUksRUFBRTtZQUNqQixTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7WUFFckIsT0FBTyxFQUFFLElBQUk7U0FDZCxDQUFDO0lBQ0osQ0FBQztJQUNTLGFBQWEsQ0FBQyxPQUFtQixFQUFFLFFBQXVDO1FBQ2xGLE9BQU87WUFDTCxPQUFPO2dCQUNMLE9BQU8sY0FBYyxPQUFPLENBQUMsVUFBVSxJQUFJLENBQUM7WUFDOUMsQ0FBQztZQUVELE1BQU07Z0JBQ0osT0FBTyxJQUFJLENBQUM7WUFDZCxDQUFDO1lBQ0QsV0FBVztnQkFDVCxPQUFPLEtBQUssQ0FBQztZQUNmLENBQUM7WUFDRCxJQUFJLEVBQUUsT0FBTyxDQUFDLFVBQVU7WUFFeEIsS0FBSyxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFBSSxJQUFJLEVBQUU7WUFDN0MsS0FBSyxFQUFFLElBQUksSUFBSSxFQUFFO1lBQ2pCLEtBQUssRUFBRSxJQUFJLElBQUksRUFBRTtZQUNqQixTQUFTLEVBQUUsUUFBUSxDQUFDLENBQUMsQ0FBQyxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUMsQ0FBQyxJQUFJLElBQUksRUFBRTtZQUVyRCxPQUFPO1NBQ1IsQ0FBQztJQUNKLENBQUM7SUFFRDtRQWhEVSxXQUFNLEdBQUcsSUFBSSxHQUFHLEVBQXNDLENBQUM7UUFDekQsY0FBUyxHQUFHLElBQUksR0FBRyxFQUF1RCxDQUFDO1FBZ0RqRixJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxJQUFBLGdCQUFTLEVBQUMsR0FBRyxDQUFDLEVBQUUsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDLENBQUM7SUFDdkQsQ0FBQztJQUVTLFdBQVcsQ0FBQyxJQUFVO1FBQzlCLE9BQU8sSUFBQSxpQkFBVSxFQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLElBQUEsZ0JBQVMsRUFBQyxHQUFHLEdBQUcsSUFBSSxDQUFDLENBQUM7SUFDekQsQ0FBQztJQUVTLGVBQWUsQ0FBQyxJQUFVLEVBQUUsSUFBd0I7UUFDNUQsTUFBTSxJQUFJLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztRQUN4QixJQUFJLFdBQVcsR0FBRyxJQUFJLENBQUM7UUFDdkIsSUFBSSxNQUFNLEdBQWdCLElBQUksQ0FBQztRQUUvQixJQUFJLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxJQUFJLENBQUMsRUFBRTtZQUM1Qix3Q0FBd0M7WUFDeEMsT0FBTztTQUNSO1FBRUQsTUFBTSxZQUFZLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDLENBQUM7UUFDckQsSUFBSSxZQUFZLEVBQUU7WUFDaEIsWUFBWSxDQUFDLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxFQUFFO2dCQUMvQixNQUFNLENBQUMsT0FBTyxFQUFFLE9BQU8sQ0FBQyxHQUFHLE9BQU8sQ0FBQztnQkFDbkMsT0FBTyxDQUFDLElBQUksQ0FBQyxFQUFFLElBQUksRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQztnQkFFbkMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxVQUFVLElBQUksSUFBSSxzQ0FBOEIsRUFBRTtvQkFDN0QsT0FBTyxDQUFDLFFBQVEsRUFBRSxDQUFDO29CQUNuQixJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxXQUFXLENBQUMsQ0FBQztpQkFDcEM7WUFDSCxDQUFDLENBQUMsQ0FBQztTQUNKO1FBRUQsR0FBRztZQUNELFdBQVcsR0FBRyxNQUFNLEtBQUssSUFBSSxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLFdBQVcsQ0FBQztZQUNyRCxNQUFNLEdBQUcsSUFBQSxjQUFPLEVBQUMsV0FBVyxDQUFDLENBQUM7WUFFOUIsTUFBTSxZQUFZLEdBQUcsSUFBSSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDLENBQUM7WUFDckQsSUFBSSxZQUFZLEVBQUU7Z0JBQ2hCLFlBQVksQ0FBQyxPQUFPLENBQUMsQ0FBQyxPQUFPLEVBQUUsRUFBRTtvQkFDL0IsTUFBTSxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsR0FBRyxPQUFPLENBQUM7b0JBQ25DLElBQUksQ0FBQyxPQUFPLENBQUMsU0FBUyxFQUFFO3dCQUN0QixPQUFPO3FCQUNSO29CQUNELE9BQU8sQ0FBQyxJQUFJLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7b0JBRW5DLElBQUksQ0FBQyxPQUFPLENBQUMsVUFBVSxJQUFJLElBQUksc0NBQThCLEVBQUU7d0JBQzdELE9BQU8sQ0FBQyxRQUFRLEVBQUUsQ0FBQzt3QkFDbkIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsV0FBVyxDQUFDLENBQUM7cUJBQ3BDO2dCQUNILENBQUMsQ0FBQyxDQUFDO2FBQ0o7U0FDRixRQUFRLE1BQU0sSUFBSSxXQUFXLEVBQUU7SUFDbEMsQ0FBQztJQUVELElBQUksWUFBWTtRQUNkLE9BQU8sRUFBRSxXQUFXLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDL0IsQ0FBQztJQUVEOzs7T0FHRztJQUNPLE1BQU0sQ0FBQyxJQUFVLEVBQUUsT0FBbUI7UUFDOUMsSUFBSSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDOUIsTUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDbEMsSUFBSSxHQUFHLElBQUksR0FBRyxDQUFDLFdBQVcsRUFBRSxFQUFFO1lBQzVCLE1BQU0sSUFBSSxvQ0FBd0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUMxQztRQUVELDJFQUEyRTtRQUMzRSxNQUFNLFNBQVMsR0FBRyxJQUFBLFlBQUssRUFBQyxJQUFJLENBQUMsQ0FBQztRQUM5QixJQUFJLElBQUksR0FBUyxJQUFBLGdCQUFTLEVBQUMsR0FBRyxDQUFDLENBQUM7UUFDaEMsS0FBSyxNQUFNLEVBQUUsSUFBSSxTQUFTLEVBQUU7WUFDMUIsSUFBSSxHQUFHLElBQUEsV0FBSSxFQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQztZQUN0QixNQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN2QyxJQUFJLFVBQVUsRUFBRTtnQkFDZCxJQUFJLFVBQVUsQ0FBQyxNQUFNLEVBQUUsRUFBRTtvQkFDdkIsTUFBTSxJQUFJLCtCQUFtQixDQUFDLElBQUksQ0FBQyxDQUFDO2lCQUNyQzthQUNGO2lCQUFNO2dCQUNMLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUMsQ0FBQzthQUM1QztTQUNGO1FBRUQsb0JBQW9CO1FBQ3BCLE1BQU0sS0FBSyxHQUFpQyxJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sRUFBRSxHQUFHLENBQUMsQ0FBQztRQUM3RSxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDN0IsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLEVBQUUsR0FBRyxDQUFDLENBQUMsb0NBQTRCLENBQUMsbUNBQTJCLENBQUMsQ0FBQztJQUM1RixDQUFDO0lBQ1MsS0FBSyxDQUFDLElBQVU7UUFDeEIsSUFBSSxHQUFHLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDOUIsTUFBTSxVQUFVLEdBQUcsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDekMsSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUNmLE1BQU0sSUFBSSxxQ0FBeUIsQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUMzQzthQUFNLElBQUksVUFBVSxDQUFDLFdBQVcsRUFBRSxFQUFFO1lBQ25DLE1BQU0sSUFBSSxvQ0FBd0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUMxQzthQUFNLElBQUksQ0FBQyxVQUFVLENBQUMsT0FBTyxFQUFFO1lBQzlCLE1BQU0sSUFBSSxvQ0FBd0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUMxQzthQUFNO1lBQ0wsT0FBTyxVQUFVLENBQUMsT0FBTyxDQUFDO1NBQzNCO0lBQ0gsQ0FBQztJQUNTLE9BQU8sQ0FBQyxJQUFVO1FBQzFCLElBQUksR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQzlCLElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUMzQixLQUFLLE1BQU0sQ0FBQyxTQUFTLENBQUMsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLE9BQU8sRUFBRSxFQUFFO2dCQUMvQyxJQUFJLFNBQVMsQ0FBQyxVQUFVLENBQUMsSUFBSSxHQUFHLG9CQUFhLENBQUMsSUFBSSxTQUFTLEtBQUssSUFBSSxFQUFFO29CQUNwRSxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsQ0FBQztpQkFDL0I7YUFDRjtTQUNGO2FBQU07WUFDTCxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQztTQUMxQjtRQUNELElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxxQ0FBNkIsQ0FBQztJQUN6RCxDQUFDO0lBQ1MsT0FBTyxDQUFDLElBQVUsRUFBRSxFQUFRO1FBQ3BDLElBQUksR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQzlCLEVBQUUsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBQzFCLElBQUksQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUMxQixNQUFNLElBQUkscUNBQXlCLENBQUMsSUFBSSxDQUFDLENBQUM7U0FDM0M7YUFBTSxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxFQUFFO1lBQzlCLE1BQU0sSUFBSSxxQ0FBeUIsQ0FBQyxFQUFFLENBQUMsQ0FBQztTQUN6QztRQUVELElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUMzQixLQUFLLE1BQU0sSUFBSSxJQUFJLElBQUksQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLEVBQUU7Z0JBQ3JDLElBQUksSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLEdBQUcsb0JBQWEsQ0FBQyxFQUFFO29CQUN6QyxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsQ0FBQztvQkFDdEMsSUFBSSxPQUFPLEVBQUU7d0JBQ1gsMkVBQTJFO3dCQUMzRSxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxJQUFBLFdBQUksRUFBQyxFQUFFLEVBQUUsb0JBQWEsRUFBRSxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDO3FCQUM1RTtpQkFDRjthQUNGO1NBQ0Y7YUFBTTtZQUNMLE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ3RDLElBQUksT0FBTyxFQUFFO2dCQUNYLE1BQU0sU0FBUyxHQUFHLElBQUEsWUFBSyxFQUFDLEVBQUUsQ0FBQyxDQUFDO2dCQUM1QixNQUFNLGNBQWMsR0FBVyxFQUFFLENBQUM7Z0JBQ2xDLElBQUksSUFBSSxHQUFTLElBQUEsZ0JBQVMsRUFBQyxHQUFHLENBQUMsQ0FBQztnQkFDaEMsS0FBSyxNQUFNLEVBQUUsSUFBSSxTQUFTLEVBQUU7b0JBQzFCLElBQUksR0FBRyxJQUFBLFdBQUksRUFBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUM7b0JBQ3RCLE1BQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxDQUFDO29CQUN2QyxJQUFJLFVBQVUsRUFBRTt3QkFDZCxJQUFJLFVBQVUsQ0FBQyxNQUFNLEVBQUUsRUFBRTs0QkFDdkIsTUFBTSxJQUFJLCtCQUFtQixDQUFDLElBQUksQ0FBQyxDQUFDO3lCQUNyQztxQkFDRjt5QkFBTTt3QkFDTCxjQUFjLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO3FCQUMzQjtpQkFDRjtnQkFDRCxLQUFLLE1BQU0sWUFBWSxJQUFJLGNBQWMsRUFBRTtvQkFDekMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsWUFBWSxFQUFFLElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQyxDQUFDO2lCQUNwRDtnQkFDRCxJQUFJLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDekIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsRUFBRSxFQUFFLE9BQU8sQ0FBQyxDQUFDO2FBQzlCO1NBQ0Y7UUFFRCxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUkscUNBQTZCLENBQUM7SUFDekQsQ0FBQztJQUVTLEtBQUssQ0FBQyxJQUFVO1FBQ3hCLElBQUksR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQzlCLElBQUksSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUN0QixNQUFNLElBQUksK0JBQW1CLENBQUMsSUFBSSxDQUFDLENBQUM7U0FDckM7UUFFRCxNQUFNLFNBQVMsR0FBRyxJQUFBLFlBQUssRUFBQyxJQUFJLENBQUMsQ0FBQztRQUM5QixNQUFNLE1BQU0sR0FBRyxJQUFJLEdBQUcsRUFBZ0IsQ0FBQztRQUN2QyxJQUFJLElBQUksS0FBSyxxQkFBYyxFQUFFO1lBQzNCLEtBQUssTUFBTSxDQUFDLElBQUksSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLEVBQUUsRUFBRTtnQkFDbEMsSUFBSSxDQUFDLENBQUMsVUFBVSxDQUFDLElBQUksR0FBRyxvQkFBYSxDQUFDLEVBQUU7b0JBQ3RDLE1BQU0sQ0FBQyxHQUFHLENBQUMsSUFBQSxZQUFLLEVBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUM7aUJBQ3hDO2FBQ0Y7U0FDRjthQUFNO1lBQ0wsS0FBSyxNQUFNLENBQUMsSUFBSSxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFO2dCQUNsQyxJQUFJLENBQUMsQ0FBQyxVQUFVLENBQUMsb0JBQWEsQ0FBQyxJQUFJLENBQUMsS0FBSyxxQkFBYyxFQUFFO29CQUN2RCxNQUFNLENBQUMsR0FBRyxDQUFDLElBQUEsWUFBSyxFQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7aUJBQ3pCO2FBQ0Y7U0FDRjtRQUVELE9BQU8sQ0FBQyxHQUFHLE1BQU0sQ0FBQyxDQUFDO0lBQ3JCLENBQUM7SUFFUyxPQUFPLENBQUMsSUFBVTtRQUMxQixPQUFPLENBQUMsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDbkQsQ0FBQztJQUNTLFlBQVksQ0FBQyxJQUFVO1FBQy9CLE1BQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUUzRCxPQUFPLFVBQVUsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUM7SUFDdkQsQ0FBQztJQUNTLE9BQU8sQ0FBQyxJQUFVO1FBQzFCLE1BQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUUzRCxPQUFPLFVBQVUsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUM7SUFDbEQsQ0FBQztJQUVTLEtBQUssQ0FBQyxJQUFVO1FBQ3hCLE1BQU0sVUFBVSxHQUFHLElBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUUzRCxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ2YsT0FBTyxJQUFJLENBQUM7U0FDYjthQUFNO1lBQ0wsT0FBTyxVQUFVLENBQUM7U0FDbkI7SUFDSCxDQUFDO0lBRVMsTUFBTSxDQUFDLElBQVUsRUFBRSxPQUEwQjtRQUNyRCxJQUFJLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUU5QixNQUFNLE9BQU8sR0FBRyxJQUFJLGNBQU8sRUFBa0IsQ0FBQztRQUM5QyxJQUFJLGlCQUFpQixHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ2pELElBQUksQ0FBQyxpQkFBaUIsRUFBRTtZQUN0QixpQkFBaUIsR0FBRyxFQUFFLENBQUM7WUFDdkIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsSUFBSSxFQUFFLGlCQUFpQixDQUFDLENBQUM7U0FDN0M7UUFFRCxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxPQUFPLElBQUksRUFBRSxFQUFFLE9BQU8sQ0FBQyxDQUFDLENBQUM7UUFFakQsT0FBTyxPQUFPLENBQUMsWUFBWSxFQUFFLENBQUM7SUFDaEMsQ0FBQztJQUVELEtBQUssQ0FBQyxJQUFVLEVBQUUsT0FBbUI7UUFDbkMsT0FBTyxJQUFJLGlCQUFVLENBQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRTtZQUNsQyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxPQUFPLENBQUMsQ0FBQztZQUMzQixHQUFHLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDWCxHQUFHLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDakIsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQsSUFBSSxDQUFDLElBQVU7UUFDYixPQUFPLElBQUksaUJBQVUsQ0FBYSxDQUFDLEdBQUcsRUFBRSxFQUFFO1lBQ3hDLE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxLQUFLLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDakMsR0FBRyxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUNsQixHQUFHLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDakIsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQsTUFBTSxDQUFDLElBQVU7UUFDZixPQUFPLElBQUksaUJBQVUsQ0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFO1lBQ2xDLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDbkIsR0FBRyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ1gsR0FBRyxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ2pCLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELE1BQU0sQ0FBQyxJQUFVLEVBQUUsRUFBUTtRQUN6QixPQUFPLElBQUksaUJBQVUsQ0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFO1lBQ2xDLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxDQUFDO1lBQ3ZCLEdBQUcsQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNYLEdBQUcsQ0FBQyxRQUFRLEVBQUUsQ0FBQztRQUNqQixDQUFDLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRCxJQUFJLENBQUMsSUFBVTtRQUNiLE9BQU8sSUFBSSxpQkFBVSxDQUFpQixDQUFDLEdBQUcsRUFBRSxFQUFFO1lBQzVDLEdBQUcsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO1lBQzNCLEdBQUcsQ0FBQyxRQUFRLEVBQUUsQ0FBQztRQUNqQixDQUFDLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRCxNQUFNLENBQUMsSUFBVTtRQUNmLE9BQU8sSUFBSSxpQkFBVSxDQUFVLENBQUMsR0FBRyxFQUFFLEVBQUU7WUFDckMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7WUFDN0IsR0FBRyxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ2pCLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELFdBQVcsQ0FBQyxJQUFVO1FBQ3BCLE9BQU8sSUFBSSxpQkFBVSxDQUFVLENBQUMsR0FBRyxFQUFFLEVBQUU7WUFDckMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7WUFDbEMsR0FBRyxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ2pCLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELE1BQU0sQ0FBQyxJQUFVO1FBQ2YsT0FBTyxJQUFJLGlCQUFVLENBQVUsQ0FBQyxHQUFHLEVBQUUsRUFBRTtZQUNyQyxHQUFHLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztZQUM3QixHQUFHLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDakIsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQsbUNBQW1DO0lBQ25DLElBQUksQ0FBQyxJQUFVO1FBQ2IsT0FBTyxJQUFJLGlCQUFVLENBQW1CLENBQUMsR0FBRyxFQUFFLEVBQUU7WUFDOUMsR0FBRyxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7WUFDM0IsR0FBRyxDQUFDLFFBQVEsRUFBRSxDQUFDO1FBQ2pCLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELEtBQUssQ0FBQyxJQUFVLEVBQUUsT0FBMEI7UUFDMUMsT0FBTyxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxPQUFPLENBQUMsQ0FBQztJQUNwQyxDQUFDO0lBRUQsS0FBSztRQUNILElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxFQUFFLENBQUM7UUFDcEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxLQUFLLEVBQUUsQ0FBQztJQUN6QixDQUFDO0NBQ0Y7QUE5VkQsNENBOFZDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7IE9ic2VydmFibGUsIFN1YmplY3QgfSBmcm9tICdyeGpzJztcbmltcG9ydCB7XG4gIEZpbGVBbHJlYWR5RXhpc3RFeGNlcHRpb24sXG4gIEZpbGVEb2VzTm90RXhpc3RFeGNlcHRpb24sXG4gIFBhdGhJc0RpcmVjdG9yeUV4Y2VwdGlvbixcbiAgUGF0aElzRmlsZUV4Y2VwdGlvbixcbn0gZnJvbSAnLi4vLi4vZXhjZXB0aW9uJztcbmltcG9ydCB7XG4gIE5vcm1hbGl6ZWRSb290LFxuICBOb3JtYWxpemVkU2VwLFxuICBQYXRoLFxuICBQYXRoRnJhZ21lbnQsXG4gIGRpcm5hbWUsXG4gIGlzQWJzb2x1dGUsXG4gIGpvaW4sXG4gIG5vcm1hbGl6ZSxcbiAgc3BsaXQsXG59IGZyb20gJy4uL3BhdGgnO1xuaW1wb3J0IHtcbiAgRmlsZUJ1ZmZlcixcbiAgSG9zdCxcbiAgSG9zdENhcGFiaWxpdGllcyxcbiAgSG9zdFdhdGNoRXZlbnQsXG4gIEhvc3RXYXRjaEV2ZW50VHlwZSxcbiAgSG9zdFdhdGNoT3B0aW9ucyxcbiAgU3RhdHMsXG59IGZyb20gJy4vaW50ZXJmYWNlJztcblxuZXhwb3J0IGludGVyZmFjZSBTaW1wbGVNZW1vcnlIb3N0U3RhdHMge1xuICByZWFkb25seSBjb250ZW50OiBGaWxlQnVmZmVyIHwgbnVsbDtcbn1cblxuZXhwb3J0IGNsYXNzIFNpbXBsZU1lbW9yeUhvc3QgaW1wbGVtZW50cyBIb3N0PHt9PiB7XG4gIHByb3RlY3RlZCBfY2FjaGUgPSBuZXcgTWFwPFBhdGgsIFN0YXRzPFNpbXBsZU1lbW9yeUhvc3RTdGF0cz4+KCk7XG4gIHByaXZhdGUgX3dhdGNoZXJzID0gbmV3IE1hcDxQYXRoLCBbSG9zdFdhdGNoT3B0aW9ucywgU3ViamVjdDxIb3N0V2F0Y2hFdmVudD5dW10+KCk7XG5cbiAgcHJvdGVjdGVkIF9uZXdEaXJTdGF0cygpIHtcbiAgICByZXR1cm4ge1xuICAgICAgaW5zcGVjdCgpIHtcbiAgICAgICAgcmV0dXJuICc8RGlyZWN0b3J5Pic7XG4gICAgICB9LFxuXG4gICAgICBpc0ZpbGUoKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH0sXG4gICAgICBpc0RpcmVjdG9yeSgpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9LFxuICAgICAgc2l6ZTogMCxcblxuICAgICAgYXRpbWU6IG5ldyBEYXRlKCksXG4gICAgICBjdGltZTogbmV3IERhdGUoKSxcbiAgICAgIG10aW1lOiBuZXcgRGF0ZSgpLFxuICAgICAgYmlydGh0aW1lOiBuZXcgRGF0ZSgpLFxuXG4gICAgICBjb250ZW50OiBudWxsLFxuICAgIH07XG4gIH1cbiAgcHJvdGVjdGVkIF9uZXdGaWxlU3RhdHMoY29udGVudDogRmlsZUJ1ZmZlciwgb2xkU3RhdHM/OiBTdGF0czxTaW1wbGVNZW1vcnlIb3N0U3RhdHM+KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGluc3BlY3QoKSB7XG4gICAgICAgIHJldHVybiBgPEZpbGUgc2l6ZSgke2NvbnRlbnQuYnl0ZUxlbmd0aH0pPmA7XG4gICAgICB9LFxuXG4gICAgICBpc0ZpbGUoKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfSxcbiAgICAgIGlzRGlyZWN0b3J5KCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9LFxuICAgICAgc2l6ZTogY29udGVudC5ieXRlTGVuZ3RoLFxuXG4gICAgICBhdGltZTogb2xkU3RhdHMgPyBvbGRTdGF0cy5hdGltZSA6IG5ldyBEYXRlKCksXG4gICAgICBjdGltZTogbmV3IERhdGUoKSxcbiAgICAgIG10aW1lOiBuZXcgRGF0ZSgpLFxuICAgICAgYmlydGh0aW1lOiBvbGRTdGF0cyA/IG9sZFN0YXRzLmJpcnRodGltZSA6IG5ldyBEYXRlKCksXG5cbiAgICAgIGNvbnRlbnQsXG4gICAgfTtcbiAgfVxuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuX2NhY2hlLnNldChub3JtYWxpemUoJy8nKSwgdGhpcy5fbmV3RGlyU3RhdHMoKSk7XG4gIH1cblxuICBwcm90ZWN0ZWQgX3RvQWJzb2x1dGUocGF0aDogUGF0aCkge1xuICAgIHJldHVybiBpc0Fic29sdXRlKHBhdGgpID8gcGF0aCA6IG5vcm1hbGl6ZSgnLycgKyBwYXRoKTtcbiAgfVxuXG4gIHByb3RlY3RlZCBfdXBkYXRlV2F0Y2hlcnMocGF0aDogUGF0aCwgdHlwZTogSG9zdFdhdGNoRXZlbnRUeXBlKSB7XG4gICAgY29uc3QgdGltZSA9IG5ldyBEYXRlKCk7XG4gICAgbGV0IGN1cnJlbnRQYXRoID0gcGF0aDtcbiAgICBsZXQgcGFyZW50OiBQYXRoIHwgbnVsbCA9IG51bGw7XG5cbiAgICBpZiAodGhpcy5fd2F0Y2hlcnMuc2l6ZSA9PSAwKSB7XG4gICAgICAvLyBOb3RoaW5nIHRvIGRvIGlmIHRoZXJlJ3Mgbm8gd2F0Y2hlcnMuXG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgbWF5YmVXYXRjaGVyID0gdGhpcy5fd2F0Y2hlcnMuZ2V0KGN1cnJlbnRQYXRoKTtcbiAgICBpZiAobWF5YmVXYXRjaGVyKSB7XG4gICAgICBtYXliZVdhdGNoZXIuZm9yRWFjaCgod2F0Y2hlcikgPT4ge1xuICAgICAgICBjb25zdCBbb3B0aW9ucywgc3ViamVjdF0gPSB3YXRjaGVyO1xuICAgICAgICBzdWJqZWN0Lm5leHQoeyBwYXRoLCB0aW1lLCB0eXBlIH0pO1xuXG4gICAgICAgIGlmICghb3B0aW9ucy5wZXJzaXN0ZW50ICYmIHR5cGUgPT0gSG9zdFdhdGNoRXZlbnRUeXBlLkRlbGV0ZWQpIHtcbiAgICAgICAgICBzdWJqZWN0LmNvbXBsZXRlKCk7XG4gICAgICAgICAgdGhpcy5fd2F0Y2hlcnMuZGVsZXRlKGN1cnJlbnRQYXRoKTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgZG8ge1xuICAgICAgY3VycmVudFBhdGggPSBwYXJlbnQgIT09IG51bGwgPyBwYXJlbnQgOiBjdXJyZW50UGF0aDtcbiAgICAgIHBhcmVudCA9IGRpcm5hbWUoY3VycmVudFBhdGgpO1xuXG4gICAgICBjb25zdCBtYXliZVdhdGNoZXIgPSB0aGlzLl93YXRjaGVycy5nZXQoY3VycmVudFBhdGgpO1xuICAgICAgaWYgKG1heWJlV2F0Y2hlcikge1xuICAgICAgICBtYXliZVdhdGNoZXIuZm9yRWFjaCgod2F0Y2hlcikgPT4ge1xuICAgICAgICAgIGNvbnN0IFtvcHRpb25zLCBzdWJqZWN0XSA9IHdhdGNoZXI7XG4gICAgICAgICAgaWYgKCFvcHRpb25zLnJlY3Vyc2l2ZSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cbiAgICAgICAgICBzdWJqZWN0Lm5leHQoeyBwYXRoLCB0aW1lLCB0eXBlIH0pO1xuXG4gICAgICAgICAgaWYgKCFvcHRpb25zLnBlcnNpc3RlbnQgJiYgdHlwZSA9PSBIb3N0V2F0Y2hFdmVudFR5cGUuRGVsZXRlZCkge1xuICAgICAgICAgICAgc3ViamVjdC5jb21wbGV0ZSgpO1xuICAgICAgICAgICAgdGhpcy5fd2F0Y2hlcnMuZGVsZXRlKGN1cnJlbnRQYXRoKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gd2hpbGUgKHBhcmVudCAhPSBjdXJyZW50UGF0aCk7XG4gIH1cblxuICBnZXQgY2FwYWJpbGl0aWVzKCk6IEhvc3RDYXBhYmlsaXRpZXMge1xuICAgIHJldHVybiB7IHN5bmNocm9ub3VzOiB0cnVlIH07XG4gIH1cblxuICAvKipcbiAgICogTGlzdCBvZiBwcm90ZWN0ZWQgbWV0aG9kcyB0aGF0IGdpdmUgZGlyZWN0IGFjY2VzcyBvdXRzaWRlIHRoZSBvYnNlcnZhYmxlcyB0byB0aGUgY2FjaGVcbiAgICogYW5kIGludGVybmFsIHN0YXRlcy5cbiAgICovXG4gIHByb3RlY3RlZCBfd3JpdGUocGF0aDogUGF0aCwgY29udGVudDogRmlsZUJ1ZmZlcik6IHZvaWQge1xuICAgIHBhdGggPSB0aGlzLl90b0Fic29sdXRlKHBhdGgpO1xuICAgIGNvbnN0IG9sZCA9IHRoaXMuX2NhY2hlLmdldChwYXRoKTtcbiAgICBpZiAob2xkICYmIG9sZC5pc0RpcmVjdG9yeSgpKSB7XG4gICAgICB0aHJvdyBuZXcgUGF0aElzRGlyZWN0b3J5RXhjZXB0aW9uKHBhdGgpO1xuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBhbGwgZGlyZWN0b3JpZXMuIElmIHdlIGZpbmQgYSBmaWxlIHdlIGtub3cgaXQncyBhbiBpbnZhbGlkIHdyaXRlLlxuICAgIGNvbnN0IGZyYWdtZW50cyA9IHNwbGl0KHBhdGgpO1xuICAgIGxldCBjdXJyOiBQYXRoID0gbm9ybWFsaXplKCcvJyk7XG4gICAgZm9yIChjb25zdCBmciBvZiBmcmFnbWVudHMpIHtcbiAgICAgIGN1cnIgPSBqb2luKGN1cnIsIGZyKTtcbiAgICAgIGNvbnN0IG1heWJlU3RhdHMgPSB0aGlzLl9jYWNoZS5nZXQoZnIpO1xuICAgICAgaWYgKG1heWJlU3RhdHMpIHtcbiAgICAgICAgaWYgKG1heWJlU3RhdHMuaXNGaWxlKCkpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgUGF0aElzRmlsZUV4Y2VwdGlvbihjdXJyKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhpcy5fY2FjaGUuc2V0KGN1cnIsIHRoaXMuX25ld0RpclN0YXRzKCkpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENyZWF0ZSB0aGUgc3RhdHMuXG4gICAgY29uc3Qgc3RhdHM6IFN0YXRzPFNpbXBsZU1lbW9yeUhvc3RTdGF0cz4gPSB0aGlzLl9uZXdGaWxlU3RhdHMoY29udGVudCwgb2xkKTtcbiAgICB0aGlzLl9jYWNoZS5zZXQocGF0aCwgc3RhdHMpO1xuICAgIHRoaXMuX3VwZGF0ZVdhdGNoZXJzKHBhdGgsIG9sZCA/IEhvc3RXYXRjaEV2ZW50VHlwZS5DaGFuZ2VkIDogSG9zdFdhdGNoRXZlbnRUeXBlLkNyZWF0ZWQpO1xuICB9XG4gIHByb3RlY3RlZCBfcmVhZChwYXRoOiBQYXRoKTogRmlsZUJ1ZmZlciB7XG4gICAgcGF0aCA9IHRoaXMuX3RvQWJzb2x1dGUocGF0aCk7XG4gICAgY29uc3QgbWF5YmVTdGF0cyA9IHRoaXMuX2NhY2hlLmdldChwYXRoKTtcbiAgICBpZiAoIW1heWJlU3RhdHMpIHtcbiAgICAgIHRocm93IG5ldyBGaWxlRG9lc05vdEV4aXN0RXhjZXB0aW9uKHBhdGgpO1xuICAgIH0gZWxzZSBpZiAobWF5YmVTdGF0cy5pc0RpcmVjdG9yeSgpKSB7XG4gICAgICB0aHJvdyBuZXcgUGF0aElzRGlyZWN0b3J5RXhjZXB0aW9uKHBhdGgpO1xuICAgIH0gZWxzZSBpZiAoIW1heWJlU3RhdHMuY29udGVudCkge1xuICAgICAgdGhyb3cgbmV3IFBhdGhJc0RpcmVjdG9yeUV4Y2VwdGlvbihwYXRoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIG1heWJlU3RhdHMuY29udGVudDtcbiAgICB9XG4gIH1cbiAgcHJvdGVjdGVkIF9kZWxldGUocGF0aDogUGF0aCk6IHZvaWQge1xuICAgIHBhdGggPSB0aGlzLl90b0Fic29sdXRlKHBhdGgpO1xuICAgIGlmICh0aGlzLl9pc0RpcmVjdG9yeShwYXRoKSkge1xuICAgICAgZm9yIChjb25zdCBbY2FjaGVQYXRoXSBvZiB0aGlzLl9jYWNoZS5lbnRyaWVzKCkpIHtcbiAgICAgICAgaWYgKGNhY2hlUGF0aC5zdGFydHNXaXRoKHBhdGggKyBOb3JtYWxpemVkU2VwKSB8fCBjYWNoZVBhdGggPT09IHBhdGgpIHtcbiAgICAgICAgICB0aGlzLl9jYWNoZS5kZWxldGUoY2FjaGVQYXRoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLl9jYWNoZS5kZWxldGUocGF0aCk7XG4gICAgfVxuICAgIHRoaXMuX3VwZGF0ZVdhdGNoZXJzKHBhdGgsIEhvc3RXYXRjaEV2ZW50VHlwZS5EZWxldGVkKTtcbiAgfVxuICBwcm90ZWN0ZWQgX3JlbmFtZShmcm9tOiBQYXRoLCB0bzogUGF0aCk6IHZvaWQge1xuICAgIGZyb20gPSB0aGlzLl90b0Fic29sdXRlKGZyb20pO1xuICAgIHRvID0gdGhpcy5fdG9BYnNvbHV0ZSh0byk7XG4gICAgaWYgKCF0aGlzLl9jYWNoZS5oYXMoZnJvbSkpIHtcbiAgICAgIHRocm93IG5ldyBGaWxlRG9lc05vdEV4aXN0RXhjZXB0aW9uKGZyb20pO1xuICAgIH0gZWxzZSBpZiAodGhpcy5fY2FjaGUuaGFzKHRvKSkge1xuICAgICAgdGhyb3cgbmV3IEZpbGVBbHJlYWR5RXhpc3RFeGNlcHRpb24odG8pO1xuICAgIH1cblxuICAgIGlmICh0aGlzLl9pc0RpcmVjdG9yeShmcm9tKSkge1xuICAgICAgZm9yIChjb25zdCBwYXRoIG9mIHRoaXMuX2NhY2hlLmtleXMoKSkge1xuICAgICAgICBpZiAocGF0aC5zdGFydHNXaXRoKGZyb20gKyBOb3JtYWxpemVkU2VwKSkge1xuICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSB0aGlzLl9jYWNoZS5nZXQocGF0aCk7XG4gICAgICAgICAgaWYgKGNvbnRlbnQpIHtcbiAgICAgICAgICAgIC8vIFdlIGRvbid0IG5lZWQgdG8gY2xvbmUgb3IgZXh0cmFjdCB0aGUgY29udGVudCwgc2luY2Ugd2UncmUgbW92aW5nIGZpbGVzLlxuICAgICAgICAgICAgdGhpcy5fY2FjaGUuc2V0KGpvaW4odG8sIE5vcm1hbGl6ZWRTZXAsIHBhdGguc2xpY2UoZnJvbS5sZW5ndGgpKSwgY29udGVudCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IGNvbnRlbnQgPSB0aGlzLl9jYWNoZS5nZXQoZnJvbSk7XG4gICAgICBpZiAoY29udGVudCkge1xuICAgICAgICBjb25zdCBmcmFnbWVudHMgPSBzcGxpdCh0byk7XG4gICAgICAgIGNvbnN0IG5ld0RpcmVjdG9yaWVzOiBQYXRoW10gPSBbXTtcbiAgICAgICAgbGV0IGN1cnI6IFBhdGggPSBub3JtYWxpemUoJy8nKTtcbiAgICAgICAgZm9yIChjb25zdCBmciBvZiBmcmFnbWVudHMpIHtcbiAgICAgICAgICBjdXJyID0gam9pbihjdXJyLCBmcik7XG4gICAgICAgICAgY29uc3QgbWF5YmVTdGF0cyA9IHRoaXMuX2NhY2hlLmdldChmcik7XG4gICAgICAgICAgaWYgKG1heWJlU3RhdHMpIHtcbiAgICAgICAgICAgIGlmIChtYXliZVN0YXRzLmlzRmlsZSgpKSB7XG4gICAgICAgICAgICAgIHRocm93IG5ldyBQYXRoSXNGaWxlRXhjZXB0aW9uKGN1cnIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBuZXdEaXJlY3Rvcmllcy5wdXNoKGN1cnIpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBmb3IgKGNvbnN0IG5ld0RpcmVjdG9yeSBvZiBuZXdEaXJlY3Rvcmllcykge1xuICAgICAgICAgIHRoaXMuX2NhY2hlLnNldChuZXdEaXJlY3RvcnksIHRoaXMuX25ld0RpclN0YXRzKCkpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuX2NhY2hlLmRlbGV0ZShmcm9tKTtcbiAgICAgICAgdGhpcy5fY2FjaGUuc2V0KHRvLCBjb250ZW50KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0aGlzLl91cGRhdGVXYXRjaGVycyhmcm9tLCBIb3N0V2F0Y2hFdmVudFR5cGUuUmVuYW1lZCk7XG4gIH1cblxuICBwcm90ZWN0ZWQgX2xpc3QocGF0aDogUGF0aCk6IFBhdGhGcmFnbWVudFtdIHtcbiAgICBwYXRoID0gdGhpcy5fdG9BYnNvbHV0ZShwYXRoKTtcbiAgICBpZiAodGhpcy5faXNGaWxlKHBhdGgpKSB7XG4gICAgICB0aHJvdyBuZXcgUGF0aElzRmlsZUV4Y2VwdGlvbihwYXRoKTtcbiAgICB9XG5cbiAgICBjb25zdCBmcmFnbWVudHMgPSBzcGxpdChwYXRoKTtcbiAgICBjb25zdCByZXN1bHQgPSBuZXcgU2V0PFBhdGhGcmFnbWVudD4oKTtcbiAgICBpZiAocGF0aCAhPT0gTm9ybWFsaXplZFJvb3QpIHtcbiAgICAgIGZvciAoY29uc3QgcCBvZiB0aGlzLl9jYWNoZS5rZXlzKCkpIHtcbiAgICAgICAgaWYgKHAuc3RhcnRzV2l0aChwYXRoICsgTm9ybWFsaXplZFNlcCkpIHtcbiAgICAgICAgICByZXN1bHQuYWRkKHNwbGl0KHApW2ZyYWdtZW50cy5sZW5ndGhdKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBmb3IgKGNvbnN0IHAgb2YgdGhpcy5fY2FjaGUua2V5cygpKSB7XG4gICAgICAgIGlmIChwLnN0YXJ0c1dpdGgoTm9ybWFsaXplZFNlcCkgJiYgcCAhPT0gTm9ybWFsaXplZFJvb3QpIHtcbiAgICAgICAgICByZXN1bHQuYWRkKHNwbGl0KHApWzFdKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBbLi4ucmVzdWx0XTtcbiAgfVxuXG4gIHByb3RlY3RlZCBfZXhpc3RzKHBhdGg6IFBhdGgpOiBib29sZWFuIHtcbiAgICByZXR1cm4gISF0aGlzLl9jYWNoZS5nZXQodGhpcy5fdG9BYnNvbHV0ZShwYXRoKSk7XG4gIH1cbiAgcHJvdGVjdGVkIF9pc0RpcmVjdG9yeShwYXRoOiBQYXRoKTogYm9vbGVhbiB7XG4gICAgY29uc3QgbWF5YmVTdGF0cyA9IHRoaXMuX2NhY2hlLmdldCh0aGlzLl90b0Fic29sdXRlKHBhdGgpKTtcblxuICAgIHJldHVybiBtYXliZVN0YXRzID8gbWF5YmVTdGF0cy5pc0RpcmVjdG9yeSgpIDogZmFsc2U7XG4gIH1cbiAgcHJvdGVjdGVkIF9pc0ZpbGUocGF0aDogUGF0aCk6IGJvb2xlYW4ge1xuICAgIGNvbnN0IG1heWJlU3RhdHMgPSB0aGlzLl9jYWNoZS5nZXQodGhpcy5fdG9BYnNvbHV0ZShwYXRoKSk7XG5cbiAgICByZXR1cm4gbWF5YmVTdGF0cyA/IG1heWJlU3RhdHMuaXNGaWxlKCkgOiBmYWxzZTtcbiAgfVxuXG4gIHByb3RlY3RlZCBfc3RhdChwYXRoOiBQYXRoKTogU3RhdHM8U2ltcGxlTWVtb3J5SG9zdFN0YXRzPiB8IG51bGwge1xuICAgIGNvbnN0IG1heWJlU3RhdHMgPSB0aGlzLl9jYWNoZS5nZXQodGhpcy5fdG9BYnNvbHV0ZShwYXRoKSk7XG5cbiAgICBpZiAoIW1heWJlU3RhdHMpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gbWF5YmVTdGF0cztcbiAgICB9XG4gIH1cblxuICBwcm90ZWN0ZWQgX3dhdGNoKHBhdGg6IFBhdGgsIG9wdGlvbnM/OiBIb3N0V2F0Y2hPcHRpb25zKTogT2JzZXJ2YWJsZTxIb3N0V2F0Y2hFdmVudD4ge1xuICAgIHBhdGggPSB0aGlzLl90b0Fic29sdXRlKHBhdGgpO1xuXG4gICAgY29uc3Qgc3ViamVjdCA9IG5ldyBTdWJqZWN0PEhvc3RXYXRjaEV2ZW50PigpO1xuICAgIGxldCBtYXliZVdhdGNoZXJBcnJheSA9IHRoaXMuX3dhdGNoZXJzLmdldChwYXRoKTtcbiAgICBpZiAoIW1heWJlV2F0Y2hlckFycmF5KSB7XG4gICAgICBtYXliZVdhdGNoZXJBcnJheSA9IFtdO1xuICAgICAgdGhpcy5fd2F0Y2hlcnMuc2V0KHBhdGgsIG1heWJlV2F0Y2hlckFycmF5KTtcbiAgICB9XG5cbiAgICBtYXliZVdhdGNoZXJBcnJheS5wdXNoKFtvcHRpb25zIHx8IHt9LCBzdWJqZWN0XSk7XG5cbiAgICByZXR1cm4gc3ViamVjdC5hc09ic2VydmFibGUoKTtcbiAgfVxuXG4gIHdyaXRlKHBhdGg6IFBhdGgsIGNvbnRlbnQ6IEZpbGVCdWZmZXIpOiBPYnNlcnZhYmxlPHZvaWQ+IHtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGU8dm9pZD4oKG9icykgPT4ge1xuICAgICAgdGhpcy5fd3JpdGUocGF0aCwgY29udGVudCk7XG4gICAgICBvYnMubmV4dCgpO1xuICAgICAgb2JzLmNvbXBsZXRlKCk7XG4gICAgfSk7XG4gIH1cblxuICByZWFkKHBhdGg6IFBhdGgpOiBPYnNlcnZhYmxlPEZpbGVCdWZmZXI+IHtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGU8RmlsZUJ1ZmZlcj4oKG9icykgPT4ge1xuICAgICAgY29uc3QgY29udGVudCA9IHRoaXMuX3JlYWQocGF0aCk7XG4gICAgICBvYnMubmV4dChjb250ZW50KTtcbiAgICAgIG9icy5jb21wbGV0ZSgpO1xuICAgIH0pO1xuICB9XG5cbiAgZGVsZXRlKHBhdGg6IFBhdGgpOiBPYnNlcnZhYmxlPHZvaWQ+IHtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGU8dm9pZD4oKG9icykgPT4ge1xuICAgICAgdGhpcy5fZGVsZXRlKHBhdGgpO1xuICAgICAgb2JzLm5leHQoKTtcbiAgICAgIG9icy5jb21wbGV0ZSgpO1xuICAgIH0pO1xuICB9XG5cbiAgcmVuYW1lKGZyb206IFBhdGgsIHRvOiBQYXRoKTogT2JzZXJ2YWJsZTx2b2lkPiB7XG4gICAgcmV0dXJuIG5ldyBPYnNlcnZhYmxlPHZvaWQ+KChvYnMpID0+IHtcbiAgICAgIHRoaXMuX3JlbmFtZShmcm9tLCB0byk7XG4gICAgICBvYnMubmV4dCgpO1xuICAgICAgb2JzLmNvbXBsZXRlKCk7XG4gICAgfSk7XG4gIH1cblxuICBsaXN0KHBhdGg6IFBhdGgpOiBPYnNlcnZhYmxlPFBhdGhGcmFnbWVudFtdPiB7XG4gICAgcmV0dXJuIG5ldyBPYnNlcnZhYmxlPFBhdGhGcmFnbWVudFtdPigob2JzKSA9PiB7XG4gICAgICBvYnMubmV4dCh0aGlzLl9saXN0KHBhdGgpKTtcbiAgICAgIG9icy5jb21wbGV0ZSgpO1xuICAgIH0pO1xuICB9XG5cbiAgZXhpc3RzKHBhdGg6IFBhdGgpOiBPYnNlcnZhYmxlPGJvb2xlYW4+IHtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGU8Ym9vbGVhbj4oKG9icykgPT4ge1xuICAgICAgb2JzLm5leHQodGhpcy5fZXhpc3RzKHBhdGgpKTtcbiAgICAgIG9icy5jb21wbGV0ZSgpO1xuICAgIH0pO1xuICB9XG5cbiAgaXNEaXJlY3RvcnkocGF0aDogUGF0aCk6IE9ic2VydmFibGU8Ym9vbGVhbj4ge1xuICAgIHJldHVybiBuZXcgT2JzZXJ2YWJsZTxib29sZWFuPigob2JzKSA9PiB7XG4gICAgICBvYnMubmV4dCh0aGlzLl9pc0RpcmVjdG9yeShwYXRoKSk7XG4gICAgICBvYnMuY29tcGxldGUoKTtcbiAgICB9KTtcbiAgfVxuXG4gIGlzRmlsZShwYXRoOiBQYXRoKTogT2JzZXJ2YWJsZTxib29sZWFuPiB7XG4gICAgcmV0dXJuIG5ldyBPYnNlcnZhYmxlPGJvb2xlYW4+KChvYnMpID0+IHtcbiAgICAgIG9icy5uZXh0KHRoaXMuX2lzRmlsZShwYXRoKSk7XG4gICAgICBvYnMuY29tcGxldGUoKTtcbiAgICB9KTtcbiAgfVxuXG4gIC8vIFNvbWUgaG9zdHMgbWF5IG5vdCBzdXBwb3J0IHN0YXQuXG4gIHN0YXQocGF0aDogUGF0aCk6IE9ic2VydmFibGU8U3RhdHM8e30+IHwgbnVsbD4gfCBudWxsIHtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGU8U3RhdHM8e30+IHwgbnVsbD4oKG9icykgPT4ge1xuICAgICAgb2JzLm5leHQodGhpcy5fc3RhdChwYXRoKSk7XG4gICAgICBvYnMuY29tcGxldGUoKTtcbiAgICB9KTtcbiAgfVxuXG4gIHdhdGNoKHBhdGg6IFBhdGgsIG9wdGlvbnM/OiBIb3N0V2F0Y2hPcHRpb25zKTogT2JzZXJ2YWJsZTxIb3N0V2F0Y2hFdmVudD4gfCBudWxsIHtcbiAgICByZXR1cm4gdGhpcy5fd2F0Y2gocGF0aCwgb3B0aW9ucyk7XG4gIH1cblxuICByZXNldCgpOiB2b2lkIHtcbiAgICB0aGlzLl9jYWNoZS5jbGVhcigpO1xuICAgIHRoaXMuX3dhdGNoZXJzLmNsZWFyKCk7XG4gIH1cbn1cbiJdfQ==