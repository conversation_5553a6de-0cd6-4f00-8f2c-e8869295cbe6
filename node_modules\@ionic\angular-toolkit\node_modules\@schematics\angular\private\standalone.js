"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.findBootstrapApplicationCall = exports.addModuleImportToStandaloneBootstrap = exports.importsProvidersFrom = void 0;
const schematics_1 = require("@angular-devkit/schematics");
const typescript_1 = __importDefault(require("../third_party/github.com/Microsoft/TypeScript/lib/typescript"));
const ast_utils_1 = require("../utility/ast-utils");
const change_1 = require("../utility/change");
/**
 * Checks whether the providers from a module are being imported in a `bootstrapApplication` call.
 * @param tree File tree of the project.
 * @param filePath Path of the file in which to check.
 * @param className Class name of the module to search for.
 */
function importsProvidersFrom(tree, filePath, className) {
    const sourceFile = typescript_1.default.createSourceFile(filePath, tree.readText(filePath), typescript_1.default.ScriptTarget.Latest, true);
    const bootstrapCall = findBootstrapApplicationCall(sourceFile);
    const importProvidersFromCall = bootstrapCall ? findImportProvidersFromCall(bootstrapCall) : null;
    return (!!importProvidersFromCall &&
        importProvidersFromCall.arguments.some((arg) => typescript_1.default.isIdentifier(arg) && arg.text === className));
}
exports.importsProvidersFrom = importsProvidersFrom;
/**
 * Adds an `importProvidersFrom` call to the `bootstrapApplication` call.
 * @param tree File tree of the project.
 * @param filePath Path to the file that should be updated.
 * @param moduleName Name of the module that should be imported.
 * @param modulePath Path from which to import the module.
 */
function addModuleImportToStandaloneBootstrap(tree, filePath, moduleName, modulePath) {
    const sourceFile = typescript_1.default.createSourceFile(filePath, tree.readText(filePath), typescript_1.default.ScriptTarget.Latest, true);
    const bootstrapCall = findBootstrapApplicationCall(sourceFile);
    if (!bootstrapCall) {
        throw new schematics_1.SchematicsException(`Could not find bootstrapApplication call in ${filePath}`);
    }
    const recorder = tree.beginUpdate(filePath);
    const importCall = findImportProvidersFromCall(bootstrapCall);
    const printer = typescript_1.default.createPrinter();
    const sourceText = sourceFile.getText();
    // Add imports to the module being added and `importProvidersFrom`. We don't
    // have to worry about duplicates, because `insertImport` handles them.
    [
        (0, ast_utils_1.insertImport)(sourceFile, sourceText, moduleName, modulePath),
        (0, ast_utils_1.insertImport)(sourceFile, sourceText, 'importProvidersFrom', '@angular/core'),
    ].forEach((change) => {
        if (change instanceof change_1.InsertChange) {
            recorder.insertLeft(change.pos, change.toAdd);
        }
    });
    // If there is an `importProvidersFrom` call already, reuse it.
    if (importCall) {
        recorder.insertRight(importCall.arguments[importCall.arguments.length - 1].getEnd(), `, ${moduleName}`);
    }
    else if (bootstrapCall.arguments.length === 1) {
        // Otherwise if there is no options parameter to `bootstrapApplication`,
        // create an object literal with a `providers` array and the import.
        const newCall = typescript_1.default.factory.updateCallExpression(bootstrapCall, bootstrapCall.expression, bootstrapCall.typeArguments, [
            ...bootstrapCall.arguments,
            typescript_1.default.factory.createObjectLiteralExpression([createProvidersAssignment(moduleName)], true),
        ]);
        recorder.remove(bootstrapCall.getStart(), bootstrapCall.getWidth());
        recorder.insertRight(bootstrapCall.getStart(), printer.printNode(typescript_1.default.EmitHint.Unspecified, newCall, sourceFile));
    }
    else {
        const providersLiteral = findProvidersLiteral(bootstrapCall);
        if (providersLiteral) {
            // If there's a `providers` array, add the import to it.
            const newProvidersLiteral = typescript_1.default.factory.updateArrayLiteralExpression(providersLiteral, [
                ...providersLiteral.elements,
                createImportProvidersFromCall(moduleName),
            ]);
            recorder.remove(providersLiteral.getStart(), providersLiteral.getWidth());
            recorder.insertRight(providersLiteral.getStart(), printer.printNode(typescript_1.default.EmitHint.Unspecified, newProvidersLiteral, sourceFile));
        }
        else {
            // Otherwise add a `providers` array to the existing object literal.
            const optionsLiteral = bootstrapCall.arguments[1];
            const newOptionsLiteral = typescript_1.default.factory.updateObjectLiteralExpression(optionsLiteral, [
                ...optionsLiteral.properties,
                createProvidersAssignment(moduleName),
            ]);
            recorder.remove(optionsLiteral.getStart(), optionsLiteral.getWidth());
            recorder.insertRight(optionsLiteral.getStart(), printer.printNode(typescript_1.default.EmitHint.Unspecified, newOptionsLiteral, sourceFile));
        }
    }
    tree.commitUpdate(recorder);
}
exports.addModuleImportToStandaloneBootstrap = addModuleImportToStandaloneBootstrap;
/** Finds the call to `bootstrapApplication` within a file. */
function findBootstrapApplicationCall(sourceFile) {
    const localName = findImportLocalName(sourceFile, 'bootstrapApplication', '@angular/platform-browser');
    return localName ? findCall(sourceFile, localName) : null;
}
exports.findBootstrapApplicationCall = findBootstrapApplicationCall;
/** Find a call to `importProvidersFrom` within a `bootstrapApplication` call. */
function findImportProvidersFromCall(bootstrapCall) {
    const providersLiteral = findProvidersLiteral(bootstrapCall);
    const importProvidersName = findImportLocalName(bootstrapCall.getSourceFile(), 'importProvidersFrom', '@angular/core');
    if (providersLiteral && importProvidersName) {
        for (const element of providersLiteral.elements) {
            // Look for an array element that calls the `importProvidersFrom` function.
            if (typescript_1.default.isCallExpression(element) &&
                typescript_1.default.isIdentifier(element.expression) &&
                element.expression.text === importProvidersName) {
                return element;
            }
        }
    }
    return null;
}
/** Finds the `providers` array literal within a `bootstrapApplication` call. */
function findProvidersLiteral(bootstrapCall) {
    // The imports have to be in the second argument of
    // the function which has to be an object literal.
    if (bootstrapCall.arguments.length > 1 &&
        typescript_1.default.isObjectLiteralExpression(bootstrapCall.arguments[1])) {
        for (const prop of bootstrapCall.arguments[1].properties) {
            if (typescript_1.default.isPropertyAssignment(prop) &&
                typescript_1.default.isIdentifier(prop.name) &&
                prop.name.text === 'providers' &&
                typescript_1.default.isArrayLiteralExpression(prop.initializer)) {
                return prop.initializer;
            }
        }
    }
    return null;
}
/**
 * Finds the local name of an imported symbol. Could be the symbol name itself or its alias.
 * @param sourceFile File within which to search for the import.
 * @param name Actual name of the import, not its local alias.
 * @param moduleName Name of the module from which the symbol is imported.
 */
function findImportLocalName(sourceFile, name, moduleName) {
    for (const node of sourceFile.statements) {
        // Only look for top-level imports.
        if (!typescript_1.default.isImportDeclaration(node) ||
            !typescript_1.default.isStringLiteral(node.moduleSpecifier) ||
            node.moduleSpecifier.text !== moduleName) {
            continue;
        }
        // Filter out imports that don't have the right shape.
        if (!node.importClause ||
            !node.importClause.namedBindings ||
            !typescript_1.default.isNamedImports(node.importClause.namedBindings)) {
            continue;
        }
        // Look through the elements of the declaration for the specific import.
        for (const element of node.importClause.namedBindings.elements) {
            if ((element.propertyName || element.name).text === name) {
                // The local name is always in `name`.
                return element.name.text;
            }
        }
    }
    return null;
}
/**
 * Finds a call to a function with a specific name.
 * @param rootNode Node from which to start searching.
 * @param name Name of the function to search for.
 */
function findCall(rootNode, name) {
    let result = null;
    rootNode.forEachChild(function walk(node) {
        if (typescript_1.default.isCallExpression(node) &&
            typescript_1.default.isIdentifier(node.expression) &&
            node.expression.text === name) {
            result = node;
        }
        if (!result) {
            node.forEachChild(walk);
        }
    });
    return result;
}
/** Creates an `importProvidersFrom({{moduleName}})` call. */
function createImportProvidersFromCall(moduleName) {
    return typescript_1.default.factory.createCallChain(typescript_1.default.factory.createIdentifier('importProvidersFrom'), undefined, undefined, [typescript_1.default.factory.createIdentifier(moduleName)]);
}
/** Creates a `providers: [importProvidersFrom({{moduleName}})]` property assignment. */
function createProvidersAssignment(moduleName) {
    return typescript_1.default.factory.createPropertyAssignment('providers', typescript_1.default.factory.createArrayLiteralExpression([createImportProvidersFromCall(moduleName)]));
}
//# sourceMappingURL=data:application/json;base64,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