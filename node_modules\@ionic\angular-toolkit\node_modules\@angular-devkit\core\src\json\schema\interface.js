"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;base64,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