"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7720],{7720:($,S,l)=>{l.r(S),l.d(S,{ion_app:()=>D,ion_buttons:()=>B,ion_content:()=>L,ion_footer:()=>H,ion_header:()=>F,ion_router_outlet:()=>U,ion_title:()=>X,ion_toolbar:()=>K});var h=l(467),r=l(4363),T=l(3113),I=l(4929),c=l(611),m=l(5638),A=l(5083),p=l(333),b=l(2885),g=l(4731),k=l(3351),w=l(5938),C=l(7838),Z=l(7555);l(8476),l(4379),l(8438);const D=class{constructor(t){(0,r.r)(this,t)}componentDidLoad(){var t=this;G((0,h.A)(function*(){const e=(0,c.a)(window,"hybrid");if(c.c.getBoolean("_testing")||l.e(2113).then(l.bind(l,2113)).then(i=>i.startTapClick(c.c)),c.c.getBoolean("statusTap",e)&&l.e(604).then(l.bind(l,604)).then(i=>i.startStatusTap()),c.c.getBoolean("inputShims",V())){const i=(0,c.a)(window,"ios")?"ios":"android";l.e(8729).then(l.bind(l,8729)).then(s=>s.startInputShims(c.c,i))}const o=yield Promise.resolve().then(l.bind(l,3113)),n=e||(0,T.shouldUseCloseWatcher)();c.c.getBoolean("hardwareBackButton",n)?o.startHardwareBackButton():((0,T.shouldUseCloseWatcher)()&&(0,I.p)("experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),o.blockHardwareBackButton()),typeof window<"u"&&l.e(2076).then(l.bind(l,1622)).then(i=>i.startKeyboardAssist(window)),l.e(2076).then(l.bind(l,3126)).then(i=>t.focusVisible=i.startFocusVisible())}))}setFocus(t){var e=this;return(0,h.A)(function*(){e.focusVisible&&e.focusVisible.setFocus(t)})()}render(){const t=(0,c.b)(this);return(0,r.h)(r.H,{key:"6d7c57453b4be454690e8f1a0721f1e3da8f92aa",class:{[t]:!0,"ion-page":!0,"force-statusbar-padding":c.c.getBoolean("_forceStatusbarPadding")}})}get el(){return(0,r.f)(this)}},V=()=>!!((0,c.a)(window,"ios")&&(0,c.a)(window,"mobile")||(0,c.a)(window,"android")&&(0,c.a)(window,"mobileweb")),G=t=>{"requestIdleCallback"in window?window.requestIdleCallback(t):setTimeout(t,32)};D.style="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}";const B=class{constructor(t){(0,r.r)(this,t),this.collapse=!1}render(){const t=(0,c.b)(this);return(0,r.h)(r.H,{key:"2929fd8c4469bab2953c23d47f601706acb104f1",class:{[t]:!0,"buttons-collapse":this.collapse}})}};B.style={ios:".sc-ion-buttons-ios-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-ios-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-ios-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:5px;--padding-end:5px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-ios-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-ios-s ion-button:not(.button-round){--border-radius:4px}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button{--color:initial;--border-color:initial;--background-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-solid,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-solid{--background:var(--ion-color-contrast);--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12;--background-hover:var(--ion-color-base);--background-hover-opacity:0.45;--color:var(--ion-color-base);--color-focused:var(--ion-color-base)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-clear,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-clear{--color-activated:var(--ion-color-contrast);--color-focused:var(--ion-color-contrast)}.sc-ion-buttons-ios-h.ion-color.sc-ion-buttons-ios-s .button-outline,.ion-color .sc-ion-buttons-ios-h.sc-ion-buttons-ios-s .button-outline{--color-activated:var(--ion-color-base);--color-focused:var(--ion-color-contrast);--background-activated:var(--ion-color-contrast)}.sc-ion-buttons-ios-s .button-clear,.sc-ion-buttons-ios-s .button-outline{--background-activated:transparent;--background-focused:currentColor;--background-hover:transparent}.sc-ion-buttons-ios-s .button-solid:not(.ion-color){--background-focused:#000;--background-focused-opacity:.12;--background-activated:#000;--background-activated-opacity:.12}.sc-ion-buttons-ios-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.41em;line-height:0.67}.sc-ion-buttons-ios-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.65em;line-height:0.67}",md:".sc-ion-buttons-md-h{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:99}.sc-ion-buttons-md-s ion-button{--padding-top:0;--padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-buttons-md-s ion-button{--padding-top:3px;--padding-bottom:3px;--padding-start:8px;--padding-end:8px;--box-shadow:none;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;min-height:32px}.sc-ion-buttons-md-s .button-has-icon-only{--padding-top:0;--padding-bottom:0}.sc-ion-buttons-md-s ion-button:not(.button-round){--border-radius:2px}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button{--color:initial;--color-focused:var(--ion-color-contrast);--color-hover:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-contrast);--background-hover:var(--ion-color-contrast)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-solid,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-solid{--background:var(--ion-color-contrast);--background-activated:transparent;--background-focused:var(--ion-color-shade);--background-hover:var(--ion-color-base);--color:var(--ion-color-base);--color-focused:var(--ion-color-base);--color-hover:var(--ion-color-base)}.sc-ion-buttons-md-h.ion-color.sc-ion-buttons-md-s .button-outline,.ion-color .sc-ion-buttons-md-h.sc-ion-buttons-md-s .button-outline{--border-color:var(--ion-color-contrast)}.sc-ion-buttons-md-s .button-has-icon-only.button-clear{--padding-top:12px;--padding-end:12px;--padding-bottom:12px;--padding-start:12px;--border-radius:50%;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:3rem;height:3rem}.sc-ion-buttons-md-s .button{--background-hover:currentColor}.sc-ion-buttons-md-s .button-solid{--color:var(--ion-toolbar-background, var(--ion-background-color, #fff));--background:var(--ion-toolbar-color, var(--ion-text-color, #424242));--background-activated:transparent;--background-focused:currentColor}.sc-ion-buttons-md-s .button-outline{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--border-color:currentColor}.sc-ion-buttons-md-s .button-clear{--color:initial;--background:transparent;--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor}.sc-ion-buttons-md-s ion-icon[slot=start]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=end]{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-margin-start:0.4em;margin-inline-start:0.4em;font-size:1.4em}.sc-ion-buttons-md-s ion-icon[slot=icon-only]{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;font-size:1.8em}"};const L=class{constructor(t){(0,r.r)(this,t),this.ionScrollStart=(0,r.d)(this,"ionScrollStart",7),this.ionScroll=(0,r.d)(this,"ionScroll",7),this.ionScrollEnd=(0,r.d)(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.color=void 0,this.fullscreen=!1,this.forceOverscroll=void 0,this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}connectedCallback(){if(this.isMainContent=null===this.el.closest("ion-menu, ion-popover, ion-modal"),(0,m.m)(this.el)){const t=this.tabsElement=this.el.closest("ion-tabs");null!==t&&(this.tabsLoadCallback=()=>this.resize(),t.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),(0,m.m)(this.el)){const{tabsElement:t,tabsLoadCallback:e}=this;null!==t&&void 0!==e&&t.removeEventListener("ionTabBarLoaded",e),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{null!==this.el.offsetParent&&this.resize()},100)}shouldForceOverscroll(){const{forceOverscroll:t}=this,e=(0,c.b)(this);return void 0===t?"ios"===e&&(0,c.a)("ios"):t}resize(){this.fullscreen?(0,r.e)(()=>this.readDimensions()):(0!==this.cTop||0!==this.cBottom)&&(this.cTop=this.cBottom=0,(0,r.i)(this))}readDimensions(){const t=ot(this.el),e=Math.max(this.el.offsetTop,0),o=Math.max(t.offsetHeight-e-this.el.offsetHeight,0);(e!==this.cTop||o!==this.cBottom)&&(this.cTop=e,this.cBottom=o,(0,r.i)(this))}onScroll(t){const e=Date.now(),o=!this.isScrolling;this.lastScroll=e,o&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,(0,r.e)(n=>{this.queued=!1,this.detail.event=t,et(this.detail,this.scrollEl,n,o),this.ionScroll.emit(this.detail)}))}getScrollElement(){var t=this;return(0,h.A)(function*(){return t.scrollEl||(yield new Promise(e=>(0,m.c)(t.el,e))),Promise.resolve(t.scrollEl)})()}getBackgroundElement(){var t=this;return(0,h.A)(function*(){return t.backgroundContentEl||(yield new Promise(e=>(0,m.c)(t.el,e))),Promise.resolve(t.backgroundContentEl)})()}scrollToTop(t=0){return this.scrollToPoint(void 0,0,t)}scrollToBottom(){var t=this;return(0,h.A)(function*(e=0){const o=yield t.getScrollElement();return t.scrollToPoint(void 0,o.scrollHeight-o.clientHeight,e)}).apply(this,arguments)}scrollByPoint(t,e,o){var n=this;return(0,h.A)(function*(){const i=yield n.getScrollElement();return n.scrollToPoint(t+i.scrollLeft,e+i.scrollTop,o)})()}scrollToPoint(t,e){var o=this;return(0,h.A)(function*(n,i,s=0){const a=yield o.getScrollElement();if(s<32)return null!=i&&(a.scrollTop=i),void(null!=n&&(a.scrollLeft=n));let d,f=0;const u=new Promise(y=>d=y),v=a.scrollTop,x=a.scrollLeft,z=null!=i?i-v:0,P=null!=n?n-x:0,Y=y=>{const mt=Math.min(1,(y-f)/s)-1,O=Math.pow(mt,3)+1;0!==z&&(a.scrollTop=Math.floor(O*z+v)),0!==P&&(a.scrollLeft=Math.floor(O*P+x)),O<1?requestAnimationFrame(Y):d()};return requestAnimationFrame(y=>{f=y,Y(y)}),u}).apply(this,arguments)}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){const{isMainContent:t,scrollX:e,scrollY:o,el:n}=this,i=(0,A.i)(n)?"rtl":"ltr",s=(0,c.b)(this),a=this.shouldForceOverscroll(),d="ios"===s,f=t?"main":"div";return this.resize(),(0,r.h)(r.H,{key:"e13815c0e6f6095150b112d3a1aaf2f509aa0d0b",class:(0,p.c)(this.color,{[s]:!0,"content-sizing":(0,p.h)("ion-popover",this.el),overscroll:a,[`content-${i}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},(0,r.h)("div",{key:"8006c4a10d8f7dc83c646246961d018a8097236e",ref:u=>this.backgroundContentEl=u,id:"background-content",part:"background"}),(0,r.h)(f,{key:"4dd2f58421493f7a4ca42f8f5d7b85cda8e320ea",class:{"inner-scroll":!0,"scroll-x":e,"scroll-y":o,overscroll:(e||o)&&a},ref:u=>this.scrollEl=u,onScroll:this.scrollEvents?u=>this.onScroll(u):void 0,part:"scroll"},(0,r.h)("slot",{key:"37904f8f1d8319156cd901feb21930ef674fe0f7"})),d?(0,r.h)("div",{class:"transition-effect"},(0,r.h)("div",{class:"transition-cover"}),(0,r.h)("div",{class:"transition-shadow"})):null,(0,r.h)("slot",{key:"8f696583903af0548d064dca1a6bae060e127485",name:"fixed"}))}get el(){return(0,r.f)(this)}},ot=t=>{const e=t.closest("ion-tabs");return e||(t.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content")||(t=>{var e;return t.parentElement?t.parentElement:null!==(e=t.parentNode)&&void 0!==e&&e.host?t.parentNode.host:null})(t))},et=(t,e,o,n)=>{const i=t.currentX,s=t.currentY,d=e.scrollLeft,f=e.scrollTop,u=o-t.currentTime;if(n&&(t.startTime=o,t.startX=d,t.startY=f,t.velocityX=t.velocityY=0),t.currentTime=o,t.currentX=t.scrollLeft=d,t.currentY=t.scrollTop=f,t.deltaX=d-t.startX,t.deltaY=f-t.startY,u>0&&u<100){const x=(f-s)/u;t.velocityX=(d-i)/u*.7+.3*t.velocityX,t.velocityY=.7*x+.3*t.velocityY}};L.style=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.outer-content){--background:var(--ion-color-step-50, #f2f2f2)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}';const _=(t,e)=>{(0,r.e)(()=>{const d=(0,m.l)(0,1-(t.scrollTop-(t.scrollHeight-t.clientHeight-10))/10,1);(0,r.w)(()=>{e.style.setProperty("--opacity-scale",d.toString())})})},H=class{constructor(t){var e=this;(0,r.r)(this,t),this.keyboardCtrl=null,this.checkCollapsibleFooter=()=>{if("ios"!==(0,c.b)(this))return;const{collapse:n}=this,i="fade"===n;if(this.destroyCollapsibleFooter(),i){const s=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),a=s?(0,b.a)(s):null;if(!a)return void(0,b.p)(this.el);this.setupFadeFooter(a)}},this.setupFadeFooter=function(){var o=(0,h.A)(function*(n){const i=e.scrollEl=yield(0,b.g)(n);e.contentScrollCallback=()=>{_(i,e.el)},i.addEventListener("scroll",e.contentScrollCallback),_(i,e.el)});return function(n){return o.apply(this,arguments)}}(),this.keyboardVisible=!1,this.collapse=void 0,this.translucent=!1}componentDidLoad(){this.checkCollapsibleFooter()}componentDidUpdate(){this.checkCollapsibleFooter()}connectedCallback(){var t=this;return(0,h.A)(function*(){t.keyboardCtrl=yield(0,g.c)(function(){var e=(0,h.A)(function*(o,n){!1===o&&void 0!==n&&(yield n),t.keyboardVisible=o});return function(o,n){return e.apply(this,arguments)}}())})()}disconnectedCallback(){this.keyboardCtrl&&this.keyboardCtrl.destroy()}destroyCollapsibleFooter(){this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0)}render(){const{translucent:t,collapse:e}=this,o=(0,c.b)(this),n=this.el.closest("ion-tabs"),i=n?.querySelector(":scope > ion-tab-bar");return(0,r.h)(r.H,{key:"dd8fa96901e8a09759a9621b6513f0492b3a6197",role:"contentinfo",class:{[o]:!0,[`footer-${o}`]:!0,"footer-translucent":t,[`footer-translucent-${o}`]:t,"footer-toolbar-padding":!(this.keyboardVisible||i&&"bottom"===i.slot),[`footer-collapse-${e}`]:void 0!==e}},"ios"===o&&t&&(0,r.h)("div",{key:"0fbb4ebf8e3951ff399f843dc11aab37fc48f8b7",class:"footer-background"}),(0,r.h)("slot",{key:"ecb14a65e3b6960670446c4428e3095b3231a3b0"}))}get el(){return(0,r.f)(this)}};H.style={ios:"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-ios ion-toolbar:first-of-type{--border-width:0.55px 0 0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.footer-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.footer-translucent-ios ion-toolbar{--opacity:.8}}.footer-ios.ion-no-border ion-toolbar:first-of-type{--border-width:0}.footer-collapse-fade ion-toolbar{--opacity-scale:inherit}",md:"ion-footer{display:block;position:relative;-ms-flex-order:1;order:1;width:100%;z-index:10}ion-footer.footer-toolbar-padding ion-toolbar:last-of-type{padding-bottom:var(--ion-safe-area-bottom, 0)}.footer-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.footer-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}"};const R=t=>{const e=document.querySelector(`${t}.ion-cloned-element`);if(null!==e)return e;const o=document.createElement(t);return o.classList.add("ion-cloned-element"),o.style.setProperty("display","none"),document.body.appendChild(o),o},W=t=>{if(!t)return;const e=t.querySelectorAll("ion-toolbar");return{el:t,toolbars:Array.from(e).map(o=>{const n=o.querySelector("ion-title");return{el:o,background:o.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:n,innerTitleEl:n?n.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(o.querySelectorAll("ion-buttons"))}})}},M=(t,e)=>{"fade"!==t.collapse&&(void 0===e?t.style.removeProperty("--opacity-scale"):t.style.setProperty("--opacity-scale",e.toString()))},E=(t,e=!0)=>{const o=t.el;e?(o.classList.remove("header-collapse-condense-inactive"),o.removeAttribute("aria-hidden")):(o.classList.add("header-collapse-condense-inactive"),o.setAttribute("aria-hidden","true"))},j=(t,e,o)=>{(0,r.e)(()=>{const n=t.scrollTop,i=e.clientHeight,s=o?o.clientHeight:0;if(null!==o&&n<s)return e.style.setProperty("--opacity-scale","0"),void t.style.setProperty("clip-path",`inset(${i}px 0px 0px 0px)`);const f=(0,m.l)(0,(n-s)/10,1);(0,r.w)(()=>{t.style.removeProperty("clip-path"),e.style.setProperty("--opacity-scale",f.toString())})})},F=class{constructor(t){var e=this;(0,r.r)(this,t),this.inheritedAttributes={},this.setupFadeHeader=function(){var o=(0,h.A)(function*(n,i){const s=e.scrollEl=yield(0,b.g)(n);e.contentScrollCallback=()=>{j(e.scrollEl,e.el,i)},s.addEventListener("scroll",e.contentScrollCallback),j(e.scrollEl,e.el,i)});return function(n,i){return o.apply(this,arguments)}}(),this.collapse=void 0,this.translucent=!1}componentWillLoad(){this.inheritedAttributes=(0,m.i)(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}checkCollapsibleHeader(){var t=this;return(0,h.A)(function*(){if("ios"!==(0,c.b)(t))return;const{collapse:o}=t,n="condense"===o,i="fade"===o;if(t.destroyCollapsibleHeader(),n){const s=t.el.closest("ion-app,ion-page,.ion-page,page-inner"),a=s?(0,b.a)(s):null;(0,r.w)(()=>{R("ion-title").size="large",R("ion-back-button")}),yield t.setupCondenseHeader(a,s)}else if(i){const s=t.el.closest("ion-app,ion-page,.ion-page,page-inner"),a=s?(0,b.a)(s):null;if(!a)return void(0,b.p)(t.el);const d=a.querySelector('ion-header[collapse="condense"]');yield t.setupFadeHeader(a,d)}})()}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}setupCondenseHeader(t,e){var o=this;return(0,h.A)(function*(){if(!t||!e)return void(0,b.p)(o.el);if(typeof IntersectionObserver>"u")return;o.scrollEl=yield(0,b.g)(t);const n=e.querySelectorAll("ion-header");if(o.collapsibleMainHeader=Array.from(n).find(d=>"condense"!==d.collapse),!o.collapsibleMainHeader)return;const i=W(o.collapsibleMainHeader),s=W(o.el);i&&s&&(E(i,!1),M(i.el,0),o.intersectionObserver=new IntersectionObserver(d=>{((t,e,o,n)=>{(0,r.w)(()=>{const i=n.scrollTop;((t,e,o)=>{if(!t[0].isIntersecting)return;const n=t[0].intersectionRatio>.9||o<=0?0:100*(1-t[0].intersectionRatio)/75;M(e.el,1===n?void 0:n)})(t,e,i);const s=t[0],a=s.intersectionRect,d=a.width*a.height,u=0===d&&0==s.rootBounds.width*s.rootBounds.height,v=Math.abs(a.left-s.boundingClientRect.left),x=Math.abs(a.right-s.boundingClientRect.right);u||d>0&&(v>=5||x>=5)||(s.isIntersecting?(E(e,!1),E(o)):(0===a.x&&0===a.y||0!==a.width&&0!==a.height)&&i>0&&(E(e),E(o,!1),M(e.el)))})})(d,i,s,o.scrollEl)},{root:t,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),o.intersectionObserver.observe(s.toolbars[s.toolbars.length-1].el),o.contentScrollCallback=()=>{((t,e,o)=>{(0,r.e)(()=>{const i=(0,m.l)(1,1+-t.scrollTop/500,1.1);null===o.querySelector("ion-refresher.refresher-native")&&(0,r.w)(()=>{((t=[],e=1,o=!1)=>{t.forEach(n=>{const i=n.ionTitleEl,s=n.innerTitleEl;!i||"large"!==i.size||(s.style.transition=o?"all 0.2s ease-in-out":"",s.style.transform=`scale3d(${e}, ${e}, 1)`)})})(e.toolbars,i)})})})(o.scrollEl,s,t)},o.scrollEl.addEventListener("scroll",o.contentScrollCallback),(0,r.w)(()=>{void 0!==o.collapsibleMainHeader&&o.collapsibleMainHeader.classList.add("header-collapse-main")}))})()}render(){const{translucent:t,inheritedAttributes:e}=this,o=(0,c.b)(this),n=this.collapse||"none",i=(0,p.h)("ion-menu",this.el)?"none":"banner";return(0,r.h)(r.H,Object.assign({key:"9fa0af97b605f9fe98b13361bc3d1289745c549f",role:i,class:{[o]:!0,[`header-${o}`]:!0,"header-translucent":this.translucent,[`header-collapse-${n}`]:!0,[`header-translucent-${o}`]:this.translucent}},e),"ios"===o&&t&&(0,r.h)("div",{key:"1a780d2625302f2465718e304bdd3794c89c9845",class:"header-background"}),(0,r.h)("slot",{key:"b2b8557b44be40c590bfcc362ac4350f9f8b889e"}))}get el(){return(0,r.f)(this)}};F.style={ios:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",md:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}"};const U=class{constructor(t){(0,r.r)(this,t),this.ionNavWillLoad=(0,r.d)(this,"ionNavWillLoad",7),this.ionNavWillChange=(0,r.d)(this,"ionNavWillChange",3),this.ionNavDidChange=(0,r.d)(this,"ionNavDidChange",3),this.lockController=(0,C.c)(),this.gestureOrAnimationInProgress=!1,this.mode=(0,c.b)(this),this.delegate=void 0,this.animated=!0,this.animation=void 0,this.swipeHandler=void 0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(void 0!==this.swipeHandler)}connectedCallback(){var t=this;return(0,h.A)(function*(){t.gesture=(yield l.e(2076).then(l.bind(l,6492))).createSwipeBackGesture(t.el,()=>!t.gestureOrAnimationInProgress&&!!t.swipeHandler&&t.swipeHandler.canStart(),()=>(t.gestureOrAnimationInProgress=!0,void(t.swipeHandler&&t.swipeHandler.onStart())),o=>{var n;return null===(n=t.ani)||void 0===n?void 0:n.progressStep(o)},(o,n,i)=>{if(t.ani){t.ani.onFinish(()=>{t.gestureOrAnimationInProgress=!1,t.swipeHandler&&t.swipeHandler.onEnd(o)},{oneTimeCallback:!0});let s=o?-.001:.001;o?s+=(0,k.g)([0,0],[.32,.72],[0,1],[1,1],n)[0]:(t.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),s+=(0,k.g)([0,0],[1,0],[.68,.28],[1,1],n)[0]),t.ani.progressEnd(o?1:0,s,i)}else t.gestureOrAnimationInProgress=!1}),t.swipeHandlerChanged()})()}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(t,e,o){var n=this;return(0,h.A)(function*(){const i=yield n.lockController.lock();let s=!1;try{s=yield n.transition(t,e,o)}catch(a){console.error(a)}return i(),s})()}setRouteId(t,e,o,n){var i=this;return(0,h.A)(function*(){return{changed:yield i.setRoot(t,e,{duration:"root"===o?0:void 0,direction:"back"===o?"back":"forward",animationBuilder:n}),element:i.activeEl}})()}getRouteId(){var t=this;return(0,h.A)(function*(){const e=t.activeEl;return e?{id:e.tagName,element:e,params:t.activeParams}:void 0})()}setRoot(t,e,o){var n=this;return(0,h.A)(function*(){if(n.activeComponent===t&&(0,m.s)(e,n.activeParams))return!1;const i=n.activeEl,s=yield(0,w.a)(n.delegate,n.el,t,["ion-page","ion-page-invisible"],e);return n.activeComponent=t,n.activeEl=s,n.activeParams=e,yield n.commit(s,i,o),yield(0,w.d)(n.delegate,i),!0})()}transition(t,e){var o=this;return(0,h.A)(function*(n,i,s={}){if(i===n)return!1;o.ionNavWillChange.emit();const{el:a,mode:d}=o,f=o.animated&&c.c.getBoolean("animated",!0),u=s.animationBuilder||o.animation||c.c.get("navAnimation");return yield(0,Z.t)(Object.assign(Object.assign({mode:d,animated:f,enteringEl:n,leavingEl:i,baseEl:a,deepWait:(0,m.m)(a),progressCallback:s.progressAnimation?v=>{void 0===v||o.gestureOrAnimationInProgress?o.ani=v:(o.gestureOrAnimationInProgress=!0,v.onFinish(()=>{o.gestureOrAnimationInProgress=!1,o.swipeHandler&&o.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),v.progressEnd(0,0,0))}:void 0},s),{animationBuilder:u})),o.ionNavDidChange.emit(),!0}).apply(this,arguments)}render(){return(0,r.h)("slot",{key:"0949db1bcfde67b462abe9cae72c7a7fd70ea678"})}get el(){return(0,r.f)(this)}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}};U.style=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}";const X=class{constructor(t){(0,r.r)(this,t),this.ionStyle=(0,r.d)(this,"ionStyle",7),this.color=void 0,this.size=void 0}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){const t=this.getSize();this.ionStyle.emit({[`title-${t}`]:!0})}getSize(){return void 0!==this.size?this.size:"default"}render(){const t=(0,c.b)(this),e=this.getSize();return(0,r.h)(r.H,{key:"6f43362b782ef7d340c241bb66f1469663c03cc1",class:(0,p.c)(this.color,{[t]:!0,[`title-${e}`]:!0,"title-rtl":"rtl"===document.dir})},(0,r.h)("div",{key:"9c3ff1a289e533ee3426b71ab5560fbea3529502",class:"toolbar-title"},(0,r.h)("slot",{key:"50d5cc5a1519ad58f1994d2f8c8f08f62baac1fe"})))}get el(){return(0,r.f)(this)}static get watchers(){return{size:["sizeChanged"]}}};X.style={ios:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}@supports (inset-inline-start: 0){:host{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host{left:0}:host-context([dir=rtl]){left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(:dir(rtl)){left:unset;right:unset;right:0}}}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",md:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}"};const K=class{constructor(t){(0,r.r)(this,t),this.childrenStyles=new Map,this.color=void 0}componentWillLoad(){const t=Array.from(this.el.querySelectorAll("ion-buttons")),e=t.find(i=>"start"===i.slot);e&&e.classList.add("buttons-first-slot");const o=t.reverse(),n=o.find(i=>"end"===i.slot)||o.find(i=>"primary"===i.slot)||o.find(i=>"secondary"===i.slot);n&&n.classList.add("buttons-last-slot")}childrenStyle(t){t.stopPropagation();const e=t.target.tagName,o=t.detail,n={},i=this.childrenStyles.get(e)||{};let s=!1;Object.keys(o).forEach(a=>{const d=`toolbar-${a}`,f=o[a];f!==i[d]&&(s=!0),f&&(n[d]=!0)}),s&&(this.childrenStyles.set(e,n),(0,r.i)(this))}render(){const t=(0,c.b)(this),e={};return this.childrenStyles.forEach(o=>{Object.assign(e,o)}),(0,r.h)(r.H,{key:"8907ed75fbb2b1dced55c481bba6363f1dca815b",class:Object.assign(Object.assign({},e),(0,p.c)(this.color,{[t]:!0,"in-toolbar":(0,p.h)("ion-toolbar",this.el)}))},(0,r.h)("div",{key:"6bfa09b08d6517f0d680f53b739854cecd631bc9",class:"toolbar-background"}),(0,r.h)("div",{key:"1531bd6dd9e0a5843309bba854b744c453037ad0",class:"toolbar-container"},(0,r.h)("slot",{key:"881b41697d386eae651b019128573f0fa432cd33",name:"start"}),(0,r.h)("slot",{key:"64a284e6eae5311ac3125dfadb4bb32bdba9d089",name:"secondary"}),(0,r.h)("div",{key:"c1f47503563b38084b27d7ba54f17ec478482b94",class:"toolbar-content"},(0,r.h)("slot",{key:"9a85acfba72252705619ae32acae9c14f81aa57d"})),(0,r.h)("slot",{key:"89e08bd761dc6940dbebc5d06f5f080af204aa72",name:"primary"}),(0,r.h)("slot",{key:"a1cb7d95627f8a3d24dd4b9c11718fc164f53674",name:"end"})))}get el(){return(0,r.f)(this)}};K.style={ios:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, #f7f7f7));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.2))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",md:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, #c1c4cd)));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}"}},333:($,S,l)=>{l.d(S,{c:()=>T,g:()=>c,h:()=>r,o:()=>A});var h=l(467);const r=(p,b)=>null!==b.closest(p),T=(p,b)=>"string"==typeof p&&p.length>0?Object.assign({"ion-color":!0,[`ion-color-${p}`]:!0},b):b,c=p=>{const b={};return(p=>void 0!==p?(Array.isArray(p)?p:p.split(" ")).filter(g=>null!=g).map(g=>g.trim()).filter(g=>""!==g):[])(p).forEach(g=>b[g]=!0),b},m=/^[a-z][a-z0-9+\-.]*:/,A=function(){var p=(0,h.A)(function*(b,g,k,w){if(null!=b&&"#"!==b[0]&&!m.test(b)){const C=document.querySelector("ion-router");if(C)return g?.preventDefault(),C.push(b,k,w)}return!1});return function(g,k,w,C){return p.apply(this,arguments)}}()}}]);