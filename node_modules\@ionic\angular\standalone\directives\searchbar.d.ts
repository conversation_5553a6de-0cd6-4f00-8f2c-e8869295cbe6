import { ChangeDetector<PERSON><PERSON>, <PERSON>ement<PERSON>ef, EventEmitter, Injector, Ng<PERSON>one } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { SearchbarInputEventDetail, SearchbarChangeEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonSearchbar extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    handleIonInput(el: HTMLIonSearchbarElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonSearchbar, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonSearchbar, "ion-searchbar", never, { "animated": "animated"; "autocomplete": "autocomplete"; "autocorrect": "autocorrect"; "cancelButtonIcon": "cancelButtonIcon"; "cancelButtonText": "cancelButtonText"; "clearIcon": "clearIcon"; "color": "color"; "debounce": "debounce"; "disabled": "disabled"; "enterkeyhint": "enterkeyhint"; "inputmode": "inputmode"; "mode": "mode"; "name": "name"; "placeholder": "placeholder"; "searchIcon": "searchIcon"; "showCancelButton": "showCancelButton"; "showClearButton": "showClearButton"; "spellcheck": "spellcheck"; "type": "type"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonSearchbar extends Components.IonSearchbar {
    /**
     * Emitted when the `value` of the `ion-searchbar` element has changed.
     */
    ionInput: EventEmitter<CustomEvent<SearchbarInputEventDetail>>;
    /**
     * The `ionChange` event is fired for `<ion-searchbar>` elements when the user
  modifies the element's value. Unlike the `ionInput` event, the `ionChange`
  event is not necessarily fired for each alteration to an element's value.
  
  The `ionChange` event is fired when the value has been committed
  by the user. This can happen when the element loses focus or
  when the "Enter" key is pressed. `ionChange` can also fire
  when clicking the clear or cancel buttons.
     */
    ionChange: EventEmitter<CustomEvent<SearchbarChangeEventDetail>>;
    /**
     * Emitted when the cancel button is clicked.
     */
    ionCancel: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the clear input button is clicked.
     */
    ionClear: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the input loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the input has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
}
