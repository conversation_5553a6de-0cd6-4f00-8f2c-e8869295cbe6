import { ChangeDetectorRef, ElementRef, EventEmitter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { DatetimeChangeEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonDatetime extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    handleIonChange(el: HTMLIonDatetimeElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonDatetime, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonDatetime, "ion-datetime", never, { "cancelText": "cancelText"; "clearText": "clearText"; "color": "color"; "dayValues": "dayValues"; "disabled": "disabled"; "doneText": "doneText"; "firstDayOfWeek": "firstDayOfWeek"; "highlightedDates": "highlightedDates"; "hourCycle": "hourCycle"; "hourValues": "hourValues"; "isDateEnabled": "isDateEnabled"; "locale": "locale"; "max": "max"; "min": "min"; "minuteValues": "minuteValues"; "mode": "mode"; "monthValues": "monthValues"; "multiple": "multiple"; "name": "name"; "preferWheel": "preferWheel"; "presentation": "presentation"; "readonly": "readonly"; "showClearButton": "showClearButton"; "showDefaultButtons": "showDefaultButtons"; "showDefaultTimeLabel": "showDefaultTimeLabel"; "showDefaultTitle": "showDefaultTitle"; "size": "size"; "titleSelectedDatesFormatter": "titleSelectedDatesFormatter"; "value": "value"; "yearValues": "yearValues"; }, {}, never, ["*"], true>;
}
export declare interface IonDatetime extends Components.IonDatetime {
    /**
     * Emitted when the datetime selection was cancelled.
     */
    ionCancel: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the value (selected date) has changed.
     */
    ionChange: EventEmitter<CustomEvent<DatetimeChangeEventDetail>>;
    /**
     * Emitted when the datetime has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the datetime loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
}
