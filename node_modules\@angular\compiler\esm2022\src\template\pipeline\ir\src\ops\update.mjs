/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { OpKind } from '../enums';
import { TRAIT_CONSUMES_VARS, TRAIT_DEPENDS_ON_SLOT_CONTEXT } from '../traits';
import { NEW_OP } from './shared';
/**
 * Create an `InterpolationTextOp`.
 */
export function createInterpolateTextOp(xref, interpolation, sourceSpan) {
    return {
        kind: OpKind.InterpolateText,
        target: xref,
        interpolation,
        sourceSpan,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
export class Interpolation {
    constructor(strings, expressions, i18nPlaceholders) {
        this.strings = strings;
        this.expressions = expressions;
        this.i18nPlaceholders = i18nPlaceholders;
        if (i18nPlaceholders.length !== 0 && i18nPlaceholders.length !== expressions.length) {
            throw new Error(`Expected ${expressions.length} placeholders to match interpolation expression count, but got ${i18nPlaceholders.length}`);
        }
    }
}
/**
 * Create a `BindingOp`, not yet transformed into a particular type of binding.
 */
export function createBindingOp(target, kind, name, expression, unit, securityContext, isTextAttribute, isStructuralTemplateAttribute, templateKind, i18nMessage, sourceSpan) {
    return {
        kind: OpKind.Binding,
        bindingKind: kind,
        target,
        name,
        expression,
        unit,
        securityContext,
        isTextAttribute,
        isStructuralTemplateAttribute,
        templateKind,
        i18nContext: null,
        i18nMessage,
        sourceSpan,
        ...NEW_OP,
    };
}
/**
 * Create a `PropertyOp`.
 */
export function createPropertyOp(target, name, expression, isAnimationTrigger, securityContext, isStructuralTemplateAttribute, templateKind, i18nContext, i18nMessage, sourceSpan) {
    return {
        kind: OpKind.Property,
        target,
        name,
        expression,
        isAnimationTrigger,
        securityContext,
        sanitizer: null,
        isStructuralTemplateAttribute,
        templateKind,
        i18nContext,
        i18nMessage,
        sourceSpan,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
/**
 * Create a `TwoWayPropertyOp`.
 */
export function createTwoWayPropertyOp(target, name, expression, securityContext, isStructuralTemplateAttribute, templateKind, i18nContext, i18nMessage, sourceSpan) {
    return {
        kind: OpKind.TwoWayProperty,
        target,
        name,
        expression,
        securityContext,
        sanitizer: null,
        isStructuralTemplateAttribute,
        templateKind,
        i18nContext,
        i18nMessage,
        sourceSpan,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
/** Create a `StylePropOp`. */
export function createStylePropOp(xref, name, expression, unit, sourceSpan) {
    return {
        kind: OpKind.StyleProp,
        target: xref,
        name,
        expression,
        unit,
        sourceSpan,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
/**
 * Create a `ClassPropOp`.
 */
export function createClassPropOp(xref, name, expression, sourceSpan) {
    return {
        kind: OpKind.ClassProp,
        target: xref,
        name,
        expression,
        sourceSpan,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
/** Create a `StyleMapOp`. */
export function createStyleMapOp(xref, expression, sourceSpan) {
    return {
        kind: OpKind.StyleMap,
        target: xref,
        expression,
        sourceSpan,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
/**
 * Create a `ClassMapOp`.
 */
export function createClassMapOp(xref, expression, sourceSpan) {
    return {
        kind: OpKind.ClassMap,
        target: xref,
        expression,
        sourceSpan,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
/**
 * Create an `AttributeOp`.
 */
export function createAttributeOp(target, namespace, name, expression, securityContext, isTextAttribute, isStructuralTemplateAttribute, templateKind, i18nMessage, sourceSpan) {
    return {
        kind: OpKind.Attribute,
        target,
        namespace,
        name,
        expression,
        securityContext,
        sanitizer: null,
        isTextAttribute,
        isStructuralTemplateAttribute,
        templateKind,
        i18nContext: null,
        i18nMessage,
        sourceSpan,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
/**
 * Create an `AdvanceOp`.
 */
export function createAdvanceOp(delta, sourceSpan) {
    return {
        kind: OpKind.Advance,
        delta,
        sourceSpan,
        ...NEW_OP,
    };
}
/**
 * Create a conditional op, which will display an embedded view according to a condtion.
 */
export function createConditionalOp(target, targetSlot, test, conditions, sourceSpan) {
    return {
        kind: OpKind.Conditional,
        target,
        targetSlot,
        test,
        conditions,
        processed: null,
        sourceSpan,
        contextValue: null,
        ...NEW_OP,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
    };
}
export function createRepeaterOp(repeaterCreate, targetSlot, collection, sourceSpan) {
    return {
        kind: OpKind.Repeater,
        target: repeaterCreate,
        targetSlot,
        collection,
        sourceSpan,
        ...NEW_OP,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
    };
}
export function createDeferWhenOp(target, expr, prefetch, sourceSpan) {
    return {
        kind: OpKind.DeferWhen,
        target,
        expr,
        prefetch,
        sourceSpan,
        ...NEW_OP,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
        ...TRAIT_CONSUMES_VARS,
    };
}
/**
 * Create an i18n expression op.
 */
export function createI18nExpressionOp(context, target, i18nOwner, handle, expression, icuPlaceholder, i18nPlaceholder, resolutionTime, usage, name, sourceSpan) {
    return {
        kind: OpKind.I18nExpression,
        context,
        target,
        i18nOwner,
        handle,
        expression,
        icuPlaceholder,
        i18nPlaceholder,
        resolutionTime,
        usage,
        name,
        sourceSpan,
        ...NEW_OP,
        ...TRAIT_CONSUMES_VARS,
        ...TRAIT_DEPENDS_ON_SLOT_CONTEXT,
    };
}
/**
 * Creates an op to apply i18n expression ops.
 */
export function createI18nApplyOp(owner, handle, sourceSpan) {
    return {
        kind: OpKind.I18nApply,
        owner,
        handle,
        sourceSpan,
        ...NEW_OP,
    };
}
//# sourceMappingURL=data:application/json;base64,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