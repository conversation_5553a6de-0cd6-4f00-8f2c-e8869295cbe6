import { ChangeDetectorRef, ElementRef, EventEmitter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { RangeChangeEventDetail, RangeKnobMoveStartEventDetail, RangeKnobMoveEndEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonRange extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    handleIonInput(el: HTMLIonRangeElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonRange, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonRange, "ion-range", never, { "activeBarStart": "activeBarStart"; "color": "color"; "debounce": "debounce"; "disabled": "disabled"; "dualKnobs": "dualKnobs"; "label": "label"; "labelPlacement": "labelPlacement"; "legacy": "legacy"; "max": "max"; "min": "min"; "mode": "mode"; "name": "name"; "pin": "pin"; "pinFormatter": "pinFormatter"; "snaps": "snaps"; "step": "step"; "ticks": "ticks"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonRange extends Components.IonRange {
    /**
     * The `ionChange` event is fired for `<ion-range>` elements when the user
  modifies the element's value:
  - When the user releases the knob after dragging;
  - When the user moves the knob with keyboard arrows
  
  `ionChange` is not fired when the value is changed programmatically.
     */
    ionChange: EventEmitter<CustomEvent<RangeChangeEventDetail>>;
    /**
     * The `ionInput` event is fired for `<ion-range>` elements when the value
  is modified. Unlike `ionChange`, `ionInput` is fired continuously
  while the user is dragging the knob.
     */
    ionInput: EventEmitter<CustomEvent<RangeChangeEventDetail>>;
    /**
     * Emitted when the range has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the range loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the user starts moving the range knob, whether through
  mouse drag, touch gesture, or keyboard interaction.
     */
    ionKnobMoveStart: EventEmitter<CustomEvent<RangeKnobMoveStartEventDetail>>;
    /**
     * Emitted when the user finishes moving the range knob, whether through
  mouse drag, touch gesture, or keyboard interaction.
     */
    ionKnobMoveEnd: EventEmitter<CustomEvent<RangeKnobMoveEndEventDetail>>;
}
