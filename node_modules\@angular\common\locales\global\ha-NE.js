/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['ha-ne'] = ["ha-NE",[["SF","YM"],u,["<PERSON><PERSON><PERSON>","Yamma"]],[["SF","YM"],u,u],[["L","L","T","L","A","J","A"],["Lah","Lit","Tal","Lar","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>"],["<PERSON>h","<PERSON>","<PERSON>","Lr","<PERSON>","<PERSON>","<PERSON>"]],u,[["J","F","M","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","S","O","N","D"],["<PERSON>","<PERSON>ab","<PERSON>","<PERSON>fi","May","<PERSON>","<PERSON>l","<PERSON>gu","<PERSON>t","<PERSON>t","<PERSON>uw","<PERSON>s"],["<PERSON>iru","<PERSON>ab<PERSON>ru","<PERSON>","<PERSON>firilu","<PERSON>u","Yuni","Yuli","Agusta","Satumba","Oktoba","Nuwamba","Disamba"]],u,[["K.H","BHAI"],u,["Kafin haihuwar annab","Bayan haihuwar annab"]],1,[6,0],["d/M/yy","d MMM, y","d MMMM, y","EEEE d MMMM, y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1}, {0}",u,"{1} 'da' {0}","{1} {0}"],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"XOF","F CFA","Kuɗin Sefa na Afirka Ta Yamma",{"NGN":["₦"]},"ltr", plural, []];
  })(globalThis);
    