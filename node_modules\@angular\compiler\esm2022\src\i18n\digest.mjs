/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * A lazily created TextEncoder instance for converting strings into UTF-8 bytes
 */
let textEncoder;
/**
 * Return the message id or compute it using the XLIFF1 digest.
 */
export function digest(message) {
    return message.id || computeDigest(message);
}
/**
 * Compute the message id using the XLIFF1 digest.
 */
export function computeDigest(message) {
    return sha1(serializeNodes(message.nodes).join('') + `[${message.meaning}]`);
}
/**
 * Return the message id or compute it using the XLIFF2/XMB/$localize digest.
 */
export function decimalDigest(message) {
    return message.id || computeDecimalDigest(message);
}
/**
 * Compute the message id using the XLIFF2/XMB/$localize digest.
 */
export function computeDecimalDigest(message) {
    const visitor = new _SerializerIgnoreIcuExpVisitor();
    const parts = message.nodes.map(a => a.visit(visitor, null));
    return computeMsgId(parts.join(''), message.meaning);
}
/**
 * Serialize the i18n ast to something xml-like in order to generate an UID.
 *
 * The visitor is also used in the i18n parser tests
 *
 * @internal
 */
class _SerializerVisitor {
    visitText(text, context) {
        return text.value;
    }
    visitContainer(container, context) {
        return `[${container.children.map(child => child.visit(this)).join(', ')}]`;
    }
    visitIcu(icu, context) {
        const strCases = Object.keys(icu.cases).map((k) => `${k} {${icu.cases[k].visit(this)}}`);
        return `{${icu.expression}, ${icu.type}, ${strCases.join(', ')}}`;
    }
    visitTagPlaceholder(ph, context) {
        return ph.isVoid ?
            `<ph tag name="${ph.startName}"/>` :
            `<ph tag name="${ph.startName}">${ph.children.map(child => child.visit(this)).join(', ')}</ph name="${ph.closeName}">`;
    }
    visitPlaceholder(ph, context) {
        return ph.value ? `<ph name="${ph.name}">${ph.value}</ph>` : `<ph name="${ph.name}"/>`;
    }
    visitIcuPlaceholder(ph, context) {
        return `<ph icu name="${ph.name}">${ph.value.visit(this)}</ph>`;
    }
    visitBlockPlaceholder(ph, context) {
        return `<ph block name="${ph.startName}">${ph.children.map(child => child.visit(this)).join(', ')}</ph name="${ph.closeName}">`;
    }
}
const serializerVisitor = new _SerializerVisitor();
export function serializeNodes(nodes) {
    return nodes.map(a => a.visit(serializerVisitor, null));
}
/**
 * Serialize the i18n ast to something xml-like in order to generate an UID.
 *
 * Ignore the ICU expressions so that message IDs stays identical if only the expression changes.
 *
 * @internal
 */
class _SerializerIgnoreIcuExpVisitor extends _SerializerVisitor {
    visitIcu(icu, context) {
        let strCases = Object.keys(icu.cases).map((k) => `${k} {${icu.cases[k].visit(this)}}`);
        // Do not take the expression into account
        return `{${icu.type}, ${strCases.join(', ')}}`;
    }
}
/**
 * Compute the SHA1 of the given string
 *
 * see https://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf
 *
 * WARNING: this function has not been designed not tested with security in mind.
 *          DO NOT USE IT IN A SECURITY SENSITIVE CONTEXT.
 */
export function sha1(str) {
    textEncoder ??= new TextEncoder();
    const utf8 = [...textEncoder.encode(str)];
    const words32 = bytesToWords32(utf8, Endian.Big);
    const len = utf8.length * 8;
    const w = new Uint32Array(80);
    let a = 0x67452301, b = 0xefcdab89, c = 0x98badcfe, d = 0x10325476, e = 0xc3d2e1f0;
    words32[len >> 5] |= 0x80 << (24 - len % 32);
    words32[((len + 64 >> 9) << 4) + 15] = len;
    for (let i = 0; i < words32.length; i += 16) {
        const h0 = a, h1 = b, h2 = c, h3 = d, h4 = e;
        for (let j = 0; j < 80; j++) {
            if (j < 16) {
                w[j] = words32[i + j];
            }
            else {
                w[j] = rol32(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);
            }
            const fkVal = fk(j, b, c, d);
            const f = fkVal[0];
            const k = fkVal[1];
            const temp = [rol32(a, 5), f, e, k, w[j]].reduce(add32);
            e = d;
            d = c;
            c = rol32(b, 30);
            b = a;
            a = temp;
        }
        a = add32(a, h0);
        b = add32(b, h1);
        c = add32(c, h2);
        d = add32(d, h3);
        e = add32(e, h4);
    }
    // Convert the output parts to a 160-bit hexadecimal string
    return toHexU32(a) + toHexU32(b) + toHexU32(c) + toHexU32(d) + toHexU32(e);
}
/**
 * Convert and format a number as a string representing a 32-bit unsigned hexadecimal number.
 * @param value The value to format as a string.
 * @returns A hexadecimal string representing the value.
 */
function toHexU32(value) {
    // unsigned right shift of zero ensures an unsigned 32-bit number
    return (value >>> 0).toString(16).padStart(8, '0');
}
function fk(index, b, c, d) {
    if (index < 20) {
        return [(b & c) | (~b & d), 0x5a827999];
    }
    if (index < 40) {
        return [b ^ c ^ d, 0x6ed9eba1];
    }
    if (index < 60) {
        return [(b & c) | (b & d) | (c & d), 0x8f1bbcdc];
    }
    return [b ^ c ^ d, 0xca62c1d6];
}
/**
 * Compute the fingerprint of the given string
 *
 * The output is 64 bit number encoded as a decimal string
 *
 * based on:
 * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java
 */
export function fingerprint(str) {
    textEncoder ??= new TextEncoder();
    const utf8 = textEncoder.encode(str);
    const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);
    let hi = hash32(view, utf8.length, 0);
    let lo = hash32(view, utf8.length, 102072);
    if (hi == 0 && (lo == 0 || lo == 1)) {
        hi = hi ^ 0x130f9bef;
        lo = lo ^ -0x6b5f56d8;
    }
    return (BigInt.asUintN(32, BigInt(hi)) << BigInt(32)) | BigInt.asUintN(32, BigInt(lo));
}
export function computeMsgId(msg, meaning = '') {
    let msgFingerprint = fingerprint(msg);
    if (meaning) {
        // Rotate the 64-bit message fingerprint one bit to the left and then add the meaning
        // fingerprint.
        msgFingerprint = BigInt.asUintN(64, msgFingerprint << BigInt(1)) |
            ((msgFingerprint >> BigInt(63)) & BigInt(1));
        msgFingerprint += fingerprint(meaning);
    }
    return BigInt.asUintN(63, msgFingerprint).toString();
}
function hash32(view, length, c) {
    let a = 0x9e3779b9, b = 0x9e3779b9;
    let index = 0;
    const end = length - 12;
    for (; index <= end; index += 12) {
        a += view.getUint32(index, true);
        b += view.getUint32(index + 4, true);
        c += view.getUint32(index + 8, true);
        const res = mix(a, b, c);
        a = res[0], b = res[1], c = res[2];
    }
    const remainder = length - index;
    // the first byte of c is reserved for the length
    c += length;
    if (remainder >= 4) {
        a += view.getUint32(index, true);
        index += 4;
        if (remainder >= 8) {
            b += view.getUint32(index, true);
            index += 4;
            // Partial 32-bit word for c
            if (remainder >= 9) {
                c += view.getUint8(index++) << 8;
            }
            if (remainder >= 10) {
                c += view.getUint8(index++) << 16;
            }
            if (remainder === 11) {
                c += view.getUint8(index++) << 24;
            }
        }
        else {
            // Partial 32-bit word for b
            if (remainder >= 5) {
                b += view.getUint8(index++);
            }
            if (remainder >= 6) {
                b += view.getUint8(index++) << 8;
            }
            if (remainder === 7) {
                b += view.getUint8(index++) << 16;
            }
        }
    }
    else {
        // Partial 32-bit word for a
        if (remainder >= 1) {
            a += view.getUint8(index++);
        }
        if (remainder >= 2) {
            a += view.getUint8(index++) << 8;
        }
        if (remainder === 3) {
            a += view.getUint8(index++) << 16;
        }
    }
    return mix(a, b, c)[2];
}
// clang-format off
function mix(a, b, c) {
    a -= b;
    a -= c;
    a ^= c >>> 13;
    b -= c;
    b -= a;
    b ^= a << 8;
    c -= a;
    c -= b;
    c ^= b >>> 13;
    a -= b;
    a -= c;
    a ^= c >>> 12;
    b -= c;
    b -= a;
    b ^= a << 16;
    c -= a;
    c -= b;
    c ^= b >>> 5;
    a -= b;
    a -= c;
    a ^= c >>> 3;
    b -= c;
    b -= a;
    b ^= a << 10;
    c -= a;
    c -= b;
    c ^= b >>> 15;
    return [a, b, c];
}
// clang-format on
// Utils
var Endian;
(function (Endian) {
    Endian[Endian["Little"] = 0] = "Little";
    Endian[Endian["Big"] = 1] = "Big";
})(Endian || (Endian = {}));
function add32(a, b) {
    return add32to64(a, b)[1];
}
function add32to64(a, b) {
    const low = (a & 0xffff) + (b & 0xffff);
    const high = (a >>> 16) + (b >>> 16) + (low >>> 16);
    return [high >>> 16, (high << 16) | (low & 0xffff)];
}
// Rotate a 32b number left `count` position
function rol32(a, count) {
    return (a << count) | (a >>> (32 - count));
}
function bytesToWords32(bytes, endian) {
    const size = (bytes.length + 3) >>> 2;
    const words32 = [];
    for (let i = 0; i < size; i++) {
        words32[i] = wordAt(bytes, i * 4, endian);
    }
    return words32;
}
function byteAt(bytes, index) {
    return index >= bytes.length ? 0 : bytes[index];
}
function wordAt(bytes, index, endian) {
    let word = 0;
    if (endian === Endian.Big) {
        for (let i = 0; i < 4; i++) {
            word += byteAt(bytes, index + i) << (24 - 8 * i);
        }
    }
    else {
        for (let i = 0; i < 4; i++) {
            word += byteAt(bytes, index + i) << 8 * i;
        }
    }
    return word;
}
//# sourceMappingURL=data:application/json;base64,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