{"version": 3, "names": ["_traverse", "data", "require", "_pluginPass", "_blockHoistPlugin", "_normalizeOpts", "_normalizeFile", "_generate", "_deepArray", "run", "config", "code", "ast", "file", "normalizeFile", "passes", "normalizeOptions", "opts", "transformFile", "e", "_opts$filename", "message", "filename", "outputCode", "outputMap", "generateCode", "_opts$filename2", "metadata", "options", "undefined", "map", "sourceType", "program", "externalDependencies", "flattenToSet", "pluginPasses", "pluginPairs", "passPairs", "visitors", "plugin", "concat", "loadBlockHoistPlugin", "pass", "Plug<PERSON><PERSON><PERSON>", "key", "push", "visitor", "fn", "pre", "result", "call", "isThenable", "Error", "traverse", "merge", "wrapPluginVisitorMethod", "scope", "post", "val", "then"], "sources": ["../../src/transformation/index.ts"], "sourcesContent": ["import traverse from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\nimport type { GeneratorResult } from \"@babel/generator\";\n\nimport type { <PERSON><PERSON> } from \"gensync\";\n\nimport type { ResolvedConfig, Plugin, PluginPasses } from \"../config/index.ts\";\n\nimport PluginPass from \"./plugin-pass.ts\";\nimport loadBlockHoistPlugin from \"./block-hoist-plugin.ts\";\nimport normalizeOptions from \"./normalize-opts.ts\";\nimport normalizeFile from \"./normalize-file.ts\";\n\nimport generateCode from \"./file/generate.ts\";\nimport type File from \"./file/file.ts\";\n\nimport { flattenToSet } from \"../config/helpers/deep-array.ts\";\n\nexport type FileResultCallback = {\n  (err: Error, file: null): void;\n  (err: null, file: FileResult | null): void;\n};\n\nexport type FileResult = {\n  metadata: { [key: string]: any };\n  options: { [key: string]: any };\n  ast: t.File | null;\n  code: string | null;\n  map: GeneratorResult[\"map\"] | null;\n  sourceType: \"script\" | \"module\";\n  externalDependencies: Set<string>;\n};\n\nexport function* run(\n  config: ResolvedConfig,\n  code: string,\n  ast?: t.File | t.Program | null,\n): Handler<FileResult> {\n  const file = yield* normalizeFile(\n    config.passes,\n    normalizeOptions(config),\n    code,\n    ast,\n  );\n\n  const opts = file.opts;\n  try {\n    yield* transformFile(file, config.passes);\n  } catch (e) {\n    e.message = `${opts.filename ?? \"unknown file\"}: ${e.message}`;\n    if (!e.code) {\n      e.code = \"BABEL_TRANSFORM_ERROR\";\n    }\n    throw e;\n  }\n\n  let outputCode, outputMap;\n  try {\n    if (opts.code !== false) {\n      ({ outputCode, outputMap } = generateCode(config.passes, file));\n    }\n  } catch (e) {\n    e.message = `${opts.filename ?? \"unknown file\"}: ${e.message}`;\n    if (!e.code) {\n      e.code = \"BABEL_GENERATE_ERROR\";\n    }\n    throw e;\n  }\n\n  return {\n    metadata: file.metadata,\n    options: opts,\n    ast: opts.ast === true ? file.ast : null,\n    code: outputCode === undefined ? null : outputCode,\n    map: outputMap === undefined ? null : outputMap,\n    sourceType: file.ast.program.sourceType,\n    externalDependencies: flattenToSet(config.externalDependencies),\n  };\n}\n\nfunction* transformFile(file: File, pluginPasses: PluginPasses): Handler<void> {\n  for (const pluginPairs of pluginPasses) {\n    const passPairs: [Plugin, PluginPass][] = [];\n    const passes = [];\n    const visitors = [];\n\n    for (const plugin of pluginPairs.concat([loadBlockHoistPlugin()])) {\n      const pass = new PluginPass(file, plugin.key, plugin.options);\n\n      passPairs.push([plugin, pass]);\n      passes.push(pass);\n      visitors.push(plugin.visitor);\n    }\n\n    for (const [plugin, pass] of passPairs) {\n      const fn = plugin.pre;\n      if (fn) {\n        // eslint-disable-next-line @typescript-eslint/no-confusing-void-expression\n        const result = fn.call(pass, file);\n\n        // @ts-expect-error - If we want to support async .pre\n        yield* [];\n\n        if (isThenable(result)) {\n          throw new Error(\n            `You appear to be using an plugin with an async .pre, ` +\n              `which your current version of Babel does not support. ` +\n              `If you're using a published plugin, you may need to upgrade ` +\n              `your @babel/core version.`,\n          );\n        }\n      }\n    }\n\n    // merge all plugin visitors into a single visitor\n    const visitor = traverse.visitors.merge(\n      visitors,\n      passes,\n      file.opts.wrapPluginVisitorMethod,\n    );\n    if (process.env.BABEL_8_BREAKING) {\n      traverse(file.ast.program, visitor, file.scope, null, file.path, true);\n    } else {\n      traverse(file.ast, visitor, file.scope);\n    }\n\n    for (const [plugin, pass] of passPairs) {\n      const fn = plugin.post;\n      if (fn) {\n        // eslint-disable-next-line @typescript-eslint/no-confusing-void-expression\n        const result = fn.call(pass, file);\n\n        // @ts-expect-error - If we want to support async .post\n        yield* [];\n\n        if (isThenable(result)) {\n          throw new Error(\n            `You appear to be using an plugin with an async .post, ` +\n              `which your current version of Babel does not support. ` +\n              `If you're using a published plugin, you may need to upgrade ` +\n              `your @babel/core version.`,\n          );\n        }\n      }\n    }\n  }\n}\n\nfunction isThenable<T extends PromiseLike<any>>(val: any): val is T {\n  return (\n    !!val &&\n    (typeof val === \"object\" || typeof val === \"function\") &&\n    !!val.then &&\n    typeof val.then === \"function\"\n  );\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,IAAAE,WAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAJ,OAAA;AAEA,IAAAK,SAAA,GAAAL,OAAA;AAGA,IAAAM,UAAA,GAAAN,OAAA;AAiBO,UAAUO,GAAGA,CAClBC,MAAsB,EACtBC,IAAY,EACZC,GAA+B,EACV;EACrB,MAAMC,IAAI,GAAG,OAAO,IAAAC,sBAAa,EAC/BJ,MAAM,CAACK,MAAM,EACb,IAAAC,sBAAgB,EAACN,MAAM,CAAC,EACxBC,IAAI,EACJC,GACF,CAAC;EAED,MAAMK,IAAI,GAAGJ,IAAI,CAACI,IAAI;EACtB,IAAI;IACF,OAAOC,aAAa,CAACL,IAAI,EAAEH,MAAM,CAACK,MAAM,CAAC;EAC3C,CAAC,CAAC,OAAOI,CAAC,EAAE;IAAA,IAAAC,cAAA;IACVD,CAAC,CAACE,OAAO,GAAI,IAAAD,cAAA,GAAEH,IAAI,CAACK,QAAQ,YAAAF,cAAA,GAAI,cAAe,KAAID,CAAC,CAACE,OAAQ,EAAC;IAC9D,IAAI,CAACF,CAAC,CAACR,IAAI,EAAE;MACXQ,CAAC,CAACR,IAAI,GAAG,uBAAuB;IAClC;IACA,MAAMQ,CAAC;EACT;EAEA,IAAII,UAAU,EAAEC,SAAS;EACzB,IAAI;IACF,IAAIP,IAAI,CAACN,IAAI,KAAK,KAAK,EAAE;MACvB,CAAC;QAAEY,UAAU;QAAEC;MAAU,CAAC,GAAG,IAAAC,iBAAY,EAACf,MAAM,CAACK,MAAM,EAAEF,IAAI,CAAC;IAChE;EACF,CAAC,CAAC,OAAOM,CAAC,EAAE;IAAA,IAAAO,eAAA;IACVP,CAAC,CAACE,OAAO,GAAI,IAAAK,eAAA,GAAET,IAAI,CAACK,QAAQ,YAAAI,eAAA,GAAI,cAAe,KAAIP,CAAC,CAACE,OAAQ,EAAC;IAC9D,IAAI,CAACF,CAAC,CAACR,IAAI,EAAE;MACXQ,CAAC,CAACR,IAAI,GAAG,sBAAsB;IACjC;IACA,MAAMQ,CAAC;EACT;EAEA,OAAO;IACLQ,QAAQ,EAAEd,IAAI,CAACc,QAAQ;IACvBC,OAAO,EAAEX,IAAI;IACbL,GAAG,EAAEK,IAAI,CAACL,GAAG,KAAK,IAAI,GAAGC,IAAI,CAACD,GAAG,GAAG,IAAI;IACxCD,IAAI,EAAEY,UAAU,KAAKM,SAAS,GAAG,IAAI,GAAGN,UAAU;IAClDO,GAAG,EAAEN,SAAS,KAAKK,SAAS,GAAG,IAAI,GAAGL,SAAS;IAC/CO,UAAU,EAAElB,IAAI,CAACD,GAAG,CAACoB,OAAO,CAACD,UAAU;IACvCE,oBAAoB,EAAE,IAAAC,uBAAY,EAACxB,MAAM,CAACuB,oBAAoB;EAChE,CAAC;AACH;AAEA,UAAUf,aAAaA,CAACL,IAAU,EAAEsB,YAA0B,EAAiB;EAC7E,KAAK,MAAMC,WAAW,IAAID,YAAY,EAAE;IACtC,MAAME,SAAiC,GAAG,EAAE;IAC5C,MAAMtB,MAAM,GAAG,EAAE;IACjB,MAAMuB,QAAQ,GAAG,EAAE;IAEnB,KAAK,MAAMC,MAAM,IAAIH,WAAW,CAACI,MAAM,CAAC,CAAC,IAAAC,yBAAoB,EAAC,CAAC,CAAC,CAAC,EAAE;MACjE,MAAMC,IAAI,GAAG,IAAIC,mBAAU,CAAC9B,IAAI,EAAE0B,MAAM,CAACK,GAAG,EAAEL,MAAM,CAACX,OAAO,CAAC;MAE7DS,SAAS,CAACQ,IAAI,CAAC,CAACN,MAAM,EAAEG,IAAI,CAAC,CAAC;MAC9B3B,MAAM,CAAC8B,IAAI,CAACH,IAAI,CAAC;MACjBJ,QAAQ,CAACO,IAAI,CAACN,MAAM,CAACO,OAAO,CAAC;IAC/B;IAEA,KAAK,MAAM,CAACP,MAAM,EAAEG,IAAI,CAAC,IAAIL,SAAS,EAAE;MACtC,MAAMU,EAAE,GAAGR,MAAM,CAACS,GAAG;MACrB,IAAID,EAAE,EAAE;QAEN,MAAME,MAAM,GAAGF,EAAE,CAACG,IAAI,CAACR,IAAI,EAAE7B,IAAI,CAAC;QAGlC,OAAO,EAAE;QAET,IAAIsC,UAAU,CAACF,MAAM,CAAC,EAAE;UACtB,MAAM,IAAIG,KAAK,CACZ,uDAAsD,GACpD,wDAAuD,GACvD,8DAA6D,GAC7D,2BACL,CAAC;QACH;MACF;IACF;IAGA,MAAMN,OAAO,GAAGO,mBAAQ,CAACf,QAAQ,CAACgB,KAAK,CACrChB,QAAQ,EACRvB,MAAM,EACNF,IAAI,CAACI,IAAI,CAACsC,uBACZ,CAAC;IAGM;MACL,IAAAF,mBAAQ,EAACxC,IAAI,CAACD,GAAG,EAAEkC,OAAO,EAAEjC,IAAI,CAAC2C,KAAK,CAAC;IACzC;IAEA,KAAK,MAAM,CAACjB,MAAM,EAAEG,IAAI,CAAC,IAAIL,SAAS,EAAE;MACtC,MAAMU,EAAE,GAAGR,MAAM,CAACkB,IAAI;MACtB,IAAIV,EAAE,EAAE;QAEN,MAAME,MAAM,GAAGF,EAAE,CAACG,IAAI,CAACR,IAAI,EAAE7B,IAAI,CAAC;QAGlC,OAAO,EAAE;QAET,IAAIsC,UAAU,CAACF,MAAM,CAAC,EAAE;UACtB,MAAM,IAAIG,KAAK,CACZ,wDAAuD,GACrD,wDAAuD,GACvD,8DAA6D,GAC7D,2BACL,CAAC;QACH;MACF;IACF;EACF;AACF;AAEA,SAASD,UAAUA,CAA6BO,GAAQ,EAAY;EAClE,OACE,CAAC,CAACA,GAAG,KACJ,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IACtD,CAAC,CAACA,GAAG,CAACC,IAAI,IACV,OAAOD,GAAG,CAACC,IAAI,KAAK,UAAU;AAElC;AAAC"}