"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isContentAction = exports.ActionList = exports.UnknownActionException = void 0;
const core_1 = require("@angular-devkit/core");
class UnknownActionException extends core_1.BaseException {
    constructor(action) {
        super(`Unknown action: "${action.kind}".`);
    }
}
exports.UnknownActionException = UnknownActionException;
let _id = 1;
class ActionList {
    constructor() {
        this._actions = [];
    }
    _action(action) {
        var _a, _b;
        this._actions.push({
            ...action,
            id: _id++,
            parent: (_b = (_a = this._actions[this._actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : 0,
        });
    }
    create(path, content) {
        this._action({ kind: 'c', path, content });
    }
    overwrite(path, content) {
        this._action({ kind: 'o', path, content });
    }
    rename(path, to) {
        this._action({ kind: 'r', path, to });
    }
    delete(path) {
        this._action({ kind: 'd', path });
    }
    optimize() {
        const toCreate = new Map();
        const toRename = new Map();
        const toOverwrite = new Map();
        const toDelete = new Set();
        for (const action of this._actions) {
            switch (action.kind) {
                case 'c':
                    toCreate.set(action.path, action.content);
                    break;
                case 'o':
                    if (toCreate.has(action.path)) {
                        toCreate.set(action.path, action.content);
                    }
                    else {
                        toOverwrite.set(action.path, action.content);
                    }
                    break;
                case 'd':
                    toDelete.add(action.path);
                    break;
                case 'r':
                    const maybeCreate = toCreate.get(action.path);
                    const maybeOverwrite = toOverwrite.get(action.path);
                    if (maybeCreate) {
                        toCreate.delete(action.path);
                        toCreate.set(action.to, maybeCreate);
                    }
                    if (maybeOverwrite) {
                        toOverwrite.delete(action.path);
                        toOverwrite.set(action.to, maybeOverwrite);
                    }
                    let maybeRename = undefined;
                    for (const [from, to] of toRename.entries()) {
                        if (to == action.path) {
                            maybeRename = from;
                            break;
                        }
                    }
                    if (maybeRename) {
                        toRename.set(maybeRename, action.to);
                    }
                    if (!maybeCreate && !maybeOverwrite && !maybeRename) {
                        toRename.set(action.path, action.to);
                    }
                    break;
            }
        }
        this._actions = [];
        toDelete.forEach((x) => {
            this.delete(x);
        });
        toRename.forEach((to, from) => {
            this.rename(from, to);
        });
        toCreate.forEach((content, path) => {
            this.create(path, content);
        });
        toOverwrite.forEach((content, path) => {
            this.overwrite(path, content);
        });
    }
    push(action) {
        this._actions.push(action);
    }
    get(i) {
        return this._actions[i];
    }
    has(action) {
        for (let i = 0; i < this._actions.length; i++) {
            const a = this._actions[i];
            if (a.id == action.id) {
                return true;
            }
            if (a.id > action.id) {
                return false;
            }
        }
        return false;
    }
    find(predicate) {
        return this._actions.find(predicate) || null;
    }
    forEach(fn, thisArg) {
        this._actions.forEach(fn, thisArg);
    }
    get length() {
        return this._actions.length;
    }
    [Symbol.iterator]() {
        return this._actions[Symbol.iterator]();
    }
}
exports.ActionList = ActionList;
function isContentAction(action) {
    return action.kind == 'c' || action.kind == 'o';
}
exports.isContentAction = isContentAction;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYWN0aW9uLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvYW5ndWxhcl9kZXZraXQvc2NoZW1hdGljcy9zcmMvdHJlZS9hY3Rpb24udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7R0FNRzs7O0FBRUgsK0NBQTJEO0FBRTNELE1BQWEsc0JBQXVCLFNBQVEsb0JBQWE7SUFDdkQsWUFBWSxNQUFjO1FBQ3hCLEtBQUssQ0FBQyxvQkFBb0IsTUFBTSxDQUFDLElBQUksSUFBSSxDQUFDLENBQUM7SUFDN0MsQ0FBQztDQUNGO0FBSkQsd0RBSUM7QUFVRCxJQUFJLEdBQUcsR0FBRyxDQUFDLENBQUM7QUFFWixNQUFhLFVBQVU7SUFBdkI7UUFDVSxhQUFRLEdBQWEsRUFBRSxDQUFDO0lBK0hsQyxDQUFDO0lBN0hXLE9BQU8sQ0FBQyxNQUF1Qjs7UUFDdkMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUM7WUFDakIsR0FBSSxNQUFpQjtZQUNyQixFQUFFLEVBQUUsR0FBRyxFQUFFO1lBQ1QsTUFBTSxFQUFFLE1BQUEsTUFBQSxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQywwQ0FBRSxFQUFFLG1DQUFJLENBQUM7U0FDekQsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELE1BQU0sQ0FBQyxJQUFVLEVBQUUsT0FBZTtRQUNoQyxJQUFJLENBQUMsT0FBTyxDQUFDLEVBQUUsSUFBSSxFQUFFLEdBQUcsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLENBQUMsQ0FBQztJQUM3QyxDQUFDO0lBQ0QsU0FBUyxDQUFDLElBQVUsRUFBRSxPQUFlO1FBQ25DLElBQUksQ0FBQyxPQUFPLENBQUMsRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQzdDLENBQUM7SUFDRCxNQUFNLENBQUMsSUFBVSxFQUFFLEVBQVE7UUFDekIsSUFBSSxDQUFDLE9BQU8sQ0FBQyxFQUFFLElBQUksRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7SUFDeEMsQ0FBQztJQUNELE1BQU0sQ0FBQyxJQUFVO1FBQ2YsSUFBSSxDQUFDLE9BQU8sQ0FBQyxFQUFFLElBQUksRUFBRSxHQUFHLEVBQUUsSUFBSSxFQUFFLENBQUMsQ0FBQztJQUNwQyxDQUFDO0lBRUQsUUFBUTtRQUNOLE1BQU0sUUFBUSxHQUFHLElBQUksR0FBRyxFQUFnQixDQUFDO1FBQ3pDLE1BQU0sUUFBUSxHQUFHLElBQUksR0FBRyxFQUFjLENBQUM7UUFDdkMsTUFBTSxXQUFXLEdBQUcsSUFBSSxHQUFHLEVBQWdCLENBQUM7UUFDNUMsTUFBTSxRQUFRLEdBQUcsSUFBSSxHQUFHLEVBQVEsQ0FBQztRQUVqQyxLQUFLLE1BQU0sTUFBTSxJQUFJLElBQUksQ0FBQyxRQUFRLEVBQUU7WUFDbEMsUUFBUSxNQUFNLENBQUMsSUFBSSxFQUFFO2dCQUNuQixLQUFLLEdBQUc7b0JBQ04sUUFBUSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztvQkFDMUMsTUFBTTtnQkFFUixLQUFLLEdBQUc7b0JBQ04sSUFBSSxRQUFRLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsRUFBRTt3QkFDN0IsUUFBUSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztxQkFDM0M7eUJBQU07d0JBQ0wsV0FBVyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsSUFBSSxFQUFFLE1BQU0sQ0FBQyxPQUFPLENBQUMsQ0FBQztxQkFDOUM7b0JBQ0QsTUFBTTtnQkFFUixLQUFLLEdBQUc7b0JBQ04sUUFBUSxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUM7b0JBQzFCLE1BQU07Z0JBRVIsS0FBSyxHQUFHO29CQUNOLE1BQU0sV0FBVyxHQUFHLFFBQVEsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDO29CQUM5QyxNQUFNLGNBQWMsR0FBRyxXQUFXLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQztvQkFDcEQsSUFBSSxXQUFXLEVBQUU7d0JBQ2YsUUFBUSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUM7d0JBQzdCLFFBQVEsQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLEVBQUUsRUFBRSxXQUFXLENBQUMsQ0FBQztxQkFDdEM7b0JBQ0QsSUFBSSxjQUFjLEVBQUU7d0JBQ2xCLFdBQVcsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDO3dCQUNoQyxXQUFXLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxFQUFFLEVBQUUsY0FBYyxDQUFDLENBQUM7cUJBQzVDO29CQUVELElBQUksV0FBVyxHQUFxQixTQUFTLENBQUM7b0JBQzlDLEtBQUssTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsSUFBSSxRQUFRLENBQUMsT0FBTyxFQUFFLEVBQUU7d0JBQzNDLElBQUksRUFBRSxJQUFJLE1BQU0sQ0FBQyxJQUFJLEVBQUU7NEJBQ3JCLFdBQVcsR0FBRyxJQUFJLENBQUM7NEJBQ25CLE1BQU07eUJBQ1A7cUJBQ0Y7b0JBRUQsSUFBSSxXQUFXLEVBQUU7d0JBQ2YsUUFBUSxDQUFDLEdBQUcsQ0FBQyxXQUFXLEVBQUUsTUFBTSxDQUFDLEVBQUUsQ0FBQyxDQUFDO3FCQUN0QztvQkFFRCxJQUFJLENBQUMsV0FBVyxJQUFJLENBQUMsY0FBYyxJQUFJLENBQUMsV0FBVyxFQUFFO3dCQUNuRCxRQUFRLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxJQUFJLEVBQUUsTUFBTSxDQUFDLEVBQUUsQ0FBQyxDQUFDO3FCQUN0QztvQkFDRCxNQUFNO2FBQ1Q7U0FDRjtRQUVELElBQUksQ0FBQyxRQUFRLEdBQUcsRUFBRSxDQUFDO1FBQ25CLFFBQVEsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsRUFBRTtZQUNyQixJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ2pCLENBQUMsQ0FBQyxDQUFDO1FBRUgsUUFBUSxDQUFDLE9BQU8sQ0FBQyxDQUFDLEVBQUUsRUFBRSxJQUFJLEVBQUUsRUFBRTtZQUM1QixJQUFJLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQztRQUN4QixDQUFDLENBQUMsQ0FBQztRQUVILFFBQVEsQ0FBQyxPQUFPLENBQUMsQ0FBQyxPQUFPLEVBQUUsSUFBSSxFQUFFLEVBQUU7WUFDakMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxJQUFJLEVBQUUsT0FBTyxDQUFDLENBQUM7UUFDN0IsQ0FBQyxDQUFDLENBQUM7UUFFSCxXQUFXLENBQUMsT0FBTyxDQUFDLENBQUMsT0FBTyxFQUFFLElBQUksRUFBRSxFQUFFO1lBQ3BDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1FBQ2hDLENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUVELElBQUksQ0FBQyxNQUFjO1FBQ2pCLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDO0lBQzdCLENBQUM7SUFDRCxHQUFHLENBQUMsQ0FBUztRQUNYLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUMxQixDQUFDO0lBQ0QsR0FBRyxDQUFDLE1BQWM7UUFDaEIsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO1lBQzdDLE1BQU0sQ0FBQyxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDM0IsSUFBSSxDQUFDLENBQUMsRUFBRSxJQUFJLE1BQU0sQ0FBQyxFQUFFLEVBQUU7Z0JBQ3JCLE9BQU8sSUFBSSxDQUFDO2FBQ2I7WUFDRCxJQUFJLENBQUMsQ0FBQyxFQUFFLEdBQUcsTUFBTSxDQUFDLEVBQUUsRUFBRTtnQkFDcEIsT0FBTyxLQUFLLENBQUM7YUFDZDtTQUNGO1FBRUQsT0FBTyxLQUFLLENBQUM7SUFDZixDQUFDO0lBQ0QsSUFBSSxDQUFDLFNBQXFDO1FBQ3hDLE9BQU8sSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLElBQUksSUFBSSxDQUFDO0lBQy9DLENBQUM7SUFDRCxPQUFPLENBQUMsRUFBMkQsRUFBRSxPQUFZO1FBQy9FLElBQUksQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsRUFBRSxPQUFPLENBQUMsQ0FBQztJQUNyQyxDQUFDO0lBQ0QsSUFBSSxNQUFNO1FBQ1IsT0FBTyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQztJQUM5QixDQUFDO0lBQ0QsQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDO1FBQ2YsT0FBTyxJQUFJLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDO0lBQzFDLENBQUM7Q0FDRjtBQWhJRCxnQ0FnSUM7QUFFRCxTQUFnQixlQUFlLENBQUMsTUFBYztJQUM1QyxPQUFPLE1BQU0sQ0FBQyxJQUFJLElBQUksR0FBRyxJQUFJLE1BQU0sQ0FBQyxJQUFJLElBQUksR0FBRyxDQUFDO0FBQ2xELENBQUM7QUFGRCwwQ0FFQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQgeyBCYXNlRXhjZXB0aW9uLCBQYXRoIH0gZnJvbSAnQGFuZ3VsYXItZGV2a2l0L2NvcmUnO1xuXG5leHBvcnQgY2xhc3MgVW5rbm93bkFjdGlvbkV4Y2VwdGlvbiBleHRlbmRzIEJhc2VFeGNlcHRpb24ge1xuICBjb25zdHJ1Y3RvcihhY3Rpb246IEFjdGlvbikge1xuICAgIHN1cGVyKGBVbmtub3duIGFjdGlvbjogXCIke2FjdGlvbi5raW5kfVwiLmApO1xuICB9XG59XG5cbmV4cG9ydCB0eXBlIEFjdGlvbiA9IENyZWF0ZUZpbGVBY3Rpb24gfCBPdmVyd3JpdGVGaWxlQWN0aW9uIHwgUmVuYW1lRmlsZUFjdGlvbiB8IERlbGV0ZUZpbGVBY3Rpb247XG5cbmV4cG9ydCBpbnRlcmZhY2UgQWN0aW9uQmFzZSB7XG4gIHJlYWRvbmx5IGlkOiBudW1iZXI7XG4gIHJlYWRvbmx5IHBhcmVudDogbnVtYmVyO1xuICByZWFkb25seSBwYXRoOiBQYXRoO1xufVxuXG5sZXQgX2lkID0gMTtcblxuZXhwb3J0IGNsYXNzIEFjdGlvbkxpc3QgaW1wbGVtZW50cyBJdGVyYWJsZTxBY3Rpb24+IHtcbiAgcHJpdmF0ZSBfYWN0aW9uczogQWN0aW9uW10gPSBbXTtcblxuICBwcm90ZWN0ZWQgX2FjdGlvbihhY3Rpb246IFBhcnRpYWw8QWN0aW9uPikge1xuICAgIHRoaXMuX2FjdGlvbnMucHVzaCh7XG4gICAgICAuLi4oYWN0aW9uIGFzIEFjdGlvbiksXG4gICAgICBpZDogX2lkKyssXG4gICAgICBwYXJlbnQ6IHRoaXMuX2FjdGlvbnNbdGhpcy5fYWN0aW9ucy5sZW5ndGggLSAxXT8uaWQgPz8gMCxcbiAgICB9KTtcbiAgfVxuXG4gIGNyZWF0ZShwYXRoOiBQYXRoLCBjb250ZW50OiBCdWZmZXIpIHtcbiAgICB0aGlzLl9hY3Rpb24oeyBraW5kOiAnYycsIHBhdGgsIGNvbnRlbnQgfSk7XG4gIH1cbiAgb3ZlcndyaXRlKHBhdGg6IFBhdGgsIGNvbnRlbnQ6IEJ1ZmZlcikge1xuICAgIHRoaXMuX2FjdGlvbih7IGtpbmQ6ICdvJywgcGF0aCwgY29udGVudCB9KTtcbiAgfVxuICByZW5hbWUocGF0aDogUGF0aCwgdG86IFBhdGgpIHtcbiAgICB0aGlzLl9hY3Rpb24oeyBraW5kOiAncicsIHBhdGgsIHRvIH0pO1xuICB9XG4gIGRlbGV0ZShwYXRoOiBQYXRoKSB7XG4gICAgdGhpcy5fYWN0aW9uKHsga2luZDogJ2QnLCBwYXRoIH0pO1xuICB9XG5cbiAgb3B0aW1pemUoKSB7XG4gICAgY29uc3QgdG9DcmVhdGUgPSBuZXcgTWFwPFBhdGgsIEJ1ZmZlcj4oKTtcbiAgICBjb25zdCB0b1JlbmFtZSA9IG5ldyBNYXA8UGF0aCwgUGF0aD4oKTtcbiAgICBjb25zdCB0b092ZXJ3cml0ZSA9IG5ldyBNYXA8UGF0aCwgQnVmZmVyPigpO1xuICAgIGNvbnN0IHRvRGVsZXRlID0gbmV3IFNldDxQYXRoPigpO1xuXG4gICAgZm9yIChjb25zdCBhY3Rpb24gb2YgdGhpcy5fYWN0aW9ucykge1xuICAgICAgc3dpdGNoIChhY3Rpb24ua2luZCkge1xuICAgICAgICBjYXNlICdjJzpcbiAgICAgICAgICB0b0NyZWF0ZS5zZXQoYWN0aW9uLnBhdGgsIGFjdGlvbi5jb250ZW50KTtcbiAgICAgICAgICBicmVhaztcblxuICAgICAgICBjYXNlICdvJzpcbiAgICAgICAgICBpZiAodG9DcmVhdGUuaGFzKGFjdGlvbi5wYXRoKSkge1xuICAgICAgICAgICAgdG9DcmVhdGUuc2V0KGFjdGlvbi5wYXRoLCBhY3Rpb24uY29udGVudCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRvT3ZlcndyaXRlLnNldChhY3Rpb24ucGF0aCwgYWN0aW9uLmNvbnRlbnQpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBicmVhaztcblxuICAgICAgICBjYXNlICdkJzpcbiAgICAgICAgICB0b0RlbGV0ZS5hZGQoYWN0aW9uLnBhdGgpO1xuICAgICAgICAgIGJyZWFrO1xuXG4gICAgICAgIGNhc2UgJ3InOlxuICAgICAgICAgIGNvbnN0IG1heWJlQ3JlYXRlID0gdG9DcmVhdGUuZ2V0KGFjdGlvbi5wYXRoKTtcbiAgICAgICAgICBjb25zdCBtYXliZU92ZXJ3cml0ZSA9IHRvT3ZlcndyaXRlLmdldChhY3Rpb24ucGF0aCk7XG4gICAgICAgICAgaWYgKG1heWJlQ3JlYXRlKSB7XG4gICAgICAgICAgICB0b0NyZWF0ZS5kZWxldGUoYWN0aW9uLnBhdGgpO1xuICAgICAgICAgICAgdG9DcmVhdGUuc2V0KGFjdGlvbi50bywgbWF5YmVDcmVhdGUpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAobWF5YmVPdmVyd3JpdGUpIHtcbiAgICAgICAgICAgIHRvT3ZlcndyaXRlLmRlbGV0ZShhY3Rpb24ucGF0aCk7XG4gICAgICAgICAgICB0b092ZXJ3cml0ZS5zZXQoYWN0aW9uLnRvLCBtYXliZU92ZXJ3cml0ZSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgbGV0IG1heWJlUmVuYW1lOiBQYXRoIHwgdW5kZWZpbmVkID0gdW5kZWZpbmVkO1xuICAgICAgICAgIGZvciAoY29uc3QgW2Zyb20sIHRvXSBvZiB0b1JlbmFtZS5lbnRyaWVzKCkpIHtcbiAgICAgICAgICAgIGlmICh0byA9PSBhY3Rpb24ucGF0aCkge1xuICAgICAgICAgICAgICBtYXliZVJlbmFtZSA9IGZyb207XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmIChtYXliZVJlbmFtZSkge1xuICAgICAgICAgICAgdG9SZW5hbWUuc2V0KG1heWJlUmVuYW1lLCBhY3Rpb24udG8pO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmICghbWF5YmVDcmVhdGUgJiYgIW1heWJlT3ZlcndyaXRlICYmICFtYXliZVJlbmFtZSkge1xuICAgICAgICAgICAgdG9SZW5hbWUuc2V0KGFjdGlvbi5wYXRoLCBhY3Rpb24udG8pO1xuICAgICAgICAgIH1cbiAgICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0aGlzLl9hY3Rpb25zID0gW107XG4gICAgdG9EZWxldGUuZm9yRWFjaCgoeCkgPT4ge1xuICAgICAgdGhpcy5kZWxldGUoeCk7XG4gICAgfSk7XG5cbiAgICB0b1JlbmFtZS5mb3JFYWNoKCh0bywgZnJvbSkgPT4ge1xuICAgICAgdGhpcy5yZW5hbWUoZnJvbSwgdG8pO1xuICAgIH0pO1xuXG4gICAgdG9DcmVhdGUuZm9yRWFjaCgoY29udGVudCwgcGF0aCkgPT4ge1xuICAgICAgdGhpcy5jcmVhdGUocGF0aCwgY29udGVudCk7XG4gICAgfSk7XG5cbiAgICB0b092ZXJ3cml0ZS5mb3JFYWNoKChjb250ZW50LCBwYXRoKSA9PiB7XG4gICAgICB0aGlzLm92ZXJ3cml0ZShwYXRoLCBjb250ZW50KTtcbiAgICB9KTtcbiAgfVxuXG4gIHB1c2goYWN0aW9uOiBBY3Rpb24pIHtcbiAgICB0aGlzLl9hY3Rpb25zLnB1c2goYWN0aW9uKTtcbiAgfVxuICBnZXQoaTogbnVtYmVyKSB7XG4gICAgcmV0dXJuIHRoaXMuX2FjdGlvbnNbaV07XG4gIH1cbiAgaGFzKGFjdGlvbjogQWN0aW9uKSB7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLl9hY3Rpb25zLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBhID0gdGhpcy5fYWN0aW9uc1tpXTtcbiAgICAgIGlmIChhLmlkID09IGFjdGlvbi5pZCkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH1cbiAgICAgIGlmIChhLmlkID4gYWN0aW9uLmlkKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgZmluZChwcmVkaWNhdGU6ICh2YWx1ZTogQWN0aW9uKSA9PiBib29sZWFuKTogQWN0aW9uIHwgbnVsbCB7XG4gICAgcmV0dXJuIHRoaXMuX2FjdGlvbnMuZmluZChwcmVkaWNhdGUpIHx8IG51bGw7XG4gIH1cbiAgZm9yRWFjaChmbjogKHZhbHVlOiBBY3Rpb24sIGluZGV4OiBudW1iZXIsIGFycmF5OiBBY3Rpb25bXSkgPT4gdm9pZCwgdGhpc0FyZz86IHt9KSB7XG4gICAgdGhpcy5fYWN0aW9ucy5mb3JFYWNoKGZuLCB0aGlzQXJnKTtcbiAgfVxuICBnZXQgbGVuZ3RoKCkge1xuICAgIHJldHVybiB0aGlzLl9hY3Rpb25zLmxlbmd0aDtcbiAgfVxuICBbU3ltYm9sLml0ZXJhdG9yXSgpIHtcbiAgICByZXR1cm4gdGhpcy5fYWN0aW9uc1tTeW1ib2wuaXRlcmF0b3JdKCk7XG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzQ29udGVudEFjdGlvbihhY3Rpb246IEFjdGlvbik6IGFjdGlvbiBpcyBDcmVhdGVGaWxlQWN0aW9uIHwgT3ZlcndyaXRlRmlsZUFjdGlvbiB7XG4gIHJldHVybiBhY3Rpb24ua2luZCA9PSAnYycgfHwgYWN0aW9uLmtpbmQgPT0gJ28nO1xufVxuXG4vLyBDcmVhdGUgYSBmaWxlLiBJZiB0aGUgZmlsZSBhbHJlYWR5IGV4aXN0cyB0aGVuIHRoaXMgaXMgYW4gZXJyb3IuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZUZpbGVBY3Rpb24gZXh0ZW5kcyBBY3Rpb25CYXNlIHtcbiAgcmVhZG9ubHkga2luZDogJ2MnO1xuICByZWFkb25seSBjb250ZW50OiBCdWZmZXI7XG59XG5cbi8vIE92ZXJ3cml0ZSBhIGZpbGUuIElmIHRoZSBmaWxlIGRvZXMgbm90IGFscmVhZHkgZXhpc3QsIHRoaXMgaXMgYW4gZXJyb3IuXG5leHBvcnQgaW50ZXJmYWNlIE92ZXJ3cml0ZUZpbGVBY3Rpb24gZXh0ZW5kcyBBY3Rpb25CYXNlIHtcbiAgcmVhZG9ubHkga2luZDogJ28nO1xuICByZWFkb25seSBjb250ZW50OiBCdWZmZXI7XG59XG5cbi8vIE1vdmUgYSBmaWxlIGZyb20gb25lIHBhdGggdG8gYW5vdGhlci4gSWYgdGhlIHNvdXJjZSBmaWxlcyBkb2VzIG5vdCBleGlzdCwgdGhpcyBpcyBhbiBlcnJvci5cbi8vIElmIHRoZSB0YXJnZXQgcGF0aCBhbHJlYWR5IGV4aXN0cywgdGhpcyBpcyBhbiBlcnJvci5cbmV4cG9ydCBpbnRlcmZhY2UgUmVuYW1lRmlsZUFjdGlvbiBleHRlbmRzIEFjdGlvbkJhc2Uge1xuICByZWFkb25seSBraW5kOiAncic7XG4gIHJlYWRvbmx5IHRvOiBQYXRoO1xufVxuXG4vLyBEZWxldGUgYSBmaWxlLiBJZiB0aGUgZmlsZSBkb2VzIG5vdCBleGlzdCwgdGhpcyBpcyBhbiBlcnJvci5cbmV4cG9ydCBpbnRlcmZhY2UgRGVsZXRlRmlsZUFjdGlvbiBleHRlbmRzIEFjdGlvbkJhc2Uge1xuICByZWFkb25seSBraW5kOiAnZCc7XG59XG4iXX0=