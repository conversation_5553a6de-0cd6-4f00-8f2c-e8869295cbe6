{"version": 3, "sources": ["../../../../../../packages/compiler-cli/src/ngtsc/file_system/src/util.ts", "../../../../../../packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.ts", "../../../../../../packages/compiler-cli/src/ngtsc/file_system/src/helpers.ts", "../../../../../../packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.ts", "../../../../../../packages/compiler-cli/src/ngtsc/file_system/src/logical.ts", "../../../../../../packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.ts"], "mappings": ";;;;;;;;;AAUA,IAAM,sBAAsB;AAKtB,SAAU,oBAAoB,MAAY;AAE9C,SAAO,KAAK,QAAQ,OAAO,GAAG;AAChC;AAKM,SAAU,eAAqC,MAAO;AAC1D,SAAO,KAAK,QAAQ,qBAAqB,EAAE;AAC7C;AAEM,SAAU,qBAAqB,SAAqB,UAAwB;AAChF,QAAM,KAAK,QAAQ,cAAc,QAAQ;AACzC,MAAI,OAAO,QAAW;AACpB,UAAM,IAAI,MAAM,6BAA6B,mCACzC,QAAQ,eAAc,EAAG,IAAI,CAAAA,QAAMA,IAAG,QAAQ,EAAE,KAAK,IAAI,GAAG;EAClE;AACA,SAAO;AACT;;;ACjBM,IAAO,oBAAP,MAAwB;EAC5B,OAAO,MAAoB;AACzB,UAAM,UAAS;EACjB;EACA,SAAS,MAAoB;AAC3B,UAAM,UAAS;EACjB;EACA,eAAe,MAAoB;AACjC,UAAM,UAAS;EACjB;EACA,UAAU,MAAsB,MAAyB,WAAmB;AAC1E,UAAM,UAAS;EACjB;EACA,WAAW,MAAoB;AAC7B,UAAM,UAAS;EACjB;EACA,QAAQ,QAAwB,MAAoB;AAClD,UAAM,UAAS;EACjB;EACA,QAAQ,MAAoB;AAC1B,UAAM,UAAS;EACjB;EACA,MAAM,MAAoB;AACxB,UAAM,UAAS;EACjB;EACA,KAAK,MAAoB;AACvB,UAAM,UAAS;EACjB;EACA,MAAG;AACD,UAAM,UAAS;EACjB;EACA,MAAM,MAAoB;AACxB,UAAM,UAAS;EACjB;EACA,QAAQ,MAAgC;AACtC,UAAM,UAAS;EACjB;EACA,SAAS,MAAsB,IAAkB;AAC/C,UAAM,UAAS;EACjB;EACA,SAAS,MAAsB,IAAkB;AAC/C,UAAM,UAAS;EACjB;EACA,UAAU,MAAoB;AAC5B,UAAM,UAAS;EACjB;EACA,WAAW,MAAoB;AAC7B,UAAM,UAAS;EACjB;EACA,kBAAe;AACb,UAAM,UAAS;EACjB;EACA,WAAW,OAAe;AACxB,UAAM,UAAS;EACjB;EACA,QAA8B,MAAO;AACnC,UAAM,UAAS;EACjB;EACA,KAA2B,aAAgB,OAAe;AACxD,UAAM,UAAS;EACjB;EACA,OAAO,MAAoB;AACzB,UAAM,UAAS;EACjB;EACA,SAAS,MAAY;AACnB,UAAM,UAAS;EACjB;EACA,SAA+B,MAAS,IAAK;AAC3C,UAAM,UAAS;EACjB;EACA,SAAS,UAAkB,WAAkB;AAC3C,UAAM,UAAS;EACjB;EACA,SAAS,UAAwB;AAC/B,UAAM,UAAS;EACjB;EACA,wBAAqB;AACnB,UAAM,UAAS;EACjB;EACA,UAAgC,MAAO;AACrC,UAAM,UAAS;EACjB;;AAGF,SAAS,YAAS;AAChB,SAAO,IAAI,MACP,+FAA+F;AACrG;;;AC3FA,IAAI,KAAiB,IAAI,kBAAiB;AACpC,SAAU,gBAAa;AAC3B,SAAO;AACT;AACM,SAAU,cAAc,YAAsB;AAClD,OAAK;AACP;AAKM,SAAU,aAAa,MAAY;AACvC,MAAI,CAAC,GAAG,SAAS,IAAI,GAAG;AACtB,UAAM,IAAI,MAAM,gCAAgC,6BAA6B;EAC/E;AACA,SAAO,GAAG,QAAQ,IAAI;AACxB;AAEA,IAAM,gBAAgB,OAAO,cAAc;AAKrC,SAAU,uBAAuB,IAAsB;AAC3D,QAAM,cAAc;AAEpB,MAAI,YAAY,mBAAmB,QAAW;AAC5C,gBAAY,iBAAiB,GAAG,QAAQ,YAAY,QAAQ;EAC9D;AAIA,SAAO,YAAY;AACrB;AAKM,SAAU,aAAa,MAAY;AACvC,QAAM,aAAa,oBAAoB,IAAI;AAC3C,MAAI,GAAG,SAAS,UAAU,GAAG;AAC3B,UAAM,IAAI,MAAM,gCAAgC,6BAA6B;EAC/E;AACA,SAAO;AACT;AAKM,SAAU,QAA8B,MAAO;AACnD,SAAO,GAAG,QAAQ,IAAI;AACxB;AAKM,SAAU,KAA2B,aAAgB,OAAe;AACxE,SAAO,GAAG,KAAK,UAAU,GAAG,KAAK;AACnC;AAKM,SAAU,QAAQ,aAAqB,OAAe;AAC1D,SAAO,GAAG,QAAQ,UAAU,GAAG,KAAK;AACtC;AAGM,SAAU,OAAO,MAAoB;AACzC,SAAO,GAAG,OAAO,IAAI;AACvB;AAKM,SAAU,SAAS,MAAY;AACnC,SAAO,GAAG,SAAS,IAAI;AACzB;AAKM,SAAU,SAA+B,MAAS,IAAK;AAC3D,SAAO,GAAG,SAAS,MAAM,EAAE;AAC7B;AAKM,SAAU,SAAS,UAAsB,WAAkB;AAC/D,SAAO,GAAG,SAAS,UAAU,SAAS;AACxC;AAQM,SAAU,oBAAoB,cAAoB;AACtD,SAAO,CAAC,SAAS,YAAY,KAAK,CAAC,aAAa,WAAW,IAAI;AACjE;AAOM,SAAU,iBAAiB,cAAwC;AAEvE,SAAO,oBAAoB,YAAY,IAAI,KAAK,iBAAgC;AAClF;;;ACnHA,YAAY,QAAQ;AACpB,OAAO,QAAQ;AAKT,IAAO,oBAAP,MAAwB;EAC5B,YAAsBC,KAA0B,UAA8B,CAAA,GAAE;AAA1D,SAAA,KAAAA;AAA0B,SAAA,UAAA;EAAmC;EAEnF,cAAc,UAAkB,iBAAgC;AAC9D,UAAM,OAAO,KAAK,SAAS,QAAQ;AACnC,WAAO,SAAS,SAAY,GAAG,iBAAiB,UAAU,MAAM,iBAAiB,IAAI,IACzD;EAC9B;EAEA,sBAAsB,SAA2B;AAC/C,WAAO,KAAK,GAAG,KAAK,KAAK,sBAAqB,GAAI,GAAG,sBAAsB,OAAO,CAAC;EACrF;EAEA,wBAAqB;AACnB,WAAO,KAAK,GAAG,sBAAqB;EACtC;EAEA,UACI,UAAkB,MAAc,oBAChC,SACA,aAA0C;AAC5C,UAAM,OAAO,aAAa,QAAQ;AAClC,SAAK,GAAG,UAAU,KAAK,GAAG,QAAQ,IAAI,CAAC;AACvC,SAAK,GAAG,UAAU,MAAM,IAAI;EAC9B;EAEA,sBAAmB;AACjB,WAAO,KAAK,GAAG,IAAG;EACpB;EAEA,qBAAqB,UAAgB;AACnC,WAAO,KAAK,0BAAyB,IAAK,WAAW,SAAS,YAAW;EAC3E;EAEA,4BAAyB;AACvB,WAAO,KAAK,GAAG,gBAAe;EAChC;EAEA,aAAU;AACR,YAAQ,KAAK,QAAQ,SAAS;MAC5B,KAAK,GAAG,YAAY;AAClB,eAAO;MACT,KAAK,GAAG,YAAY;AAClB,eAAO;MACT;AACE,eAAU;IACd;EACF;EAEA,WAAW,UAAgB;AACzB,UAAM,UAAU,KAAK,GAAG,QAAQ,QAAQ;AACxC,WAAO,KAAK,GAAG,OAAO,OAAO,KAAK,KAAK,GAAG,KAAK,OAAO,EAAE,OAAM;EAChE;EAEA,SAAS,UAAgB;AACvB,UAAM,UAAU,KAAK,GAAG,QAAQ,QAAQ;AACxC,QAAI,CAAC,KAAK,WAAW,OAAO,GAAG;AAC7B,aAAO;IACT;AACA,WAAO,KAAK,GAAG,SAAS,OAAO;EACjC;EAEA,SAAS,MAAY;AACnB,WAAO,KAAK,GAAG,SAAS,KAAK,GAAG,QAAQ,IAAI,CAAC;EAC/C;;;;ACxDK,IAAM,qBAAqB;EAOhC,qBAAqB,SAAS,MAA0B,IAAsB;AAC5E,UAAM,eAAe,SAAS,QAAQ,QAAQ,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;AACjE,WAAO,iBAAiB,YAAY;EACtC;;AAOI,IAAO,oBAAP,MAAwB;EAkB5B,YACI,UACQ,cAA2D;AAA3D,SAAA,eAAA;AAJJ,SAAA,QAAsD,oBAAI,IAAG;AAOnE,SAAK,WAAW,SAAS,OAAO,CAAA,CAAE,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AACtE,SAAK,oBACD,KAAK,SAAS,IAAI,SAAO,KAAK,aAAa,qBAAqB,GAAG,CAAmB;EAC5F;EAQA,gBAAgB,IAAiB;AAC/B,WAAO,KAAK,kBAAkB,uBAAuB,EAAE,CAAC;EAC1D;EAQA,kBAAkB,cAA4B;AAC5C,QAAI,CAAC,KAAK,MAAM,IAAI,YAAY,GAAG;AACjC,YAAM,oBACF,KAAK,aAAa,qBAAqB,YAAY;AACvD,UAAI,cAAuC;AAC3C,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,cAAM,UAAU,KAAK,SAAS;AAC9B,cAAM,mBAAmB,KAAK,kBAAkB;AAChD,YAAI,iBAAiB,kBAAkB,iBAAiB,GAAG;AAGzD,wBAAc,KAAK,yBAAyB,cAAc,OAAO;AAEjE,cAAI,YAAY,QAAQ,gBAAgB,MAAM,IAAI;AAChD,0BAAc;UAChB,OAAO;AACL;UACF;QACF;MACF;AACA,WAAK,MAAM,IAAI,cAAc,WAAW;IAC1C;AACA,WAAO,KAAK,MAAM,IAAI,YAAY;EACpC;EAEQ,yBAAyB,MAAsB,SAAuB;AAE5E,UAAM,cAAc,eAAe,KAAK,MAAM,QAAQ,MAAM,CAAC;AAC7D,WAAQ,YAAY,WAAW,GAAG,IAAI,cAAc,MAAM;EAC5D;;AAOF,SAAS,iBAAiB,MAAsB,MAAoB;AAClE,SAAO,oBAAoB,SAAS,MAAM,IAAI,CAAC;AACjD;;;AClHA,OAAOC,SAAQ;AACf,SAAQ,qBAAoB;AAC5B,YAAY,OAAO;AACnB,SAAQ,qBAAoB;AAOtB,IAAO,yBAAP,MAA6B;EACjC,MAAG;AACD,WAAO,KAAK,UAAU,QAAQ,IAAG,CAAE;EACrC;EACA,MAAM,KAAmB;AACvB,YAAQ,MAAM,GAAG;EACnB;EACA,WAAW,OAAe;AACxB,WAAO,KAAK,UAAY,UAAQ,GAAG,KAAK,CAAC;EAC3C;EAEA,QAA0B,MAAO;AAC/B,WAAO,KAAK,UAAY,UAAQ,IAAI,CAAC;EACvC;EACA,KAAuB,aAAgB,OAAe;AACpD,WAAO,KAAK,UAAY,OAAK,UAAU,GAAG,KAAK,CAAC;EAClD;EACA,OAAO,MAAoB;AACzB,WAAO,KAAK,QAAQ,IAAI,MAAM,KAAK,UAAU,IAAI;EACnD;EACA,SAAS,MAAY;AACnB,WAAS,aAAW,IAAI;EAC1B;EACA,SAA+B,MAAS,IAAK;AAC3C,WAAO,KAAK,UAAY,WAAS,MAAM,EAAE,CAAC;EAC5C;EACA,SAAS,UAAkB,WAAkB;AAC3C,WAAS,WAAS,UAAU,SAAS;EACvC;EACA,QAAQ,MAAgC;AACtC,WAAS,UAAQ,IAAI;EACvB;EACA,UAA4B,MAAO;AAEjC,WAAO,KAAK,QAAQ,OAAO,GAAG;EAChC;;AAKF,IAAM,aAAa,OAAO,eAAe;AACzC,IAAM,iBAAiB,aAAa,OAAO,YAAY;AACvD,IAAM,kBAAkB,aAAa,aAAa,cAAc,cAAe;AAKzE,IAAO,2BAAP,cAAwC,uBAAsB;EAApE,cAAA;;AACU,SAAA,iBAAoC;EAmC9C;EAlCE,kBAAe;AACb,QAAI,KAAK,mBAAmB,QAAW;AAGrC,WAAK,iBAAiB,CAACC,IAAG,WAAW,KAAK,UAAU,WAAW,eAAe,CAAC,CAAC;IAClF;AACA,WAAO,KAAK;EACd;EACA,OAAO,MAAoB;AACzB,WAAOA,IAAG,WAAW,IAAI;EAC3B;EACA,SAAS,MAAoB;AAC3B,WAAOA,IAAG,aAAa,MAAM,MAAM;EACrC;EACA,eAAe,MAAoB;AACjC,WAAOA,IAAG,aAAa,IAAI;EAC7B;EACA,QAAQ,MAAoB;AAC1B,WAAOA,IAAG,YAAY,IAAI;EAC5B;EACA,MAAM,MAAoB;AACxB,WAAOA,IAAG,UAAU,IAAI;EAC1B;EACA,KAAK,MAAoB;AACvB,WAAOA,IAAG,SAAS,IAAI;EACzB;EACA,SAAS,MAAoB;AAC3B,WAAO,KAAK,QAAQA,IAAG,aAAa,IAAI,CAAC;EAC3C;EACA,wBAAqB;AAEnB,UAAM,YAAY,aAAa,YAAU,cAAc,cAAe;AACtE,WAAO,KAAK,QAAQ,UAAU,QAAQ,YAAY,GAAG,IAAI;EAC3D;;AAMI,IAAO,mBAAP,cAAgC,yBAAwB;EAC5D,UAAU,MAAsB,MAAyB,YAAqB,OAAK;AACjF,IAAAA,IAAG,cAAc,MAAM,MAAM,YAAY,EAAC,MAAM,KAAI,IAAI,MAAS;EACnE;EACA,WAAW,MAAoB;AAC7B,IAAAA,IAAG,WAAW,IAAI;EACpB;EACA,QAAQ,QAAwB,MAAoB;AAClD,IAAAA,IAAG,YAAY,QAAQ,IAAI;EAC7B;EACA,SAAS,MAAsB,IAAkB;AAC/C,IAAAA,IAAG,aAAa,MAAM,EAAE;EAC1B;EACA,SAAS,MAAsB,IAAkB;AAC/C,IAAAA,IAAG,WAAW,MAAM,EAAE;EACxB;EACA,UAAU,MAAoB;AAC5B,IAAAA,IAAG,UAAU,MAAM,EAAC,WAAW,KAAI,CAAC;EACtC;EACA,WAAW,MAAoB;AAC7B,IAAAA,IAAG,UAAU,MAAM,EAAC,WAAW,KAAI,CAAC;EACtC;;AAMF,SAAS,WAAW,KAAW;AAC7B,SAAO,IAAI,QAAQ,OAAO,QAAM,GAAG,YAAW,MAAO,KAAK,GAAG,YAAW,IAAK,GAAG,YAAW,CAAE;AAC/F;", "names": ["sf", "fs", "fs", "fs"]}