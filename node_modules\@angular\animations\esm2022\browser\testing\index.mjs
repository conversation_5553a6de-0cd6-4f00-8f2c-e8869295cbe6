/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// This file is not used to build this module. It is only used during editing
// by the TypeScript language service and during build for verifcation. `ngc`
// replaces this file with production index.ts when it rewrites private symbol
// names.
export * from './public_api';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9hbmltYXRpb25zL2Jyb3dzZXIvdGVzdGluZy9pbmRleC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCw2RUFBNkU7QUFDN0UsNkVBQTZFO0FBQzdFLDhFQUE4RTtBQUM5RSxTQUFTO0FBRVQsY0FBYyxjQUFjLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuLy8gVGhpcyBmaWxlIGlzIG5vdCB1c2VkIHRvIGJ1aWxkIHRoaXMgbW9kdWxlLiBJdCBpcyBvbmx5IHVzZWQgZHVyaW5nIGVkaXRpbmdcbi8vIGJ5IHRoZSBUeXBlU2NyaXB0IGxhbmd1YWdlIHNlcnZpY2UgYW5kIGR1cmluZyBidWlsZCBmb3IgdmVyaWZjYXRpb24uIGBuZ2NgXG4vLyByZXBsYWNlcyB0aGlzIGZpbGUgd2l0aCBwcm9kdWN0aW9uIGluZGV4LnRzIHdoZW4gaXQgcmV3cml0ZXMgcHJpdmF0ZSBzeW1ib2xcbi8vIG5hbWVzLlxuXG5leHBvcnQgKiBmcm9tICcuL3B1YmxpY19hcGknO1xuIl19