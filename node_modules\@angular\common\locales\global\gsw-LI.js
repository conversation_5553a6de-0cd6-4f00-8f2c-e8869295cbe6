/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['gsw-li'] = ["gsw-LI",[["vorm.","nam."],u,["am Vormittag","am Namittag"]],[["vorm.","nam."],u,["Vormittag","Namittag"]],[["S","M","D","M","D","F","S"],["Su.","Mä.","Zi.","<PERSON>.","<PERSON><PERSON>","<PERSON>.","<PERSON>."],["<PERSON>ntig","<PERSON><PERSON><PERSON>ntig","<PERSON>iischtig","<PERSON>ttwu<PERSON>","Dunschtig","<PERSON>iitig","Samschtig"],["Su.","<PERSON>ä.","Zi.","<PERSON>.","Du.","<PERSON>.","<PERSON>."]],u,[["J","F","M","A","M","J","J","A","<PERSON>","O","N","<PERSON>"],["<PERSON>","<PERSON>","M<PERSON>r","Apr","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>t","Nov","Dez"],["Januar","Februar","März","April","Mai","Juni","Juli","Auguscht","Septämber","Oktoober","Novämber","Dezämber"]],u,[["v. Chr.","n. Chr."],u,u],1,[6,0],["dd.MM.yy","dd.MM.y","d. MMMM y","EEEE, d. MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".","’",";","%","+","−","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0 %","#,##0.00 ¤","#E0"],"CHF","CHF","Schwiizer Franke",{"ATS":["öS"]},"ltr", plural, [[["Mitternacht","am Morge","zmittag","am Namittag","zaabig","znacht"],u,u],[["Mitternacht","am Morge","zmittag","am Namittag","zaabig","znacht"],u,["Mitternacht","Morge","Mittag","Namittag","Aabig","Nacht"]],["00:00",["05:00","12:00"],["12:00","14:00"],["14:00","18:00"],["18:00","24:00"],["00:00","05:00"]]]];
  })(globalThis);
    