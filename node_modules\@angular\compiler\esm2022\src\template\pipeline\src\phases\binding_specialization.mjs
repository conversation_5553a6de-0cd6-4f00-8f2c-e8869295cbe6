/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { splitNsName } from '../../../../ml_parser/tags';
import * as o from '../../../../output/output_ast';
import * as ir from '../../ir';
import { CompilationJobKind } from '../compilation';
/**
 * Looks up an element in the given map by xref ID.
 */
function lookupElement(elements, xref) {
    const el = elements.get(xref);
    if (el === undefined) {
        throw new Error('All attributes should have an element-like target.');
    }
    return el;
}
export function specializeBindings(job) {
    const elements = new Map();
    for (const unit of job.units) {
        for (const op of unit.create) {
            if (!ir.isElementOrContainerOp(op)) {
                continue;
            }
            elements.set(op.xref, op);
        }
    }
    for (const unit of job.units) {
        for (const op of unit.ops()) {
            if (op.kind !== ir.OpKind.Binding) {
                continue;
            }
            switch (op.bindingKind) {
                case ir.BindingKind.Attribute:
                    if (op.name === 'ngNonBindable') {
                        ir.OpList.remove(op);
                        const target = lookupElement(elements, op.target);
                        target.nonBindable = true;
                    }
                    else {
                        const [namespace, name] = splitNsName(op.name);
                        ir.OpList.replace(op, ir.createAttributeOp(op.target, namespace, name, op.expression, op.securityContext, op.isTextAttribute, op.isStructuralTemplateAttribute, op.templateKind, op.i18nMessage, op.sourceSpan));
                    }
                    break;
                case ir.BindingKind.Property:
                case ir.BindingKind.Animation:
                    if (job.kind === CompilationJobKind.Host) {
                        ir.OpList.replace(op, ir.createHostPropertyOp(op.name, op.expression, op.bindingKind === ir.BindingKind.Animation, op.i18nContext, op.securityContext, op.sourceSpan));
                    }
                    else {
                        ir.OpList.replace(op, ir.createPropertyOp(op.target, op.name, op.expression, op.bindingKind === ir.BindingKind.Animation, op.securityContext, op.isStructuralTemplateAttribute, op.templateKind, op.i18nContext, op.i18nMessage, op.sourceSpan));
                    }
                    break;
                case ir.BindingKind.TwoWayProperty:
                    if (!(op.expression instanceof o.Expression)) {
                        // We shouldn't be able to hit this code path since interpolations in two-way bindings
                        // result in a parser error. We assert here so that downstream we can assume that
                        // the value is always an expression.
                        throw new Error(`Expected value of two-way property binding "${op.name}" to be an expression`);
                    }
                    ir.OpList.replace(op, ir.createTwoWayPropertyOp(op.target, op.name, op.expression, op.securityContext, op.isStructuralTemplateAttribute, op.templateKind, op.i18nContext, op.i18nMessage, op.sourceSpan));
                    break;
                case ir.BindingKind.I18n:
                case ir.BindingKind.ClassName:
                case ir.BindingKind.StyleProperty:
                    throw new Error(`Unhandled binding of kind ${ir.BindingKind[op.bindingKind]}`);
            }
        }
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmluZGluZ19zcGVjaWFsaXphdGlvbi5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvbXBpbGVyL3NyYy90ZW1wbGF0ZS9waXBlbGluZS9zcmMvcGhhc2VzL2JpbmRpbmdfc3BlY2lhbGl6YXRpb24udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsT0FBTyxFQUFDLFdBQVcsRUFBQyxNQUFNLDRCQUE0QixDQUFDO0FBQ3ZELE9BQU8sS0FBSyxDQUFDLE1BQU0sK0JBQStCLENBQUM7QUFDbkQsT0FBTyxLQUFLLEVBQUUsTUFBTSxVQUFVLENBQUM7QUFDL0IsT0FBTyxFQUFpQixrQkFBa0IsRUFBQyxNQUFNLGdCQUFnQixDQUFDO0FBRWxFOztHQUVHO0FBQ0gsU0FBUyxhQUFhLENBQ2xCLFFBQWtELEVBQUUsSUFBZTtJQUNyRSxNQUFNLEVBQUUsR0FBRyxRQUFRLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQzlCLElBQUksRUFBRSxLQUFLLFNBQVMsRUFBRSxDQUFDO1FBQ3JCLE1BQU0sSUFBSSxLQUFLLENBQUMsb0RBQW9ELENBQUMsQ0FBQztJQUN4RSxDQUFDO0lBQ0QsT0FBTyxFQUFFLENBQUM7QUFDWixDQUFDO0FBRUQsTUFBTSxVQUFVLGtCQUFrQixDQUFDLEdBQW1CO0lBQ3BELE1BQU0sUUFBUSxHQUFHLElBQUksR0FBRyxFQUF1QyxDQUFDO0lBQ2hFLEtBQUssTUFBTSxJQUFJLElBQUksR0FBRyxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQzdCLEtBQUssTUFBTSxFQUFFLElBQUksSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQzdCLElBQUksQ0FBQyxFQUFFLENBQUMsc0JBQXNCLENBQUMsRUFBRSxDQUFDLEVBQUUsQ0FBQztnQkFDbkMsU0FBUztZQUNYLENBQUM7WUFDRCxRQUFRLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUM7UUFDNUIsQ0FBQztJQUNILENBQUM7SUFFRCxLQUFLLE1BQU0sSUFBSSxJQUFJLEdBQUcsQ0FBQyxLQUFLLEVBQUUsQ0FBQztRQUM3QixLQUFLLE1BQU0sRUFBRSxJQUFJLElBQUksQ0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDO1lBQzVCLElBQUksRUFBRSxDQUFDLElBQUksS0FBSyxFQUFFLENBQUMsTUFBTSxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUNsQyxTQUFTO1lBQ1gsQ0FBQztZQUNELFFBQVEsRUFBRSxDQUFDLFdBQVcsRUFBRSxDQUFDO2dCQUN2QixLQUFLLEVBQUUsQ0FBQyxXQUFXLENBQUMsU0FBUztvQkFDM0IsSUFBSSxFQUFFLENBQUMsSUFBSSxLQUFLLGVBQWUsRUFBRSxDQUFDO3dCQUNoQyxFQUFFLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBYyxFQUFFLENBQUMsQ0FBQzt3QkFDbEMsTUFBTSxNQUFNLEdBQUcsYUFBYSxDQUFDLFFBQVEsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUM7d0JBQ2xELE1BQU0sQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDO29CQUM1QixDQUFDO3lCQUFNLENBQUM7d0JBQ04sTUFBTSxDQUFDLFNBQVMsRUFBRSxJQUFJLENBQUMsR0FBRyxXQUFXLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDO3dCQUMvQyxFQUFFLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FDYixFQUFFLEVBQ0YsRUFBRSxDQUFDLGlCQUFpQixDQUNoQixFQUFFLENBQUMsTUFBTSxFQUFFLFNBQVMsRUFBRSxJQUFJLEVBQUUsRUFBRSxDQUFDLFVBQVUsRUFBRSxFQUFFLENBQUMsZUFBZSxFQUM3RCxFQUFFLENBQUMsZUFBZSxFQUFFLEVBQUUsQ0FBQyw2QkFBNkIsRUFBRSxFQUFFLENBQUMsWUFBWSxFQUNyRSxFQUFFLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDO29CQUMxQyxDQUFDO29CQUNELE1BQU07Z0JBQ1IsS0FBSyxFQUFFLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQztnQkFDN0IsS0FBSyxFQUFFLENBQUMsV0FBVyxDQUFDLFNBQVM7b0JBQzNCLElBQUksR0FBRyxDQUFDLElBQUksS0FBSyxrQkFBa0IsQ0FBQyxJQUFJLEVBQUUsQ0FBQzt3QkFDekMsRUFBRSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQ2IsRUFBRSxFQUNGLEVBQUUsQ0FBQyxvQkFBb0IsQ0FDbkIsRUFBRSxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsVUFBVSxFQUFFLEVBQUUsQ0FBQyxXQUFXLEtBQUssRUFBRSxDQUFDLFdBQVcsQ0FBQyxTQUFTLEVBQ25FLEVBQUUsQ0FBQyxXQUFXLEVBQUUsRUFBRSxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQztvQkFDOUQsQ0FBQzt5QkFBTSxDQUFDO3dCQUNOLEVBQUUsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUNiLEVBQUUsRUFDRixFQUFFLENBQUMsZ0JBQWdCLENBQ2YsRUFBRSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLFdBQVcsS0FBSyxFQUFFLENBQUMsV0FBVyxDQUFDLFNBQVMsRUFDOUUsRUFBRSxDQUFDLGVBQWUsRUFBRSxFQUFFLENBQUMsNkJBQTZCLEVBQUUsRUFBRSxDQUFDLFlBQVksRUFDckUsRUFBRSxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDO29CQUMxRCxDQUFDO29CQUVELE1BQU07Z0JBQ1IsS0FBSyxFQUFFLENBQUMsV0FBVyxDQUFDLGNBQWM7b0JBQ2hDLElBQUksQ0FBQyxDQUFDLEVBQUUsQ0FBQyxVQUFVLFlBQVksQ0FBQyxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUM7d0JBQzdDLHNGQUFzRjt3QkFDdEYsaUZBQWlGO3dCQUNqRixxQ0FBcUM7d0JBQ3JDLE1BQU0sSUFBSSxLQUFLLENBQ1gsK0NBQStDLEVBQUUsQ0FBQyxJQUFJLHVCQUF1QixDQUFDLENBQUM7b0JBQ3JGLENBQUM7b0JBRUQsRUFBRSxDQUFDLE1BQU0sQ0FBQyxPQUFPLENBQ2IsRUFBRSxFQUNGLEVBQUUsQ0FBQyxzQkFBc0IsQ0FDckIsRUFBRSxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQyxVQUFVLEVBQUUsRUFBRSxDQUFDLGVBQWUsRUFDckQsRUFBRSxDQUFDLDZCQUE2QixFQUFFLEVBQUUsQ0FBQyxZQUFZLEVBQUUsRUFBRSxDQUFDLFdBQVcsRUFBRSxFQUFFLENBQUMsV0FBVyxFQUNqRixFQUFFLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQztvQkFDeEIsTUFBTTtnQkFDUixLQUFLLEVBQUUsQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDO2dCQUN6QixLQUFLLEVBQUUsQ0FBQyxXQUFXLENBQUMsU0FBUyxDQUFDO2dCQUM5QixLQUFLLEVBQUUsQ0FBQyxXQUFXLENBQUMsYUFBYTtvQkFDL0IsTUFBTSxJQUFJLEtBQUssQ0FBQyw2QkFBNkIsRUFBRSxDQUFDLFdBQVcsQ0FBQyxFQUFFLENBQUMsV0FBVyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ25GLENBQUM7UUFDSCxDQUFDO0lBQ0gsQ0FBQztBQUNILENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHtzcGxpdE5zTmFtZX0gZnJvbSAnLi4vLi4vLi4vLi4vbWxfcGFyc2VyL3RhZ3MnO1xuaW1wb3J0ICogYXMgbyBmcm9tICcuLi8uLi8uLi8uLi9vdXRwdXQvb3V0cHV0X2FzdCc7XG5pbXBvcnQgKiBhcyBpciBmcm9tICcuLi8uLi9pcic7XG5pbXBvcnQge0NvbXBpbGF0aW9uSm9iLCBDb21waWxhdGlvbkpvYktpbmR9IGZyb20gJy4uL2NvbXBpbGF0aW9uJztcblxuLyoqXG4gKiBMb29rcyB1cCBhbiBlbGVtZW50IGluIHRoZSBnaXZlbiBtYXAgYnkgeHJlZiBJRC5cbiAqL1xuZnVuY3Rpb24gbG9va3VwRWxlbWVudChcbiAgICBlbGVtZW50czogTWFwPGlyLlhyZWZJZCwgaXIuRWxlbWVudE9yQ29udGFpbmVyT3BzPiwgeHJlZjogaXIuWHJlZklkKTogaXIuRWxlbWVudE9yQ29udGFpbmVyT3BzIHtcbiAgY29uc3QgZWwgPSBlbGVtZW50cy5nZXQoeHJlZik7XG4gIGlmIChlbCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdBbGwgYXR0cmlidXRlcyBzaG91bGQgaGF2ZSBhbiBlbGVtZW50LWxpa2UgdGFyZ2V0LicpO1xuICB9XG4gIHJldHVybiBlbDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHNwZWNpYWxpemVCaW5kaW5ncyhqb2I6IENvbXBpbGF0aW9uSm9iKTogdm9pZCB7XG4gIGNvbnN0IGVsZW1lbnRzID0gbmV3IE1hcDxpci5YcmVmSWQsIGlyLkVsZW1lbnRPckNvbnRhaW5lck9wcz4oKTtcbiAgZm9yIChjb25zdCB1bml0IG9mIGpvYi51bml0cykge1xuICAgIGZvciAoY29uc3Qgb3Agb2YgdW5pdC5jcmVhdGUpIHtcbiAgICAgIGlmICghaXIuaXNFbGVtZW50T3JDb250YWluZXJPcChvcCkpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgICBlbGVtZW50cy5zZXQob3AueHJlZiwgb3ApO1xuICAgIH1cbiAgfVxuXG4gIGZvciAoY29uc3QgdW5pdCBvZiBqb2IudW5pdHMpIHtcbiAgICBmb3IgKGNvbnN0IG9wIG9mIHVuaXQub3BzKCkpIHtcbiAgICAgIGlmIChvcC5raW5kICE9PSBpci5PcEtpbmQuQmluZGluZykge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIHN3aXRjaCAob3AuYmluZGluZ0tpbmQpIHtcbiAgICAgICAgY2FzZSBpci5CaW5kaW5nS2luZC5BdHRyaWJ1dGU6XG4gICAgICAgICAgaWYgKG9wLm5hbWUgPT09ICduZ05vbkJpbmRhYmxlJykge1xuICAgICAgICAgICAgaXIuT3BMaXN0LnJlbW92ZTxpci5VcGRhdGVPcD4ob3ApO1xuICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gbG9va3VwRWxlbWVudChlbGVtZW50cywgb3AudGFyZ2V0KTtcbiAgICAgICAgICAgIHRhcmdldC5ub25CaW5kYWJsZSA9IHRydWU7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IFtuYW1lc3BhY2UsIG5hbWVdID0gc3BsaXROc05hbWUob3AubmFtZSk7XG4gICAgICAgICAgICBpci5PcExpc3QucmVwbGFjZTxpci5VcGRhdGVPcD4oXG4gICAgICAgICAgICAgICAgb3AsXG4gICAgICAgICAgICAgICAgaXIuY3JlYXRlQXR0cmlidXRlT3AoXG4gICAgICAgICAgICAgICAgICAgIG9wLnRhcmdldCwgbmFtZXNwYWNlLCBuYW1lLCBvcC5leHByZXNzaW9uLCBvcC5zZWN1cml0eUNvbnRleHQsXG4gICAgICAgICAgICAgICAgICAgIG9wLmlzVGV4dEF0dHJpYnV0ZSwgb3AuaXNTdHJ1Y3R1cmFsVGVtcGxhdGVBdHRyaWJ1dGUsIG9wLnRlbXBsYXRlS2luZCxcbiAgICAgICAgICAgICAgICAgICAgb3AuaTE4bk1lc3NhZ2UsIG9wLnNvdXJjZVNwYW4pKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgaXIuQmluZGluZ0tpbmQuUHJvcGVydHk6XG4gICAgICAgIGNhc2UgaXIuQmluZGluZ0tpbmQuQW5pbWF0aW9uOlxuICAgICAgICAgIGlmIChqb2Iua2luZCA9PT0gQ29tcGlsYXRpb25Kb2JLaW5kLkhvc3QpIHtcbiAgICAgICAgICAgIGlyLk9wTGlzdC5yZXBsYWNlPGlyLlVwZGF0ZU9wPihcbiAgICAgICAgICAgICAgICBvcCxcbiAgICAgICAgICAgICAgICBpci5jcmVhdGVIb3N0UHJvcGVydHlPcChcbiAgICAgICAgICAgICAgICAgICAgb3AubmFtZSwgb3AuZXhwcmVzc2lvbiwgb3AuYmluZGluZ0tpbmQgPT09IGlyLkJpbmRpbmdLaW5kLkFuaW1hdGlvbixcbiAgICAgICAgICAgICAgICAgICAgb3AuaTE4bkNvbnRleHQsIG9wLnNlY3VyaXR5Q29udGV4dCwgb3Auc291cmNlU3BhbikpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBpci5PcExpc3QucmVwbGFjZTxpci5VcGRhdGVPcD4oXG4gICAgICAgICAgICAgICAgb3AsXG4gICAgICAgICAgICAgICAgaXIuY3JlYXRlUHJvcGVydHlPcChcbiAgICAgICAgICAgICAgICAgICAgb3AudGFyZ2V0LCBvcC5uYW1lLCBvcC5leHByZXNzaW9uLCBvcC5iaW5kaW5nS2luZCA9PT0gaXIuQmluZGluZ0tpbmQuQW5pbWF0aW9uLFxuICAgICAgICAgICAgICAgICAgICBvcC5zZWN1cml0eUNvbnRleHQsIG9wLmlzU3RydWN0dXJhbFRlbXBsYXRlQXR0cmlidXRlLCBvcC50ZW1wbGF0ZUtpbmQsXG4gICAgICAgICAgICAgICAgICAgIG9wLmkxOG5Db250ZXh0LCBvcC5pMThuTWVzc2FnZSwgb3Auc291cmNlU3BhbikpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIGlyLkJpbmRpbmdLaW5kLlR3b1dheVByb3BlcnR5OlxuICAgICAgICAgIGlmICghKG9wLmV4cHJlc3Npb24gaW5zdGFuY2VvZiBvLkV4cHJlc3Npb24pKSB7XG4gICAgICAgICAgICAvLyBXZSBzaG91bGRuJ3QgYmUgYWJsZSB0byBoaXQgdGhpcyBjb2RlIHBhdGggc2luY2UgaW50ZXJwb2xhdGlvbnMgaW4gdHdvLXdheSBiaW5kaW5nc1xuICAgICAgICAgICAgLy8gcmVzdWx0IGluIGEgcGFyc2VyIGVycm9yLiBXZSBhc3NlcnQgaGVyZSBzbyB0aGF0IGRvd25zdHJlYW0gd2UgY2FuIGFzc3VtZSB0aGF0XG4gICAgICAgICAgICAvLyB0aGUgdmFsdWUgaXMgYWx3YXlzIGFuIGV4cHJlc3Npb24uXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICAgICAgYEV4cGVjdGVkIHZhbHVlIG9mIHR3by13YXkgcHJvcGVydHkgYmluZGluZyBcIiR7b3AubmFtZX1cIiB0byBiZSBhbiBleHByZXNzaW9uYCk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaXIuT3BMaXN0LnJlcGxhY2U8aXIuVXBkYXRlT3A+KFxuICAgICAgICAgICAgICBvcCxcbiAgICAgICAgICAgICAgaXIuY3JlYXRlVHdvV2F5UHJvcGVydHlPcChcbiAgICAgICAgICAgICAgICAgIG9wLnRhcmdldCwgb3AubmFtZSwgb3AuZXhwcmVzc2lvbiwgb3Auc2VjdXJpdHlDb250ZXh0LFxuICAgICAgICAgICAgICAgICAgb3AuaXNTdHJ1Y3R1cmFsVGVtcGxhdGVBdHRyaWJ1dGUsIG9wLnRlbXBsYXRlS2luZCwgb3AuaTE4bkNvbnRleHQsIG9wLmkxOG5NZXNzYWdlLFxuICAgICAgICAgICAgICAgICAgb3Auc291cmNlU3BhbikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIGlyLkJpbmRpbmdLaW5kLkkxOG46XG4gICAgICAgIGNhc2UgaXIuQmluZGluZ0tpbmQuQ2xhc3NOYW1lOlxuICAgICAgICBjYXNlIGlyLkJpbmRpbmdLaW5kLlN0eWxlUHJvcGVydHk6XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbmhhbmRsZWQgYmluZGluZyBvZiBraW5kICR7aXIuQmluZGluZ0tpbmRbb3AuYmluZGluZ0tpbmRdfWApO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuIl19