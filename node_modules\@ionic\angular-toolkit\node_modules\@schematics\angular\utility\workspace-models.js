"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Builders = exports.ProjectType = void 0;
var ProjectType;
(function (ProjectType) {
    ProjectType["Application"] = "application";
    ProjectType["Library"] = "library";
})(ProjectType = exports.ProjectType || (exports.ProjectType = {}));
/**
 * An enum of the official Angular builders.
 * Each enum value provides the fully qualified name of the associated builder.
 * This enum can be used when analyzing the `builder` fields of project configurations from the
 * `angular.json` workspace file.
 */
var Builders;
(function (Builders) {
    Builders["AppShell"] = "@angular-devkit/build-angular:app-shell";
    Builders["Server"] = "@angular-devkit/build-angular:server";
    Builders["Browser"] = "@angular-devkit/build-angular:browser";
    Builders["Karma"] = "@angular-devkit/build-angular:karma";
    Builders["TsLint"] = "@angular-devkit/build-angular:tslint";
    Builders["DeprecatedNgPackagr"] = "@angular-devkit/build-ng-packagr:build";
    Builders["NgPackagr"] = "@angular-devkit/build-angular:ng-packagr";
    Builders["DevServer"] = "@angular-devkit/build-angular:dev-server";
    Builders["ExtractI18n"] = "@angular-devkit/build-angular:extract-i18n";
    Builders["Protractor"] = "@angular-devkit/build-angular:protractor";
})(Builders = exports.Builders || (exports.Builders = {}));
//# sourceMappingURL=data:application/json;base64,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