/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ɵRuntimeError as RuntimeError } from '@angular/core';
import { formArrayNameExample, formControlNameExample, formGroupNameExample, ngModelGroupExample } from './error_examples';
export function controlParentException() {
    return new RuntimeError(1050 /* RuntimeErrorCode.FORM_CONTROL_NAME_MISSING_PARENT */, `formControlName must be used with a parent formGroup directive.  You'll want to add a formGroup
      directive and pass it an existing FormGroup instance (you can create one in your class).

    Example:

    ${formControlNameExample}`);
}
export function ngModelGroupException() {
    return new RuntimeError(1051 /* RuntimeErrorCode.FORM_CONTROL_NAME_INSIDE_MODEL_GROUP */, `formControlName cannot be used with an ngModelGroup parent. It is only compatible with parents
      that also have a "form" prefix: formGroup<PERSON>ame, formArrayName, or formGroup.

      Option 1:  Update the parent to be formGroupName (reactive form strategy)

      ${formGroupNameExample}

      Option 2: Use ngModel instead of formControlName (template-driven strategy)

      ${ngModelGroupExample}`);
}
export function missingFormException() {
    return new RuntimeError(1052 /* RuntimeErrorCode.FORM_GROUP_MISSING_INSTANCE */, `formGroup expects a FormGroup instance. Please pass one in.

      Example:

      ${formControlNameExample}`);
}
export function groupParentException() {
    return new RuntimeError(1053 /* RuntimeErrorCode.FORM_GROUP_NAME_MISSING_PARENT */, `formGroupName must be used with a parent formGroup directive.  You'll want to add a formGroup
    directive and pass it an existing FormGroup instance (you can create one in your class).

    Example:

    ${formGroupNameExample}`);
}
export function arrayParentException() {
    return new RuntimeError(1054 /* RuntimeErrorCode.FORM_ARRAY_NAME_MISSING_PARENT */, `formArrayName must be used with a parent formGroup directive.  You'll want to add a formGroup
      directive and pass it an existing FormGroup instance (you can create one in your class).

      Example:

      ${formArrayNameExample}`);
}
export const disabledAttrWarning = `
  It looks like you're using the disabled attribute with a reactive form directive. If you set disabled to true
  when you set up this control in your component class, the disabled attribute will actually be set in the DOM for
  you. We recommend using this approach to avoid 'changed after checked' errors.

  Example:
  // Specify the \`disabled\` property at control creation time:
  form = new FormGroup({
    first: new FormControl({value: 'Nancy', disabled: true}, Validators.required),
    last: new FormControl('Drew', Validators.required)
  });

  // Controls can also be enabled/disabled after creation:
  form.get('first')?.enable();
  form.get('last')?.disable();
`;
export const asyncValidatorsDroppedWithOptsWarning = `
  It looks like you're constructing using a FormControl with both an options argument and an
  async validators argument. Mixing these arguments will cause your async validators to be dropped.
  You should either put all your validators in the options object, or in separate validators
  arguments. For example:

  // Using validators arguments
  fc = new FormControl(42, Validators.required, myAsyncValidator);

  // Using AbstractControlOptions
  fc = new FormControl(42, {validators: Validators.required, asyncValidators: myAV});

  // Do NOT mix them: async validators will be dropped!
  fc = new FormControl(42, {validators: Validators.required}, /* Oops! */ myAsyncValidator);
`;
export function ngModelWarning(directiveName) {
    return `
  It looks like you're using ngModel on the same form field as ${directiveName}.
  Support for using the ngModel input property and ngModelChange event with
  reactive form directives has been deprecated in Angular v6 and will be removed
  in a future version of Angular.

  For more information on this, see our API docs here:
  https://angular.io/api/forms/${directiveName === 'formControl' ? 'FormControlDirective' : 'FormControlName'}#use-with-ngmodel
  `;
}
function describeKey(isFormGroup, key) {
    return isFormGroup ? `with name: '${key}'` : `at index: ${key}`;
}
export function noControlsError(isFormGroup) {
    return `
    There are no form controls registered with this ${isFormGroup ? 'group' : 'array'} yet. If you're using ngModel,
    you may want to check next tick (e.g. use setTimeout).
  `;
}
export function missingControlError(isFormGroup, key) {
    return `Cannot find form control ${describeKey(isFormGroup, key)}`;
}
export function missingControlValueError(isFormGroup, key) {
    return `Must supply a value for form control ${describeKey(isFormGroup, key)}`;
}
//# sourceMappingURL=data:application/json;base64,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