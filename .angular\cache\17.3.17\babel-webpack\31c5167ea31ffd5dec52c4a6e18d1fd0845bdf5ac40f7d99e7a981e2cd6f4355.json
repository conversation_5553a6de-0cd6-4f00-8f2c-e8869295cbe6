{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { FormsModule } from '@angular/forms';\nimport { HomePage } from './home.page';\nimport { HomePageRoutingModule } from './home-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class HomePageModule {\n  static {\n    this.ɵfac = function HomePageModule_Factory(t) {\n      return new (t || HomePageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomePageModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, IonicModule, HomePageRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomePageModule, {\n    declarations: [HomePage],\n    imports: [CommonModule, FormsModule, IonicModule, HomePageRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "FormsModule", "HomePage", "HomePageRoutingModule", "HomePageModule", "declarations", "imports"], "sources": ["D:\\2025\\agmuicon\\src\\app\\home\\home.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { FormsModule } from '@angular/forms';\nimport { HomePage } from './home.page';\n\nimport { HomePageRoutingModule } from './home-routing.module';\n\n\n@NgModule({\n  imports: [\n    CommonModule,\n    FormsModule,\n    IonicModule,\n    HomePageRoutingModule\n  ],\n  declarations: [HomePage]\n})\nexport class HomePageModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,QAAQ,aAAa;AAEtC,SAASC,qBAAqB,QAAQ,uBAAuB;;AAY7D,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;gBAPvBL,YAAY,EACZE,WAAW,EACXD,WAAW,EACXG,qBAAqB;IAAA;EAAA;;;2EAIZC,cAAc;IAAAC,YAAA,GAFVH,QAAQ;IAAAI,OAAA,GALrBP,YAAY,EACZE,WAAW,EACXD,WAAW,EACXG,qBAAqB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}