"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.HostSink = void 0;
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const update_buffer_1 = require("../utility/update-buffer");
const sink_1 = require("./sink");
class HostSink extends sink_1.SimpleSinkBase {
    constructor(_host, _force = false) {
        super();
        this._host = _host;
        this._force = _force;
        this._filesToDelete = new Set();
        this._filesToRename = new Set();
        this._filesToCreate = new Map();
        this._filesToUpdate = new Map();
    }
    _validateCreateAction(action) {
        return this._force ? rxjs_1.EMPTY : super._validateCreateAction(action);
    }
    _validateFileExists(p) {
        if (this._filesToCreate.has(p) || this._filesToUpdate.has(p)) {
            return (0, rxjs_1.of)(true);
        }
        if (this._filesToDelete.has(p)) {
            return (0, rxjs_1.of)(false);
        }
        for (const [from, to] of this._filesToRename.values()) {
            switch (p) {
                case from:
                    return (0, rxjs_1.of)(false);
                case to:
                    return (0, rxjs_1.of)(true);
            }
        }
        return this._host.exists(p);
    }
    _overwriteFile(path, content) {
        this._filesToUpdate.set(path, update_buffer_1.UpdateBufferBase.create(content));
        return rxjs_1.EMPTY;
    }
    _createFile(path, content) {
        this._filesToCreate.set(path, update_buffer_1.UpdateBufferBase.create(content));
        return rxjs_1.EMPTY;
    }
    _renameFile(from, to) {
        this._filesToRename.add([from, to]);
        return rxjs_1.EMPTY;
    }
    _deleteFile(path) {
        if (this._filesToCreate.has(path)) {
            this._filesToCreate.delete(path);
            this._filesToUpdate.delete(path);
        }
        else {
            this._filesToDelete.add(path);
        }
        return rxjs_1.EMPTY;
    }
    _done() {
        // Really commit everything to the actual filesystem.
        return (0, rxjs_1.concat)((0, rxjs_1.from)([...this._filesToDelete.values()]).pipe((0, operators_1.concatMap)((path) => this._host.delete(path))), (0, rxjs_1.from)([...this._filesToRename.entries()]).pipe((0, operators_1.concatMap)(([_, [path, to]]) => this._host.rename(path, to))), (0, rxjs_1.from)([...this._filesToCreate.entries()]).pipe((0, operators_1.concatMap)(([path, buffer]) => {
            return this._host.write(path, buffer.generate());
        })), (0, rxjs_1.from)([...this._filesToUpdate.entries()]).pipe((0, operators_1.concatMap)(([path, buffer]) => {
            return this._host.write(path, buffer.generate());
        }))).pipe((0, operators_1.reduce)(() => { }));
    }
}
exports.HostSink = HostSink;
//# sourceMappingURL=data:application/json;base64,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