/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { OpKind } from '../enums';
import { TRAIT_CONSUMES_VARS } from '../traits';
import { NEW_OP } from './shared';
export function createHostPropertyOp(name, expression, isAnimationTrigger, i18nContext, securityContext, sourceSpan) {
    return {
        kind: OpKind.HostProperty,
        name,
        expression,
        isAnimationTrigger,
        i18nContext,
        securityContext,
        sanitizer: null,
        sourceSpan,
        ...TRAIT_CONSUMES_VARS,
        ...NEW_OP,
    };
}
//# sourceMappingURL=data:application/json;base64,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