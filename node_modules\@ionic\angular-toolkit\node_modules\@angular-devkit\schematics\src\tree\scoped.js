"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScopedTree = void 0;
const core_1 = require("@angular-devkit/core");
const delegate_1 = require("./delegate");
const interface_1 = require("./interface");
class ScopedFileEntry {
    constructor(_base, scope) {
        this._base = _base;
        this.scope = scope;
    }
    get path() {
        return (0, core_1.join)(core_1.NormalizedRoot, (0, core_1.relative)(this.scope, this._base.path));
    }
    get content() {
        return this._base.content;
    }
}
class ScopedDirEntry {
    constructor(_base, scope) {
        this._base = _base;
        this.scope = scope;
    }
    get parent() {
        if (!this._base.parent || this._base.path == this.scope) {
            return null;
        }
        return new ScopedDirEntry(this._base.parent, this.scope);
    }
    get path() {
        return (0, core_1.join)(core_1.NormalizedRoot, (0, core_1.relative)(this.scope, this._base.path));
    }
    get subdirs() {
        return this._base.subdirs;
    }
    get subfiles() {
        return this._base.subfiles;
    }
    dir(name) {
        const entry = this._base.dir(name);
        return entry && new ScopedDirEntry(entry, this.scope);
    }
    file(name) {
        const entry = this._base.file(name);
        return entry && new ScopedFileEntry(entry, this.scope);
    }
    visit(visitor) {
        return this._base.visit((path, entry) => {
            visitor((0, core_1.join)(core_1.NormalizedRoot, (0, core_1.relative)(this.scope, path)), entry && new ScopedFileEntry(entry, this.scope));
        });
    }
}
class ScopedTree {
    constructor(_base, scope) {
        this._base = _base;
        const normalizedScope = (0, core_1.normalize)('/' + scope);
        this._root = new ScopedDirEntry(this._base.getDir(normalizedScope), normalizedScope);
    }
    get root() {
        return this._root;
    }
    branch() {
        return new ScopedTree(this._base.branch(), this._root.scope);
    }
    merge(other, strategy) {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const self = this;
        const delegate = new (class extends delegate_1.DelegateTree {
            get actions() {
                return other.actions.map((action) => self._fullPathAction(action));
            }
        })(other);
        this._base.merge(delegate, strategy);
    }
    // Readonly.
    read(path) {
        return this._base.read(this._fullPath(path));
    }
    readText(path) {
        return this._base.readText(this._fullPath(path));
    }
    readJson(path) {
        return this._base.readJson(this._fullPath(path));
    }
    exists(path) {
        return this._base.exists(this._fullPath(path));
    }
    get(path) {
        const entry = this._base.get(this._fullPath(path));
        return entry && new ScopedFileEntry(entry, this._root.scope);
    }
    getDir(path) {
        const entry = this._base.getDir(this._fullPath(path));
        return entry && new ScopedDirEntry(entry, this._root.scope);
    }
    visit(visitor) {
        return this._root.visit(visitor);
    }
    // Change content of host files.
    overwrite(path, content) {
        return this._base.overwrite(this._fullPath(path), content);
    }
    beginUpdate(path) {
        return this._base.beginUpdate(this._fullPath(path));
    }
    commitUpdate(record) {
        return this._base.commitUpdate(record);
    }
    // Structural methods.
    create(path, content) {
        return this._base.create(this._fullPath(path), content);
    }
    delete(path) {
        return this._base.delete(this._fullPath(path));
    }
    rename(from, to) {
        return this._base.rename(this._fullPath(from), this._fullPath(to));
    }
    apply(action, strategy) {
        return this._base.apply(this._fullPathAction(action), strategy);
    }
    get actions() {
        const scopedActions = [];
        for (const action of this._base.actions) {
            if (!action.path.startsWith(this._root.scope + '/')) {
                continue;
            }
            if (action.kind !== 'r') {
                scopedActions.push({
                    ...action,
                    path: (0, core_1.join)(core_1.NormalizedRoot, (0, core_1.relative)(this._root.scope, action.path)),
                });
            }
            else if (action.to.startsWith(this._root.scope + '/')) {
                scopedActions.push({
                    ...action,
                    path: (0, core_1.join)(core_1.NormalizedRoot, (0, core_1.relative)(this._root.scope, action.path)),
                    to: (0, core_1.join)(core_1.NormalizedRoot, (0, core_1.relative)(this._root.scope, action.to)),
                });
            }
        }
        return scopedActions;
    }
    [interface_1.TreeSymbol]() {
        return this;
    }
    _fullPath(path) {
        return (0, core_1.join)(this._root.scope, (0, core_1.normalize)('/' + path));
    }
    _fullPathAction(action) {
        let fullPathAction;
        if (action.kind === 'r') {
            fullPathAction = {
                ...action,
                path: this._fullPath(action.path),
                to: this._fullPath(action.to),
            };
        }
        else {
            fullPathAction = {
                ...action,
                path: this._fullPath(action.path),
            };
        }
        return fullPathAction;
    }
}
exports.ScopedTree = ScopedTree;
//# sourceMappingURL=data:application/json;base64,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