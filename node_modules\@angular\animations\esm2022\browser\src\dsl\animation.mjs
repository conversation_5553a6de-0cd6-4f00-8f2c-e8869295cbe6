import { buildingFailed, validationFailed } from '../error_helpers';
import { ENTER_CLASSNAME, LEAVE_CLASSNAME, normalizeStyles } from '../util';
import { warnValidation } from '../warning_helpers';
import { buildAnimationAst } from './animation_ast_builder';
import { buildAnimationTimelines } from './animation_timeline_builder';
import { ElementInstructionMap } from './element_instruction_map';
export class Animation {
    constructor(_driver, input) {
        this._driver = _driver;
        const errors = [];
        const warnings = [];
        const ast = buildAnimationAst(_driver, input, errors, warnings);
        if (errors.length) {
            throw validationFailed(errors);
        }
        if (warnings.length) {
            warnValidation(warnings);
        }
        this._animationAst = ast;
    }
    buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {
        const start = Array.isArray(startingStyles)
            ? normalizeStyles(startingStyles)
            : startingStyles;
        const dest = Array.isArray(destinationStyles)
            ? normalizeStyles(destinationStyles)
            : destinationStyles;
        const errors = [];
        subInstructions = subInstructions || new ElementInstructionMap();
        const result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);
        if (errors.length) {
            throw buildingFailed(errors);
        }
        return result;
    }
}
//# sourceMappingURL=data:application/json;base64,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