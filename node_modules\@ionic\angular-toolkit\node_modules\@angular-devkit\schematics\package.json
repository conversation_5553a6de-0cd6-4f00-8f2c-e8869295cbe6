{"name": "@angular-devkit/schematics", "version": "15.2.11", "description": "Angular Schematics - Library", "main": "src/index.js", "typings": "src/index.d.ts", "keywords": ["Angular CLI", "Angular DevKit", "angular", "blueprints", "code generation", "devkit", "scaffolding", "schematics", "sdk", "template", "tooling"], "dependencies": {"@angular-devkit/core": "15.2.11", "jsonc-parser": "3.2.0", "magic-string": "0.29.0", "ora": "5.4.1", "rxjs": "6.6.7"}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "engines": {"node": "^14.20.0 || ^16.13.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli"}