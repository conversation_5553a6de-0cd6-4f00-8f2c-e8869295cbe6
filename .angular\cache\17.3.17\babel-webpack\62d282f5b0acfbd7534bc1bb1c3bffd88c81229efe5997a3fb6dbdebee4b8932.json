{"ast": null, "code": "import { PreloadAllModules, RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'home',\n  loadChildren: () => import('./home/<USER>').then(m => m.HomePageModule)\n}, {\n  path: '',\n  redirectTo: 'home',\n  pathMatch: 'full'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, {\n        preloadingStrategy: PreloadAllModules\n      }), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["PreloadAllModules", "RouterModule", "routes", "path", "loadChildren", "then", "m", "HomePageModule", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "preloadingStrategy", "imports", "i1", "exports"], "sources": ["D:\\2025\\agmuicon\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { PreloadAllModules, RouterModule, Routes } from '@angular/router';\n\nconst routes: Routes = [\n  {\n    path: 'home',\n    loadChildren: () => import('./home/<USER>').then( m => m.HomePageModule)\n  },\n  {\n    path: '',\n    redirectTo: 'home',\n    pathMatch: 'full'\n  },\n];\n\n@NgModule({\n  imports: [\n    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })\n  ],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,iBAAiB,EAAEC,YAAY,QAAgB,iBAAiB;;;AAEzE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACC,cAAc;CAC7E,EACD;EACEJ,IAAI,EAAE,EAAE;EACRK,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE;CACZ,CACF;AAQD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAJzBT,YAAY,CAACU,OAAO,CAACT,MAAM,EAAE;QAAEU,kBAAkB,EAAEZ;MAAiB,CAAE,CAAC,EAE/DC,YAAY;IAAA;EAAA;;;2EAEXS,gBAAgB;IAAAG,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFjBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}