"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DelegateTree = void 0;
const interface_1 = require("./interface");
class DelegateTree {
    constructor(_other) {
        this._other = _other;
    }
    branch() {
        return this._other.branch();
    }
    merge(other, strategy) {
        this._other.merge(other, strategy);
    }
    get root() {
        return this._other.root;
    }
    // Readonly.
    read(path) {
        return this._other.read(path);
    }
    readText(path) {
        return this._other.readText(path);
    }
    readJson(path) {
        return this._other.readJson(path);
    }
    exists(path) {
        return this._other.exists(path);
    }
    get(path) {
        return this._other.get(path);
    }
    getDir(path) {
        return this._other.getDir(path);
    }
    visit(visitor) {
        return this._other.visit(visitor);
    }
    // Change content of host files.
    overwrite(path, content) {
        return this._other.overwrite(path, content);
    }
    beginUpdate(path) {
        return this._other.beginUpdate(path);
    }
    commitUpdate(record) {
        return this._other.commitUpdate(record);
    }
    // Structural methods.
    create(path, content) {
        return this._other.create(path, content);
    }
    delete(path) {
        return this._other.delete(path);
    }
    rename(from, to) {
        return this._other.rename(from, to);
    }
    apply(action, strategy) {
        return this._other.apply(action, strategy);
    }
    get actions() {
        return this._other.actions;
    }
    [interface_1.TreeSymbol]() {
        return this;
    }
}
exports.DelegateTree = DelegateTree;
//# sourceMappingURL=data:application/json;base64,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