"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1577],{1577:(E,a,o)=>{o.r(a),o.d(a,{ion_text:()=>c});var r=o(4363),u=o(333),l=o(611);const c=class{constructor(s){(0,r.r)(this,s),this.color=void 0}render(){const s=(0,l.b)(this);return(0,r.h)(r.H,{key:"4330b56cbc4e15953d9b3162fb40af728a8195dd",class:(0,u.c)(this.color,{[s]:!0})},(0,r.h)("slot",{key:"ec674a71d8fbb04d537fd79d617d9db4a607c340"}))}};c.style=":host(.ion-color){color:var(--ion-color-base)}"},333:(E,a,o)=>{o.d(a,{c:()=>l,g:()=>_,h:()=>u,o:()=>s});var r=o(467);const u=(t,n)=>null!==n.closest(t),l=(t,n)=>"string"==typeof t&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},n):n,_=t=>{const n={};return(t=>void 0!==t?(Array.isArray(t)?t:t.split(" ")).filter(e=>null!=e).map(e=>e.trim()).filter(e=>""!==e):[])(t).forEach(e=>n[e]=!0),n},c=/^[a-z][a-z0-9+\-.]*:/,s=function(){var t=(0,r.A)(function*(n,e,f,h){if(null!=n&&"#"!==n[0]&&!c.test(n)){const i=document.querySelector("ion-router");if(i)return e?.preventDefault(),i.push(n,f,h)}return!1});return function(e,f,h,i){return t.apply(this,arguments)}}()}}]);