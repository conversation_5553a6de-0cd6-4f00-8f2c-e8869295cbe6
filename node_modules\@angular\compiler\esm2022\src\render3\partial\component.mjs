/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as core from '../../core';
import { DEFAULT_INTERPOLATION_CONFIG } from '../../ml_parser/defaults';
import * as o from '../../output/output_ast';
import { ParseLocation, ParseSourceFile, ParseSourceSpan } from '../../parse_util';
import { RecursiveVisitor, visitAll } from '../r3_ast';
import { Identifiers as R3 } from '../r3_identifiers';
import { generateForwardRef } from '../util';
import { R3TemplateDependencyKind } from '../view/api';
import { createComponentType } from '../view/compiler';
import { DefinitionMap } from '../view/util';
import { createDirectiveDefinitionMap } from './directive';
import { toOptionalLiteralArray } from './util';
/**
 * Compile a component declaration defined by the `R3ComponentMetadata`.
 */
export function compileDeclareComponentFromMetadata(meta, template, additionalTemplateInfo) {
    const definitionMap = createComponentDefinitionMap(meta, template, additionalTemplateInfo);
    const expression = o.importExpr(R3.declareComponent).callFn([definitionMap.toLiteralMap()]);
    const type = createComponentType(meta);
    return { expression, type, statements: [] };
}
/**
 * Gathers the declaration fields for a component into a `DefinitionMap`.
 */
export function createComponentDefinitionMap(meta, template, templateInfo) {
    const definitionMap = createDirectiveDefinitionMap(meta);
    const blockVisitor = new BlockPresenceVisitor();
    visitAll(blockVisitor, template.nodes);
    definitionMap.set('template', getTemplateExpression(template, templateInfo));
    if (templateInfo.isInline) {
        definitionMap.set('isInline', o.literal(true));
    }
    // Set the minVersion to 17.0.0 if the component is using at least one block in its template.
    // We don't do this for templates without blocks, in order to preserve backwards compatibility.
    if (blockVisitor.hasBlocks) {
        definitionMap.set('minVersion', o.literal('17.0.0'));
    }
    definitionMap.set('styles', toOptionalLiteralArray(meta.styles, o.literal));
    definitionMap.set('dependencies', compileUsedDependenciesMetadata(meta));
    definitionMap.set('viewProviders', meta.viewProviders);
    definitionMap.set('animations', meta.animations);
    if (meta.changeDetection !== null) {
        if (typeof meta.changeDetection === 'object') {
            throw new Error('Impossible state! Change detection flag is not resolved!');
        }
        definitionMap.set('changeDetection', o.importExpr(R3.ChangeDetectionStrategy)
            .prop(core.ChangeDetectionStrategy[meta.changeDetection]));
    }
    if (meta.encapsulation !== core.ViewEncapsulation.Emulated) {
        definitionMap.set('encapsulation', o.importExpr(R3.ViewEncapsulation).prop(core.ViewEncapsulation[meta.encapsulation]));
    }
    if (meta.interpolation !== DEFAULT_INTERPOLATION_CONFIG) {
        definitionMap.set('interpolation', o.literalArr([o.literal(meta.interpolation.start), o.literal(meta.interpolation.end)]));
    }
    if (template.preserveWhitespaces === true) {
        definitionMap.set('preserveWhitespaces', o.literal(true));
    }
    return definitionMap;
}
function getTemplateExpression(template, templateInfo) {
    // If the template has been defined using a direct literal, we use that expression directly
    // without any modifications. This is ensures proper source mapping from the partially
    // compiled code to the source file declaring the template. Note that this does not capture
    // template literals referenced indirectly through an identifier.
    if (templateInfo.inlineTemplateLiteralExpression !== null) {
        return templateInfo.inlineTemplateLiteralExpression;
    }
    // If the template is defined inline but not through a literal, the template has been resolved
    // through static interpretation. We create a literal but cannot provide any source span. Note
    // that we cannot use the expression defining the template because the linker expects the template
    // to be defined as a literal in the declaration.
    if (templateInfo.isInline) {
        return o.literal(templateInfo.content, null, null);
    }
    // The template is external so we must synthesize an expression node with
    // the appropriate source-span.
    const contents = templateInfo.content;
    const file = new ParseSourceFile(contents, templateInfo.sourceUrl);
    const start = new ParseLocation(file, 0, 0, 0);
    const end = computeEndLocation(file, contents);
    const span = new ParseSourceSpan(start, end);
    return o.literal(contents, null, span);
}
function computeEndLocation(file, contents) {
    const length = contents.length;
    let lineStart = 0;
    let lastLineStart = 0;
    let line = 0;
    do {
        lineStart = contents.indexOf('\n', lastLineStart);
        if (lineStart !== -1) {
            lastLineStart = lineStart + 1;
            line++;
        }
    } while (lineStart !== -1);
    return new ParseLocation(file, length, line, length - lastLineStart);
}
function compileUsedDependenciesMetadata(meta) {
    const wrapType = meta.declarationListEmitMode !== 0 /* DeclarationListEmitMode.Direct */ ?
        generateForwardRef :
        (expr) => expr;
    if (meta.declarationListEmitMode === 3 /* DeclarationListEmitMode.RuntimeResolved */) {
        throw new Error(`Unsupported emit mode`);
    }
    return toOptionalLiteralArray(meta.declarations, decl => {
        switch (decl.kind) {
            case R3TemplateDependencyKind.Directive:
                const dirMeta = new DefinitionMap();
                dirMeta.set('kind', o.literal(decl.isComponent ? 'component' : 'directive'));
                dirMeta.set('type', wrapType(decl.type));
                dirMeta.set('selector', o.literal(decl.selector));
                dirMeta.set('inputs', toOptionalLiteralArray(decl.inputs, o.literal));
                dirMeta.set('outputs', toOptionalLiteralArray(decl.outputs, o.literal));
                dirMeta.set('exportAs', toOptionalLiteralArray(decl.exportAs, o.literal));
                return dirMeta.toLiteralMap();
            case R3TemplateDependencyKind.Pipe:
                const pipeMeta = new DefinitionMap();
                pipeMeta.set('kind', o.literal('pipe'));
                pipeMeta.set('type', wrapType(decl.type));
                pipeMeta.set('name', o.literal(decl.name));
                return pipeMeta.toLiteralMap();
            case R3TemplateDependencyKind.NgModule:
                const ngModuleMeta = new DefinitionMap();
                ngModuleMeta.set('kind', o.literal('ngmodule'));
                ngModuleMeta.set('type', wrapType(decl.type));
                return ngModuleMeta.toLiteralMap();
        }
    });
}
class BlockPresenceVisitor extends RecursiveVisitor {
    constructor() {
        super(...arguments);
        this.hasBlocks = false;
    }
    visitDeferredBlock() {
        this.hasBlocks = true;
    }
    visitDeferredBlockPlaceholder() {
        this.hasBlocks = true;
    }
    visitDeferredBlockLoading() {
        this.hasBlocks = true;
    }
    visitDeferredBlockError() {
        this.hasBlocks = true;
    }
    visitIfBlock() {
        this.hasBlocks = true;
    }
    visitIfBlockBranch() {
        this.hasBlocks = true;
    }
    visitForLoopBlock() {
        this.hasBlocks = true;
    }
    visitForLoopBlockEmpty() {
        this.hasBlocks = true;
    }
    visitSwitchBlock() {
        this.hasBlocks = true;
    }
    visitSwitchBlockCase() {
        this.hasBlocks = true;
    }
}
//# sourceMappingURL=data:application/json;base64,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