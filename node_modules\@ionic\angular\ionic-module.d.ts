import { ModuleWithProviders } from '@angular/core';
import { IonicConfig } from '@ionic/core';
import * as i0 from "@angular/core";
import * as i1 from "./directives/proxies";
import * as i2 from "./directives/overlays/modal";
import * as i3 from "./directives/overlays/popover";
import * as i4 from "./directives/control-value-accessors/boolean-value-accessor";
import * as i5 from "./directives/control-value-accessors/numeric-value-accessor";
import * as i6 from "./directives/control-value-accessors/radio-value-accessor";
import * as i7 from "./directives/control-value-accessors/select-value-accessor";
import * as i8 from "./directives/control-value-accessors/text-value-accessor";
import * as i9 from "./directives/navigation/ion-tabs";
import * as i10 from "./directives/navigation/ion-router-outlet";
import * as i11 from "./directives/navigation/ion-back-button";
import * as i12 from "./directives/navigation/ion-nav";
import * as i13 from "./directives/navigation/router-link-delegate";
import * as i14 from "./directives/validators/min-validator";
import * as i15 from "./directives/validators/max-validator";
import * as i16 from "@angular/common";
export declare class IonicModule {
    static forRoot(config?: IonicConfig): ModuleWithProviders<IonicModule>;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonicModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<IonicModule, [typeof i1.IonAccordion, typeof i1.IonAccordionGroup, typeof i1.IonActionSheet, typeof i1.IonAlert, typeof i1.IonApp, typeof i1.IonAvatar, typeof i1.IonBackdrop, typeof i1.IonBadge, typeof i1.IonBreadcrumb, typeof i1.IonBreadcrumbs, typeof i1.IonButton, typeof i1.IonButtons, typeof i1.IonCard, typeof i1.IonCardContent, typeof i1.IonCardHeader, typeof i1.IonCardSubtitle, typeof i1.IonCardTitle, typeof i1.IonCheckbox, typeof i1.IonChip, typeof i1.IonCol, typeof i1.IonContent, typeof i1.IonDatetime, typeof i1.IonDatetimeButton, typeof i1.IonFab, typeof i1.IonFabButton, typeof i1.IonFabList, typeof i1.IonFooter, typeof i1.IonGrid, typeof i1.IonHeader, typeof i1.IonIcon, typeof i1.IonImg, typeof i1.IonInfiniteScroll, typeof i1.IonInfiniteScrollContent, typeof i1.IonInput, typeof i1.IonItem, typeof i1.IonItemDivider, typeof i1.IonItemGroup, typeof i1.IonItemOption, typeof i1.IonItemOptions, typeof i1.IonItemSliding, typeof i1.IonLabel, typeof i1.IonList, typeof i1.IonListHeader, typeof i1.IonLoading, typeof i1.IonMenu, typeof i1.IonMenuButton, typeof i1.IonMenuToggle, typeof i1.IonNavLink, typeof i1.IonNote, typeof i1.IonPicker, typeof i1.IonProgressBar, typeof i1.IonRadio, typeof i1.IonRadioGroup, typeof i1.IonRange, typeof i1.IonRefresher, typeof i1.IonRefresherContent, typeof i1.IonReorder, typeof i1.IonReorderGroup, typeof i1.IonRippleEffect, typeof i1.IonRow, typeof i1.IonSearchbar, typeof i1.IonSegment, typeof i1.IonSegmentButton, typeof i1.IonSelect, typeof i1.IonSelectOption, typeof i1.IonSkeletonText, typeof i1.IonSpinner, typeof i1.IonSplitPane, typeof i1.IonTabBar, typeof i1.IonTabButton, typeof i1.IonText, typeof i1.IonTextarea, typeof i1.IonThumbnail, typeof i1.IonTitle, typeof i1.IonToast, typeof i1.IonToggle, typeof i1.IonToolbar, typeof i2.IonModal, typeof i3.IonPopover, typeof i4.BooleanValueAccessorDirective, typeof i5.NumericValueAccessorDirective, typeof i6.RadioValueAccessorDirective, typeof i7.SelectValueAccessorDirective, typeof i8.TextValueAccessorDirective, typeof i9.IonTabs, typeof i10.IonRouterOutlet, typeof i11.IonBackButton, typeof i12.IonNav, typeof i13.RouterLinkDelegateDirective, typeof i13.RouterLinkWithHrefDelegateDirective, typeof i14.IonMinValidator, typeof i15.IonMaxValidator], [typeof i16.CommonModule], [typeof i1.IonAccordion, typeof i1.IonAccordionGroup, typeof i1.IonActionSheet, typeof i1.IonAlert, typeof i1.IonApp, typeof i1.IonAvatar, typeof i1.IonBackdrop, typeof i1.IonBadge, typeof i1.IonBreadcrumb, typeof i1.IonBreadcrumbs, typeof i1.IonButton, typeof i1.IonButtons, typeof i1.IonCard, typeof i1.IonCardContent, typeof i1.IonCardHeader, typeof i1.IonCardSubtitle, typeof i1.IonCardTitle, typeof i1.IonCheckbox, typeof i1.IonChip, typeof i1.IonCol, typeof i1.IonContent, typeof i1.IonDatetime, typeof i1.IonDatetimeButton, typeof i1.IonFab, typeof i1.IonFabButton, typeof i1.IonFabList, typeof i1.IonFooter, typeof i1.IonGrid, typeof i1.IonHeader, typeof i1.IonIcon, typeof i1.IonImg, typeof i1.IonInfiniteScroll, typeof i1.IonInfiniteScrollContent, typeof i1.IonInput, typeof i1.IonItem, typeof i1.IonItemDivider, typeof i1.IonItemGroup, typeof i1.IonItemOption, typeof i1.IonItemOptions, typeof i1.IonItemSliding, typeof i1.IonLabel, typeof i1.IonList, typeof i1.IonListHeader, typeof i1.IonLoading, typeof i1.IonMenu, typeof i1.IonMenuButton, typeof i1.IonMenuToggle, typeof i1.IonNavLink, typeof i1.IonNote, typeof i1.IonPicker, typeof i1.IonProgressBar, typeof i1.IonRadio, typeof i1.IonRadioGroup, typeof i1.IonRange, typeof i1.IonRefresher, typeof i1.IonRefresherContent, typeof i1.IonReorder, typeof i1.IonReorderGroup, typeof i1.IonRippleEffect, typeof i1.IonRow, typeof i1.IonSearchbar, typeof i1.IonSegment, typeof i1.IonSegmentButton, typeof i1.IonSelect, typeof i1.IonSelectOption, typeof i1.IonSkeletonText, typeof i1.IonSpinner, typeof i1.IonSplitPane, typeof i1.IonTabBar, typeof i1.IonTabButton, typeof i1.IonText, typeof i1.IonTextarea, typeof i1.IonThumbnail, typeof i1.IonTitle, typeof i1.IonToast, typeof i1.IonToggle, typeof i1.IonToolbar, typeof i2.IonModal, typeof i3.IonPopover, typeof i4.BooleanValueAccessorDirective, typeof i5.NumericValueAccessorDirective, typeof i6.RadioValueAccessorDirective, typeof i7.SelectValueAccessorDirective, typeof i8.TextValueAccessorDirective, typeof i9.IonTabs, typeof i10.IonRouterOutlet, typeof i11.IonBackButton, typeof i12.IonNav, typeof i13.RouterLinkDelegateDirective, typeof i13.RouterLinkWithHrefDelegateDirective, typeof i14.IonMinValidator, typeof i15.IonMaxValidator]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<IonicModule>;
}
