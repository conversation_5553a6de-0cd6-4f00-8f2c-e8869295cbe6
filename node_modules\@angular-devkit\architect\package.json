{"name": "@angular-devkit/architect", "version": "0.1703.17", "description": "Angular Build Facade", "experimental": true, "main": "src/index.js", "typings": "src/index.d.ts", "dependencies": {"@angular-devkit/core": "17.3.17", "rxjs": "7.8.1"}, "builders": "./builders/builders.json", "keywords": ["Angular CLI", "Angular DevKit", "angular", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "engines": {"node": "^18.13.0 || >=20.9.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}