import { OverlayBaseController } from '@ionic/angular/common';
import type { ActionSheetOptions } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class ActionSheetController extends OverlayBaseController<ActionSheetOptions, HTMLIonActionSheetElement> {
    constructor();
    static ɵfac: i0.ɵɵFactoryDeclaration<ActionSheetController, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<ActionSheetController>;
}
