"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;base64,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