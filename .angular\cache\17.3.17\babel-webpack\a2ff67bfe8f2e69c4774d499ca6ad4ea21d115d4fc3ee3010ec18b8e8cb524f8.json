{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class QueueScheduler extends AsyncScheduler {}", "map": {"version": 3, "names": ["AsyncScheduler", "QueueScheduler"], "sources": ["D:/2025/agmuicon/node_modules/rxjs/dist/esm/internal/scheduler/QueueScheduler.js"], "sourcesContent": ["import { AsyncScheduler } from './AsyncScheduler';\nexport class QueueScheduler extends AsyncScheduler {\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAO,MAAMC,cAAc,SAASD,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}