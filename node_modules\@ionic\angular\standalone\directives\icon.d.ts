import { ChangeDetectorRef, ElementRef, NgZone } from '@angular/core';
import * as i0 from "@angular/core";
export declare class IonIcon {
    protected z: Ng<PERSON>one;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonIcon, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonIcon, "ion-icon", never, { "color": "color"; "flipRtl": "flipRtl"; "icon": "icon"; "ios": "ios"; "lazy": "lazy"; "md": "md"; "mode": "mode"; "name": "name"; "sanitize": "sanitize"; "size": "size"; "src": "src"; }, {}, never, ["*"], true>;
}
