/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Set of tagName|propertyName corresponding to Trusted Types sinks. Properties applying to all
 * tags use '*'.
 *
 * Extracted from, and should be kept in sync with
 * https://w3c.github.io/webappsec-trusted-types/dist/spec/#integrations
 */
const TRUSTED_TYPES_SINKS = new Set([
    // NOTE: All strings in this set *must* be lowercase!
    // TrustedHTML
    'iframe|srcdoc',
    '*|innerhtml',
    '*|outerhtml',
    // NB: no TrustedScript here, as the corresponding tags are stripped by the compiler.
    // TrustedScriptURL
    'embed|src',
    'object|codebase',
    'object|data',
]);
/**
 * isTrustedTypesSink returns true if the given property on the given DOM tag is a Trusted Types
 * sink. In that case, use `ElementSchemaRegistry.securityContext` to determine which particular
 * Trusted Type is required for values passed to the sink:
 * - SecurityContext.HTML corresponds to TrustedHTML
 * - SecurityContext.RESOURCE_URL corresponds to TrustedScriptURL
 */
export function isTrustedTypesSink(tagName, propName) {
    // Make sure comparisons are case insensitive, so that case differences between attribute and
    // property names do not have a security impact.
    tagName = tagName.toLowerCase();
    propName = propName.toLowerCase();
    return TRUSTED_TYPES_SINKS.has(tagName + '|' + propName) ||
        TRUSTED_TYPES_SINKS.has('*|' + propName);
}
//# sourceMappingURL=data:application/json;base64,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