{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { k as inheritAttributes } from './helpers-be245865.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst imgCss = \":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}\";\nconst IonImgStyle0 = imgCss;\nconst Img = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionImgWillLoad = createEvent(this, \"ionImgWillLoad\", 7);\n    this.ionImgDidLoad = createEvent(this, \"ionImgDidLoad\", 7);\n    this.ionError = createEvent(this, \"ionError\", 7);\n    this.inheritedAttributes = {};\n    this.onLoad = () => {\n      this.ionImgDidLoad.emit();\n    };\n    this.onError = () => {\n      this.ionError.emit();\n    };\n    this.loadSrc = undefined;\n    this.loadError = undefined;\n    this.alt = undefined;\n    this.src = undefined;\n  }\n  srcChanged() {\n    this.addIO();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['draggable']);\n  }\n  componentDidLoad() {\n    this.addIO();\n  }\n  addIO() {\n    if (this.src === undefined) {\n      return;\n    }\n    if (typeof window !== 'undefined' && 'IntersectionObserver' in window && 'IntersectionObserverEntry' in window && 'isIntersecting' in window.IntersectionObserverEntry.prototype) {\n      this.removeIO();\n      this.io = new IntersectionObserver(data => {\n        /**\n         * On slower devices, it is possible for an intersection observer entry to contain multiple\n         * objects in the array. This happens when quickly scrolling an image into view and then out of\n         * view. In this case, the last object represents the current state of the component.\n         */\n        if (data[data.length - 1].isIntersecting) {\n          this.load();\n          this.removeIO();\n        }\n      });\n      this.io.observe(this.el);\n    } else {\n      // fall back to setTimeout for Safari and IE\n      setTimeout(() => this.load(), 200);\n    }\n  }\n  load() {\n    this.loadError = this.onError;\n    this.loadSrc = this.src;\n    this.ionImgWillLoad.emit();\n  }\n  removeIO() {\n    if (this.io) {\n      this.io.disconnect();\n      this.io = undefined;\n    }\n  }\n  render() {\n    const {\n      loadSrc,\n      alt,\n      onLoad,\n      loadError,\n      inheritedAttributes\n    } = this;\n    const {\n      draggable\n    } = inheritedAttributes;\n    return h(Host, {\n      key: 'efff4d1bd0e54dbeff140c137eb50b803a9f6f60',\n      class: getIonMode(this)\n    }, h(\"img\", {\n      key: '3a1e0276ae67a7e40ec8c4ecd0061634573b2094',\n      decoding: \"async\",\n      src: loadSrc,\n      alt: alt,\n      onLoad: onLoad,\n      onError: loadError,\n      part: \"image\",\n      draggable: isDraggable(draggable)\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"src\": [\"srcChanged\"]\n    };\n  }\n};\n/**\n * Enumerated strings must be set as booleans\n * as Stencil will not render 'false' in the DOM.\n * The need to explicitly render draggable=\"true\"\n * as only certain elements are draggable by default.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.\n */\nconst isDraggable = draggable => {\n  switch (draggable) {\n    case 'true':\n      return true;\n    case 'false':\n      return false;\n    default:\n      return undefined;\n  }\n};\nImg.style = IonImgStyle0;\nexport { Img as ion_img };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "k", "inheritAttributes", "b", "getIonMode", "imgCss", "IonImgStyle0", "Img", "constructor", "hostRef", "ionImgWillLoad", "ionImgDidLoad", "ionError", "inheritedAttributes", "onLoad", "emit", "onError", "loadSrc", "undefined", "loadError", "alt", "src", "srcChanged", "addIO", "componentWillLoad", "el", "componentDidLoad", "window", "IntersectionObserverEntry", "prototype", "removeIO", "io", "IntersectionObserver", "data", "length", "isIntersecting", "load", "observe", "setTimeout", "disconnect", "render", "draggable", "key", "class", "decoding", "part", "isDraggable", "watchers", "style", "ion_img"], "sources": ["D:/2025/agmuicon/node_modules/@ionic/core/dist/esm/ion-img.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { k as inheritAttributes } from './helpers-be245865.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst imgCss = \":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}\";\nconst IonImgStyle0 = imgCss;\n\nconst Img = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionImgWillLoad = createEvent(this, \"ionImgWillLoad\", 7);\n        this.ionImgDidLoad = createEvent(this, \"ionImgDidLoad\", 7);\n        this.ionError = createEvent(this, \"ionError\", 7);\n        this.inheritedAttributes = {};\n        this.onLoad = () => {\n            this.ionImgDidLoad.emit();\n        };\n        this.onError = () => {\n            this.ionError.emit();\n        };\n        this.loadSrc = undefined;\n        this.loadError = undefined;\n        this.alt = undefined;\n        this.src = undefined;\n    }\n    srcChanged() {\n        this.addIO();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['draggable']);\n    }\n    componentDidLoad() {\n        this.addIO();\n    }\n    addIO() {\n        if (this.src === undefined) {\n            return;\n        }\n        if (typeof window !== 'undefined' &&\n            'IntersectionObserver' in window &&\n            'IntersectionObserverEntry' in window &&\n            'isIntersecting' in window.IntersectionObserverEntry.prototype) {\n            this.removeIO();\n            this.io = new IntersectionObserver((data) => {\n                /**\n                 * On slower devices, it is possible for an intersection observer entry to contain multiple\n                 * objects in the array. This happens when quickly scrolling an image into view and then out of\n                 * view. In this case, the last object represents the current state of the component.\n                 */\n                if (data[data.length - 1].isIntersecting) {\n                    this.load();\n                    this.removeIO();\n                }\n            });\n            this.io.observe(this.el);\n        }\n        else {\n            // fall back to setTimeout for Safari and IE\n            setTimeout(() => this.load(), 200);\n        }\n    }\n    load() {\n        this.loadError = this.onError;\n        this.loadSrc = this.src;\n        this.ionImgWillLoad.emit();\n    }\n    removeIO() {\n        if (this.io) {\n            this.io.disconnect();\n            this.io = undefined;\n        }\n    }\n    render() {\n        const { loadSrc, alt, onLoad, loadError, inheritedAttributes } = this;\n        const { draggable } = inheritedAttributes;\n        return (h(Host, { key: 'efff4d1bd0e54dbeff140c137eb50b803a9f6f60', class: getIonMode(this) }, h(\"img\", { key: '3a1e0276ae67a7e40ec8c4ecd0061634573b2094', decoding: \"async\", src: loadSrc, alt: alt, onLoad: onLoad, onError: loadError, part: \"image\", draggable: isDraggable(draggable) })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"src\": [\"srcChanged\"]\n    }; }\n};\n/**\n * Enumerated strings must be set as booleans\n * as Stencil will not render 'false' in the DOM.\n * The need to explicitly render draggable=\"true\"\n * as only certain elements are draggable by default.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.\n */\nconst isDraggable = (draggable) => {\n    switch (draggable) {\n        case 'true':\n            return true;\n        case 'false':\n            return false;\n        default:\n            return undefined;\n    }\n};\nImg.style = IonImgStyle0;\n\nexport { Img as ion_img };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,uBAAuB;AAC9D,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAE5D,MAAMC,MAAM,GAAG,oMAAoM;AACnN,MAAMC,YAAY,GAAGD,MAAM;AAE3B,MAAME,GAAG,GAAG,MAAM;EACdC,WAAWA,CAACC,OAAO,EAAE;IACjBhB,gBAAgB,CAAC,IAAI,EAAEgB,OAAO,CAAC;IAC/B,IAAI,CAACC,cAAc,GAAGf,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACgB,aAAa,GAAGhB,WAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACiB,QAAQ,GAAGjB,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkB,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACH,aAAa,CAACI,IAAI,CAAC,CAAC;IAC7B,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACJ,QAAQ,CAACG,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACE,OAAO,GAAGC,SAAS;IACxB,IAAI,CAACC,SAAS,GAAGD,SAAS;IAC1B,IAAI,CAACE,GAAG,GAAGF,SAAS;IACpB,IAAI,CAACG,GAAG,GAAGH,SAAS;EACxB;EACAI,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,KAAK,CAAC,CAAC;EAChB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACX,mBAAmB,GAAGX,iBAAiB,CAAC,IAAI,CAACuB,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC;EACxE;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACH,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACF,GAAG,KAAKH,SAAS,EAAE;MACxB;IACJ;IACA,IAAI,OAAOS,MAAM,KAAK,WAAW,IAC7B,sBAAsB,IAAIA,MAAM,IAChC,2BAA2B,IAAIA,MAAM,IACrC,gBAAgB,IAAIA,MAAM,CAACC,yBAAyB,CAACC,SAAS,EAAE;MAChE,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACC,EAAE,GAAG,IAAIC,oBAAoB,CAAEC,IAAI,IAAK;QACzC;AAChB;AACA;AACA;AACA;QACgB,IAAIA,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAACC,cAAc,EAAE;UACtC,IAAI,CAACC,IAAI,CAAC,CAAC;UACX,IAAI,CAACN,QAAQ,CAAC,CAAC;QACnB;MACJ,CAAC,CAAC;MACF,IAAI,CAACC,EAAE,CAACM,OAAO,CAAC,IAAI,CAACZ,EAAE,CAAC;IAC5B,CAAC,MACI;MACD;MACAa,UAAU,CAAC,MAAM,IAAI,CAACF,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;IACtC;EACJ;EACAA,IAAIA,CAAA,EAAG;IACH,IAAI,CAACjB,SAAS,GAAG,IAAI,CAACH,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAG,IAAI,CAACI,GAAG;IACvB,IAAI,CAACX,cAAc,CAACK,IAAI,CAAC,CAAC;EAC9B;EACAe,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACC,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,CAACQ,UAAU,CAAC,CAAC;MACpB,IAAI,CAACR,EAAE,GAAGb,SAAS;IACvB;EACJ;EACAsB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEvB,OAAO;MAAEG,GAAG;MAAEN,MAAM;MAAEK,SAAS;MAAEN;IAAoB,CAAC,GAAG,IAAI;IACrE,MAAM;MAAE4B;IAAU,CAAC,GAAG5B,mBAAmB;IACzC,OAAQjB,CAAC,CAACE,IAAI,EAAE;MAAE4C,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEvC,UAAU,CAAC,IAAI;IAAE,CAAC,EAAER,CAAC,CAAC,KAAK,EAAE;MAAE8C,GAAG,EAAE,0CAA0C;MAAEE,QAAQ,EAAE,OAAO;MAAEvB,GAAG,EAAEJ,OAAO;MAAEG,GAAG,EAAEA,GAAG;MAAEN,MAAM,EAAEA,MAAM;MAAEE,OAAO,EAAEG,SAAS;MAAE0B,IAAI,EAAE,OAAO;MAAEJ,SAAS,EAAEK,WAAW,CAACL,SAAS;IAAE,CAAC,CAAC,CAAC;EACjS;EACA,IAAIhB,EAAEA,CAAA,EAAG;IAAE,OAAOzB,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW+C,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,KAAK,EAAE,CAAC,YAAY;IACxB,CAAC;EAAE;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,WAAW,GAAIL,SAAS,IAAK;EAC/B,QAAQA,SAAS;IACb,KAAK,MAAM;MACP,OAAO,IAAI;IACf,KAAK,OAAO;MACR,OAAO,KAAK;IAChB;MACI,OAAOvB,SAAS;EACxB;AACJ,CAAC;AACDX,GAAG,CAACyC,KAAK,GAAG1C,YAAY;AAExB,SAASC,GAAG,IAAI0C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}