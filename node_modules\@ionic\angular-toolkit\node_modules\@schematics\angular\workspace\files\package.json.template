{
  "name": "<%= utils.dasherize(name) %>",
  "version": "0.0.0",
  "scripts": {
    "ng": "ng",
    "start": "ng serve",
    "build": "ng build",
    "watch": "ng build --watch --configuration development"<% if (!minimal) { %>,
    "test": "ng test"<% } %>
  },
  "private": true,
  "dependencies": {
    "@angular/animations": "<%= latestVersions.Angular %>",
    "@angular/common": "<%= latestVersions.Angular %>",
    "@angular/compiler": "<%= latestVersions.Angular %>",
    "@angular/core": "<%= latestVersions.Angular %>",
    "@angular/forms": "<%= latestVersions.Angular %>",
    "@angular/platform-browser": "<%= latestVersions.Angular %>",
    "@angular/platform-browser-dynamic": "<%= latestVersions.Angular %>",
    "@angular/router": "<%= latestVersions.Angular %>",
    "rxjs": "<%= latestVersions['rxjs'] %>",
    "tslib": "<%= latestVersions['tslib'] %>",
    "zone.js": "<%= latestVersions['zone.js'] %>"
  },
  "devDependencies": {
    "@angular/cli": "<%= '~' + version %>",
    "@angular/compiler-cli": "<%= latestVersions.Angular %>",<% if (!minimal) { %>
    "@types/jasmine": "<%= latestVersions['@types/jasmine'] %>",
    "jasmine-core": "<%= latestVersions['jasmine-core'] %>",
    "karma": "<%= latestVersions['karma'] %>",
    "karma-chrome-launcher": "<%= latestVersions['karma-chrome-launcher'] %>",
    "karma-coverage": "<%= latestVersions['karma-coverage'] %>",
    "karma-jasmine": "<%= latestVersions['karma-jasmine'] %>",
    "karma-jasmine-html-reporter": "<%= latestVersions['karma-jasmine-html-reporter'] %>",<% } %>
    "typescript": "<%= latestVersions['typescript'] %>"
  }
}
