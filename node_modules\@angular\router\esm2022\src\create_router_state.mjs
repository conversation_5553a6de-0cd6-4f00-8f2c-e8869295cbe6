/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { BehaviorSubject } from 'rxjs';
import { ActivatedRoute, RouterState, } from './router_state';
import { TreeNode } from './utils/tree';
export function createRouterState(routeReuseStrategy, curr, prevState) {
    const root = createNode(routeReuseStrategy, curr._root, prevState ? prevState._root : undefined);
    return new RouterState(root, curr);
}
function createNode(routeReuseStrategy, curr, prevState) {
    // reuse an activated route that is currently displayed on the screen
    if (prevState && routeReuseStrategy.shouldReuseRoute(curr.value, prevState.value.snapshot)) {
        const value = prevState.value;
        value._futureSnapshot = curr.value;
        const children = createOrReuseChildren(routeReuseStrategy, curr, prevState);
        return new TreeNode(value, children);
    }
    else {
        if (routeReuseStrategy.shouldAttach(curr.value)) {
            // retrieve an activated route that is used to be displayed, but is not currently displayed
            const detachedRouteHandle = routeReuseStrategy.retrieve(curr.value);
            if (detachedRouteHandle !== null) {
                const tree = detachedRouteHandle.route;
                tree.value._futureSnapshot = curr.value;
                tree.children = curr.children.map((c) => createNode(routeReuseStrategy, c));
                return tree;
            }
        }
        const value = createActivatedRoute(curr.value);
        const children = curr.children.map((c) => createNode(routeReuseStrategy, c));
        return new TreeNode(value, children);
    }
}
function createOrReuseChildren(routeReuseStrategy, curr, prevState) {
    return curr.children.map((child) => {
        for (const p of prevState.children) {
            if (routeReuseStrategy.shouldReuseRoute(child.value, p.value.snapshot)) {
                return createNode(routeReuseStrategy, child, p);
            }
        }
        return createNode(routeReuseStrategy, child);
    });
}
function createActivatedRoute(c) {
    return new ActivatedRoute(new BehaviorSubject(c.url), new BehaviorSubject(c.params), new BehaviorSubject(c.queryParams), new BehaviorSubject(c.fragment), new BehaviorSubject(c.data), c.outlet, c.component, c);
}
//# sourceMappingURL=data:application/json;base64,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