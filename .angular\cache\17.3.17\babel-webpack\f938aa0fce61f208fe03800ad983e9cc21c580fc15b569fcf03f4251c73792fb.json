{"ast": null, "code": "import _asyncToGenerator from \"D:/2025/agmuicon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, e as readTask, h, f as getElement, H as Host } from './index-a1a47f01.js';\nimport { f as findClosestIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-f3946ac1.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport './helpers-be245865.js';\nimport './index-9b0d46f4.js';\nconst infiniteScrollCss = \"ion-infinite-scroll{display:none;width:100%}.infinite-scroll-enabled{display:block}\";\nconst IonInfiniteScrollStyle0 = infiniteScrollCss;\nconst InfiniteScroll = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInfinite = createEvent(this, \"ionInfinite\", 7);\n    this.thrPx = 0;\n    this.thrPc = 0;\n    /**\n     * didFire exists so that ionInfinite\n     * does not fire multiple times if\n     * users continue to scroll after\n     * scrolling into the infinite\n     * scroll threshold.\n     */\n    this.didFire = false;\n    this.isBusy = false;\n    this.onScroll = () => {\n      const scrollEl = this.scrollEl;\n      if (!scrollEl || !this.canStart()) {\n        return 1;\n      }\n      const infiniteHeight = this.el.offsetHeight;\n      if (infiniteHeight === 0) {\n        // if there is no height of this element then do nothing\n        return 2;\n      }\n      const scrollTop = scrollEl.scrollTop;\n      const scrollHeight = scrollEl.scrollHeight;\n      const height = scrollEl.offsetHeight;\n      const threshold = this.thrPc !== 0 ? height * this.thrPc : this.thrPx;\n      const distanceFromInfinite = this.position === 'bottom' ? scrollHeight - infiniteHeight - scrollTop - threshold - height : scrollTop - infiniteHeight - threshold;\n      if (distanceFromInfinite < 0) {\n        if (!this.didFire) {\n          this.isLoading = true;\n          this.didFire = true;\n          this.ionInfinite.emit();\n          return 3;\n        }\n      }\n      return 4;\n    };\n    this.isLoading = false;\n    this.threshold = '15%';\n    this.disabled = false;\n    this.position = 'bottom';\n  }\n  thresholdChanged() {\n    const val = this.threshold;\n    if (val.lastIndexOf('%') > -1) {\n      this.thrPx = 0;\n      this.thrPc = parseFloat(val) / 100;\n    } else {\n      this.thrPx = parseFloat(val);\n      this.thrPc = 0;\n    }\n  }\n  disabledChanged() {\n    const disabled = this.disabled;\n    if (disabled) {\n      this.isLoading = false;\n      this.isBusy = false;\n    }\n    this.enableScrollEvents(!disabled);\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const contentEl = findClosestIonContent(_this.el);\n      if (!contentEl) {\n        printIonContentErrorMsg(_this.el);\n        return;\n      }\n      _this.scrollEl = yield getScrollElement(contentEl);\n      _this.thresholdChanged();\n      _this.disabledChanged();\n      if (_this.position === 'top') {\n        writeTask(() => {\n          if (_this.scrollEl) {\n            _this.scrollEl.scrollTop = _this.scrollEl.scrollHeight - _this.scrollEl.clientHeight;\n          }\n        });\n      }\n    })();\n  }\n  disconnectedCallback() {\n    this.enableScrollEvents(false);\n    this.scrollEl = undefined;\n  }\n  /**\n   * Call `complete()` within the `ionInfinite` output event handler when\n   * your async operation has completed. For example, the `loading`\n   * state is while the app is performing an asynchronous operation,\n   * such as receiving more data from an AJAX request to add more items\n   * to a data list. Once the data has been received and UI updated, you\n   * then call this method to signify that the loading has completed.\n   * This method will change the infinite scroll's state from `loading`\n   * to `enabled`.\n   */\n  complete() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const scrollEl = _this2.scrollEl;\n      if (!_this2.isLoading || !scrollEl) {\n        return;\n      }\n      _this2.isLoading = false;\n      if (_this2.position === 'top') {\n        /**\n         * New content is being added at the top, but the scrollTop position stays the same,\n         * which causes a scroll jump visually. This algorithm makes sure to prevent this.\n         * (Frame 1)\n         *    - complete() is called, but the UI hasn't had time to update yet.\n         *    - Save the current content dimensions.\n         *    - Wait for the next frame using _dom.read, so the UI will be updated.\n         * (Frame 2)\n         *    - Read the new content dimensions.\n         *    - Calculate the height difference and the new scroll position.\n         *    - Delay the scroll position change until other possible dom reads are done using _dom.write to be performant.\n         * (Still frame 2, if I'm correct)\n         *    - Change the scroll position (= visually maintain the scroll position).\n         *    - Change the state to re-enable the InfiniteScroll.\n         *    - This should be after changing the scroll position, or it could\n         *    cause the InfiniteScroll to be triggered again immediately.\n         * (Frame 3)\n         *    Done.\n         */\n        _this2.isBusy = true;\n        // ******** DOM READ ****************\n        // Save the current content dimensions before the UI updates\n        const prev = scrollEl.scrollHeight - scrollEl.scrollTop;\n        // ******** DOM READ ****************\n        requestAnimationFrame(() => {\n          readTask(() => {\n            // UI has updated, save the new content dimensions\n            const scrollHeight = scrollEl.scrollHeight;\n            // New content was added on top, so the scroll position should be changed immediately to prevent it from jumping around\n            const newScrollTop = scrollHeight - prev;\n            // ******** DOM WRITE ****************\n            requestAnimationFrame(() => {\n              writeTask(() => {\n                scrollEl.scrollTop = newScrollTop;\n                _this2.isBusy = false;\n                _this2.didFire = false;\n              });\n            });\n          });\n        });\n      } else {\n        _this2.didFire = false;\n      }\n    })();\n  }\n  canStart() {\n    return !this.disabled && !this.isBusy && !!this.scrollEl && !this.isLoading;\n  }\n  enableScrollEvents(shouldListen) {\n    if (this.scrollEl) {\n      if (shouldListen) {\n        this.scrollEl.addEventListener('scroll', this.onScroll);\n      } else {\n        this.scrollEl.removeEventListener('scroll', this.onScroll);\n      }\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    const disabled = this.disabled;\n    return h(Host, {\n      key: 'c2248d06232dd7771dd155693ec75f9258dc969e',\n      class: {\n        [mode]: true,\n        'infinite-scroll-loading': this.isLoading,\n        'infinite-scroll-enabled': !disabled\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"threshold\": [\"thresholdChanged\"],\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nInfiniteScroll.style = IonInfiniteScrollStyle0;\nconst infiniteScrollContentIosCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-ios .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-small-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}\";\nconst IonInfiniteScrollContentIosStyle0 = infiniteScrollContentIosCss;\nconst infiniteScrollContentMdCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-md .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-small-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}\";\nconst IonInfiniteScrollContentMdStyle0 = infiniteScrollContentMdCss;\nconst InfiniteScrollContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.loadingSpinner = undefined;\n    this.loadingText = undefined;\n  }\n  componentDidLoad() {\n    if (this.loadingSpinner === undefined) {\n      const mode = getIonMode(this);\n      this.loadingSpinner = config.get('infiniteLoadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n    }\n  }\n  renderLoadingText() {\n    const {\n      customHTMLEnabled,\n      loadingText\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"infinite-loading-text\",\n        innerHTML: sanitizeDOMString(loadingText)\n      });\n    }\n    return h(\"div\", {\n      class: \"infinite-loading-text\"\n    }, this.loadingText);\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '2f4afb07bcfe3e12528eb9cee8646a097e0b359f',\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`infinite-scroll-content-${mode}`]: true\n      }\n    }, h(\"div\", {\n      key: 'af038177bf10c88c8970682487a4328689aaa5f2',\n      class: \"infinite-loading\"\n    }, this.loadingSpinner && h(\"div\", {\n      key: '1da5d419bc6a978b6a509fdab47dae347fc8d221',\n      class: \"infinite-loading-spinner\"\n    }, h(\"ion-spinner\", {\n      key: '60cc5c64e0a317ac0005d5afe42c4bb8da58136f',\n      name: this.loadingSpinner\n    })), this.loadingText !== undefined && this.renderLoadingText()));\n  }\n};\nInfiniteScrollContent.style = {\n  ios: IonInfiniteScrollContentIosStyle0,\n  md: IonInfiniteScrollContentMdStyle0\n};\nexport { InfiniteScroll as ion_infinite_scroll, InfiniteScrollContent as ion_infinite_scroll_content };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "e", "readTask", "h", "f", "getElement", "H", "Host", "findClosestIonContent", "p", "printIonContentErrorMsg", "g", "getScrollElement", "b", "getIonMode", "c", "config", "E", "ENABLE_HTML_CONTENT_DEFAULT", "a", "sanitizeDOMString", "infiniteScrollCss", "IonInfiniteScrollStyle0", "InfiniteScroll", "constructor", "hostRef", "ionInfinite", "thrPx", "thrPc", "<PERSON><PERSON><PERSON>", "isBusy", "onScroll", "scrollEl", "canStart", "infiniteHeight", "el", "offsetHeight", "scrollTop", "scrollHeight", "height", "threshold", "distanceFromInfinite", "position", "isLoading", "emit", "disabled", "thresholdChanged", "val", "lastIndexOf", "parseFloat", "disabled<PERSON><PERSON>ed", "enableScrollEvents", "connectedCallback", "_this", "_asyncToGenerator", "contentEl", "clientHeight", "disconnectedCallback", "undefined", "complete", "_this2", "prev", "requestAnimationFrame", "newScrollTop", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "render", "mode", "key", "class", "watchers", "style", "infiniteScrollContentIosCss", "IonInfiniteScrollContentIosStyle0", "infiniteScrollContentMdCss", "IonInfiniteScrollContentMdStyle0", "InfiniteScrollContent", "customHTMLEnabled", "get", "loadingSpinner", "loadingText", "componentDidLoad", "renderLoadingText", "innerHTML", "name", "ios", "md", "ion_infinite_scroll", "ion_infinite_scroll_content"], "sources": ["D:/2025/agmuicon/node_modules/@ionic/core/dist/esm/ion-infinite-scroll_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, e as readTask, h, f as getElement, H as Host } from './index-a1a47f01.js';\nimport { f as findClosestIonContent, p as printIonContentErrorMsg, g as getScrollElement } from './index-f3946ac1.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport './helpers-be245865.js';\nimport './index-9b0d46f4.js';\n\nconst infiniteScrollCss = \"ion-infinite-scroll{display:none;width:100%}.infinite-scroll-enabled{display:block}\";\nconst IonInfiniteScrollStyle0 = infiniteScrollCss;\n\nconst InfiniteScroll = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInfinite = createEvent(this, \"ionInfinite\", 7);\n        this.thrPx = 0;\n        this.thrPc = 0;\n        /**\n         * didFire exists so that ionInfinite\n         * does not fire multiple times if\n         * users continue to scroll after\n         * scrolling into the infinite\n         * scroll threshold.\n         */\n        this.didFire = false;\n        this.isBusy = false;\n        this.onScroll = () => {\n            const scrollEl = this.scrollEl;\n            if (!scrollEl || !this.canStart()) {\n                return 1;\n            }\n            const infiniteHeight = this.el.offsetHeight;\n            if (infiniteHeight === 0) {\n                // if there is no height of this element then do nothing\n                return 2;\n            }\n            const scrollTop = scrollEl.scrollTop;\n            const scrollHeight = scrollEl.scrollHeight;\n            const height = scrollEl.offsetHeight;\n            const threshold = this.thrPc !== 0 ? height * this.thrPc : this.thrPx;\n            const distanceFromInfinite = this.position === 'bottom'\n                ? scrollHeight - infiniteHeight - scrollTop - threshold - height\n                : scrollTop - infiniteHeight - threshold;\n            if (distanceFromInfinite < 0) {\n                if (!this.didFire) {\n                    this.isLoading = true;\n                    this.didFire = true;\n                    this.ionInfinite.emit();\n                    return 3;\n                }\n            }\n            return 4;\n        };\n        this.isLoading = false;\n        this.threshold = '15%';\n        this.disabled = false;\n        this.position = 'bottom';\n    }\n    thresholdChanged() {\n        const val = this.threshold;\n        if (val.lastIndexOf('%') > -1) {\n            this.thrPx = 0;\n            this.thrPc = parseFloat(val) / 100;\n        }\n        else {\n            this.thrPx = parseFloat(val);\n            this.thrPc = 0;\n        }\n    }\n    disabledChanged() {\n        const disabled = this.disabled;\n        if (disabled) {\n            this.isLoading = false;\n            this.isBusy = false;\n        }\n        this.enableScrollEvents(!disabled);\n    }\n    async connectedCallback() {\n        const contentEl = findClosestIonContent(this.el);\n        if (!contentEl) {\n            printIonContentErrorMsg(this.el);\n            return;\n        }\n        this.scrollEl = await getScrollElement(contentEl);\n        this.thresholdChanged();\n        this.disabledChanged();\n        if (this.position === 'top') {\n            writeTask(() => {\n                if (this.scrollEl) {\n                    this.scrollEl.scrollTop = this.scrollEl.scrollHeight - this.scrollEl.clientHeight;\n                }\n            });\n        }\n    }\n    disconnectedCallback() {\n        this.enableScrollEvents(false);\n        this.scrollEl = undefined;\n    }\n    /**\n     * Call `complete()` within the `ionInfinite` output event handler when\n     * your async operation has completed. For example, the `loading`\n     * state is while the app is performing an asynchronous operation,\n     * such as receiving more data from an AJAX request to add more items\n     * to a data list. Once the data has been received and UI updated, you\n     * then call this method to signify that the loading has completed.\n     * This method will change the infinite scroll's state from `loading`\n     * to `enabled`.\n     */\n    async complete() {\n        const scrollEl = this.scrollEl;\n        if (!this.isLoading || !scrollEl) {\n            return;\n        }\n        this.isLoading = false;\n        if (this.position === 'top') {\n            /**\n             * New content is being added at the top, but the scrollTop position stays the same,\n             * which causes a scroll jump visually. This algorithm makes sure to prevent this.\n             * (Frame 1)\n             *    - complete() is called, but the UI hasn't had time to update yet.\n             *    - Save the current content dimensions.\n             *    - Wait for the next frame using _dom.read, so the UI will be updated.\n             * (Frame 2)\n             *    - Read the new content dimensions.\n             *    - Calculate the height difference and the new scroll position.\n             *    - Delay the scroll position change until other possible dom reads are done using _dom.write to be performant.\n             * (Still frame 2, if I'm correct)\n             *    - Change the scroll position (= visually maintain the scroll position).\n             *    - Change the state to re-enable the InfiniteScroll.\n             *    - This should be after changing the scroll position, or it could\n             *    cause the InfiniteScroll to be triggered again immediately.\n             * (Frame 3)\n             *    Done.\n             */\n            this.isBusy = true;\n            // ******** DOM READ ****************\n            // Save the current content dimensions before the UI updates\n            const prev = scrollEl.scrollHeight - scrollEl.scrollTop;\n            // ******** DOM READ ****************\n            requestAnimationFrame(() => {\n                readTask(() => {\n                    // UI has updated, save the new content dimensions\n                    const scrollHeight = scrollEl.scrollHeight;\n                    // New content was added on top, so the scroll position should be changed immediately to prevent it from jumping around\n                    const newScrollTop = scrollHeight - prev;\n                    // ******** DOM WRITE ****************\n                    requestAnimationFrame(() => {\n                        writeTask(() => {\n                            scrollEl.scrollTop = newScrollTop;\n                            this.isBusy = false;\n                            this.didFire = false;\n                        });\n                    });\n                });\n            });\n        }\n        else {\n            this.didFire = false;\n        }\n    }\n    canStart() {\n        return !this.disabled && !this.isBusy && !!this.scrollEl && !this.isLoading;\n    }\n    enableScrollEvents(shouldListen) {\n        if (this.scrollEl) {\n            if (shouldListen) {\n                this.scrollEl.addEventListener('scroll', this.onScroll);\n            }\n            else {\n                this.scrollEl.removeEventListener('scroll', this.onScroll);\n            }\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        const disabled = this.disabled;\n        return (h(Host, { key: 'c2248d06232dd7771dd155693ec75f9258dc969e', class: {\n                [mode]: true,\n                'infinite-scroll-loading': this.isLoading,\n                'infinite-scroll-enabled': !disabled,\n            } }));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"threshold\": [\"thresholdChanged\"],\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nInfiniteScroll.style = IonInfiniteScrollStyle0;\n\nconst infiniteScrollContentIosCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-ios .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-small-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}\";\nconst IonInfiniteScrollContentIosStyle0 = infiniteScrollContentIosCss;\n\nconst infiniteScrollContentMdCss = \"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-md .infinite-loading-text{color:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-small-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, #666666)}.infinite-scroll-content-md .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, #666666)}\";\nconst IonInfiniteScrollContentMdStyle0 = infiniteScrollContentMdCss;\n\nconst InfiniteScrollContent = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n        this.loadingSpinner = undefined;\n        this.loadingText = undefined;\n    }\n    componentDidLoad() {\n        if (this.loadingSpinner === undefined) {\n            const mode = getIonMode(this);\n            this.loadingSpinner = config.get('infiniteLoadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n        }\n    }\n    renderLoadingText() {\n        const { customHTMLEnabled, loadingText } = this;\n        if (customHTMLEnabled) {\n            return h(\"div\", { class: \"infinite-loading-text\", innerHTML: sanitizeDOMString(loadingText) });\n        }\n        return h(\"div\", { class: \"infinite-loading-text\" }, this.loadingText);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '2f4afb07bcfe3e12528eb9cee8646a097e0b359f', class: {\n                [mode]: true,\n                // Used internally for styling\n                [`infinite-scroll-content-${mode}`]: true,\n            } }, h(\"div\", { key: 'af038177bf10c88c8970682487a4328689aaa5f2', class: \"infinite-loading\" }, this.loadingSpinner && (h(\"div\", { key: '1da5d419bc6a978b6a509fdab47dae347fc8d221', class: \"infinite-loading-spinner\" }, h(\"ion-spinner\", { key: '60cc5c64e0a317ac0005d5afe42c4bb8da58136f', name: this.loadingSpinner }))), this.loadingText !== undefined && this.renderLoadingText())));\n    }\n};\nInfiniteScrollContent.style = {\n    ios: IonInfiniteScrollContentIosStyle0,\n    md: IonInfiniteScrollContentMdStyle0\n};\n\nexport { InfiniteScroll as ion_infinite_scroll, InfiniteScrollContent as ion_infinite_scroll_content };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AAC3I,SAASH,CAAC,IAAII,qBAAqB,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,gBAAgB,QAAQ,qBAAqB;AACrH,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,MAAM,QAAQ,4BAA4B;AACzE,SAASC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,sBAAsB;AAC/F,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;AAE5B,MAAMC,iBAAiB,GAAG,qFAAqF;AAC/G,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,cAAc,GAAG,MAAM;EACzBC,WAAWA,CAACC,OAAO,EAAE;IACjB7B,gBAAgB,CAAC,IAAI,EAAE6B,OAAO,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG5B,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAAC6B,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;IACd;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,QAAQ,GAAG,MAAM;MAClB,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACA,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;QAC/B,OAAO,CAAC;MACZ;MACA,MAAMC,cAAc,GAAG,IAAI,CAACC,EAAE,CAACC,YAAY;MAC3C,IAAIF,cAAc,KAAK,CAAC,EAAE;QACtB;QACA,OAAO,CAAC;MACZ;MACA,MAAMG,SAAS,GAAGL,QAAQ,CAACK,SAAS;MACpC,MAAMC,YAAY,GAAGN,QAAQ,CAACM,YAAY;MAC1C,MAAMC,MAAM,GAAGP,QAAQ,CAACI,YAAY;MACpC,MAAMI,SAAS,GAAG,IAAI,CAACZ,KAAK,KAAK,CAAC,GAAGW,MAAM,GAAG,IAAI,CAACX,KAAK,GAAG,IAAI,CAACD,KAAK;MACrE,MAAMc,oBAAoB,GAAG,IAAI,CAACC,QAAQ,KAAK,QAAQ,GACjDJ,YAAY,GAAGJ,cAAc,GAAGG,SAAS,GAAGG,SAAS,GAAGD,MAAM,GAC9DF,SAAS,GAAGH,cAAc,GAAGM,SAAS;MAC5C,IAAIC,oBAAoB,GAAG,CAAC,EAAE;QAC1B,IAAI,CAAC,IAAI,CAACZ,OAAO,EAAE;UACf,IAAI,CAACc,SAAS,GAAG,IAAI;UACrB,IAAI,CAACd,OAAO,GAAG,IAAI;UACnB,IAAI,CAACH,WAAW,CAACkB,IAAI,CAAC,CAAC;UACvB,OAAO,CAAC;QACZ;MACJ;MACA,OAAO,CAAC;IACZ,CAAC;IACD,IAAI,CAACD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACH,SAAS,GAAG,KAAK;IACtB,IAAI,CAACK,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACH,QAAQ,GAAG,QAAQ;EAC5B;EACAI,gBAAgBA,CAAA,EAAG;IACf,MAAMC,GAAG,GAAG,IAAI,CAACP,SAAS;IAC1B,IAAIO,GAAG,CAACC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3B,IAAI,CAACrB,KAAK,GAAG,CAAC;MACd,IAAI,CAACC,KAAK,GAAGqB,UAAU,CAACF,GAAG,CAAC,GAAG,GAAG;IACtC,CAAC,MACI;MACD,IAAI,CAACpB,KAAK,GAAGsB,UAAU,CAACF,GAAG,CAAC;MAC5B,IAAI,CAACnB,KAAK,GAAG,CAAC;IAClB;EACJ;EACAsB,eAAeA,CAAA,EAAG;IACd,MAAML,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACF,SAAS,GAAG,KAAK;MACtB,IAAI,CAACb,MAAM,GAAG,KAAK;IACvB;IACA,IAAI,CAACqB,kBAAkB,CAAC,CAACN,QAAQ,CAAC;EACtC;EACMO,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAMC,SAAS,GAAG/C,qBAAqB,CAAC6C,KAAI,CAAClB,EAAE,CAAC;MAChD,IAAI,CAACoB,SAAS,EAAE;QACZ7C,uBAAuB,CAAC2C,KAAI,CAAClB,EAAE,CAAC;QAChC;MACJ;MACAkB,KAAI,CAACrB,QAAQ,SAASpB,gBAAgB,CAAC2C,SAAS,CAAC;MACjDF,KAAI,CAACP,gBAAgB,CAAC,CAAC;MACvBO,KAAI,CAACH,eAAe,CAAC,CAAC;MACtB,IAAIG,KAAI,CAACX,QAAQ,KAAK,KAAK,EAAE;QACzB1C,SAAS,CAAC,MAAM;UACZ,IAAIqD,KAAI,CAACrB,QAAQ,EAAE;YACfqB,KAAI,CAACrB,QAAQ,CAACK,SAAS,GAAGgB,KAAI,CAACrB,QAAQ,CAACM,YAAY,GAAGe,KAAI,CAACrB,QAAQ,CAACwB,YAAY;UACrF;QACJ,CAAC,CAAC;MACN;IAAC;EACL;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACN,kBAAkB,CAAC,KAAK,CAAC;IAC9B,IAAI,CAACnB,QAAQ,GAAG0B,SAAS;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAN,iBAAA;MACb,MAAMtB,QAAQ,GAAG4B,MAAI,CAAC5B,QAAQ;MAC9B,IAAI,CAAC4B,MAAI,CAACjB,SAAS,IAAI,CAACX,QAAQ,EAAE;QAC9B;MACJ;MACA4B,MAAI,CAACjB,SAAS,GAAG,KAAK;MACtB,IAAIiB,MAAI,CAAClB,QAAQ,KAAK,KAAK,EAAE;QACzB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACYkB,MAAI,CAAC9B,MAAM,GAAG,IAAI;QAClB;QACA;QACA,MAAM+B,IAAI,GAAG7B,QAAQ,CAACM,YAAY,GAAGN,QAAQ,CAACK,SAAS;QACvD;QACAyB,qBAAqB,CAAC,MAAM;UACxB5D,QAAQ,CAAC,MAAM;YACX;YACA,MAAMoC,YAAY,GAAGN,QAAQ,CAACM,YAAY;YAC1C;YACA,MAAMyB,YAAY,GAAGzB,YAAY,GAAGuB,IAAI;YACxC;YACAC,qBAAqB,CAAC,MAAM;cACxB9D,SAAS,CAAC,MAAM;gBACZgC,QAAQ,CAACK,SAAS,GAAG0B,YAAY;gBACjCH,MAAI,CAAC9B,MAAM,GAAG,KAAK;gBACnB8B,MAAI,CAAC/B,OAAO,GAAG,KAAK;cACxB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,MACI;QACD+B,MAAI,CAAC/B,OAAO,GAAG,KAAK;MACxB;IAAC;EACL;EACAI,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACY,QAAQ,IAAI,CAAC,IAAI,CAACf,MAAM,IAAI,CAAC,CAAC,IAAI,CAACE,QAAQ,IAAI,CAAC,IAAI,CAACW,SAAS;EAC/E;EACAQ,kBAAkBA,CAACa,YAAY,EAAE;IAC7B,IAAI,IAAI,CAAChC,QAAQ,EAAE;MACf,IAAIgC,YAAY,EAAE;QACd,IAAI,CAAChC,QAAQ,CAACiC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAClC,QAAQ,CAAC;MAC3D,CAAC,MACI;QACD,IAAI,CAACC,QAAQ,CAACkC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACnC,QAAQ,CAAC;MAC9D;IACJ;EACJ;EACAoC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGtD,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM+B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,OAAQ1C,CAAC,CAACI,IAAI,EAAE;MAAE8D,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ,yBAAyB,EAAE,IAAI,CAACzB,SAAS;QACzC,yBAAyB,EAAE,CAACE;MAChC;IAAE,CAAC,CAAC;EACZ;EACA,IAAIV,EAAEA,CAAA,EAAG;IAAE,OAAO9B,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWkE,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,WAAW,EAAE,CAAC,kBAAkB,CAAC;MACjC,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACDhD,cAAc,CAACiD,KAAK,GAAGlD,uBAAuB;AAE9C,MAAMmD,2BAA2B,GAAG,mvCAAmvC;AACvxC,MAAMC,iCAAiC,GAAGD,2BAA2B;AAErE,MAAME,0BAA0B,GAAG,0uCAA0uC;AAC7wC,MAAMC,gCAAgC,GAAGD,0BAA0B;AAEnE,MAAME,qBAAqB,GAAG,MAAM;EAChCrD,WAAWA,CAACC,OAAO,EAAE;IACjB7B,gBAAgB,CAAC,IAAI,EAAE6B,OAAO,CAAC;IAC/B,IAAI,CAACqD,iBAAiB,GAAG9D,MAAM,CAAC+D,GAAG,CAAC,2BAA2B,EAAE7D,2BAA2B,CAAC;IAC7F,IAAI,CAAC8D,cAAc,GAAGtB,SAAS;IAC/B,IAAI,CAACuB,WAAW,GAAGvB,SAAS;EAChC;EACAwB,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACF,cAAc,KAAKtB,SAAS,EAAE;MACnC,MAAMU,IAAI,GAAGtD,UAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAACkE,cAAc,GAAGhE,MAAM,CAAC+D,GAAG,CAAC,wBAAwB,EAAE/D,MAAM,CAAC+D,GAAG,CAAC,SAAS,EAAEX,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC,CAAC;IAC5H;EACJ;EACAe,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEL,iBAAiB;MAAEG;IAAY,CAAC,GAAG,IAAI;IAC/C,IAAIH,iBAAiB,EAAE;MACnB,OAAO3E,CAAC,CAAC,KAAK,EAAE;QAAEmE,KAAK,EAAE,uBAAuB;QAAEc,SAAS,EAAEhE,iBAAiB,CAAC6D,WAAW;MAAE,CAAC,CAAC;IAClG;IACA,OAAO9E,CAAC,CAAC,KAAK,EAAE;MAAEmE,KAAK,EAAE;IAAwB,CAAC,EAAE,IAAI,CAACW,WAAW,CAAC;EACzE;EACAd,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGtD,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQX,CAAC,CAACI,IAAI,EAAE;MAAE8D,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACF,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,2BAA2BA,IAAI,EAAE,GAAG;MACzC;IAAE,CAAC,EAAEjE,CAAC,CAAC,KAAK,EAAE;MAAEkE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAAmB,CAAC,EAAE,IAAI,CAACU,cAAc,IAAK7E,CAAC,CAAC,KAAK,EAAE;MAAEkE,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;IAA2B,CAAC,EAAEnE,CAAC,CAAC,aAAa,EAAE;MAAEkE,GAAG,EAAE,0CAA0C;MAAEgB,IAAI,EAAE,IAAI,CAACL;IAAe,CAAC,CAAC,CAAE,EAAE,IAAI,CAACC,WAAW,KAAKvB,SAAS,IAAI,IAAI,CAACyB,iBAAiB,CAAC,CAAC,CAAC,CAAC;EAC/X;AACJ,CAAC;AACDN,qBAAqB,CAACL,KAAK,GAAG;EAC1Bc,GAAG,EAAEZ,iCAAiC;EACtCa,EAAE,EAAEX;AACR,CAAC;AAED,SAASrD,cAAc,IAAIiE,mBAAmB,EAAEX,qBAAqB,IAAIY,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}