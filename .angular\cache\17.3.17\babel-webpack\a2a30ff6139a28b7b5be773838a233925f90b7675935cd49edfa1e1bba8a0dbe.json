{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class AppComponent {\n  constructor() {}\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 2,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-app\");\n          i0.ɵɵelement(1, \"ion-router-outlet\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i1.IonApp, i1.IonRouterOutlet],\n      styles: [\"\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGVBQUEiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBcHAgc3R5bGVzICovXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["D:\\2025\\agmuicon\\src\\app\\app.component.ts", "D:\\2025\\agmuicon\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: 'app.component.html',\n  styleUrls: ['app.component.scss'],\n})\nexport class AppComponent {\n  constructor() {}\n}\n", "<ion-app>\n  <ion-router-outlet></ion-router-outlet>\n</ion-app>\n"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EACvBC,YAAA,GAAe;;;uBADJD,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,cAAA,cAAS;UACPD,EAAA,CAAAE,SAAA,wBAAuC;UACzCF,EAAA,CAAAG,YAAA,EAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}