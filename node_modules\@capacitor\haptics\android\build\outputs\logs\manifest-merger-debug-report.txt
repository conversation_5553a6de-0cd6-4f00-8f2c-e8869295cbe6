-- Merging decision tree log ---
manifest
ADDED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:1:1-3:12
INJECTED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:1:1-3:12
	package
		INJECTED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.VIBRATE
ADDED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:2:5-66
	android:name
		ADDED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml:2:22-63
uses-sdk
INJECTED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
INJECTED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\2025\agmuicon\node_modules\@capacitor\haptics\android\src\main\AndroidManifest.xml
