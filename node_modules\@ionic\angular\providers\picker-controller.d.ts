import { OverlayBaseController } from '@ionic/angular/common';
import type { PickerOptions } from '@ionic/core';
import * as i0 from "@angular/core";
export declare class PickerController extends OverlayBaseController<PickerOptions, HTMLIonPickerElement> {
    constructor();
    static ɵfac: i0.ɵɵFactoryDeclaration<PickerController, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<PickerController>;
}
