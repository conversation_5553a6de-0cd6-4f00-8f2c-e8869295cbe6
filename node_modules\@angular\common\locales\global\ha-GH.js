/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['ha-gh'] = ["ha-GH",[["SF","YM"],u,["<PERSON><PERSON><PERSON>","Yamma"]],[["SF","YM"],u,u],[["L","L","T","L","A","J","A"],["Lah","Lit","Tal","Lar","<PERSON>h","<PERSON><PERSON>","<PERSON><PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>ʼ<PERSON>","<PERSON><PERSON>"],["Lh","<PERSON>","<PERSON>","<PERSON>r","<PERSON>","<PERSON>","<PERSON>"]],u,[["J","F","M","A","<PERSON>","Y","Y","A","S","O","N","<PERSON>"],["<PERSON>","<PERSON>ab","<PERSON>","<PERSON>fi","May","<PERSON>","<PERSON>l","<PERSON>gu","<PERSON>t","<PERSON>t","<PERSON>uw","<PERSON>s"],["<PERSON>iru","<PERSON>ab<PERSON>ru","<PERSON>","<PERSON>firilu","Mayu","Yuni","Yuli","Agusta","Satumba","Oktoba","Nuwamba","Disamba"]],u,[["K.H","BHAI"],u,["Kafin haihuwar annab","Bayan haihuwar annab"]],1,[6,0],["d/M/yy","d MMM, y","d MMMM, y","EEEE d MMMM, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",u,"{1} 'da' {0}","{1} {0}"],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00","#E0"],"GHS","GH₵","Sidi na Ghana",{"GHS":["GH₵"],"NGN":["₦"]},"ltr", plural, []];
  })(globalThis);
    