{"name": "@angular/cli", "version": "17.3.17", "description": "CLI tool for Angular", "main": "lib/cli/index.js", "bin": {"ng": "./bin/ng.js"}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "angular-cli", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "dependencies": {"@angular-devkit/architect": "0.1703.17", "@angular-devkit/core": "17.3.17", "@angular-devkit/schematics": "17.3.17", "@schematics/angular": "17.3.17", "@yarnpkg/lockfile": "1.1.0", "ansi-colors": "4.1.3", "ini": "4.1.2", "inquirer": "9.2.15", "jsonc-parser": "3.2.1", "npm-package-arg": "11.0.1", "npm-pick-manifest": "9.0.0", "open": "8.4.2", "ora": "5.4.1", "pacote": "17.0.6", "resolve": "1.22.8", "semver": "7.6.0", "symbol-observable": "4.0.0", "yargs": "17.7.2"}, "ng-update": {"migrations": "@schematics/angular/migrations/migration-collection.json", "packageGroup": {"@angular/cli": "17.3.17", "@angular/ssr": "17.3.17", "@angular-devkit/architect": "0.1703.17", "@angular-devkit/build-angular": "17.3.17", "@angular-devkit/build-webpack": "0.1703.17", "@angular-devkit/core": "17.3.17", "@angular-devkit/schematics": "17.3.17"}}, "engines": {"node": "^18.13.0 || >=20.9.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}