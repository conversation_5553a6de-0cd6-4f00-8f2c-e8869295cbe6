/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { mapLiteral } from '../output/map_util';
import * as o from '../output/output_ast';
import { Identifiers as R3 } from './r3_identifiers';
import { devOnlyGuardedExpression } from './util';
/**
 * Generate an ngDevMode guarded call to setClassDebugInfo with the debug info about the class
 * (e.g., the file name in which the class is defined)
 */
export function compileClassDebugInfo(debugInfo) {
    const debugInfoObject = {
        className: debugInfo.className,
    };
    // Include file path and line number only if the file relative path is calculated successfully.
    if (debugInfo.filePath) {
        debugInfoObject.filePath = debugInfo.filePath;
        debugInfoObject.lineNumber = debugInfo.lineNumber;
    }
    // Include forbidOrphanRendering only if it's set to true (to reduce generated code)
    if (debugInfo.forbidOrphanRendering) {
        debugInfoObject.forbidOrphanRendering = o.literal(true);
    }
    const fnCall = o.importExpr(R3.setClassDebugInfo).callFn([
        debugInfo.type,
        mapLiteral(debugInfoObject),
    ]);
    const iife = o.arrowFn([], [devOnlyGuardedExpression(fnCall).toStmt()]);
    return iife.callFn([]);
}
//# sourceMappingURL=data:application/json;base64,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