import { ChangeDetectorRef, ElementRef, EventEmitter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonRadio extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    handleIonSelect(el: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonRadio, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonRadio, "ion-radio", never, { "color": "color"; "disabled": "disabled"; "justify": "justify"; "labelPlacement": "labelPlacement"; "legacy": "legacy"; "mode": "mode"; "name": "name"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonRadio extends Components.IonRadio {
    /**
     * Emitted when the radio button has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the radio button loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
}
