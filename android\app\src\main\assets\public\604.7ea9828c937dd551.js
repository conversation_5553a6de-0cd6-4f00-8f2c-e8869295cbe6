"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[604],{604:(P,s,t)=>{t.r(s),t.d(s,{startStatusTap:()=>r});var d=t(467),o=t(4363),_=t(2885),i=t(5638);const r=()=>{const e=window;e.addEventListener("statusTap",()=>{(0,o.e)(()=>{const a=document.elementFromPoint(e.innerWidth/2,e.innerHeight/2);if(!a)return;const n=(0,_.f)(a);n&&new Promise(h=>(0,i.c)(n,h)).then(()=>{(0,o.w)((0,d.A)(function*(){n.style.setProperty("--overflow","hidden"),yield(0,_.s)(n,300),n.style.removeProperty("--overflow")}))})})})}}}]);