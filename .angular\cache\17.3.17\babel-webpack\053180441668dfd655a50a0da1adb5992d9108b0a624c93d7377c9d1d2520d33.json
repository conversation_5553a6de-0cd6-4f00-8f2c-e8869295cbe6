{"ast": null, "code": "import _asyncToGenerator from \"D:/2025/agmuicon/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index5.js';\nimport { MENU_BACK_BUTTON_PRIORITY } from './hardware-back-button.js';\nimport { p as printIonWarning } from './index6.js';\nimport { c as componentOnReady } from './helpers.js';\nimport { b as getIonMode } from './ionic-global.js';\nimport { c as createAnimation } from './animation.js';\n\n/**\n * baseAnimation\n * Base class which is extended by the various types. Each\n * type will provide their own animations for open and close\n * and registers itself with Menu.\n */\nconst baseAnimation = isIos => {\n  // https://material.io/guidelines/motion/movement.html#movement-movement-in-out-of-screen-bounds\n  // https://material.io/guidelines/motion/duration-easing.html#duration-easing-natural-easing-curves\n  /**\n   * \"Apply the sharp curve to items temporarily leaving the screen that may return\n   * from the same exit point. When they return, use the deceleration curve. On mobile,\n   * this transition typically occurs over 300ms\" -- MD Motion Guide\n   */\n  return createAnimation().duration(isIos ? 400 : 300);\n};\n\n/**\n * Menu Overlay Type\n * The menu slides over the content. The content\n * itself, which is under the menu, does not move.\n */\nconst menuOverlayAnimation = menu => {\n  let closedX;\n  let openedX;\n  const width = menu.width + 8;\n  const menuAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  if (menu.isEndSide) {\n    // right side\n    closedX = width + 'px';\n    openedX = '0px';\n  } else {\n    // left side\n    closedX = -width + 'px';\n    openedX = '0px';\n  }\n  menuAnimation.addElement(menu.menuInnerEl).fromTo('transform', `translateX(${closedX})`, `translateX(${openedX})`);\n  const mode = getIonMode(menu);\n  const isIos = mode === 'ios';\n  const opacity = isIos ? 0.2 : 0.25;\n  backdropAnimation.addElement(menu.backdropEl).fromTo('opacity', 0.01, opacity);\n  return baseAnimation(isIos).addAnimation([menuAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Push Type\n * The content slides over to reveal the menu underneath.\n * The menu itself also slides over to reveal its bad self.\n */\nconst menuPushAnimation = menu => {\n  let contentOpenedX;\n  let menuClosedX;\n  const mode = getIonMode(menu);\n  const width = menu.width;\n  if (menu.isEndSide) {\n    contentOpenedX = -width + 'px';\n    menuClosedX = width + 'px';\n  } else {\n    contentOpenedX = width + 'px';\n    menuClosedX = -width + 'px';\n  }\n  const menuAnimation = createAnimation().addElement(menu.menuInnerEl).fromTo('transform', `translateX(${menuClosedX})`, 'translateX(0px)');\n  const contentAnimation = createAnimation().addElement(menu.contentEl).fromTo('transform', 'translateX(0px)', `translateX(${contentOpenedX})`);\n  const backdropAnimation = createAnimation().addElement(menu.backdropEl).fromTo('opacity', 0.01, 0.32);\n  return baseAnimation(mode === 'ios').addAnimation([menuAnimation, contentAnimation, backdropAnimation]);\n};\n\n/**\n * Menu Reveal Type\n * The content slides over to reveal the menu underneath.\n * The menu itself, which is under the content, does not move.\n */\nconst menuRevealAnimation = menu => {\n  const mode = getIonMode(menu);\n  const openedX = menu.width * (menu.isEndSide ? -1 : 1) + 'px';\n  const contentOpen = createAnimation().addElement(menu.contentEl) // REVIEW\n  .fromTo('transform', 'translateX(0px)', `translateX(${openedX})`);\n  return baseAnimation(mode === 'ios').addAnimation(contentOpen);\n};\nconst createMenuController = () => {\n  const menuAnimations = new Map();\n  const menus = [];\n  const open = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* (menu) {\n      const menuEl = yield get(menu, true);\n      if (menuEl) {\n        return menuEl.open();\n      }\n      return false;\n    });\n    return function open(_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  const close = /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(function* (menu) {\n      const menuEl = yield menu !== undefined ? get(menu, true) : getOpen();\n      if (menuEl !== undefined) {\n        return menuEl.close();\n      }\n      return false;\n    });\n    return function close(_x2) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  const toggle = /*#__PURE__*/function () {\n    var _ref3 = _asyncToGenerator(function* (menu) {\n      const menuEl = yield get(menu, true);\n      if (menuEl) {\n        return menuEl.toggle();\n      }\n      return false;\n    });\n    return function toggle(_x3) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  const enable = /*#__PURE__*/function () {\n    var _ref4 = _asyncToGenerator(function* (shouldEnable, menu) {\n      const menuEl = yield get(menu);\n      if (menuEl) {\n        menuEl.disabled = !shouldEnable;\n      }\n      return menuEl;\n    });\n    return function enable(_x4, _x5) {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  const swipeGesture = /*#__PURE__*/function () {\n    var _ref5 = _asyncToGenerator(function* (shouldEnable, menu) {\n      const menuEl = yield get(menu);\n      if (menuEl) {\n        menuEl.swipeGesture = shouldEnable;\n      }\n      return menuEl;\n    });\n    return function swipeGesture(_x6, _x7) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  const isOpen = /*#__PURE__*/function () {\n    var _ref6 = _asyncToGenerator(function* (menu) {\n      if (menu != null) {\n        const menuEl = yield get(menu);\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        return menuEl !== undefined && menuEl.isOpen();\n      } else {\n        const menuEl = yield getOpen();\n        return menuEl !== undefined;\n      }\n    });\n    return function isOpen(_x8) {\n      return _ref6.apply(this, arguments);\n    };\n  }();\n  const isEnabled = /*#__PURE__*/function () {\n    var _ref7 = _asyncToGenerator(function* (menu) {\n      const menuEl = yield get(menu);\n      if (menuEl) {\n        return !menuEl.disabled;\n      }\n      return false;\n    });\n    return function isEnabled(_x9) {\n      return _ref7.apply(this, arguments);\n    };\n  }();\n  /**\n   * Finds and returns the menu specified by \"menu\" if registered.\n   * @param menu - The side or ID of the desired menu\n   * @param logOnMultipleSideMenus - If true, this function will log a warning\n   * if \"menu\" is a side but multiple menus on the same side were found. Since this function\n   * is used in multiple places, we default this log to false so that the calling\n   * functions can choose whether or not it is appropriate to log this warning.\n   */\n  const get = /*#__PURE__*/function () {\n    var _ref8 = _asyncToGenerator(function* (menu, logOnMultipleSideMenus = false) {\n      yield waitUntilReady();\n      if (menu === 'start' || menu === 'end') {\n        // there could be more than one menu on the same side\n        // so first try to get the enabled one\n        const menuRefs = menus.filter(m => m.side === menu && !m.disabled);\n        if (menuRefs.length >= 1) {\n          if (menuRefs.length > 1 && logOnMultipleSideMenus) {\n            printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${menuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, menuRefs.map(m => m.el));\n          }\n          return menuRefs[0].el;\n        }\n        // didn't find a menu side that is enabled\n        // so try to get the first menu side found\n        const sideMenuRefs = menus.filter(m => m.side === menu);\n        if (sideMenuRefs.length >= 1) {\n          if (sideMenuRefs.length > 1 && logOnMultipleSideMenus) {\n            printIonWarning(`menuController queried for a menu on the \"${menu}\" side, but ${sideMenuRefs.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`, sideMenuRefs.map(m => m.el));\n          }\n          return sideMenuRefs[0].el;\n        }\n      } else if (menu != null) {\n        // the menuId was not left or right\n        // so try to get the menu by its \"id\"\n        return find(m => m.menuId === menu);\n      }\n      // return the first enabled menu\n      const menuEl = find(m => !m.disabled);\n      if (menuEl) {\n        return menuEl;\n      }\n      // get the first menu in the array, if one exists\n      return menus.length > 0 ? menus[0].el : undefined;\n    });\n    return function get(_x0) {\n      return _ref8.apply(this, arguments);\n    };\n  }();\n  /**\n   * Get the instance of the opened menu. Returns `null` if a menu is not found.\n   */\n  const getOpen = /*#__PURE__*/function () {\n    var _ref9 = _asyncToGenerator(function* () {\n      yield waitUntilReady();\n      return _getOpenSync();\n    });\n    return function getOpen() {\n      return _ref9.apply(this, arguments);\n    };\n  }();\n  /**\n   * Get all menu instances.\n   */\n  const getMenus = /*#__PURE__*/function () {\n    var _ref0 = _asyncToGenerator(function* () {\n      yield waitUntilReady();\n      return getMenusSync();\n    });\n    return function getMenus() {\n      return _ref0.apply(this, arguments);\n    };\n  }();\n  /**\n   * Get whether or not a menu is animating. Returns `true` if any\n   * menu is currently animating.\n   */\n  const isAnimating = /*#__PURE__*/function () {\n    var _ref1 = _asyncToGenerator(function* () {\n      yield waitUntilReady();\n      return isAnimatingSync();\n    });\n    return function isAnimating() {\n      return _ref1.apply(this, arguments);\n    };\n  }();\n  const registerAnimation = (name, animation) => {\n    menuAnimations.set(name, animation);\n  };\n  const _register = menu => {\n    if (menus.indexOf(menu) < 0) {\n      menus.push(menu);\n    }\n  };\n  const _unregister = menu => {\n    const index = menus.indexOf(menu);\n    if (index > -1) {\n      menus.splice(index, 1);\n    }\n  };\n  const _setOpen = /*#__PURE__*/function () {\n    var _ref10 = _asyncToGenerator(function* (menu, shouldOpen, animated) {\n      if (isAnimatingSync()) {\n        return false;\n      }\n      if (shouldOpen) {\n        const openedMenu = yield getOpen();\n        if (openedMenu && menu.el !== openedMenu) {\n          yield openedMenu.setOpen(false, false);\n        }\n      }\n      return menu._setOpen(shouldOpen, animated);\n    });\n    return function _setOpen(_x1, _x10, _x11) {\n      return _ref10.apply(this, arguments);\n    };\n  }();\n  const _createAnimation = (type, menuCmp) => {\n    const animationBuilder = menuAnimations.get(type); // TODO(FW-2832): type\n    if (!animationBuilder) {\n      throw new Error('animation not registered');\n    }\n    const animation = animationBuilder(menuCmp);\n    return animation;\n  };\n  const _getOpenSync = () => {\n    return find(m => m._isOpen);\n  };\n  const getMenusSync = () => {\n    return menus.map(menu => menu.el);\n  };\n  const isAnimatingSync = () => {\n    return menus.some(menu => menu.isAnimating);\n  };\n  const find = predicate => {\n    const instance = menus.find(predicate);\n    if (instance !== undefined) {\n      return instance.el;\n    }\n    return undefined;\n  };\n  const waitUntilReady = () => {\n    return Promise.all(Array.from(document.querySelectorAll('ion-menu')).map(menu => new Promise(resolve => componentOnReady(menu, resolve))));\n  };\n  registerAnimation('reveal', menuRevealAnimation);\n  registerAnimation('push', menuPushAnimation);\n  registerAnimation('overlay', menuOverlayAnimation);\n  doc === null || doc === void 0 ? void 0 : doc.addEventListener('ionBackButton', ev => {\n    const openMenu = _getOpenSync();\n    if (openMenu) {\n      ev.detail.register(MENU_BACK_BUTTON_PRIORITY, () => {\n        return openMenu.close();\n      });\n    }\n  });\n  return {\n    registerAnimation,\n    get,\n    getMenus,\n    getOpen,\n    isEnabled,\n    swipeGesture,\n    isAnimating,\n    isOpen,\n    enable,\n    toggle,\n    close,\n    open,\n    _getOpenSync,\n    _createAnimation,\n    _register,\n    _unregister,\n    _setOpen\n  };\n};\nconst menuController = /*@__PURE__*/createMenuController();\nexport { menuController as m };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}