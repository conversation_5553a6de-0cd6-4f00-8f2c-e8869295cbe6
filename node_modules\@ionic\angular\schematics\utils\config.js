"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWorkspace = exports.getWorkspacePath = exports.addSchematics = exports.addCli = exports.addArchitectBuilder = exports.addAsset = exports.addStyle = exports.getDefaultAngularAppName = exports.writeConfig = exports.readConfig = void 0;
const schematics_1 = require("@angular-devkit/schematics");
const jsonc_parser_1 = require("jsonc-parser");
const ANGULAR_JSON_PATH = 'angular.json';
function readConfig(host) {
    return host.readJson(ANGULAR_JSON_PATH);
}
exports.readConfig = readConfig;
function writeConfig(host, config) {
    host.overwrite(ANGULAR_JSON_PATH, JSON.stringify(config, null, 2));
}
exports.writeConfig = writeConfig;
function isAngularBrowserProject(projectConfig) {
    if (projectConfig.projectType === 'application') {
        const buildConfig = projectConfig.architect.build;
        // Angular 16 and lower
        const legacyAngularBuilder = buildConfig.builder === '@angular-devkit/build-angular:browser';
        // Angular 17+
        const modernAngularBuilder = buildConfig.builder === '@angular-devkit/build-angular:application';
        return legacyAngularBuilder || modernAngularBuilder;
    }
    return false;
}
function getDefaultAngularAppName(config) {
    const projects = config.projects;
    const projectNames = Object.keys(projects);
    for (const projectName of projectNames) {
        const projectConfig = projects[projectName];
        if (isAngularBrowserProject(projectConfig)) {
            return projectName;
        }
    }
    return projectNames[0];
}
exports.getDefaultAngularAppName = getDefaultAngularAppName;
function getAngularJson(config, projectName) {
    // eslint-disable-next-line no-prototype-builtins
    if (!config.projects.hasOwnProperty(projectName)) {
        throw new schematics_1.SchematicsException(`Could not find project: ${projectName}`);
    }
    const projectConfig = config.projects[projectName];
    if (isAngularBrowserProject(projectConfig)) {
        return projectConfig;
    }
    if (config.projectType !== 'application') {
        throw new schematics_1.SchematicsException(`Invalid projectType for ${projectName}: ${config.projectType}`);
    }
    else {
        const buildConfig = projectConfig.architect.build;
        throw new schematics_1.SchematicsException(`Invalid builder for ${projectName}: ${buildConfig.builder}`);
    }
}
function addStyle(host, projectName, stylePath) {
    const config = readConfig(host);
    const angularJson = getAngularJson(config, projectName);
    angularJson.architect.build.options.styles.push({
        input: stylePath,
    });
    writeConfig(host, config);
}
exports.addStyle = addStyle;
function addAsset(host, projectName, architect, asset) {
    const config = readConfig(host);
    const angularJson = getAngularJson(config, projectName);
    const target = angularJson.architect[architect];
    if (target) {
        target.options.assets.push(asset);
        writeConfig(host, config);
    }
}
exports.addAsset = addAsset;
function addArchitectBuilder(host, projectName, builderName, builderOpts) {
    const config = readConfig(host);
    const angularJson = getAngularJson(config, projectName);
    angularJson.architect[builderName] = builderOpts;
    writeConfig(host, config);
}
exports.addArchitectBuilder = addArchitectBuilder;
/**
 * Updates the angular.json to add an additional schematic collection
 * to the CLI configuration.
 */
function addCli(host, collectionName) {
    const angularJson = readConfig(host);
    if (angularJson.cli === undefined) {
        angularJson.cli = {};
    }
    if (angularJson.cli.schematicCollections === undefined) {
        angularJson.cli.schematicCollections = [];
    }
    angularJson.cli.schematicCollections.push(collectionName);
    writeConfig(host, angularJson);
}
exports.addCli = addCli;
// TODO(FW-5639): can remove [property: string]: any; when upgrading @angular/cli dev-dep to v16 or later
function addSchematics(host, schematicName, schematicOpts) {
    const angularJson = readConfig(host);
    if (angularJson.schematics === undefined) {
        angularJson.schematics = {};
    }
    angularJson.schematics[schematicName] = schematicOpts;
    writeConfig(host, angularJson);
}
exports.addSchematics = addSchematics;
function getWorkspacePath(host) {
    const possibleFiles = ['/angular.json', '/.angular.json'];
    const path = possibleFiles.filter((path) => host.exists(path))[0];
    return path;
}
exports.getWorkspacePath = getWorkspacePath;
function getWorkspace(host) {
    const path = getWorkspacePath(host);
    const configBuffer = host.read(path);
    if (configBuffer === null) {
        throw new schematics_1.SchematicsException(`Could not find (${path})`);
    }
    const content = configBuffer.toString();
    return (0, jsonc_parser_1.parse)(content);
}
exports.getWorkspace = getWorkspace;
