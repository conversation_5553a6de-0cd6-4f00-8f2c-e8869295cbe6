{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class HomePage {\n  constructor() {}\n  static {\n    this.ɵfac = function HomePage_Factory(t) {\n      return new (t || HomePage)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomePage,\n      selectors: [[\"app-home\"]],\n      decls: 35,\n      vars: 2,\n      consts: [[3, \"translucent\"], [3, \"fullscreen\"], [\"collapse\", \"condense\"], [\"size\", \"large\"], [\"id\", \"container\"], [\"expand\", \"block\", \"color\", \"primary\"], [\"name\", \"rocket\", \"slot\", \"start\"], [\"name\", \"checkmark-circle\", \"slot\", \"start\", \"color\", \"success\"]],\n      template: function HomePage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\")(2, \"ion-title\");\n          i0.ɵɵtext(3, \" \\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u062A\\u0637\\u0628\\u064A\\u0642 Ionic \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(4, \"ion-content\", 1)(5, \"ion-header\", 2)(6, \"ion-toolbar\")(7, \"ion-title\", 3);\n          i0.ɵɵtext(8, \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 4)(10, \"strong\");\n          i0.ɵɵtext(11, \"\\u062A\\u0637\\u0628\\u064A\\u0642 Ionic Android \\u0628\\u0633\\u064A\\u0637\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\");\n          i0.ɵɵtext(13, \"\\u0647\\u0630\\u0627 \\u062A\\u0637\\u0628\\u064A\\u0642 Ionic \\u0628\\u0633\\u064A\\u0637 \\u064A\\u0645\\u0643\\u0646 \\u062A\\u0634\\u063A\\u064A\\u0644\\u0647 \\u0639\\u0644\\u0649 Android\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"ion-button\", 5);\n          i0.ɵɵelement(15, \"ion-icon\", 6);\n          i0.ɵɵtext(16, \" \\u0627\\u0628\\u062F\\u0623 \\u0627\\u0644\\u0622\\u0646 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"ion-card\")(18, \"ion-card-header\")(19, \"ion-card-title\");\n          i0.ɵɵtext(20, \"\\u0645\\u0645\\u064A\\u0632\\u0627\\u062A \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"ion-card-content\")(22, \"ion-list\")(23, \"ion-item\");\n          i0.ɵɵelement(24, \"ion-icon\", 7);\n          i0.ɵɵelementStart(25, \"ion-label\");\n          i0.ɵɵtext(26, \"\\u0633\\u0647\\u0644 \\u0627\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"ion-item\");\n          i0.ɵɵelement(28, \"ion-icon\", 7);\n          i0.ɵɵelementStart(29, \"ion-label\");\n          i0.ɵɵtext(30, \"\\u0645\\u062A\\u0648\\u0627\\u0641\\u0642 \\u0645\\u0639 Android\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"ion-item\");\n          i0.ɵɵelement(32, \"ion-icon\", 7);\n          i0.ɵɵelementStart(33, \"ion-label\");\n          i0.ɵɵtext(34, \"\\u062A\\u0635\\u0645\\u064A\\u0645 \\u062C\\u0645\\u064A\\u0644\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"translucent\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"fullscreen\", true);\n        }\n      },\n      dependencies: [i1.IonButton, i1.IonCard, i1.IonCardContent, i1.IonCardHeader, i1.IonCardTitle, i1.IonContent, i1.IonHeader, i1.IonIcon, i1.IonItem, i1.IonLabel, i1.IonList, i1.IonTitle, i1.IonToolbar],\n      styles: [\"#container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  padding: 20px;\\n}\\n\\n#container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  line-height: 26px;\\n}\\n\\n#container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  line-height: 22px;\\n  color: #8c8c8c;\\n  margin: 0;\\n}\\n\\n#container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\nion-button[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n\\nion-card[_ngcontent-%COMP%] {\\n  margin: 20px;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaG9tZS9ob21lLnBhZ2Uuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFFBQUE7RUFDQSwyQkFBQTtFQUNBLGFBQUE7QUFDRjs7QUFFQTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtBQUNGOztBQUVBO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLFNBQUE7QUFDRjs7QUFFQTtFQUNFLHFCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxZQUFBO0FBQ0Y7O0FBRUE7RUFDRSxxQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiI2NvbnRhaW5lciB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgdG9wOiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTtcbiAgcGFkZGluZzogMjBweDtcbn1cblxuI2NvbnRhaW5lciBzdHJvbmcge1xuICBmb250LXNpemU6IDIwcHg7XG4gIGxpbmUtaGVpZ2h0OiAyNnB4O1xufVxuXG4jY29udGFpbmVyIHAge1xuICBmb250LXNpemU6IDE2cHg7XG4gIGxpbmUtaGVpZ2h0OiAyMnB4O1xuICBjb2xvcjogIzhjOGM4YztcbiAgbWFyZ2luOiAwO1xufVxuXG4jY29udGFpbmVyIGEge1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG59XG5cbmlvbi1idXR0b24ge1xuICBtYXJnaW46IDIwcHggMDtcbn1cblxuaW9uLWNhcmQge1xuICBtYXJnaW46IDIwcHg7XG59XG5cbmlvbi1pdGVtIHtcbiAgLS1wYWRkaW5nLXN0YXJ0OiAxNnB4O1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HomePage", "constructor", "selectors", "decls", "vars", "consts", "template", "HomePage_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵproperty", "ɵɵadvance"], "sources": ["D:\\2025\\agmuicon\\src\\app\\home\\home.page.ts", "D:\\2025\\agmuicon\\src\\app\\home\\home.page.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: 'home.page.html',\n  styleUrls: ['home.page.scss'],\n})\nexport class HomePage {\n\n  constructor() {}\n\n}\n", "<ion-header [translucent]=\"true\">\n  <ion-toolbar>\n    <ion-title>\n      مرحباً بك في تطبيق Ionic\n    </ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\">\n  <ion-header collapse=\"condense\">\n    <ion-toolbar>\n      <ion-title size=\"large\">مرحباً بك</ion-title>\n    </ion-toolbar>\n  </ion-header>\n\n  <div id=\"container\">\n    <strong>تطبيق Ionic Android بسيط</strong>\n    <p>هذا تطبيق Ionic بسيط يمكن تشغيله على Android</p>\n    \n    <ion-button expand=\"block\" color=\"primary\">\n      <ion-icon name=\"rocket\" slot=\"start\"></ion-icon>\n      ابدأ الآن\n    </ion-button>\n    \n    <ion-card>\n      <ion-card-header>\n        <ion-card-title>مميزات التطبيق</ion-card-title>\n      </ion-card-header>\n      <ion-card-content>\n        <ion-list>\n          <ion-item>\n            <ion-icon name=\"checkmark-circle\" slot=\"start\" color=\"success\"></ion-icon>\n            <ion-label>سهل الاستخدام</ion-label>\n          </ion-item>\n          <ion-item>\n            <ion-icon name=\"checkmark-circle\" slot=\"start\" color=\"success\"></ion-icon>\n            <ion-label>متوافق مع Android</ion-label>\n          </ion-item>\n          <ion-item>\n            <ion-icon name=\"checkmark-circle\" slot=\"start\" color=\"success\"></ion-icon>\n            <ion-label>تصميم جميل</ion-label>\n          </ion-item>\n        </ion-list>\n      </ion-card-content>\n    </ion-card>\n  </div>\n</ion-content>\n"], "mappings": ";;AAOA,OAAM,MAAOA,QAAQ;EAEnBC,YAAA,GAAe;;;uBAFJD,QAAQ;IAAA;EAAA;;;YAARA,QAAQ;MAAAE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLjBE,EAFJ,CAAAC,cAAA,oBAAiC,kBAClB,gBACA;UACTD,EAAA,CAAAE,MAAA,4GACF;UAEJF,EAFI,CAAAG,YAAA,EAAY,EACA,EACH;UAKPH,EAHN,CAAAC,cAAA,qBAAiC,oBACC,kBACjB,mBACa;UAAAD,EAAA,CAAAE,MAAA,wDAAS;UAErCF,EAFqC,CAAAG,YAAA,EAAY,EACjC,EACH;UAGXH,EADF,CAAAC,cAAA,aAAoB,cACV;UAAAD,EAAA,CAAAE,MAAA,6EAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,iLAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEnDH,EAAA,CAAAC,cAAA,qBAA2C;UACzCD,EAAA,CAAAI,SAAA,mBAAgD;UAChDJ,EAAA,CAAAE,MAAA,2DACF;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAITH,EAFJ,CAAAC,cAAA,gBAAU,uBACS,sBACC;UAAAD,EAAA,CAAAE,MAAA,uFAAc;UAChCF,EADgC,CAAAG,YAAA,EAAiB,EAC/B;UAGdH,EAFJ,CAAAC,cAAA,wBAAkB,gBACN,gBACE;UACRD,EAAA,CAAAI,SAAA,mBAA0E;UAC1EJ,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,iFAAa;UAC1BF,EAD0B,CAAAG,YAAA,EAAY,EAC3B;UACXH,EAAA,CAAAC,cAAA,gBAAU;UACRD,EAAA,CAAAI,SAAA,mBAA0E;UAC1EJ,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,iEAAiB;UAC9BF,EAD8B,CAAAG,YAAA,EAAY,EAC/B;UACXH,EAAA,CAAAC,cAAA,gBAAU;UACRD,EAAA,CAAAI,SAAA,mBAA0E;UAC1EJ,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,+DAAU;UAMjCF,EANiC,CAAAG,YAAA,EAAY,EACxB,EACF,EACM,EACV,EACP,EACM;;;UA9CFH,EAAA,CAAAK,UAAA,qBAAoB;UAQnBL,EAAA,CAAAM,SAAA,GAAmB;UAAnBN,EAAA,CAAAK,UAAA,oBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}