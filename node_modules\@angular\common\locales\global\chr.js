/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['chr'] = ["chr",[["Ꮜ","Ꮢ"],["ᏌᎾᎴ","ᏒᎯᏱᎢ"],["ᏌᎾᎴ","ᏒᎯᏱᎢᏗᏢ"]],[["ᏌᎾᎴ","ᏒᎯᏱᎢ"],u,["ᏌᎾᎴ","ᏒᎯᏱᎢᏗᏢ"]],[["Ꮖ","Ꮙ","Ꮤ","Ꮶ","Ꮕ","Ꮷ","Ꭴ"],["ᏆᏍᎬ","ᏉᏅᎯ","ᏔᎵᏁ","ᏦᎢᏁ","ᏅᎩᏁ","ᏧᎾᎩ","ᏈᏕᎾ"],["ᎤᎾᏙᏓᏆᏍᎬ","ᎤᎾᏙᏓᏉᏅᎯ","ᏔᎵᏁᎢᎦ","ᏦᎢᏁᎢᎦ","ᏅᎩᏁᎢᎦ","ᏧᎾᎩᎶᏍᏗ","ᎤᎾᏙᏓᏈᏕᎾ"],["ᏍᎬ","ᏅᎯ","ᏔᎵ","ᏦᎢ","ᏅᎩ","ᏧᎾ","ᏕᎾ"]],u,[["Ꭴ","Ꭷ","Ꭰ","Ꭷ","Ꭰ","Ꮥ","Ꭻ","Ꭶ","Ꮪ","Ꮪ","Ꮕ","Ꭵ"],["ᎤᏃ","ᎧᎦ","ᎠᏅ","ᎧᏬ","ᎠᏂ","ᏕᎭ","ᎫᏰ","ᎦᎶ","ᏚᎵ","ᏚᏂ","ᏅᏓ","ᎥᏍ"],["ᎤᏃᎸᏔᏅ","ᎧᎦᎵ","ᎠᏅᏱ","ᎧᏬᏂ","ᎠᏂᏍᎬᏘ","ᏕᎭᎷᏱ","ᎫᏰᏉᏂ","ᎦᎶᏂ","ᏚᎵᏍᏗ","ᏚᏂᏅᏗ","ᏅᏓᏕᏆ","ᎥᏍᎩᏱ"]],u,[["BC","AD"],u,["ᏧᏓᎷᎸ ᎤᎷᎯᏍᏗ ᎦᎶᏁᏛ","ᎠᏃ ᏙᎻᏂ"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",u,"{1} ᎤᎾᎢ {0}",u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"USD","$","US ᎠᏕᎳ",{"BYN":[u,"р."],"JPY":["JP¥","¥"],"PHP":[u,"₱"]},"ltr", plural, [[["Ꭲ","ᏌᎾᎴ","ᏒᎯᏱᎢᏗᏢ"],["ᎢᎦ","ᏌᎾᎴ","ᏒᎯᏱᎢᏗᏢ"],u],[["ᎢᎦ","ᏌᎾᎴ","ᏒᎯᏱᎢᏗᏢ"],u,u],["12:00",["00:00","12:00"],["12:00","24:00"]]]];
  })(globalThis);
    