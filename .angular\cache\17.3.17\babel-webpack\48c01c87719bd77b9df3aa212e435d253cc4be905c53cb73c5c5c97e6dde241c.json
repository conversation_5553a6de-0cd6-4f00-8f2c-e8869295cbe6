{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Does a simple sanitization of all elements\n * in an untrusted string\n */\nconst sanitizeDOMString = untrustedString => {\n  try {\n    if (untrustedString instanceof IonicSafeString) {\n      return untrustedString.value;\n    }\n    if (!isSanitizerEnabled() || typeof untrustedString !== 'string' || untrustedString === '') {\n      return untrustedString;\n    }\n    /**\n     * onload is fired when appending to a document\n     * fragment in Chrome. If a string\n     * contains onload then we should not\n     * attempt to add this to the fragment.\n     */\n    if (untrustedString.includes('onload=')) {\n      return '';\n    }\n    /**\n     * Create a document fragment\n     * separate from the main DOM,\n     * create a div to do our work in\n     */\n    const documentFragment = document.createDocumentFragment();\n    const workingDiv = document.createElement('div');\n    documentFragment.appendChild(workingDiv);\n    workingDiv.innerHTML = untrustedString;\n    /**\n     * Remove any elements\n     * that are blocked\n     */\n    blockedTags.forEach(blockedTag => {\n      const getElementsToRemove = documentFragment.querySelectorAll(blockedTag);\n      for (let elementIndex = getElementsToRemove.length - 1; elementIndex >= 0; elementIndex--) {\n        const element = getElementsToRemove[elementIndex];\n        if (element.parentNode) {\n          element.parentNode.removeChild(element);\n        } else {\n          documentFragment.removeChild(element);\n        }\n        /**\n         * We still need to sanitize\n         * the children of this element\n         * as they are left behind\n         */\n        const childElements = getElementChildren(element);\n        /* eslint-disable-next-line */\n        for (let childIndex = 0; childIndex < childElements.length; childIndex++) {\n          sanitizeElement(childElements[childIndex]);\n        }\n      }\n    });\n    /**\n     * Go through remaining elements and remove\n     * non-allowed attribs\n     */\n    // IE does not support .children on document fragments, only .childNodes\n    const dfChildren = getElementChildren(documentFragment);\n    /* eslint-disable-next-line */\n    for (let childIndex = 0; childIndex < dfChildren.length; childIndex++) {\n      sanitizeElement(dfChildren[childIndex]);\n    }\n    // Append document fragment to div\n    const fragmentDiv = document.createElement('div');\n    fragmentDiv.appendChild(documentFragment);\n    // First child is always the div we did our work in\n    const getInnerDiv = fragmentDiv.querySelector('div');\n    return getInnerDiv !== null ? getInnerDiv.innerHTML : fragmentDiv.innerHTML;\n  } catch (err) {\n    console.error(err);\n    return '';\n  }\n};\n/**\n * Clean up current element based on allowed attributes\n * and then recursively dig down into any child elements to\n * clean those up as well\n */\n// TODO(FW-2832): type (using Element triggers other type errors as well)\nconst sanitizeElement = element => {\n  // IE uses childNodes, so ignore nodes that are not elements\n  if (element.nodeType && element.nodeType !== 1) {\n    return;\n  }\n  /**\n   * If attributes is not a NamedNodeMap\n   * then we should remove the element entirely.\n   * This helps avoid DOM Clobbering attacks where\n   * attributes is overridden.\n   */\n  if (typeof NamedNodeMap !== 'undefined' && !(element.attributes instanceof NamedNodeMap)) {\n    element.remove();\n    return;\n  }\n  for (let i = element.attributes.length - 1; i >= 0; i--) {\n    const attribute = element.attributes.item(i);\n    const attributeName = attribute.name;\n    // remove non-allowed attribs\n    if (!allowedAttributes.includes(attributeName.toLowerCase())) {\n      element.removeAttribute(attributeName);\n      continue;\n    }\n    // clean up any allowed attribs\n    // that attempt to do any JS funny-business\n    const attributeValue = attribute.value;\n    /**\n     * We also need to check the property value\n     * as javascript: can allow special characters\n     * such as &Tab; and still be valid (i.e. java&Tab;script)\n     */\n    const propertyValue = element[attributeName];\n    /* eslint-disable */\n    if (attributeValue != null && attributeValue.toLowerCase().includes('javascript:') || propertyValue != null && propertyValue.toLowerCase().includes('javascript:')) {\n      element.removeAttribute(attributeName);\n    }\n    /* eslint-enable */\n  }\n  /**\n   * Sanitize any nested children\n   */\n  const childElements = getElementChildren(element);\n  /* eslint-disable-next-line */\n  for (let i = 0; i < childElements.length; i++) {\n    sanitizeElement(childElements[i]);\n  }\n};\n/**\n * IE doesn't always support .children\n * so we revert to .childNodes instead\n */\n// TODO(FW-2832): type\nconst getElementChildren = el => {\n  return el.children != null ? el.children : el.childNodes;\n};\nconst isSanitizerEnabled = () => {\n  var _a;\n  const win = window;\n  const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n  if (config) {\n    if (config.get) {\n      return config.get('sanitizerEnabled', true);\n    } else {\n      return config.sanitizerEnabled === true || config.sanitizerEnabled === undefined;\n    }\n  }\n  return true;\n};\nconst allowedAttributes = ['class', 'id', 'href', 'src', 'name', 'slot'];\nconst blockedTags = ['script', 'style', 'iframe', 'meta', 'link', 'object', 'embed'];\nclass IonicSafeString {\n  constructor(value) {\n    this.value = value;\n  }\n}\nconst setupConfig = config => {\n  const win = window;\n  const Ionic = win.Ionic;\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (Ionic && Ionic.config && Ionic.config.constructor.name !== 'Object') {\n    return;\n  }\n  win.Ionic = win.Ionic || {};\n  win.Ionic.config = Object.assign(Object.assign({}, win.Ionic.config), config);\n  return win.Ionic.config;\n};\nconst getMode = () => {\n  var _a;\n  const win = window;\n  const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n  if (config) {\n    if (config.mode) {\n      return config.mode;\n    } else {\n      return config.get('mode');\n    }\n  }\n  return 'md';\n};\nconst ENABLE_HTML_CONTENT_DEFAULT = false;\nexport { ENABLE_HTML_CONTENT_DEFAULT as E, IonicSafeString as I, sanitizeDOMString as a, getMode as g, setupConfig as s };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}