import { ChangeDetector<PERSON>ef, ElementRef, EventEmitter, NgZone } from '@angular/core';
import type { Components } from '@ionic/core/components';
import type { AccordionGroupChangeEventDetail as IIonAccordionGroupAccordionGroupChangeEventDetail } from '@ionic/core/components';
import type { OverlayEventDetail as IIonActionSheetOverlayEventDetail } from '@ionic/core/components';
import type { OverlayEventDetail as IIonAlertOverlayEventDetail } from '@ionic/core/components';
import type { BreadcrumbCollapsedClickEventDetail as IIonBreadcrumbsBreadcrumbCollapsedClickEventDetail } from '@ionic/core/components';
import type { ScrollBaseDetail as IIonContentScrollBaseDetail } from '@ionic/core/components';
import type { ScrollDetail as IIonContentScrollDetail } from '@ionic/core/components';
import type { OverlayEventDetail as IIonLoadingOverlayEventDetail } from '@ionic/core/components';
import type { OverlayEventDetail as IIonPickerOverlayEventDetail } from '@ionic/core/components';
import type { RefresherEventDetail as IIonRefresherRefresherEventDetail } from '@ionic/core/components';
import type { ItemReorderEventDetail as IIonReorderGroupItemReorderEventDetail } from '@ionic/core/components';
import type { OverlayEventDetail as IIonToastOverlayEventDetail } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonAccordion {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonAccordion, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonAccordion, "ion-accordion", never, { "disabled": "disabled"; "mode": "mode"; "readonly": "readonly"; "toggleIcon": "toggleIcon"; "toggleIconSlot": "toggleIconSlot"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonAccordion extends Components.IonAccordion {
}
export declare class IonAccordionGroup {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonAccordionGroup, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonAccordionGroup, "ion-accordion-group", never, { "animated": "animated"; "disabled": "disabled"; "expand": "expand"; "mode": "mode"; "multiple": "multiple"; "readonly": "readonly"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonAccordionGroup extends Components.IonAccordionGroup {
    /**
     * Emitted when the value property has changed
  as a result of a user action such as a click.
  This event will not emit when programmatically setting
  the value property.
     */
    ionChange: EventEmitter<CustomEvent<IIonAccordionGroupAccordionGroupChangeEventDetail>>;
}
export declare class IonActionSheet {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonActionSheet, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonActionSheet, "ion-action-sheet", never, { "animated": "animated"; "backdropDismiss": "backdropDismiss"; "buttons": "buttons"; "cssClass": "cssClass"; "enterAnimation": "enterAnimation"; "header": "header"; "htmlAttributes": "htmlAttributes"; "isOpen": "isOpen"; "keyboardClose": "keyboardClose"; "leaveAnimation": "leaveAnimation"; "mode": "mode"; "subHeader": "subHeader"; "translucent": "translucent"; "trigger": "trigger"; }, {}, never, ["*"], true>;
}
export declare interface IonActionSheet extends Components.IonActionSheet {
    /**
     * Emitted after the action sheet has presented.
     */
    ionActionSheetDidPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the action sheet has presented.
     */
    ionActionSheetWillPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the action sheet has dismissed.
     */
    ionActionSheetWillDismiss: EventEmitter<CustomEvent<IIonActionSheetOverlayEventDetail>>;
    /**
     * Emitted after the action sheet has dismissed.
     */
    ionActionSheetDidDismiss: EventEmitter<CustomEvent<IIonActionSheetOverlayEventDetail>>;
    /**
     * Emitted after the action sheet has presented.
  Shorthand for ionActionSheetWillDismiss.
     */
    didPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the action sheet has presented.
  Shorthand for ionActionSheetWillPresent.
     */
    willPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the action sheet has dismissed.
  Shorthand for ionActionSheetWillDismiss.
     */
    willDismiss: EventEmitter<CustomEvent<IIonActionSheetOverlayEventDetail>>;
    /**
     * Emitted after the action sheet has dismissed.
  Shorthand for ionActionSheetDidDismiss.
     */
    didDismiss: EventEmitter<CustomEvent<IIonActionSheetOverlayEventDetail>>;
}
export declare class IonAlert {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonAlert, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonAlert, "ion-alert", never, { "animated": "animated"; "backdropDismiss": "backdropDismiss"; "buttons": "buttons"; "cssClass": "cssClass"; "enterAnimation": "enterAnimation"; "header": "header"; "htmlAttributes": "htmlAttributes"; "inputs": "inputs"; "isOpen": "isOpen"; "keyboardClose": "keyboardClose"; "leaveAnimation": "leaveAnimation"; "message": "message"; "mode": "mode"; "subHeader": "subHeader"; "translucent": "translucent"; "trigger": "trigger"; }, {}, never, ["*"], true>;
}
export declare interface IonAlert extends Components.IonAlert {
    /**
     * Emitted after the alert has presented.
     */
    ionAlertDidPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the alert has presented.
     */
    ionAlertWillPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the alert has dismissed.
     */
    ionAlertWillDismiss: EventEmitter<CustomEvent<IIonAlertOverlayEventDetail>>;
    /**
     * Emitted after the alert has dismissed.
     */
    ionAlertDidDismiss: EventEmitter<CustomEvent<IIonAlertOverlayEventDetail>>;
    /**
     * Emitted after the alert has presented.
  Shorthand for ionAlertWillDismiss.
     */
    didPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the alert has presented.
  Shorthand for ionAlertWillPresent.
     */
    willPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the alert has dismissed.
  Shorthand for ionAlertWillDismiss.
     */
    willDismiss: EventEmitter<CustomEvent<IIonAlertOverlayEventDetail>>;
    /**
     * Emitted after the alert has dismissed.
  Shorthand for ionAlertDidDismiss.
     */
    didDismiss: EventEmitter<CustomEvent<IIonAlertOverlayEventDetail>>;
}
export declare class IonApp {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonApp, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonApp, "ion-app", never, {}, {}, never, ["*"], true>;
}
export declare interface IonApp extends Components.IonApp {
}
export declare class IonAvatar {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonAvatar, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonAvatar, "ion-avatar", never, {}, {}, never, ["*"], true>;
}
export declare interface IonAvatar extends Components.IonAvatar {
}
export declare class IonBackdrop {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonBackdrop, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonBackdrop, "ion-backdrop", never, { "stopPropagation": "stopPropagation"; "tappable": "tappable"; "visible": "visible"; }, {}, never, ["*"], true>;
}
export declare interface IonBackdrop extends Components.IonBackdrop {
    /**
     * Emitted when the backdrop is tapped.
     */
    ionBackdropTap: EventEmitter<CustomEvent<void>>;
}
export declare class IonBadge {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonBadge, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonBadge, "ion-badge", never, { "color": "color"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonBadge extends Components.IonBadge {
}
export declare class IonBreadcrumb {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonBreadcrumb, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonBreadcrumb, "ion-breadcrumb", never, { "active": "active"; "color": "color"; "disabled": "disabled"; "download": "download"; "href": "href"; "mode": "mode"; "rel": "rel"; "routerAnimation": "routerAnimation"; "routerDirection": "routerDirection"; "separator": "separator"; "target": "target"; }, {}, never, ["*"], true>;
}
export declare interface IonBreadcrumb extends Components.IonBreadcrumb {
    /**
     * Emitted when the breadcrumb has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the breadcrumb loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
}
export declare class IonBreadcrumbs {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonBreadcrumbs, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonBreadcrumbs, "ion-breadcrumbs", never, { "color": "color"; "itemsAfterCollapse": "itemsAfterCollapse"; "itemsBeforeCollapse": "itemsBeforeCollapse"; "maxItems": "maxItems"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonBreadcrumbs extends Components.IonBreadcrumbs {
    /**
     * Emitted when the collapsed indicator is clicked on.
     */
    ionCollapsedClick: EventEmitter<CustomEvent<IIonBreadcrumbsBreadcrumbCollapsedClickEventDetail>>;
}
export declare class IonButton {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonButton, "ion-button", never, { "buttonType": "buttonType"; "color": "color"; "disabled": "disabled"; "download": "download"; "expand": "expand"; "fill": "fill"; "form": "form"; "href": "href"; "mode": "mode"; "rel": "rel"; "routerAnimation": "routerAnimation"; "routerDirection": "routerDirection"; "shape": "shape"; "size": "size"; "strong": "strong"; "target": "target"; "type": "type"; }, {}, never, ["*"], true>;
}
export declare interface IonButton extends Components.IonButton {
    /**
     * Emitted when the button has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the button loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
}
export declare class IonButtons {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonButtons, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonButtons, "ion-buttons", never, { "collapse": "collapse"; }, {}, never, ["*"], true>;
}
export declare interface IonButtons extends Components.IonButtons {
}
export declare class IonCard {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonCard, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonCard, "ion-card", never, { "button": "button"; "color": "color"; "disabled": "disabled"; "download": "download"; "href": "href"; "mode": "mode"; "rel": "rel"; "routerAnimation": "routerAnimation"; "routerDirection": "routerDirection"; "target": "target"; "type": "type"; }, {}, never, ["*"], true>;
}
export declare interface IonCard extends Components.IonCard {
}
export declare class IonCardContent {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonCardContent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonCardContent, "ion-card-content", never, { "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonCardContent extends Components.IonCardContent {
}
export declare class IonCardHeader {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonCardHeader, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonCardHeader, "ion-card-header", never, { "color": "color"; "mode": "mode"; "translucent": "translucent"; }, {}, never, ["*"], true>;
}
export declare interface IonCardHeader extends Components.IonCardHeader {
}
export declare class IonCardSubtitle {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonCardSubtitle, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonCardSubtitle, "ion-card-subtitle", never, { "color": "color"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonCardSubtitle extends Components.IonCardSubtitle {
}
export declare class IonCardTitle {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonCardTitle, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonCardTitle, "ion-card-title", never, { "color": "color"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonCardTitle extends Components.IonCardTitle {
}
export declare class IonChip {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonChip, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonChip, "ion-chip", never, { "color": "color"; "disabled": "disabled"; "mode": "mode"; "outline": "outline"; }, {}, never, ["*"], true>;
}
export declare interface IonChip extends Components.IonChip {
}
export declare class IonCol {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonCol, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonCol, "ion-col", never, { "offset": "offset"; "offsetLg": "offsetLg"; "offsetMd": "offsetMd"; "offsetSm": "offsetSm"; "offsetXl": "offsetXl"; "offsetXs": "offsetXs"; "pull": "pull"; "pullLg": "pullLg"; "pullMd": "pullMd"; "pullSm": "pullSm"; "pullXl": "pullXl"; "pullXs": "pullXs"; "push": "push"; "pushLg": "pushLg"; "pushMd": "pushMd"; "pushSm": "pushSm"; "pushXl": "pushXl"; "pushXs": "pushXs"; "size": "size"; "sizeLg": "sizeLg"; "sizeMd": "sizeMd"; "sizeSm": "sizeSm"; "sizeXl": "sizeXl"; "sizeXs": "sizeXs"; }, {}, never, ["*"], true>;
}
export declare interface IonCol extends Components.IonCol {
}
export declare class IonContent {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonContent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonContent, "ion-content", never, { "color": "color"; "forceOverscroll": "forceOverscroll"; "fullscreen": "fullscreen"; "scrollEvents": "scrollEvents"; "scrollX": "scrollX"; "scrollY": "scrollY"; }, {}, never, ["*"], true>;
}
export declare interface IonContent extends Components.IonContent {
    /**
     * Emitted when the scroll has started. This event is disabled by default.
  Set `scrollEvents` to `true` to enable.
     */
    ionScrollStart: EventEmitter<CustomEvent<IIonContentScrollBaseDetail>>;
    /**
     * Emitted while scrolling. This event is disabled by default.
  Set `scrollEvents` to `true` to enable.
     */
    ionScroll: EventEmitter<CustomEvent<IIonContentScrollDetail>>;
    /**
     * Emitted when the scroll has ended. This event is disabled by default.
  Set `scrollEvents` to `true` to enable.
     */
    ionScrollEnd: EventEmitter<CustomEvent<IIonContentScrollBaseDetail>>;
}
export declare class IonDatetimeButton {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonDatetimeButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonDatetimeButton, "ion-datetime-button", never, { "color": "color"; "datetime": "datetime"; "disabled": "disabled"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonDatetimeButton extends Components.IonDatetimeButton {
}
export declare class IonFab {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonFab, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonFab, "ion-fab", never, { "activated": "activated"; "edge": "edge"; "horizontal": "horizontal"; "vertical": "vertical"; }, {}, never, ["*"], true>;
}
export declare interface IonFab extends Components.IonFab {
}
export declare class IonFabButton {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonFabButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonFabButton, "ion-fab-button", never, { "activated": "activated"; "closeIcon": "closeIcon"; "color": "color"; "disabled": "disabled"; "download": "download"; "href": "href"; "mode": "mode"; "rel": "rel"; "routerAnimation": "routerAnimation"; "routerDirection": "routerDirection"; "show": "show"; "size": "size"; "target": "target"; "translucent": "translucent"; "type": "type"; }, {}, never, ["*"], true>;
}
export declare interface IonFabButton extends Components.IonFabButton {
    /**
     * Emitted when the button has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the button loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
}
export declare class IonFabList {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonFabList, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonFabList, "ion-fab-list", never, { "activated": "activated"; "side": "side"; }, {}, never, ["*"], true>;
}
export declare interface IonFabList extends Components.IonFabList {
}
export declare class IonFooter {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonFooter, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonFooter, "ion-footer", never, { "collapse": "collapse"; "mode": "mode"; "translucent": "translucent"; }, {}, never, ["*"], true>;
}
export declare interface IonFooter extends Components.IonFooter {
}
export declare class IonGrid {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonGrid, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonGrid, "ion-grid", never, { "fixed": "fixed"; }, {}, never, ["*"], true>;
}
export declare interface IonGrid extends Components.IonGrid {
}
export declare class IonHeader {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonHeader, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonHeader, "ion-header", never, { "collapse": "collapse"; "mode": "mode"; "translucent": "translucent"; }, {}, never, ["*"], true>;
}
export declare interface IonHeader extends Components.IonHeader {
}
export declare class IonImg {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonImg, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonImg, "ion-img", never, { "alt": "alt"; "src": "src"; }, {}, never, ["*"], true>;
}
export declare interface IonImg extends Components.IonImg {
    /**
     * Emitted when the img src has been set
     */
    ionImgWillLoad: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the image has finished loading
     */
    ionImgDidLoad: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the img fails to load
     */
    ionError: EventEmitter<CustomEvent<void>>;
}
export declare class IonInfiniteScroll {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonInfiniteScroll, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonInfiniteScroll, "ion-infinite-scroll", never, { "disabled": "disabled"; "position": "position"; "threshold": "threshold"; }, {}, never, ["*"], true>;
}
export declare interface IonInfiniteScroll extends Components.IonInfiniteScroll {
    /**
     * Emitted when the scroll reaches
  the threshold distance. From within your infinite handler,
  you must call the infinite scroll's `complete()` method when
  your async operation has completed.
     */
    ionInfinite: EventEmitter<CustomEvent<void>>;
}
export declare class IonInfiniteScrollContent {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonInfiniteScrollContent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonInfiniteScrollContent, "ion-infinite-scroll-content", never, { "loadingSpinner": "loadingSpinner"; "loadingText": "loadingText"; }, {}, never, ["*"], true>;
}
export declare interface IonInfiniteScrollContent extends Components.IonInfiniteScrollContent {
}
export declare class IonItem {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonItem, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonItem, "ion-item", never, { "button": "button"; "color": "color"; "counter": "counter"; "counterFormatter": "counterFormatter"; "detail": "detail"; "detailIcon": "detailIcon"; "disabled": "disabled"; "download": "download"; "fill": "fill"; "href": "href"; "lines": "lines"; "mode": "mode"; "rel": "rel"; "routerAnimation": "routerAnimation"; "routerDirection": "routerDirection"; "shape": "shape"; "target": "target"; "type": "type"; }, {}, never, ["*"], true>;
}
export declare interface IonItem extends Components.IonItem {
}
export declare class IonItemDivider {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonItemDivider, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonItemDivider, "ion-item-divider", never, { "color": "color"; "mode": "mode"; "sticky": "sticky"; }, {}, never, ["*"], true>;
}
export declare interface IonItemDivider extends Components.IonItemDivider {
}
export declare class IonItemGroup {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonItemGroup, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonItemGroup, "ion-item-group", never, {}, {}, never, ["*"], true>;
}
export declare interface IonItemGroup extends Components.IonItemGroup {
}
export declare class IonItemOption {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonItemOption, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonItemOption, "ion-item-option", never, { "color": "color"; "disabled": "disabled"; "download": "download"; "expandable": "expandable"; "href": "href"; "mode": "mode"; "rel": "rel"; "target": "target"; "type": "type"; }, {}, never, ["*"], true>;
}
export declare interface IonItemOption extends Components.IonItemOption {
}
export declare class IonItemOptions {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonItemOptions, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonItemOptions, "ion-item-options", never, { "side": "side"; }, {}, never, ["*"], true>;
}
export declare interface IonItemOptions extends Components.IonItemOptions {
    /**
     * Emitted when the item has been fully swiped.
     */
    ionSwipe: EventEmitter<CustomEvent<any>>;
}
export declare class IonItemSliding {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonItemSliding, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonItemSliding, "ion-item-sliding", never, { "disabled": "disabled"; }, {}, never, ["*"], true>;
}
export declare interface IonItemSliding extends Components.IonItemSliding {
    /**
     * Emitted when the sliding position changes.
     */
    ionDrag: EventEmitter<CustomEvent<any>>;
}
export declare class IonLabel {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonLabel, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonLabel, "ion-label", never, { "color": "color"; "mode": "mode"; "position": "position"; }, {}, never, ["*"], true>;
}
export declare interface IonLabel extends Components.IonLabel {
}
export declare class IonList {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonList, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonList, "ion-list", never, { "inset": "inset"; "lines": "lines"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonList extends Components.IonList {
}
export declare class IonListHeader {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonListHeader, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonListHeader, "ion-list-header", never, { "color": "color"; "lines": "lines"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonListHeader extends Components.IonListHeader {
}
export declare class IonLoading {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonLoading, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonLoading, "ion-loading", never, { "animated": "animated"; "backdropDismiss": "backdropDismiss"; "cssClass": "cssClass"; "duration": "duration"; "enterAnimation": "enterAnimation"; "htmlAttributes": "htmlAttributes"; "isOpen": "isOpen"; "keyboardClose": "keyboardClose"; "leaveAnimation": "leaveAnimation"; "message": "message"; "mode": "mode"; "showBackdrop": "showBackdrop"; "spinner": "spinner"; "translucent": "translucent"; "trigger": "trigger"; }, {}, never, ["*"], true>;
}
export declare interface IonLoading extends Components.IonLoading {
    /**
     * Emitted after the loading has presented.
     */
    ionLoadingDidPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the loading has presented.
     */
    ionLoadingWillPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the loading has dismissed.
     */
    ionLoadingWillDismiss: EventEmitter<CustomEvent<IIonLoadingOverlayEventDetail>>;
    /**
     * Emitted after the loading has dismissed.
     */
    ionLoadingDidDismiss: EventEmitter<CustomEvent<IIonLoadingOverlayEventDetail>>;
    /**
     * Emitted after the loading indicator has presented.
  Shorthand for ionLoadingWillDismiss.
     */
    didPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the loading indicator has presented.
  Shorthand for ionLoadingWillPresent.
     */
    willPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the loading indicator has dismissed.
  Shorthand for ionLoadingWillDismiss.
     */
    willDismiss: EventEmitter<CustomEvent<IIonLoadingOverlayEventDetail>>;
    /**
     * Emitted after the loading indicator has dismissed.
  Shorthand for ionLoadingDidDismiss.
     */
    didDismiss: EventEmitter<CustomEvent<IIonLoadingOverlayEventDetail>>;
}
export declare class IonMenu {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonMenu, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonMenu, "ion-menu", never, { "contentId": "contentId"; "disabled": "disabled"; "maxEdgeStart": "maxEdgeStart"; "menuId": "menuId"; "side": "side"; "swipeGesture": "swipeGesture"; "type": "type"; }, {}, never, ["*"], true>;
}
export declare interface IonMenu extends Components.IonMenu {
    /**
     * Emitted when the menu is about to be opened.
     */
    ionWillOpen: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the menu is about to be closed.
     */
    ionWillClose: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the menu is open.
     */
    ionDidOpen: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the menu is closed.
     */
    ionDidClose: EventEmitter<CustomEvent<void>>;
}
export declare class IonMenuButton {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonMenuButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonMenuButton, "ion-menu-button", never, { "autoHide": "autoHide"; "color": "color"; "disabled": "disabled"; "menu": "menu"; "mode": "mode"; "type": "type"; }, {}, never, ["*"], true>;
}
export declare interface IonMenuButton extends Components.IonMenuButton {
}
export declare class IonMenuToggle {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonMenuToggle, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonMenuToggle, "ion-menu-toggle", never, { "autoHide": "autoHide"; "menu": "menu"; }, {}, never, ["*"], true>;
}
export declare interface IonMenuToggle extends Components.IonMenuToggle {
}
export declare class IonNavLink {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonNavLink, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonNavLink, "ion-nav-link", never, { "component": "component"; "componentProps": "componentProps"; "routerAnimation": "routerAnimation"; "routerDirection": "routerDirection"; }, {}, never, ["*"], true>;
}
export declare interface IonNavLink extends Components.IonNavLink {
}
export declare class IonNote {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonNote, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonNote, "ion-note", never, { "color": "color"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonNote extends Components.IonNote {
}
export declare class IonPicker {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonPicker, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonPicker, "ion-picker", never, { "animated": "animated"; "backdropDismiss": "backdropDismiss"; "buttons": "buttons"; "columns": "columns"; "cssClass": "cssClass"; "duration": "duration"; "enterAnimation": "enterAnimation"; "htmlAttributes": "htmlAttributes"; "isOpen": "isOpen"; "keyboardClose": "keyboardClose"; "leaveAnimation": "leaveAnimation"; "mode": "mode"; "showBackdrop": "showBackdrop"; "trigger": "trigger"; }, {}, never, ["*"], true>;
}
export declare interface IonPicker extends Components.IonPicker {
    /**
     * Emitted after the picker has presented.
     */
    ionPickerDidPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the picker has presented.
     */
    ionPickerWillPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the picker has dismissed.
     */
    ionPickerWillDismiss: EventEmitter<CustomEvent<IIonPickerOverlayEventDetail>>;
    /**
     * Emitted after the picker has dismissed.
     */
    ionPickerDidDismiss: EventEmitter<CustomEvent<IIonPickerOverlayEventDetail>>;
    /**
     * Emitted after the picker has presented.
  Shorthand for ionPickerWillDismiss.
     */
    didPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the picker has presented.
  Shorthand for ionPickerWillPresent.
     */
    willPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the picker has dismissed.
  Shorthand for ionPickerWillDismiss.
     */
    willDismiss: EventEmitter<CustomEvent<IIonPickerOverlayEventDetail>>;
    /**
     * Emitted after the picker has dismissed.
  Shorthand for ionPickerDidDismiss.
     */
    didDismiss: EventEmitter<CustomEvent<IIonPickerOverlayEventDetail>>;
}
export declare class IonProgressBar {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonProgressBar, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonProgressBar, "ion-progress-bar", never, { "buffer": "buffer"; "color": "color"; "mode": "mode"; "reversed": "reversed"; "type": "type"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonProgressBar extends Components.IonProgressBar {
}
export declare class IonRefresher {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonRefresher, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonRefresher, "ion-refresher", never, { "closeDuration": "closeDuration"; "disabled": "disabled"; "mode": "mode"; "pullFactor": "pullFactor"; "pullMax": "pullMax"; "pullMin": "pullMin"; "snapbackDuration": "snapbackDuration"; }, {}, never, ["*"], true>;
}
export declare interface IonRefresher extends Components.IonRefresher {
    /**
     * Emitted when the user lets go of the content and has pulled down
  further than the `pullMin` or pulls the content down and exceeds the pullMax.
  Updates the refresher state to `refreshing`. The `complete()` method should be
  called when the async operation has completed.
     */
    ionRefresh: EventEmitter<CustomEvent<IIonRefresherRefresherEventDetail>>;
    /**
     * Emitted while the user is pulling down the content and exposing the refresher.
     */
    ionPull: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the user begins to start pulling down.
     */
    ionStart: EventEmitter<CustomEvent<void>>;
}
export declare class IonRefresherContent {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonRefresherContent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonRefresherContent, "ion-refresher-content", never, { "pullingIcon": "pullingIcon"; "pullingText": "pullingText"; "refreshingSpinner": "refreshingSpinner"; "refreshingText": "refreshingText"; }, {}, never, ["*"], true>;
}
export declare interface IonRefresherContent extends Components.IonRefresherContent {
}
export declare class IonReorder {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonReorder, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonReorder, "ion-reorder", never, {}, {}, never, ["*"], true>;
}
export declare interface IonReorder extends Components.IonReorder {
}
export declare class IonReorderGroup {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonReorderGroup, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonReorderGroup, "ion-reorder-group", never, { "disabled": "disabled"; }, {}, never, ["*"], true>;
}
export declare interface IonReorderGroup extends Components.IonReorderGroup {
    /**
     * Event that needs to be listened to in order to complete the reorder action.
  Once the event has been emitted, the `complete()` method then needs
  to be called in order to finalize the reorder action.
     */
    ionItemReorder: EventEmitter<CustomEvent<IIonReorderGroupItemReorderEventDetail>>;
}
export declare class IonRippleEffect {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonRippleEffect, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonRippleEffect, "ion-ripple-effect", never, { "type": "type"; }, {}, never, ["*"], true>;
}
export declare interface IonRippleEffect extends Components.IonRippleEffect {
}
export declare class IonRow {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonRow, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonRow, "ion-row", never, {}, {}, never, ["*"], true>;
}
export declare interface IonRow extends Components.IonRow {
}
export declare class IonSegmentButton {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonSegmentButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonSegmentButton, "ion-segment-button", never, { "disabled": "disabled"; "layout": "layout"; "mode": "mode"; "type": "type"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonSegmentButton extends Components.IonSegmentButton {
}
export declare class IonSelectOption {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonSelectOption, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonSelectOption, "ion-select-option", never, { "disabled": "disabled"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonSelectOption extends Components.IonSelectOption {
}
export declare class IonSkeletonText {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonSkeletonText, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonSkeletonText, "ion-skeleton-text", never, { "animated": "animated"; }, {}, never, ["*"], true>;
}
export declare interface IonSkeletonText extends Components.IonSkeletonText {
}
export declare class IonSpinner {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonSpinner, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonSpinner, "ion-spinner", never, { "color": "color"; "duration": "duration"; "name": "name"; "paused": "paused"; }, {}, never, ["*"], true>;
}
export declare interface IonSpinner extends Components.IonSpinner {
}
export declare class IonSplitPane {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonSplitPane, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonSplitPane, "ion-split-pane", never, { "contentId": "contentId"; "disabled": "disabled"; "when": "when"; }, {}, never, ["*"], true>;
}
export declare interface IonSplitPane extends Components.IonSplitPane {
    /**
     * Expression to be called when the split-pane visibility has changed
     */
    ionSplitPaneVisible: EventEmitter<CustomEvent<{
        visible: boolean;
    }>>;
}
export declare class IonTabBar {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonTabBar, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonTabBar, "ion-tab-bar", never, { "color": "color"; "mode": "mode"; "selectedTab": "selectedTab"; "translucent": "translucent"; }, {}, never, ["*"], true>;
}
export declare interface IonTabBar extends Components.IonTabBar {
}
export declare class IonTabButton {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonTabButton, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonTabButton, "ion-tab-button", never, { "disabled": "disabled"; "download": "download"; "href": "href"; "layout": "layout"; "mode": "mode"; "rel": "rel"; "selected": "selected"; "tab": "tab"; "target": "target"; }, {}, never, ["*"], true>;
}
export declare interface IonTabButton extends Components.IonTabButton {
}
export declare class IonText {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonText, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonText, "ion-text", never, { "color": "color"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonText extends Components.IonText {
}
export declare class IonThumbnail {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonThumbnail, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonThumbnail, "ion-thumbnail", never, {}, {}, never, ["*"], true>;
}
export declare interface IonThumbnail extends Components.IonThumbnail {
}
export declare class IonTitle {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonTitle, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonTitle, "ion-title", never, { "color": "color"; "size": "size"; }, {}, never, ["*"], true>;
}
export declare interface IonTitle extends Components.IonTitle {
}
export declare class IonToast {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonToast, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonToast, "ion-toast", never, { "animated": "animated"; "buttons": "buttons"; "color": "color"; "cssClass": "cssClass"; "duration": "duration"; "enterAnimation": "enterAnimation"; "header": "header"; "htmlAttributes": "htmlAttributes"; "icon": "icon"; "isOpen": "isOpen"; "keyboardClose": "keyboardClose"; "layout": "layout"; "leaveAnimation": "leaveAnimation"; "message": "message"; "mode": "mode"; "position": "position"; "positionAnchor": "positionAnchor"; "swipeGesture": "swipeGesture"; "translucent": "translucent"; "trigger": "trigger"; }, {}, never, ["*"], true>;
}
export declare interface IonToast extends Components.IonToast {
    /**
     * Emitted after the toast has presented.
     */
    ionToastDidPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the toast has presented.
     */
    ionToastWillPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the toast has dismissed.
     */
    ionToastWillDismiss: EventEmitter<CustomEvent<IIonToastOverlayEventDetail>>;
    /**
     * Emitted after the toast has dismissed.
     */
    ionToastDidDismiss: EventEmitter<CustomEvent<IIonToastOverlayEventDetail>>;
    /**
     * Emitted after the toast has presented.
  Shorthand for ionToastWillDismiss.
     */
    didPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the toast has presented.
  Shorthand for ionToastWillPresent.
     */
    willPresent: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted before the toast has dismissed.
  Shorthand for ionToastWillDismiss.
     */
    willDismiss: EventEmitter<CustomEvent<IIonToastOverlayEventDetail>>;
    /**
     * Emitted after the toast has dismissed.
  Shorthand for ionToastDidDismiss.
     */
    didDismiss: EventEmitter<CustomEvent<IIonToastOverlayEventDetail>>;
}
export declare class IonToolbar {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<IonToolbar, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonToolbar, "ion-toolbar", never, { "color": "color"; "mode": "mode"; }, {}, never, ["*"], true>;
}
export declare interface IonToolbar extends Components.IonToolbar {
}
