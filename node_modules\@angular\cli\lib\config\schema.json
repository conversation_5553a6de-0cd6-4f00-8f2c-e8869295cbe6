{"$schema": "http://json-schema.org/draft-07/schema", "$id": "ng-cli://config/schema.json", "title": "Angular CLI Workspace Configuration", "type": "object", "properties": {"$schema": {"type": "string"}, "version": {"$ref": "#/definitions/fileVersion"}, "cli": {"$ref": "#/definitions/cliOptions"}, "schematics": {"$ref": "#/definitions/schematicOptions"}, "newProjectRoot": {"type": "string", "description": "Path where new projects will be created."}, "projects": {"type": "object", "patternProperties": {"^(?:@[a-zA-Z0-9._-]+/)?[a-zA-Z0-9._-]+$": {"$ref": "#/definitions/project"}}, "additionalProperties": false}}, "additionalProperties": false, "required": ["version"], "definitions": {"cliOptions": {"type": "object", "properties": {"schematicCollections": {"type": "array", "description": "The list of schematic collections to use.", "items": {"type": "string", "uniqueItems": true}}, "packageManager": {"description": "Specify which package manager tool to use.", "type": "string", "enum": ["npm", "cnpm", "yarn", "pnpm", "bun"]}, "warnings": {"description": "Control CLI specific console warnings", "type": "object", "properties": {"versionMismatch": {"description": "Show a warning when the global version is newer than the local one.", "type": "boolean"}}, "additionalProperties": false}, "analytics": {"type": ["boolean", "string"], "description": "Share pseudonymous usage data with the Angular Team at Google."}, "cache": {"description": "Control disk cache.", "type": "object", "properties": {"environment": {"description": "Configure in which environment disk cache is enabled.", "type": "string", "enum": ["local", "ci", "all"]}, "enabled": {"description": "Configure whether disk caching is enabled.", "type": "boolean"}, "path": {"description": "Cache base path.", "type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "cliGlobalOptions": {"type": "object", "properties": {"schematicCollections": {"type": "array", "description": "The list of schematic collections to use.", "items": {"type": "string", "uniqueItems": true}}, "packageManager": {"description": "Specify which package manager tool to use.", "type": "string", "enum": ["npm", "cnpm", "yarn", "pnpm", "bun"]}, "warnings": {"description": "Control CLI specific console warnings", "type": "object", "properties": {"versionMismatch": {"description": "Show a warning when the global version is newer than the local one.", "type": "boolean"}}, "additionalProperties": false}, "analytics": {"type": ["boolean", "string"], "description": "Share pseudonymous usage data with the Angular Team at Google."}, "completion": {"type": "object", "description": "Angular CLI completion settings.", "properties": {"prompted": {"type": "boolean", "description": "Whether the user has been prompted to add completion command prompt."}}, "additionalProperties": false}}, "additionalProperties": false}, "schematicOptions": {"type": "object", "properties": {"@schematics/angular:application": {"$ref": "#/definitions/SchematicsAngularApplicationSchema"}, "@schematics/angular:class": {"$ref": "#/definitions/SchematicsAngularClassSchema"}, "@schematics/angular:component": {"$ref": "#/definitions/SchematicsAngularComponentSchema"}, "@schematics/angular:directive": {"$ref": "#/definitions/SchematicsAngularDirectiveSchema"}, "@schematics/angular:enum": {"$ref": "#/definitions/SchematicsAngularEnumSchema"}, "@schematics/angular:guard": {"$ref": "#/definitions/SchematicsAngularGuardSchema"}, "@schematics/angular:interceptor": {"$ref": "#/definitions/SchematicsAngularInterceptorSchema"}, "@schematics/angular:interface": {"$ref": "#/definitions/SchematicsAngularInterfaceSchema"}, "@schematics/angular:library": {"$ref": "#/definitions/SchematicsAngularLibrarySchema"}, "@schematics/angular:pipe": {"$ref": "#/definitions/SchematicsAngularPipeSchema"}, "@schematics/angular:ng-new": {"$ref": "#/definitions/SchematicsAngularNgNewSchema"}, "@schematics/angular:resolver": {"$ref": "#/definitions/SchematicsAngularResolverSchema"}, "@schematics/angular:service": {"$ref": "#/definitions/SchematicsAngularServiceSchema"}, "@schematics/angular:web-worker": {"$ref": "#/definitions/SchematicsAngularWebWorkerSchema"}}, "additionalProperties": true}, "fileVersion": {"type": "integer", "description": "File format version", "minimum": 1}, "project": {"type": "object", "properties": {"cli": {"schematicCollections": {"type": "array", "description": "The list of schematic collections to use.", "items": {"type": "string", "uniqueItems": true}}}, "schematics": {"$ref": "#/definitions/schematicOptions"}, "prefix": {"type": "string", "format": "html-selector", "description": "The prefix to apply to generated selectors."}, "root": {"type": "string", "description": "Root of the project files."}, "i18n": {"$ref": "#/definitions/project/definitions/i18n"}, "sourceRoot": {"type": "string", "description": "The root of the source files, assets and index.html file structure."}, "projectType": {"type": "string", "description": "Project type.", "enum": ["application", "library"]}, "architect": {"type": "object", "additionalProperties": {"$ref": "#/definitions/project/definitions/target"}}, "targets": {"type": "object", "additionalProperties": {"$ref": "#/definitions/project/definitions/target"}}}, "required": ["root", "projectType"], "anyOf": [{"required": ["architect"], "not": {"required": ["targets"]}}, {"required": ["targets"], "not": {"required": ["architect"]}}, {"not": {"required": ["targets", "architect"]}}], "additionalProperties": false, "patternProperties": {"^[a-z]{1,3}-.*": {}}, "definitions": {"i18n": {"description": "Project i18n options", "type": "object", "properties": {"sourceLocale": {"oneOf": [{"type": "string", "description": "Specifies the source locale of the application.", "default": "en-US", "$comment": "IETF BCP 47 language tag (simplified)", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}, {"type": "object", "description": "Localization options to use for the source locale", "properties": {"code": {"type": "string", "description": "Specifies the locale code of the source locale", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}, "baseHref": {"type": "string", "description": "HTML base HREF to use for the locale (defaults to the locale code)"}}, "additionalProperties": false}]}, "locales": {"type": "object", "additionalProperties": false, "patternProperties": {"^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$": {"oneOf": [{"type": "string", "description": "Localization file to use for i18n"}, {"type": "array", "description": "Localization files to use for i18n", "items": {"type": "string", "uniqueItems": true}}, {"type": "object", "description": "Localization options to use for the locale", "properties": {"translation": {"oneOf": [{"type": "string", "description": "Localization file to use for i18n"}, {"type": "array", "description": "Localization files to use for i18n", "items": {"type": "string", "uniqueItems": true}}]}, "baseHref": {"type": "string", "description": "HTML base HREF to use for the locale (defaults to the locale code)"}}, "additionalProperties": false}]}}}}, "additionalProperties": false}, "target": {"oneOf": [{"$comment": "Extendable target with custom builder", "type": "object", "properties": {"builder": {"type": "string", "description": "The builder used for this package.", "not": {"enum": ["@angular-devkit/build-angular:application", "@angular-devkit/build-angular:app-shell", "@angular-devkit/build-angular:browser", "@angular-devkit/build-angular:browser-esbuild", "@angular-devkit/build-angular:dev-server", "@angular-devkit/build-angular:extract-i18n", "@angular-devkit/build-angular:karma", "@angular-devkit/build-angular:ng-packagr", "@angular-devkit/build-angular:prerender", "@angular-devkit/build-angular:jest", "@angular-devkit/build-angular:web-test-runner", "@angular-devkit/build-angular:protractor", "@angular-devkit/build-angular:server", "@angular-devkit/build-angular:ssr-dev-server"]}}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"type": "object"}, "configurations": {"type": "object", "description": "A map of alternative target options.", "additionalProperties": {"type": "object"}}}, "additionalProperties": false, "required": ["builder"]}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:application"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersApplicationSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersApplicationSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:app-shell"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersAppShellSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersAppShellSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:browser"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:browser-esbuild"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:dev-server"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersDevServerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersDevServerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:extract-i18n"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersExtractI18nSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersExtractI18nSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:karma"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersKarmaSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersKarmaSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:jest"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersJestSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersJestSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:web-test-runner"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersWebTestRunnerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersWebTestRunnerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:protractor"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersProtractorSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersProtractorSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:prerender"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersPrerenderSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersPrerenderSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:ssr-dev-server"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersSsrDevServerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersSsrDevServerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:server"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersServerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersServerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:ng-packagr"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersNgPackagrSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersNgPackagrSchema"}}}}]}}}, "global": {"type": "object", "properties": {"$schema": {"type": "string"}, "version": {"$ref": "#/definitions/fileVersion"}, "cli": {"$ref": "#/definitions/cliGlobalOptions"}, "schematics": {"$ref": "#/definitions/schematicOptions"}}, "required": ["version"]}, "SchematicsAngularApplicationSchema": {"title": "Angular Application Options Schema", "type": "object", "description": "Generates a new basic application definition in the \"projects\" subfolder of the workspace.", "additionalProperties": false, "properties": {"projectRoot": {"description": "The root directory of the new application.", "type": "string"}, "name": {"description": "The name of the new application.", "type": "string", "pattern": "^(?:@[a-zA-Z0-9-*~][a-zA-Z0-9-*._~]*/)?[a-zA-Z0-9-~][a-zA-Z0-9-._~]*$", "$default": {"$source": "argv", "index": 0}}, "inlineStyle": {"description": "Include styles inline in the root component.ts file. Only CSS styles can be included inline. Default is false, meaning that an external styles file is created and referenced in the root component.ts file.", "type": "boolean", "alias": "s"}, "inlineTemplate": {"description": "Include template inline in the root component.ts file. Default is false, meaning that an external template file is created and referenced in the root component.ts file. ", "type": "boolean", "alias": "t"}, "viewEncapsulation": {"description": "The view encapsulation strategy to use in the new application.", "enum": ["Emulated", "None", "ShadowDom"], "type": "string"}, "routing": {"type": "boolean", "description": "Creates an application with routing enabled.", "default": true}, "prefix": {"type": "string", "format": "html-selector", "description": "A prefix to apply to generated selectors.", "default": "app", "alias": "p"}, "style": {"description": "The file extension or preprocessor to use for style files.", "type": "string", "default": "css", "enum": ["css", "scss", "sass", "less"]}, "skipTests": {"description": "Do not create \"spec.ts\" test files for the application.", "type": "boolean", "default": false, "alias": "S"}, "skipPackageJson": {"type": "boolean", "default": false, "description": "Do not add dependencies to the \"package.json\" file."}, "minimal": {"description": "Create a bare-bones project without any testing frameworks. (Use for learning purposes only.)", "type": "boolean", "default": false}, "skipInstall": {"description": "Skip installing dependency packages.", "type": "boolean", "default": false}, "strict": {"description": "Creates an application with stricter bundle budgets settings.", "type": "boolean", "default": true}, "standalone": {"description": "Creates an application based upon the standalone API, without NgModules.", "type": "boolean", "default": true}, "ssr": {"description": "Creates an application with Server-Side Rendering (SSR) and Static Site Generation (SSG/Prerendering) enabled.", "type": "boolean", "default": false}}}, "SchematicsAngularClassSchema": {"title": "Angular Class Options Schema", "type": "object", "description": "Creates a new, generic class definition in the given project.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the new class.", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the class, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new class.", "default": false}, "type": {"type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\"."}}}, "SchematicsAngularComponentSchema": {"title": "Angular Component Options Schema", "type": "object", "description": "Creates a new, generic component definition in the given project.", "additionalProperties": false, "properties": {"path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the component file, relative to the current workspace. Default is a folder with the same name as the component in the project root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "name": {"type": "string", "description": "The name of the component.", "$default": {"$source": "argv", "index": 0}}, "displayBlock": {"description": "Specifies if the style will contain `:host { display: block; }`.", "type": "boolean", "default": false, "alias": "b"}, "inlineStyle": {"description": "Include styles inline in the component.ts file. Only CSS styles can be included inline. By default, an external styles file is created and referenced in the component.ts file.", "type": "boolean", "default": false, "alias": "s"}, "inlineTemplate": {"description": "Include template inline in the component.ts file. By default, an external template file is created and referenced in the component.ts file.", "type": "boolean", "default": false, "alias": "t"}, "standalone": {"description": "Whether the generated component is standalone.", "type": "boolean", "default": true}, "viewEncapsulation": {"description": "The view encapsulation strategy to use in the new component.", "enum": ["Emulated", "None", "ShadowDom"], "type": "string", "alias": "v"}, "changeDetection": {"description": "The change detection strategy to use in the new component.", "enum": ["<PERSON><PERSON><PERSON>", "OnPush"], "type": "string", "default": "<PERSON><PERSON><PERSON>", "alias": "c"}, "prefix": {"type": "string", "description": "The prefix to apply to the generated component selector.", "alias": "p", "oneOf": [{"maxLength": 0}, {"minLength": 1, "format": "html-selector"}]}, "style": {"description": "The file extension or preprocessor to use for style files, or 'none' to skip generating the style file.", "type": "string", "default": "css", "enum": ["css", "scss", "sass", "less", "none"]}, "type": {"type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\".", "default": "Component"}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new component.", "default": false}, "flat": {"type": "boolean", "description": "Create the new files at the top level of the current project.", "default": false}, "skipImport": {"type": "boolean", "description": "Do not import this component into the owning NgModule.", "default": false}, "selector": {"type": "string", "format": "html-selector", "description": "The HTML selector to use for this component."}, "skipSelector": {"type": "boolean", "default": false, "description": "Specifies if the component should have a selector or not."}, "module": {"type": "string", "description": "The declaring NgModule.", "alias": "m"}, "export": {"type": "boolean", "default": false, "description": "The declaring NgModule exports this component."}}}, "SchematicsAngularDirectiveSchema": {"title": "Angular Directive Options Schema", "type": "object", "description": "Creates a new, generic directive definition in the given project.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the new directive.", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interface that defines the directive, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "prefix": {"type": "string", "description": "A prefix to apply to generated selectors.", "alias": "p", "oneOf": [{"maxLength": 0}, {"minLength": 1, "format": "html-selector"}]}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new class.", "default": false}, "skipImport": {"type": "boolean", "description": "Do not import this directive into the owning NgModule.", "default": false}, "selector": {"type": "string", "format": "html-selector", "description": "The HTML selector to use for this directive."}, "standalone": {"description": "Whether the generated directive is standalone.", "type": "boolean", "default": true}, "flat": {"type": "boolean", "description": "When true (the default), creates the new files at the top level of the current project.", "default": true}, "module": {"type": "string", "description": "The declaring NgModule.", "alias": "m"}, "export": {"type": "boolean", "default": false, "description": "The declaring NgModule exports this directive."}}}, "SchematicsAngularEnumSchema": {"title": "Angular Enum Options Schema", "type": "object", "description": "Generates a new, generic enum definition in the given project.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the enum.", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the enum definition, relative to the current workspace.", "visible": false}, "project": {"type": "string", "description": "The name of the project in which to create the enum. Default is the configured default project for the workspace.", "$default": {"$source": "projectName"}}, "type": {"type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\"."}}}, "SchematicsAngularGuardSchema": {"title": "Angular Guard Options Schema", "type": "object", "description": "Generates a new, generic route guard definition in the given project.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the new route guard.", "$default": {"$source": "argv", "index": 0}}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new guard.", "default": false}, "flat": {"type": "boolean", "description": "When true (the default), creates the new files at the top level of the current project.", "default": true}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interface that defines the guard, relative to the current workspace.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "functional": {"type": "boolean", "description": "Specifies whether to generate a guard as a function.", "default": true}, "implements": {"alias": "guardType", "type": "array", "description": "Specifies which type of guard to create.", "uniqueItems": true, "minItems": 1, "items": {"enum": ["CanActivate", "CanActivateChild", "CanDeactivate", "CanMatch"], "type": "string"}, "default": ["CanActivate"]}}}, "SchematicsAngularInterceptorSchema": {"title": "Angular Interceptor Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic interceptor definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the interceptor.", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interceptor, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "flat": {"type": "boolean", "default": true, "description": "When true (the default), creates files at the top level of the project."}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new interceptor.", "default": false}, "functional": {"type": "boolean", "description": "Creates the interceptor as a `HttpInterceptorFn`.", "default": true}}}, "SchematicsAngularInterfaceSchema": {"title": "Angular Interface Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic interface definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the interface.", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interface, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "prefix": {"type": "string", "description": "A prefix to apply to generated selectors."}, "type": {"type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\".", "$default": {"$source": "argv", "index": 1}}}}, "SchematicsAngularLibrarySchema": {"title": "Library Options Schema", "type": "object", "description": "Creates a new, generic library project in the current workspace.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the library.", "pattern": "^(?:@[a-zA-Z0-9-*~][a-zA-Z0-9-*._~]*/)?[a-zA-Z0-9-~][a-zA-Z0-9-._~]*$", "$default": {"$source": "argv", "index": 0}}, "entryFile": {"type": "string", "format": "path", "description": "The path at which to create the library's public API file, relative to the workspace root.", "default": "public-api"}, "prefix": {"type": "string", "format": "html-selector", "description": "A prefix to apply to generated selectors.", "default": "lib", "alias": "p"}, "skipPackageJson": {"type": "boolean", "default": false, "description": "Do not add dependencies to the \"package.json\" file. "}, "skipInstall": {"description": "Do not install dependency packages.", "type": "boolean", "default": false}, "skipTsConfig": {"type": "boolean", "default": false, "description": "Do not update \"tsconfig.json\" to add a path mapping for the new library. The path mapping is needed to use the library in an app, but can be disabled here to simplify development."}, "projectRoot": {"type": "string", "description": "The root directory of the new library."}, "standalone": {"description": "Creates a library based upon the standalone API, without NgModules.", "type": "boolean", "default": true}}}, "SchematicsAngularPipeSchema": {"title": "Angular Pipe Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic pipe definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the pipe.", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the pipe, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "flat": {"type": "boolean", "default": true, "description": "When true (the default) creates files at the top level of the project."}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new pipe.", "default": false}, "skipImport": {"type": "boolean", "default": false, "description": "Do not import this pipe into the owning NgModule."}, "standalone": {"description": "Whether the generated pipe is standalone.", "type": "boolean", "default": true}, "module": {"type": "string", "description": "The declaring NgModule.", "alias": "m"}, "export": {"type": "boolean", "default": false, "description": "The declaring NgModule exports this pipe."}}}, "SchematicsAngularNgNewSchema": {"title": "Angular Ng New Options Schema", "type": "object", "description": "Creates a new project by combining the workspace and application schematics.", "additionalProperties": false, "properties": {"directory": {"type": "string", "description": "The directory name to create the workspace in."}, "name": {"description": "The name of the new workspace and initial project.", "type": "string", "$default": {"$source": "argv", "index": 0}}, "skipInstall": {"description": "Do not install dependency packages.", "type": "boolean", "default": false}, "linkCli": {"description": "Link the CLI to the global version (internal development only).", "type": "boolean", "default": false, "visible": false}, "skipGit": {"description": "Do not initialize a git repository.", "type": "boolean", "default": false, "alias": "g"}, "commit": {"description": "Initial git repository commit information.", "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "message": {"type": "string"}}}], "default": true}, "newProjectRoot": {"description": "The path where new projects will be created, relative to the new workspace root.", "type": "string", "default": "projects"}, "inlineStyle": {"description": "Include styles inline in the component TS file. By default, an external styles file is created and referenced in the component TypeScript file.", "type": "boolean", "alias": "s"}, "inlineTemplate": {"description": "Include template inline in the component TS file. By default, an external template file is created and referenced in the component TypeScript file.", "type": "boolean", "alias": "t"}, "viewEncapsulation": {"description": "The view encapsulation strategy to use in the initial project.", "enum": ["Emulated", "None", "ShadowDom"], "type": "string"}, "version": {"type": "string", "description": "The version of the Angular CLI to use.", "visible": false, "$default": {"$source": "ng-cli-version"}}, "routing": {"type": "boolean", "description": "Enable routing in the initial project."}, "prefix": {"type": "string", "format": "html-selector", "description": "The prefix to apply to generated selectors for the initial project.", "minLength": 1, "default": "app", "alias": "p"}, "style": {"description": "The file extension or preprocessor to use for style files.", "type": "string", "enum": ["css", "scss", "sass", "less"]}, "skipTests": {"description": "Do not generate \"spec.ts\" test files for the new project.", "type": "boolean", "default": false, "alias": "S"}, "createApplication": {"description": "Create a new initial application project in the 'src' folder of the new workspace. When false, creates an empty workspace with no initial application. You can then use the generate application command so that all applications are created in the projects folder.", "type": "boolean", "default": true}, "minimal": {"description": "Create a workspace without any testing frameworks. (Use for learning purposes only.)", "type": "boolean", "default": false}, "strict": {"description": "Creates a workspace with stricter type checking and stricter bundle budgets settings. This setting helps improve maintainability and catch bugs ahead of time. For more information, see https://angular.io/guide/strict-mode", "type": "boolean", "default": true}, "packageManager": {"description": "The package manager used to install dependencies.", "type": "string", "enum": ["npm", "yarn", "pnpm", "cnpm", "bun"]}, "standalone": {"description": "Creates an application based upon the standalone API, without NgModules.", "type": "boolean", "default": true}, "ssr": {"description": "Creates an application with Server-Side Rendering (SSR) and Static Site Generation (SSG/Prerendering) enabled.", "type": "boolean"}}}, "SchematicsAngularResolverSchema": {"title": "Angular resolver Options Schema", "type": "object", "additionalProperties": false, "description": "Generates a new, generic resolver definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the new resolver.", "$default": {"$source": "argv", "index": 0}}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new resolver.", "default": false}, "flat": {"type": "boolean", "description": "When true (the default), creates the new files at the top level of the current project.", "default": true}, "functional": {"type": "boolean", "description": "Creates the resolver as a `ResolveFn`.", "default": true}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the interface that defines the resolver, relative to the current workspace.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}}}, "SchematicsAngularServiceSchema": {"title": "Angular Service Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic service definition in the given project.", "properties": {"name": {"type": "string", "description": "The name of the service.", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the service, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "flat": {"type": "boolean", "default": true, "description": "When true (the default), creates files at the top level of the project."}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new service.", "default": false}}}, "SchematicsAngularWebWorkerSchema": {"title": "Angular Web Worker Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new, generic web worker definition in the given project.", "properties": {"path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the worker file, relative to the current workspace.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "name": {"type": "string", "description": "The name of the worker.", "$default": {"$source": "argv", "index": 0}}, "snippet": {"type": "boolean", "default": true, "description": "Add a worker creation snippet in a sibling file of the same name."}}}, "AngularDevkitBuildAngularBuildersApplicationSchema": {"title": "Application schema for Build Facade.", "description": "Application builder target options", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersApplicationSchema/definitions/assetPattern"}}, "browser": {"type": "string", "description": "The full path for the browser entry point to the application, relative to the current workspace."}, "server": {"type": "string", "description": "The full path for the server entry point to the application, relative to the current workspace."}, "polyfills": {"description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "type": "array", "items": {"type": "string", "uniqueItems": true}, "default": []}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The JavaScript/TypeScript file or package containing the file to include."}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "clearScreen": {"type": "boolean", "default": false, "description": "Automatically clear the terminal screen during rebuilds."}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking, dead-code elimination, inlining of critical CSS and fonts inlining. For more information, see https://angular.io/guide/workspace-config#optimization-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"description": "Enables optimization of the styles output.", "default": true, "oneOf": [{"type": "object", "properties": {"minify": {"type": "boolean", "description": "Minify CSS definitions by removing extraneous whitespace and comments, merging identifiers and minimizing values.", "default": true}, "inlineCritical": {"type": "boolean", "description": "Extract and inline critical CSS definitions to improve first paint time.", "default": true}, "removeSpecialComments": {"type": "boolean", "description": "Remove comments in global CSS that contains '@license' or '@preserve' or that starts with '//!' or '/*!'.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fonts": {"description": "Enables optimization for fonts. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true, "oneOf": [{"type": "object", "properties": {"inline": {"type": "boolean", "description": "Reduce render blocking requests by inlining external Google Fonts and Adobe Fonts CSS definitions in the application's HTML index file. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}}, "additionalProperties": false}, {"type": "boolean"}]}, "loader": {"description": "Defines the type of loader to use with a specified file extension when used with a JavaScript `import`. `text` inlines the content as a string; `binary` inlines the content as a Uint8Array; `file` emits the file and provides the runtime location of the file; `empty` considers the content to be empty and not include it in bundles.", "type": "object", "patternProperties": {"^\\.\\S+$": {"enum": ["text", "binary", "file", "empty"]}}}, "define": {"description": "Defines global identifiers that will be replaced with a specified constant value when found in any JavaScript or TypeScript code including libraries. The value will be used directly. String values must be put in quotes. Identifiers within Angular metadata such as Component Decorators will not be replaced.", "type": "object", "additionalProperties": {"type": "string"}}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersApplicationSchema/definitions/fileReplacement"}, "default": []}, "outputPath": {"description": "Specify the output path relative to workspace root.", "oneOf": [{"type": "object", "properties": {"base": {"type": "string", "description": "Specify the output path relative to workspace root."}, "browser": {"type": "string", "pattern": "^[-\\w\\.]*$", "default": "browser", "description": "The output directory name of your browser build within the output path base. Defaults to 'browser'."}, "server": {"type": "string", "pattern": "^[-\\w\\.]*$", "default": "server", "description": "The output directory name of your server build within the output path base. Defaults to 'server'."}, "media": {"type": "string", "pattern": "^[-\\w\\.]+$", "default": "media", "description": "The output directory name of your media files within the output browser directory. Defaults to 'media'."}}, "additionalProperties": false}, {"type": "string"}]}, "aot": {"type": "boolean", "description": "Build using Ahead of Time compilation.", "default": true}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.io/guide/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "subresourceIntegrity": {"type": "boolean", "description": "Enables the use of subresource integrity validation.", "default": false}, "serviceWorker": {"description": "Generates a service worker configuration.", "default": false, "oneOf": [{"type": "string", "description": "Path to ngsw-config.json."}, {"const": false, "type": "boolean", "description": "Does not generate a service worker configuration."}]}, "index": {"description": "Configures the generation of the application's HTML index.", "oneOf": [{"type": "string", "description": "The path of a file to use for the application's HTML index. The filename of the specified path will be used for the generated file and will be created in the root of the application's configured output path."}, {"type": "object", "description": "", "properties": {"input": {"type": "string", "minLength": 1, "description": "The path of a file to use for the application's generated HTML index."}, "output": {"type": "string", "minLength": 1, "default": "index.html", "description": "The output path of the application's generated HTML index file. The full provided path will be used and will be considered relative to the application's configured output path."}, "preloadInitial": {"type": "boolean", "default": true, "description": "Generates 'preload', 'modulepreload', and 'preconnect' link elements for initial application files and resources."}}}, {"const": false, "type": "boolean", "description": "Does not generate an `index.html` file."}]}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed with https://esbuild.github.io/analyze/.", "default": false}, "budgets": {"description": "Budget thresholds to ensure parts of your application stay within boundaries which you set.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersApplicationSchema/definitions/budget"}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "crossOrigin": {"type": "string", "description": "Define the crossorigin attribute setting of elements that provide CORS support.", "default": "none", "enum": ["none", "anonymous", "use-credentials"]}, "allowedCommonJsDependencies": {"description": "A list of CommonJS or AMD packages that are allowed to be used without a build time warning. Use `'*'` to allow all.", "type": "array", "items": {"type": "string"}, "default": []}, "prerender": {"description": "Prerender (SSG) pages of your application during build time.", "default": false, "oneOf": [{"type": "boolean", "description": "Enable prerending of pages of your application during build time."}, {"type": "object", "properties": {"routesFile": {"type": "string", "description": "The path to a file that contains a list of all routes to prerender, separated by newlines. This option is useful if you want to prerender routes with parameterized URLs."}, "discoverRoutes": {"type": "boolean", "description": "Whether the builder should process the Angular Router configuration to find all unparameterized routes and prerender them.", "default": true}}, "additionalProperties": false}]}, "ssr": {"description": "Server side render (SSR) pages of your application during runtime.", "default": false, "oneOf": [{"type": "boolean", "description": "Enable the server bundles to be written to disk."}, {"type": "object", "properties": {"entry": {"type": "string", "description": "The server entry-point that when executed will spawn the web server."}}, "additionalProperties": false}]}, "appShell": {"type": "boolean", "description": "Generates an application shell during build time.", "default": false}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "description": "Absolute path within the output."}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}, "budget": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of budget.", "enum": ["all", "allScript", "any", "anyScript", "anyComponentStyle", "bundle", "initial"]}, "name": {"type": "string", "description": "The name of the bundle."}, "baseline": {"type": "string", "description": "The baseline size for comparison."}, "maximumWarning": {"type": "string", "description": "The maximum threshold for warning relative to the baseline."}, "maximumError": {"type": "string", "description": "The maximum threshold for error relative to the baseline."}, "minimumWarning": {"type": "string", "description": "The minimum threshold for warning relative to the baseline."}, "minimumError": {"type": "string", "description": "The minimum threshold for error relative to the baseline."}, "warning": {"type": "string", "description": "The threshold for warning relative to the baseline (min & max)."}, "error": {"type": "string", "description": "The threshold for error relative to the baseline (min & max)."}}, "additionalProperties": false}}}, "AngularDevkitBuildAngularBuildersAppShellSchema": {"title": "App Shell Target", "description": "App Shell target options for Build Facade.", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "A browser builder target use for rendering the application shell in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$"}, "serverTarget": {"type": "string", "description": "A server builder target use for rendering the application shell in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$"}, "appModuleBundle": {"type": "string", "description": "Script that exports the Server AppModule to render. This should be the main JavaScript outputted by the server target. By default we will resolve the outputPath of the serverTarget and find a bundle named 'main' in it (whether or not there's a hash tag)."}, "route": {"type": "string", "description": "The route to render.", "default": "/"}, "inputIndexPath": {"type": "string", "description": "The input path for the index.html file. By default uses the output index.html of the browser target."}, "outputIndexPath": {"type": "string", "description": "The output path of the index.html file. By default will overwrite the input file."}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersBrowserSchema": {"title": "Webpack browser schema for Build Facade.", "description": "Browser target options", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema/definitions/assetPattern"}}, "main": {"type": "string", "description": "The full path for the main entry point to the app, relative to the current workspace."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking, dead-code elimination, inlining of critical CSS and fonts inlining. For more information, see https://angular.io/guide/workspace-config#optimization-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"description": "Enables optimization of the styles output.", "default": true, "oneOf": [{"type": "object", "properties": {"minify": {"type": "boolean", "description": "Minify CSS definitions by removing extraneous whitespace and comments, merging identifiers and minimizing values.", "default": true}, "inlineCritical": {"type": "boolean", "description": "Extract and inline critical CSS definitions to improve first paint time.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fonts": {"description": "Enables optimization for fonts. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true, "oneOf": [{"type": "object", "properties": {"inline": {"type": "boolean", "description": "Reduce render blocking requests by inlining external Google Fonts and Adobe Fonts CSS definitions in the application's HTML index file. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}}, "additionalProperties": false}, {"type": "boolean"}]}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema/definitions/fileReplacement"}, "default": []}, "outputPath": {"type": "string", "description": "The full path for the new output directory, relative to the current workspace."}, "resourcesOutputPath": {"type": "string", "description": "The path where style resources will be placed, relative to outputPath."}, "aot": {"type": "boolean", "description": "Build using Ahead of Time compilation.", "default": true}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.io/guide/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "vendorChunk": {"type": "boolean", "description": "Generate a seperate bundle containing only vendor libraries. This option should only be used for development to reduce the incremental compilation time.", "default": false}, "commonChunk": {"type": "boolean", "description": "Generate a seperate bundle containing code used across multiple bundles.", "default": true}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file.", "default": true}, "buildOptimizer": {"type": "boolean", "description": "Enables advanced build optimizations when using the 'aot' option.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "subresourceIntegrity": {"type": "boolean", "description": "Enables the use of subresource integrity validation.", "default": false}, "serviceWorker": {"type": "boolean", "description": "Generates a service worker config for production builds.", "default": false}, "ngswConfigPath": {"type": "string", "description": "Path to ngsw-config.json."}, "index": {"description": "Configures the generation of the application's HTML index.", "oneOf": [{"type": "string", "description": "The path of a file to use for the application's HTML index. The filename of the specified path will be used for the generated file and will be created in the root of the application's configured output path."}, {"type": "object", "description": "", "properties": {"input": {"type": "string", "minLength": 1, "description": "The path of a file to use for the application's generated HTML index."}, "output": {"type": "string", "minLength": 1, "default": "index.html", "description": "The output path of the application's generated HTML index file. The full provided path will be used and will be considered relative to the application's configured output path."}}}]}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed using tools such as 'webpack-bundle-analyzer'.", "default": false}, "budgets": {"description": "Budget thresholds to ensure parts of your application stay within boundaries which you set.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema/definitions/budget"}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "crossOrigin": {"type": "string", "description": "Define the crossorigin attribute setting of elements that provide CORS support.", "default": "none", "enum": ["none", "anonymous", "use-credentials"]}, "allowedCommonJsDependencies": {"description": "A list of CommonJS or AMD packages that are allowed to be used without a build time warning. Use `'*'` to allow all.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "description": "Absolute path within the output."}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "replaceWith": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}, {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}]}, "budget": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of budget.", "enum": ["all", "allScript", "any", "anyScript", "anyComponentStyle", "bundle", "initial"]}, "name": {"type": "string", "description": "The name of the bundle."}, "baseline": {"type": "string", "description": "The baseline size for comparison."}, "maximumWarning": {"type": "string", "description": "The maximum threshold for warning relative to the baseline."}, "maximumError": {"type": "string", "description": "The maximum threshold for error relative to the baseline."}, "minimumWarning": {"type": "string", "description": "The minimum threshold for warning relative to the baseline."}, "minimumError": {"type": "string", "description": "The minimum threshold for error relative to the baseline."}, "warning": {"type": "string", "description": "The threshold for warning relative to the baseline (min & max)."}, "error": {"type": "string", "description": "The threshold for error relative to the baseline (min & max)."}}, "additionalProperties": false}}}, "AngularDevkitBuildAngularBuildersBrowserEsbuildSchema": {"title": "Esbuild browser schema for Build Facade.", "description": "Browser target options", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema/definitions/assetPattern"}}, "main": {"type": "string", "description": "The full path for the main entry point to the app, relative to the current workspace."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The JavaScript/TypeScript file or package containing the file to include."}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking, dead-code elimination, inlining of critical CSS and fonts inlining. For more information, see https://angular.io/guide/workspace-config#optimization-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"description": "Enables optimization of the styles output.", "default": true, "oneOf": [{"type": "object", "properties": {"minify": {"type": "boolean", "description": "Minify CSS definitions by removing extraneous whitespace and comments, merging identifiers and minimizing values.", "default": true}, "inlineCritical": {"type": "boolean", "description": "Extract and inline critical CSS definitions to improve first paint time.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fonts": {"description": "Enables optimization for fonts. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true, "oneOf": [{"type": "object", "properties": {"inline": {"type": "boolean", "description": "Reduce render blocking requests by inlining external Google Fonts and Adobe Fonts CSS definitions in the application's HTML index file. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}}, "additionalProperties": false}, {"type": "boolean"}]}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema/definitions/fileReplacement"}, "default": []}, "outputPath": {"type": "string", "description": "The full path for the new output directory, relative to the current workspace."}, "resourcesOutputPath": {"type": "string", "description": "The path where style resources will be placed, relative to outputPath."}, "aot": {"type": "boolean", "description": "Build using Ahead of Time compilation.", "default": true}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.io/guide/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "vendorChunk": {"type": "boolean", "description": "Generate a seperate bundle containing only vendor libraries. This option should only be used for development to reduce the incremental compilation time.", "default": false}, "commonChunk": {"type": "boolean", "description": "Generate a seperate bundle containing code used across multiple bundles.", "default": true}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file.", "default": true}, "buildOptimizer": {"type": "boolean", "description": "Enables advanced build optimizations when using the 'aot' option.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "subresourceIntegrity": {"type": "boolean", "description": "Enables the use of subresource integrity validation.", "default": false}, "serviceWorker": {"type": "boolean", "description": "Generates a service worker config for production builds.", "default": false}, "ngswConfigPath": {"type": "string", "description": "Path to ngsw-config.json."}, "index": {"description": "Configures the generation of the application's HTML index.", "oneOf": [{"type": "string", "description": "The path of a file to use for the application's HTML index. The filename of the specified path will be used for the generated file and will be created in the root of the application's configured output path."}, {"type": "object", "description": "", "properties": {"input": {"type": "string", "minLength": 1, "description": "The path of a file to use for the application's generated HTML index."}, "output": {"type": "string", "minLength": 1, "default": "index.html", "description": "The output path of the application's generated HTML index file. The full provided path will be used and will be considered relative to the application's configured output path."}}}, {"const": false, "type": "boolean", "description": "Does not generate an `index.html` file."}]}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed using tools such as 'webpack-bundle-analyzer'.", "default": false}, "budgets": {"description": "Budget thresholds to ensure parts of your application stay within boundaries which you set.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema/definitions/budget"}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "crossOrigin": {"type": "string", "description": "Define the crossorigin attribute setting of elements that provide CORS support.", "default": "none", "enum": ["none", "anonymous", "use-credentials"]}, "allowedCommonJsDependencies": {"description": "A list of CommonJS or AMD packages that are allowed to be used without a build time warning. Use `'*'` to allow all.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "description": "Absolute path within the output."}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}, "budget": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of budget.", "enum": ["all", "allScript", "any", "anyScript", "anyComponentStyle", "bundle", "initial"]}, "name": {"type": "string", "description": "The name of the bundle."}, "baseline": {"type": "string", "description": "The baseline size for comparison."}, "maximumWarning": {"type": "string", "description": "The maximum threshold for warning relative to the baseline."}, "maximumError": {"type": "string", "description": "The maximum threshold for error relative to the baseline."}, "minimumWarning": {"type": "string", "description": "The minimum threshold for warning relative to the baseline."}, "minimumError": {"type": "string", "description": "The minimum threshold for error relative to the baseline."}, "warning": {"type": "string", "description": "The threshold for warning relative to the baseline (min & max)."}, "error": {"type": "string", "description": "The threshold for error relative to the baseline (min & max)."}}, "additionalProperties": false}}}, "AngularDevkitBuildAngularBuildersDevServerSchema": {"title": "Dev Server Target", "description": "Dev Server target options for Build Facade.", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "A browser builder target to serve in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$", "deprecated": true}, "buildTarget": {"type": "string", "description": "A build builder target to serve in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "port": {"type": "number", "description": "Port to listen on.", "default": 4200}, "host": {"type": "string", "description": "Host to listen on.", "default": "localhost"}, "proxyConfig": {"type": "string", "description": "Proxy configuration file. For more information, see https://angular.io/guide/build#proxying-to-a-backend-server."}, "ssl": {"type": "boolean", "description": "Serve using HTTPS.", "default": false}, "sslKey": {"type": "string", "description": "SSL key to use for serving HTTPS."}, "sslCert": {"type": "string", "description": "SSL certificate to use for serving HTTPS."}, "headers": {"type": "object", "description": "Custom HTTP headers to be added to all responses.", "propertyNames": {"pattern": "^[-_A-Za-z0-9]+$"}, "additionalProperties": {"type": "string"}}, "open": {"type": "boolean", "description": "Opens the url in default browser.", "default": false, "alias": "o"}, "verbose": {"type": "boolean", "description": "Adds more details to output logging."}, "liveReload": {"type": "boolean", "description": "Whether to reload the page on change, using live-reload.", "default": true}, "publicHost": {"type": "string", "description": "The URL that the browser client (or live-reload client, if enabled) should use to connect to the development server. Use for a complex dev server setup, such as one with reverse proxies. This option has no effect when using the 'application' or other esbuild-based builders."}, "allowedHosts": {"type": "array", "description": "List of hosts that are allowed to access the dev server.", "default": [], "items": {"type": "string"}}, "servePath": {"type": "string", "description": "The pathname where the application will be served."}, "disableHostCheck": {"type": "boolean", "description": "Don't verify connected clients are part of allowed hosts. This option has no effect when using the 'application' or other esbuild-based builders.", "default": false}, "hmr": {"type": "boolean", "description": "Enable hot module replacement.", "default": false}, "watch": {"type": "boolean", "description": "Rebuild on change.", "default": true}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "forceEsbuild": {"type": "boolean", "description": "Force the development server to use the 'browser-esbuild' builder when building. This is a developer preview option for the esbuild-based build system.", "default": false}, "prebundle": {"description": "Enable and control the Vite-based development server's prebundling capabilities. To enable prebundling, the Angular CLI cache must also be enabled. This option has no effect when using the 'browser' or other Webpack-based builders.", "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"exclude": {"description": "List of package imports that should not be prebundled by the development server. The packages will be bundled into the application code itself.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}]}}, "additionalProperties": false, "anyOf": [{}, {}]}, "AngularDevkitBuildAngularBuildersExtractI18nSchema": {"title": "Extract i18n Target", "description": "Extract i18n target options for Build Facade.", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "A browser builder target to extract i18n messages in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$", "deprecated": true}, "buildTarget": {"type": "string", "description": "A builder target to extract i18n messages in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "format": {"type": "string", "description": "Output format for the generated file.", "default": "xlf", "enum": ["xmb", "xlf", "xlif", "xliff", "xlf2", "xliff2", "json", "arb", "legacy-migrate"]}, "progress": {"type": "boolean", "description": "Log progress to the console.", "default": true}, "outputPath": {"type": "string", "description": "Path where output will be placed."}, "outFile": {"type": "string", "description": "Name of the file to output."}}, "additionalProperties": false, "anyOf": [{}, {}]}, "AngularDevkitBuildAngularBuildersKarmaSchema": {"title": "Karma Target", "description": "Karma target options for Build Facade.", "type": "object", "properties": {"main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "karmaConfig": {"type": "string", "description": "The name of the Karma configuration file."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersKarmaSchema/definitions/assetPattern"}}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root. \nThere are 2 special cases:\n - when a path to directory is provided, all spec files ending \".spec.@(ts|tsx)\" will be included\n - when a path to a file is provided, and a matching spec file exists it will be included instead."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.io/guide/workspace-config#source-map-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "watch": {"type": "boolean", "description": "Run build when files change."}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "browsers": {"description": "Override which browsers tests are run against. Set to `false` to not use any browser.", "oneOf": [{"type": "string", "description": "A comma seperate list of browsers to run tests against."}, {"const": false, "type": "boolean", "description": "Does use run tests against a browser."}]}, "codeCoverage": {"type": "boolean", "description": "Output a code coverage report.", "default": false}, "codeCoverageExclude": {"type": "array", "description": "Globs to exclude from code coverage.", "items": {"type": "string"}, "default": []}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string"}, "replaceWith": {"type": "string"}}, "additionalProperties": false}, {"type": "object", "properties": {"replace": {"type": "string"}, "with": {"type": "string"}}, "additionalProperties": false}]}, "default": []}, "reporters": {"type": "array", "description": "Karma reporters to use. Directly passed to the karma runner.", "items": {"type": "string"}}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "output": {"type": "string", "description": "Absolute path within the output."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, {"type": "string"}]}}}, "AngularDevkitBuildAngularBuildersJestSchema": {"title": "Jest browser schema for Build Facade.", "description": "Jest target options", "type": "object", "properties": {"include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "polyfills": {"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersWebTestRunnerSchema": {"title": "Web Test Runner Target", "description": "Web Test Runner target options for Build Facade.", "type": "object", "properties": {"main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersWebTestRunnerSchema/definitions/assetPattern"}}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root. \nThere are 2 special cases:\n - when a path to directory is provided, all spec files ending \".spec.@(ts|tsx)\" will be included\n - when a path to a file is provided, and a matching spec file exists it will be included instead."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.io/guide/workspace-config#source-map-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "watch": {"type": "boolean", "description": "Run build when files change."}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "browsers": {"type": "string", "description": "Override which browsers tests are run against."}, "codeCoverage": {"type": "boolean", "description": "Output a code coverage report.", "default": false}, "codeCoverageExclude": {"type": "array", "description": "Globs to exclude from code coverage.", "items": {"type": "string"}, "default": []}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string"}, "replaceWith": {"type": "string"}}, "additionalProperties": false}, {"type": "object", "properties": {"replace": {"type": "string"}, "with": {"type": "string"}}, "additionalProperties": false}]}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "output": {"type": "string", "description": "Absolute path within the output."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, {"type": "string"}]}}}, "AngularDevkitBuildAngularBuildersProtractorSchema": {"title": "Protractor Target", "description": "Protractor target options for Build Facade.", "type": "object", "properties": {"protractorConfig": {"type": "string", "description": "The name of the Protractor configuration file."}, "devServerTarget": {"type": "string", "description": "A dev-server builder target to run tests against in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^([^:\\s]+:[^:\\s]+(:[^\\s]+)?)?$"}, "grep": {"type": "string", "description": "Execute specs whose names match the pattern, which is internally compiled to a RegExp."}, "invertGrep": {"type": "boolean", "description": "Invert the selection specified by the 'grep' option.", "default": false}, "specs": {"type": "array", "description": "Override specs in the protractor config.", "default": [], "items": {"type": "string", "description": "Spec name."}}, "suite": {"type": "string", "description": "Override suite in the protractor config."}, "webdriverUpdate": {"type": "boolean", "description": "Try to update webdriver.", "default": true}, "port": {"type": "number", "description": "The port to use to serve the application."}, "host": {"type": "string", "description": "Host to listen on."}, "baseUrl": {"type": "string", "description": "Base URL for protractor to connect to."}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersPrerenderSchema": {"title": "Prerender Target", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "Target to build.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$"}, "serverTarget": {"type": "string", "description": "Server target to use for prerendering the app.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$"}, "routesFile": {"type": "string", "description": "The path to a file that contains a list of all routes to prerender, separated by newlines. This option is useful if you want to prerender routes with parameterized URLs."}, "routes": {"type": "array", "description": "The routes to render.", "items": {"minItems": 1, "type": "string", "uniqueItems": true}, "default": []}, "discoverRoutes": {"type": "boolean", "description": "Whether the builder should process the Angular Router configuration to find all unparameterized routes and prerender them.", "default": true}}, "anyOf": [{}, {}], "additionalProperties": false}, "AngularDevkitBuildAngularBuildersSsrDevServerSchema": {"title": "SSR Dev Server Target", "description": "SSR Dev Server target options for Build Facade.", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "Browser target to build.", "pattern": ".+:.+(:.+)?"}, "serverTarget": {"type": "string", "description": "Server target to build.", "pattern": ".+:.+(:.+)?"}, "host": {"type": "string", "description": "Host to listen on.", "default": "localhost"}, "port": {"type": "number", "default": 4200, "description": "Port to start the development server at. Default is 4200. Pass 0 to get a dynamically assigned port."}, "watch": {"type": "boolean", "description": "Rebuild on change.", "default": true}, "publicHost": {"type": "string", "description": "The URL that the browser client should use to connect to the development server. Use for a complex dev server setup, such as one with reverse proxies."}, "open": {"type": "boolean", "description": "Opens the url in default browser.", "default": false, "alias": "o"}, "progress": {"type": "boolean", "description": "Log progress to the console while building."}, "inspect": {"type": "boolean", "description": "Launch the development server in inspector mode and listen on address and port '127.0.0.1:9229'.", "default": false}, "ssl": {"type": "boolean", "description": "Serve using HTTPS.", "default": false}, "sslKey": {"type": "string", "description": "SSL key to use for serving HTTPS."}, "sslCert": {"type": "string", "description": "SSL certificate to use for serving HTTPS."}, "proxyConfig": {"type": "string", "description": "Proxy configuration file."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging."}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersServerSchema": {"title": "Universal Target", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersServerSchema/definitions/assetPattern"}}, "main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "default": "tsconfig.app.json", "description": "The name of the TypeScript configuration file."}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking and dead-code elimination. For more information, see https://angular.io/guide/workspace-config#optimization-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"type": "boolean", "description": "Enables optimization of the styles output.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersServerSchema/definitions/fileReplacement"}, "default": []}, "outputPath": {"type": "string", "description": "Path where output will be placed."}, "resourcesOutputPath": {"type": "string", "description": "The path where style resources will be placed, relative to outputPath."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.io/guide/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "vendorChunk": {"type": "boolean", "description": "Generate a seperate bundle containing only vendor libraries. This option should only be used for development to reduce the incremental compilation time.", "default": false}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file, in the case of production builds only.", "default": true}, "buildOptimizer": {"type": "boolean", "description": "Enables advanced build optimizations.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed using tools such as 'webpack-bundle-analyzer'.", "default": false}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "description": "Absolute path within the output."}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "replaceWith": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}, {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}]}}}, "AngularDevkitBuildAngularBuildersNgPackagrSchema": {"title": "ng-packagr Target", "description": "ng-packagr target options for Build Architect. Use to build library projects.", "type": "object", "properties": {"project": {"type": "string", "description": "The file path for the ng-packagr configuration file, relative to the current workspace."}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}}, "additionalProperties": false}}}