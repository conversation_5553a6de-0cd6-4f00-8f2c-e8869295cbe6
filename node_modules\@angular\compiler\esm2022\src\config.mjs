/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ViewEncapsulation } from './core';
import { noUndefined } from './util';
export class CompilerConfig {
    constructor({ defaultEncapsulation = ViewEncapsulation.Emulated, preserveWhitespaces, strictInjectionParameters } = {}) {
        this.defaultEncapsulation = defaultEncapsulation;
        this.preserveWhitespaces = preserveWhitespacesDefault(noUndefined(preserveWhitespaces));
        this.strictInjectionParameters = strictInjectionParameters === true;
    }
}
export function preserveWhitespacesDefault(preserveWhitespacesOption, defaultSetting = false) {
    return preserveWhitespacesOption === null ? defaultSetting : preserveWhitespacesOption;
}
//# sourceMappingURL=data:application/json;base64,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