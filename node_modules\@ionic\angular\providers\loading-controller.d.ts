import { OverlayBaseController } from '@ionic/angular/common';
import type { LoadingOptions } from '@ionic/core';
import * as i0 from "@angular/core";
export declare class LoadingController extends OverlayBaseController<LoadingOptions, HTMLIonLoadingElement> {
    constructor();
    static ɵfac: i0.ɵɵFactoryDeclaration<LoadingController, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<LoadingController>;
}
