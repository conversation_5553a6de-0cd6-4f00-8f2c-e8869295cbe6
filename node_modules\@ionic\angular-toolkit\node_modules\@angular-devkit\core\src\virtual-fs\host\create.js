"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSyncHost = void 0;
const rxjs_1 = require("rxjs");
function wrapAction(action) {
    return new rxjs_1.Observable((subscriber) => {
        subscriber.next(action());
        subscriber.complete();
    });
}
function createSyncHost(handler) {
    return new (class {
        get capabilities() {
            return { synchronous: true };
        }
        read(path) {
            return wrapAction(() => handler.read(path));
        }
        list(path) {
            return wrapAction(() => handler.list(path));
        }
        exists(path) {
            return wrapAction(() => handler.exists(path));
        }
        isDirectory(path) {
            return wrapAction(() => handler.isDirectory(path));
        }
        isFile(path) {
            return wrapAction(() => handler.isFile(path));
        }
        stat(path) {
            return wrapAction(() => handler.stat(path));
        }
        write(path, content) {
            return wrapAction(() => handler.write(path, content));
        }
        delete(path) {
            return wrapAction(() => handler.delete(path));
        }
        rename(from, to) {
            return wrapAction(() => handler.rename(from, to));
        }
        watch() {
            return null;
        }
    })();
}
exports.createSyncHost = createSyncHost;
//# sourceMappingURL=data:application/json;base64,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