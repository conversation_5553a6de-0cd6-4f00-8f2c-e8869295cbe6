/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import type { PartialMessage } from 'esbuild';
import type ts from 'typescript';
/**
 * Converts a TypeScript Diagnostic message into an esbuild compatible message object.
 * @param diagnostic The TypeScript diagnostic to convert.
 * @returns An esbuild diagnostic message as a PartialMessage object
 */
export declare function convertTypeScriptDiagnostic(typescript: typeof ts, diagnostic: ts.Diagnostic): PartialMessage;
