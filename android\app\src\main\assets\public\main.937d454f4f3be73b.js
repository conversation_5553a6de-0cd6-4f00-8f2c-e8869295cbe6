(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8792],{1656:(Ct,We,O)=>{"use strict";O.d(We,{c:()=>ne,r:()=>te});const ne=(V,A)=>{V.componentOnReady?V.componentOnReady().then(Y=>A(Y)):te(()=>A(V))},te=V=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(V):"function"==typeof requestAnimationFrame?requestAnimationFrame(V):setTimeout(V)},6031:(Ct,We,O)=>{"use strict";O.d(We,{L:()=>i,a:()=>C,b:()=>ne,c:()=>X,d:()=>Me,g:()=>V});const i="ionViewWillEnter",C="ionViewDidEnter",ne="ionViewWillLeave",X="ionViewDidLeave",Me="ionViewWillUnload",V=A=>A.classList.contains("ion-page")?A:A.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")||A},3503:(Ct,We,O)=>{"use strict";O.d(We,{c:()=>Ve});var i=O(8476),C=O(5638);let ne;const Me=ge=>ge.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ie=ge=>(void 0===ne&&(ne=void 0===ge.style.animationName&&void 0!==ge.style.webkitAnimationName?"-webkit-":""),ne),L=(ge,se,me)=>{const Ze=se.startsWith("animation")?Ie(ge):"";ge.style.setProperty(Ze+se,me)},Pe=(ge,se)=>{const me=se.startsWith("animation")?Ie(ge):"";ge.style.removeProperty(me+se)},te=[],ie=(ge=[],se)=>{if(void 0!==se){const me=Array.isArray(se)?se:[se];return[...ge,...me]}return ge},Ve=ge=>{let se,me,Ze,Ee,Ce,ke,Z,at,Ue,H,q,Le,Xe,Ge=[],V=[],A=[],Y=!1,de={},_e=[],Re=[],Ke={},pt=0,Rt=!1,ze=!1,ae=!0,$=!1,Oe=!0,It=!1;const Tt=ge,vt=[],xt=[],Pt=[],Ft=[],Be=[],xe=[],he=[],Ae=[],ht=[],Lt=[],Ht=[],on="function"==typeof AnimationEffect||void 0!==i.w&&"function"==typeof i.w.AnimationEffect,$t="function"==typeof Element&&"function"==typeof Element.prototype.animate&&on,yt=()=>Ht,er=(J,He)=>{const b=He.findIndex(x=>x.c===J);b>-1&&He.splice(b,1)},yr=(J,He)=>((He?.oneTimeCallback?xt:vt).push({c:J,o:He}),Xe),Ln=()=>{if($t)Ht.forEach(J=>{J.cancel()}),Ht.length=0;else{const J=Ft.slice();(0,C.r)(()=>{J.forEach(He=>{Pe(He,"animation-name"),Pe(He,"animation-duration"),Pe(He,"animation-timing-function"),Pe(He,"animation-iteration-count"),Pe(He,"animation-delay"),Pe(He,"animation-play-state"),Pe(He,"animation-fill-mode"),Pe(He,"animation-direction")})})}},Zt=()=>{xe.forEach(J=>{J?.parentNode&&J.parentNode.removeChild(J)}),xe.length=0},gt=()=>void 0!==Ce?Ce:Z?Z.getFill():"both",Et=()=>void 0!==Ue?Ue:void 0!==ke?ke:Z?Z.getDirection():"normal",Qt=()=>Rt?"linear":void 0!==Ze?Ze:Z?Z.getEasing():"linear",kt=()=>ze?0:void 0!==H?H:void 0!==me?me:Z?Z.getDuration():0,ln=()=>void 0!==Ee?Ee:Z?Z.getIterations():1,Sn=()=>void 0!==q?q:void 0!==se?se:Z?Z.getDelay():0,Vn=()=>{0!==pt&&(pt--,0===pt&&((()=>{br(),ht.forEach(G=>G()),Lt.forEach(G=>G());const J=ae?1:0,He=_e,b=Re,x=Ke;Ft.forEach(G=>{const Q=G.classList;He.forEach(Fe=>Q.add(Fe)),b.forEach(Fe=>Q.remove(Fe));for(const Fe in x)x.hasOwnProperty(Fe)&&L(G,Fe,x[Fe])}),H=void 0,Ue=void 0,q=void 0,vt.forEach(G=>G.c(J,Xe)),xt.forEach(G=>G.c(J,Xe)),xt.length=0,Oe=!0,ae&&($=!0),ae=!0})(),Z&&Z.animationFinish()))},pn=(J=!0)=>{Zt();const He=(ge=>(ge.forEach(se=>{for(const me in se)if(se.hasOwnProperty(me)){const Ze=se[me];if("easing"===me)se["animation-timing-function"]=Ze,delete se[me];else{const Ee=Me(me);Ee!==me&&(se[Ee]=Ze,delete se[me])}}}),ge))(Ge);Ft.forEach(b=>{if(He.length>0){const x=((ge=[])=>ge.map(se=>{const me=se.offset,Ze=[];for(const Ee in se)se.hasOwnProperty(Ee)&&"offset"!==Ee&&Ze.push(`${Ee}: ${se[Ee]};`);return`${100*me}% { ${Ze.join(" ")} }`}).join(" "))(He);Le=void 0!==ge?ge:(ge=>{let se=te.indexOf(ge);return se<0&&(se=te.push(ge)-1),`ion-animation-${se}`})(x);const G=((ge,se,me)=>{var Ze;const Ee=(ge=>{const se=void 0!==ge.getRootNode?ge.getRootNode():ge;return se.head||se})(me),Ce=Ie(me),ke=Ee.querySelector("#"+ge);if(ke)return ke;const Ge=(null!==(Ze=me.ownerDocument)&&void 0!==Ze?Ze:document).createElement("style");return Ge.id=ge,Ge.textContent=`@${Ce}keyframes ${ge} { ${se} } @${Ce}keyframes ${ge}-alt { ${se} }`,Ee.appendChild(Ge),Ge})(Le,x,b);xe.push(G),L(b,"animation-duration",`${kt()}ms`),L(b,"animation-timing-function",Qt()),L(b,"animation-delay",`${Sn()}ms`),L(b,"animation-fill-mode",gt()),L(b,"animation-direction",Et());const Q=ln()===1/0?"infinite":ln().toString();L(b,"animation-iteration-count",Q),L(b,"animation-play-state","paused"),J&&L(b,"animation-name",`${G.id}-alt`),(0,C.r)(()=>{L(b,"animation-name",G.id||null)})}})},Nr=(J=!0)=>{(()=>{he.forEach(x=>x()),Ae.forEach(x=>x());const J=V,He=A,b=de;Ft.forEach(x=>{const G=x.classList;J.forEach(Q=>G.add(Q)),He.forEach(Q=>G.remove(Q));for(const Q in b)b.hasOwnProperty(Q)&&L(x,Q,b[Q])})})(),Ge.length>0&&($t?(Ft.forEach(J=>{const He=J.animate(Ge,{id:Tt,delay:Sn(),duration:kt(),easing:Qt(),iterations:ln(),fill:gt(),direction:Et()});He.pause(),Ht.push(He)}),Ht.length>0&&(Ht[0].onfinish=()=>{Vn()})):pn(J)),Y=!0},Xn=J=>{if(J=Math.min(Math.max(J,0),.9999),$t)Ht.forEach(He=>{He.currentTime=He.effect.getComputedTiming().delay+kt()*J,He.pause()});else{const He=`-${kt()*J}ms`;Ft.forEach(b=>{Ge.length>0&&(L(b,"animation-delay",He),L(b,"animation-play-state","paused"))})}},Bn=J=>{Ht.forEach(He=>{He.effect.updateTiming({delay:Sn(),duration:kt(),easing:Qt(),iterations:ln(),fill:gt(),direction:Et()})}),void 0!==J&&Xn(J)},Un=(J=!0,He)=>{(0,C.r)(()=>{Ft.forEach(b=>{L(b,"animation-name",Le||null),L(b,"animation-duration",`${kt()}ms`),L(b,"animation-timing-function",Qt()),L(b,"animation-delay",void 0!==He?`-${He*kt()}ms`:`${Sn()}ms`),L(b,"animation-fill-mode",gt()||null),L(b,"animation-direction",Et()||null);const x=ln()===1/0?"infinite":ln().toString();L(b,"animation-iteration-count",x),J&&L(b,"animation-name",`${Le}-alt`),(0,C.r)(()=>{L(b,"animation-name",Le||null)})})})},_n=(J=!1,He=!0,b)=>(J&&Be.forEach(x=>{x.update(J,He,b)}),$t?Bn(b):Un(He,b),Xe),Jt=()=>{Y&&($t?Ht.forEach(J=>{J.pause()}):Ft.forEach(J=>{L(J,"animation-play-state","paused")}),It=!0)},ur=()=>{at=void 0,Vn()},br=()=>{at&&clearTimeout(at)},T=J=>new Promise(He=>{J?.sync&&(ze=!0,yr(()=>ze=!1,{oneTimeCallback:!0})),Y||Nr(),$&&($t?(Xn(0),Bn()):Un(),$=!1),Oe&&(pt=Be.length+1,Oe=!1);const b=()=>{er(x,xt),He()},x=()=>{er(b,Pt),He()};yr(x,{oneTimeCallback:!0}),((J,He)=>{Pt.push({c:J,o:{oneTimeCallback:!0}})})(b),Be.forEach(G=>{G.play()}),$t?(Ht.forEach(J=>{J.play()}),(0===Ge.length||0===Ft.length)&&Vn()):(()=>{if(br(),(0,C.r)(()=>{Ft.forEach(J=>{Ge.length>0&&L(J,"animation-play-state","running")})}),0===Ge.length||0===Ft.length)Vn();else{const J=Sn()||0,He=kt()||0,b=ln()||1;isFinite(b)&&(at=setTimeout(ur,J+He*b+100)),((ge,se)=>{let me;const Ze={passive:!0},Ce=ke=>{ge===ke.target&&(me&&me(),br(),(0,C.r)(()=>{Ft.forEach(J=>{Pe(J,"animation-duration"),Pe(J,"animation-delay"),Pe(J,"animation-play-state")}),(0,C.r)(Vn)}))};ge&&(ge.addEventListener("webkitAnimationEnd",Ce,Ze),ge.addEventListener("animationend",Ce,Ze),me=()=>{ge.removeEventListener("webkitAnimationEnd",Ce,Ze),ge.removeEventListener("animationend",Ce,Ze)})})(Ft[0])}})(),It=!1}),fe=(J,He)=>{const b=Ge[0];return void 0===b||void 0!==b.offset&&0!==b.offset?Ge=[{offset:0,[J]:He},...Ge]:b[J]=He,Xe};return Xe={parentAnimation:Z,elements:Ft,childAnimations:Be,id:Tt,animationFinish:Vn,from:fe,to:(J,He)=>{const b=Ge[Ge.length-1];return void 0===b||void 0!==b.offset&&1!==b.offset?Ge=[...Ge,{offset:1,[J]:He}]:b[J]=He,Xe},fromTo:(J,He,b)=>fe(J,He).to(J,b),parent:J=>(Z=J,Xe),play:T,pause:()=>(Be.forEach(J=>{J.pause()}),Jt(),Xe),stop:()=>{Be.forEach(J=>{J.stop()}),Y&&(Ln(),Y=!1),Rt=!1,ze=!1,Oe=!0,Ue=void 0,H=void 0,q=void 0,pt=0,$=!1,ae=!0,It=!1,Pt.forEach(J=>J.c(0,Xe)),Pt.length=0},destroy:J=>(Be.forEach(He=>{He.destroy(J)}),(J=>{Ln(),J&&Zt()})(J),Ft.length=0,Be.length=0,Ge.length=0,vt.length=0,xt.length=0,Y=!1,Oe=!0,Xe),keyframes:J=>{const He=Ge!==J;return Ge=J,He&&(J=>{$t?yt().forEach(He=>{const b=He.effect;if(b.setKeyframes)b.setKeyframes(J);else{const x=new KeyframeEffect(b.target,J,b.getTiming());He.effect=x}}):pn()})(Ge),Xe},addAnimation:J=>{if(null!=J)if(Array.isArray(J))for(const He of J)He.parent(Xe),Be.push(He);else J.parent(Xe),Be.push(J);return Xe},addElement:J=>{if(null!=J)if(1===J.nodeType)Ft.push(J);else if(J.length>=0)for(let He=0;He<J.length;He++)Ft.push(J[He]);else console.error("Invalid addElement value");return Xe},update:_n,fill:J=>(Ce=J,_n(!0),Xe),direction:J=>(ke=J,_n(!0),Xe),iterations:J=>(Ee=J,_n(!0),Xe),duration:J=>(!$t&&0===J&&(J=1),me=J,_n(!0),Xe),easing:J=>(Ze=J,_n(!0),Xe),delay:J=>(se=J,_n(!0),Xe),getWebAnimations:yt,getKeyframes:()=>Ge,getFill:gt,getDirection:Et,getDelay:Sn,getIterations:ln,getEasing:Qt,getDuration:kt,afterAddRead:J=>(ht.push(J),Xe),afterAddWrite:J=>(Lt.push(J),Xe),afterClearStyles:(J=[])=>{for(const He of J)Ke[He]="";return Xe},afterStyles:(J={})=>(Ke=J,Xe),afterRemoveClass:J=>(Re=ie(Re,J),Xe),afterAddClass:J=>(_e=ie(_e,J),Xe),beforeAddRead:J=>(he.push(J),Xe),beforeAddWrite:J=>(Ae.push(J),Xe),beforeClearStyles:(J=[])=>{for(const He of J)de[He]="";return Xe},beforeStyles:(J={})=>(de=J,Xe),beforeRemoveClass:J=>(A=ie(A,J),Xe),beforeAddClass:J=>(V=ie(V,J),Xe),onFinish:yr,isRunning:()=>0!==pt&&!It,progressStart:(J=!1,He)=>(Be.forEach(b=>{b.progressStart(J,He)}),Jt(),Rt=J,Y||Nr(),_n(!1,!0,He),Xe),progressStep:J=>(Be.forEach(He=>{He.progressStep(J)}),Xn(J),Xe),progressEnd:(J,He,b)=>(Rt=!1,Be.forEach(x=>{x.progressEnd(J,He,b)}),void 0!==b&&(H=b),$=!1,ae=!0,0===J?(Ue="reverse"===Et()?"normal":"reverse","reverse"===Ue&&(ae=!1),$t?(_n(),Xn(1-He)):(q=(1-He)*kt()*-1,_n(!1,!1))):1===J&&($t?(_n(),Xn(He)):(q=He*kt()*-1,_n(!1,!1))),void 0!==J&&!Z&&T(),Xe)}}},464:(Ct,We,O)=>{"use strict";O.d(We,{E:()=>ye,a:()=>i,s:()=>Pe});const i=te=>{try{if(te instanceof L)return te.value;if(!X()||"string"!=typeof te||""===te)return te;if(te.includes("onload="))return"";const De=document.createDocumentFragment(),pe=document.createElement("div");De.appendChild(pe),pe.innerHTML=te,Ie.forEach(ge=>{const se=De.querySelectorAll(ge);for(let me=se.length-1;me>=0;me--){const Ze=se[me];Ze.parentNode?Ze.parentNode.removeChild(Ze):De.removeChild(Ze);const Ee=ne(Ze);for(let Ce=0;Ce<Ee.length;Ce++)C(Ee[Ce])}});const ue=ne(De);for(let ge=0;ge<ue.length;ge++)C(ue[ge]);const ie=document.createElement("div");ie.appendChild(De);const Ve=ie.querySelector("div");return null!==Ve?Ve.innerHTML:ie.innerHTML}catch(De){return console.error(De),""}},C=te=>{if(te.nodeType&&1!==te.nodeType)return;if(typeof NamedNodeMap<"u"&&!(te.attributes instanceof NamedNodeMap))return void te.remove();for(let pe=te.attributes.length-1;pe>=0;pe--){const ue=te.attributes.item(pe),ie=ue.name;if(!Me.includes(ie.toLowerCase())){te.removeAttribute(ie);continue}const Ve=ue.value,ge=te[ie];(null!=Ve&&Ve.toLowerCase().includes("javascript:")||null!=ge&&ge.toLowerCase().includes("javascript:"))&&te.removeAttribute(ie)}const De=ne(te);for(let pe=0;pe<De.length;pe++)C(De[pe])},ne=te=>null!=te.children?te.children:te.childNodes,X=()=>{var te;const De=window,pe=null===(te=De?.Ionic)||void 0===te?void 0:te.config;return!pe||(pe.get?pe.get("sanitizerEnabled",!0):!0===pe.sanitizerEnabled||void 0===pe.sanitizerEnabled)},Me=["class","id","href","src","name","slot"],Ie=["script","style","iframe","meta","link","object","embed"];class L{constructor(De){this.value=De}}const Pe=te=>{const De=window,pe=De.Ionic;if(!pe||!pe.config||"Object"===pe.config.constructor.name)return De.Ionic=De.Ionic||{},De.Ionic.config=Object.assign(Object.assign({},De.Ionic.config),te),De.Ionic.config},ye=!1},5938:(Ct,We,O)=>{"use strict";O.d(We,{C:()=>Me,a:()=>ne,d:()=>X});var i=O(467),C=O(5638);const ne=function(){var Ie=(0,i.A)(function*(L,Pe,je,ye,te,De){var pe;if(L)return L.attachViewToDom(Pe,je,te,ye);if(!(De||"string"==typeof je||je instanceof HTMLElement))throw new Error("framework delegate is missing");const ue="string"==typeof je?null===(pe=Pe.ownerDocument)||void 0===pe?void 0:pe.createElement(je):je;return ye&&ye.forEach(ie=>ue.classList.add(ie)),te&&Object.assign(ue,te),Pe.appendChild(ue),yield new Promise(ie=>(0,C.c)(ue,ie)),ue});return function(Pe,je,ye,te,De,pe){return Ie.apply(this,arguments)}}(),X=(Ie,L)=>{if(L){if(Ie)return Ie.removeViewFromDom(L.parentElement,L);L.remove()}return Promise.resolve()},Me=()=>{let Ie,L;return{attachViewToDom:function(){var ye=(0,i.A)(function*(te,De,pe={},ue=[]){var ie,Ve;let ge;if(Ie=te,De){const me="string"==typeof De?null===(ie=Ie.ownerDocument)||void 0===ie?void 0:ie.createElement(De):De;ue.forEach(Ze=>me.classList.add(Ze)),Object.assign(me,pe),Ie.appendChild(me),ge=me,yield new Promise(Ze=>(0,C.c)(me,Ze))}else if(Ie.children.length>0&&("ION-MODAL"===Ie.tagName||"ION-POPOVER"===Ie.tagName)&&!(ge=Ie.children[0]).classList.contains("ion-delegate-host")){const Ze=null===(Ve=Ie.ownerDocument)||void 0===Ve?void 0:Ve.createElement("div");Ze.classList.add("ion-delegate-host"),ue.forEach(Ee=>Ze.classList.add(Ee)),Ze.append(...Ie.children),Ie.appendChild(Ze),ge=Ze}const se=document.querySelector("ion-app")||document.body;return L=document.createComment("ionic teleport"),Ie.parentNode.insertBefore(L,Ie),se.appendChild(Ie),ge??Ie});return function(De,pe){return ye.apply(this,arguments)}}(),removeViewFromDom:()=>(Ie&&L&&(L.parentNode.insertBefore(Ie,L),L.remove()),Promise.resolve())}}},8221:(Ct,We,O)=>{"use strict";O.d(We,{G:()=>Me});class C{constructor(L,Pe,je,ye,te){this.id=Pe,this.name=je,this.disableScroll=te,this.priority=1e6*ye+Pe,this.ctrl=L}canStart(){return!!this.ctrl&&this.ctrl.canStart(this.name)}start(){return!!this.ctrl&&this.ctrl.start(this.name,this.id,this.priority)}capture(){if(!this.ctrl)return!1;const L=this.ctrl.capture(this.name,this.id,this.priority);return L&&this.disableScroll&&this.ctrl.disableScroll(this.id),L}release(){this.ctrl&&(this.ctrl.release(this.id),this.disableScroll&&this.ctrl.enableScroll(this.id))}destroy(){this.release(),this.ctrl=void 0}}class ne{constructor(L,Pe,je,ye){this.id=Pe,this.disable=je,this.disableScroll=ye,this.ctrl=L}block(){if(this.ctrl){if(this.disable)for(const L of this.disable)this.ctrl.disableGesture(L,this.id);this.disableScroll&&this.ctrl.disableScroll(this.id)}}unblock(){if(this.ctrl){if(this.disable)for(const L of this.disable)this.ctrl.enableGesture(L,this.id);this.disableScroll&&this.ctrl.enableScroll(this.id)}}destroy(){this.unblock(),this.ctrl=void 0}}const X="backdrop-no-scroll",Me=new class i{constructor(){this.gestureId=0,this.requestedStart=new Map,this.disabledGestures=new Map,this.disabledScroll=new Set}createGesture(L){var Pe;return new C(this,this.newID(),L.name,null!==(Pe=L.priority)&&void 0!==Pe?Pe:0,!!L.disableScroll)}createBlocker(L={}){return new ne(this,this.newID(),L.disable,!!L.disableScroll)}start(L,Pe,je){return this.canStart(L)?(this.requestedStart.set(Pe,je),!0):(this.requestedStart.delete(Pe),!1)}capture(L,Pe,je){if(!this.start(L,Pe,je))return!1;const ye=this.requestedStart;let te=-1e4;if(ye.forEach(De=>{te=Math.max(te,De)}),te===je){this.capturedId=Pe,ye.clear();const De=new CustomEvent("ionGestureCaptured",{detail:{gestureName:L}});return document.dispatchEvent(De),!0}return ye.delete(Pe),!1}release(L){this.requestedStart.delete(L),this.capturedId===L&&(this.capturedId=void 0)}disableGesture(L,Pe){let je=this.disabledGestures.get(L);void 0===je&&(je=new Set,this.disabledGestures.set(L,je)),je.add(Pe)}enableGesture(L,Pe){const je=this.disabledGestures.get(L);void 0!==je&&je.delete(Pe)}disableScroll(L){this.disabledScroll.add(L),1===this.disabledScroll.size&&document.body.classList.add(X)}enableScroll(L){this.disabledScroll.delete(L),0===this.disabledScroll.size&&document.body.classList.remove(X)}canStart(L){return!(void 0!==this.capturedId||this.isDisabled(L))}isCaptured(){return void 0!==this.capturedId}isScrollDisabled(){return this.disabledScroll.size>0}isDisabled(L){const Pe=this.disabledGestures.get(L);return!!(Pe&&Pe.size>0)}newID(){return this.gestureId++,this.gestureId}}},3113:(Ct,We,O)=>{"use strict";O.r(We),O.d(We,{MENU_BACK_BUTTON_PRIORITY:()=>je,OVERLAY_BACK_BUTTON_PRIORITY:()=>Pe,blockHardwareBackButton:()=>Ie,shouldUseCloseWatcher:()=>Me,startHardwareBackButton:()=>L});var i=O(467),C=O(8476),ne=O(611);O(4363);const Me=()=>ne.c.get("experimentalCloseWatcher",!1)&&void 0!==C.w&&"CloseWatcher"in C.w,Ie=()=>{document.addEventListener("backbutton",()=>{})},L=()=>{const ye=document;let te=!1;const De=()=>{if(te)return;let pe=0,ue=[];const ie=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(se,me){ue.push({priority:se,handler:me,id:pe++})}}});ye.dispatchEvent(ie);const Ve=function(){var se=(0,i.A)(function*(me){try{if(me?.handler){const Ze=me.handler(ge);null!=Ze&&(yield Ze)}}catch(Ze){console.error(Ze)}});return function(Ze){return se.apply(this,arguments)}}(),ge=()=>{if(ue.length>0){let se={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};ue.forEach(me=>{me.priority>=se.priority&&(se=me)}),te=!0,ue=ue.filter(me=>me.id!==se.id),Ve(se).then(()=>te=!1)}};ge()};if(Me()){let pe;const ue=()=>{pe?.destroy(),pe=new C.w.CloseWatcher,pe.onclose=()=>{De(),ue()}};ue()}else ye.addEventListener("backbutton",De)},Pe=100,je=99},5638:(Ct,We,O)=>{"use strict";O.d(We,{a:()=>Pe,b:()=>je,c:()=>ne,d:()=>Ve,e:()=>ie,f:()=>ue,g:()=>ye,h:()=>pe,i:()=>L,j:()=>Ce,k:()=>Me,l:()=>ge,m:()=>X,n:()=>De,o:()=>se,p:()=>Ee,q:()=>ke,r:()=>te,s:()=>Ge,t:()=>i,u:()=>me,v:()=>Ze});const i=(V,A=0)=>new Promise(Y=>{C(V,A,Y)}),C=(V,A=0,Y)=>{let Z,de;const _e={passive:!0},Ke=()=>{Z&&Z()},pt=Rt=>{(void 0===Rt||V===Rt.target)&&(Ke(),Y(Rt))};return V&&(V.addEventListener("webkitTransitionEnd",pt,_e),V.addEventListener("transitionend",pt,_e),de=setTimeout(pt,A+500),Z=()=>{void 0!==de&&(clearTimeout(de),de=void 0),V.removeEventListener("webkitTransitionEnd",pt,_e),V.removeEventListener("transitionend",pt,_e)}),Ke},ne=(V,A)=>{V.componentOnReady?V.componentOnReady().then(Y=>A(Y)):te(()=>A(V))},X=V=>void 0!==V.componentOnReady,Me=(V,A=[])=>{const Y={};return A.forEach(Z=>{V.hasAttribute(Z)&&(null!==V.getAttribute(Z)&&(Y[Z]=V.getAttribute(Z)),V.removeAttribute(Z))}),Y},Ie=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],L=(V,A)=>{let Y=Ie;return A&&A.length>0&&(Y=Y.filter(Z=>!A.includes(Z))),Me(V,Y)},Pe=(V,A,Y,Z)=>{var de;if(typeof window<"u"){const _e=window,Re=null===(de=_e?.Ionic)||void 0===de?void 0:de.config;if(Re){const Ke=Re.get("_ael");if(Ke)return Ke(V,A,Y,Z);if(Re._ael)return Re._ael(V,A,Y,Z)}}return V.addEventListener(A,Y,Z)},je=(V,A,Y,Z)=>{var de;if(typeof window<"u"){const _e=window,Re=null===(de=_e?.Ionic)||void 0===de?void 0:de.config;if(Re){const Ke=Re.get("_rel");if(Ke)return Ke(V,A,Y,Z);if(Re._rel)return Re._rel(V,A,Y,Z)}}return V.removeEventListener(A,Y,Z)},ye=(V,A=V)=>V.shadowRoot||A,te=V=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(V):"function"==typeof requestAnimationFrame?requestAnimationFrame(V):setTimeout(V),De=V=>!!V.shadowRoot&&!!V.attachShadow,pe=V=>{const A=V.closest("ion-item");return A?A.querySelector("ion-label"):null},ue=V=>{if(V.focus(),V.classList.contains("ion-focusable")){const A=V.closest("ion-app");A&&A.setFocus([V])}},ie=(V,A)=>{let Y;const Z=V.getAttribute("aria-labelledby"),de=V.id;let _e=null!==Z&&""!==Z.trim()?Z:A+"-lbl",Re=null!==Z&&""!==Z.trim()?document.getElementById(Z):pe(V);return Re?(null===Z&&(Re.id=_e),Y=Re.textContent,Re.setAttribute("aria-hidden","true")):""!==de.trim()&&(Re=document.querySelector(`label[for="${de}"]`),Re&&(""!==Re.id?_e=Re.id:Re.id=_e=`${de}-lbl`,Y=Re.textContent)),{label:Re,labelId:_e,labelText:Y}},Ve=(V,A,Y,Z,de)=>{if(V||De(A)){let _e=A.querySelector("input.aux-input");_e||(_e=A.ownerDocument.createElement("input"),_e.type="hidden",_e.classList.add("aux-input"),A.appendChild(_e)),_e.disabled=de,_e.name=Y,_e.value=Z||""}},ge=(V,A,Y)=>Math.max(V,Math.min(A,Y)),se=(V,A)=>{if(!V){const Y="ASSERT: "+A;throw console.error(Y),new Error(Y)}},me=V=>V.timeStamp||Date.now(),Ze=V=>{if(V){const A=V.changedTouches;if(A&&A.length>0){const Y=A[0];return{x:Y.clientX,y:Y.clientY}}if(void 0!==V.pageX)return{x:V.pageX,y:V.pageY}}return{x:0,y:0}},Ee=V=>{const A="rtl"===document.dir;switch(V){case"start":return A;case"end":return!A;default:throw new Error(`"${V}" is not a valid value for [side]. Use "start" or "end" instead.`)}},Ce=(V,A)=>{const Y=V._original||V;return{_original:V,emit:ke(Y.emit.bind(Y),A)}},ke=(V,A=0)=>{let Y;return(...Z)=>{clearTimeout(Y),Y=setTimeout(V,A,...Z)}},Ge=(V,A)=>{if(V??(V={}),A??(A={}),V===A)return!0;const Y=Object.keys(V);if(Y.length!==Object.keys(A).length)return!1;for(const Z of Y)if(!(Z in A)||V[Z]!==A[Z])return!1;return!0}},405:(Ct,We,O)=>{"use strict";O.r(We),O.d(We,{GESTURE_CONTROLLER:()=>i.G,createGesture:()=>je});var i=O(8221);const C=(pe,ue,ie,Ve)=>{const ge=ne(pe)?{capture:!!Ve.capture,passive:!!Ve.passive}:!!Ve.capture;let se,me;return pe.__zone_symbol__addEventListener?(se="__zone_symbol__addEventListener",me="__zone_symbol__removeEventListener"):(se="addEventListener",me="removeEventListener"),pe[se](ue,ie,ge),()=>{pe[me](ue,ie,ge)}},ne=pe=>{if(void 0===X)try{const ue=Object.defineProperty({},"passive",{get:()=>{X=!0}});pe.addEventListener("optsTest",()=>{},ue)}catch{X=!1}return!!X};let X;const L=pe=>pe instanceof Document?pe:pe.ownerDocument,je=pe=>{let ue=!1,ie=!1,Ve=!0,ge=!1;const se=Object.assign({disableScroll:!1,direction:"x",gesturePriority:0,passive:!0,maxAngle:40,threshold:10},pe),me=se.canStart,Ze=se.onWillStart,Ee=se.onStart,Ce=se.onEnd,ke=se.notCaptured,Ge=se.onMove,V=se.threshold,A=se.passive,Y=se.blurOnStart,Z={type:"pan",startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,event:void 0,data:void 0},de=((pe,ue,ie)=>{const Ve=ie*(Math.PI/180),ge="x"===pe,se=Math.cos(Ve),me=ue*ue;let Ze=0,Ee=0,Ce=!1,ke=0;return{start(Ge,V){Ze=Ge,Ee=V,ke=0,Ce=!0},detect(Ge,V){if(!Ce)return!1;const A=Ge-Ze,Y=V-Ee,Z=A*A+Y*Y;if(Z<me)return!1;const de=Math.sqrt(Z),_e=(ge?A:Y)/de;return ke=_e>se?1:_e<-se?-1:0,Ce=!1,!0},isGesture:()=>0!==ke,getDirection:()=>ke}})(se.direction,se.threshold,se.maxAngle),_e=i.G.createGesture({name:pe.gestureName,priority:pe.gesturePriority,disableScroll:pe.disableScroll}),pt=()=>{ue&&(ge=!1,Ge&&Ge(Z))},Rt=()=>!!_e.capture()&&(ue=!0,Ve=!1,Z.startX=Z.currentX,Z.startY=Z.currentY,Z.startTime=Z.currentTime,Ze?Ze(Z).then(at):at(),!0),at=()=>{Y&&(()=>{if(typeof document<"u"){const $=document.activeElement;$?.blur&&$.blur()}})(),Ee&&Ee(Z),Ve=!0},Ue=()=>{ue=!1,ie=!1,ge=!1,Ve=!0,_e.release()},H=$=>{const Oe=ue,Le=Ve;if(Ue(),Le){if(ye(Z,$),Oe)return void(Ce&&Ce(Z));ke&&ke(Z)}},q=((pe,ue,ie,Ve,ge)=>{let se,me,Ze,Ee,Ce,ke,Ge,V=0;const A=ze=>{V=Date.now()+2e3,ue(ze)&&(!me&&ie&&(me=C(pe,"touchmove",ie,ge)),Ze||(Ze=C(ze.target,"touchend",Z,ge)),Ee||(Ee=C(ze.target,"touchcancel",Z,ge)))},Y=ze=>{V>Date.now()||ue(ze)&&(!ke&&ie&&(ke=C(L(pe),"mousemove",ie,ge)),Ge||(Ge=C(L(pe),"mouseup",de,ge)))},Z=ze=>{_e(),Ve&&Ve(ze)},de=ze=>{Re(),Ve&&Ve(ze)},_e=()=>{me&&me(),Ze&&Ze(),Ee&&Ee(),me=Ze=Ee=void 0},Re=()=>{ke&&ke(),Ge&&Ge(),ke=Ge=void 0},Ke=()=>{_e(),Re()},pt=(ze=!0)=>{ze?(se||(se=C(pe,"touchstart",A,ge)),Ce||(Ce=C(pe,"mousedown",Y,ge))):(se&&se(),Ce&&Ce(),se=Ce=void 0,Ke())};return{enable:pt,stop:Ke,destroy:()=>{pt(!1),Ve=ie=ue=void 0}}})(se.el,$=>{const Oe=De($);return!(ie||!Ve||(te($,Z),Z.startX=Z.currentX,Z.startY=Z.currentY,Z.startTime=Z.currentTime=Oe,Z.velocityX=Z.velocityY=Z.deltaX=Z.deltaY=0,Z.event=$,me&&!1===me(Z))||(_e.release(),!_e.start()))&&(ie=!0,0===V?Rt():(de.start(Z.startX,Z.startY),!0))},$=>{ue?!ge&&Ve&&(ge=!0,ye(Z,$),requestAnimationFrame(pt)):(ye(Z,$),de.detect(Z.currentX,Z.currentY)&&(!de.isGesture()||!Rt())&&ae())},H,{capture:!1,passive:A}),ae=()=>{Ue(),q.stop(),ke&&ke(Z)};return{enable($=!0){$||(ue&&H(void 0),Ue()),q.enable($)},destroy(){_e.destroy(),q.destroy()}}},ye=(pe,ue)=>{if(!ue)return;const ie=pe.currentX,Ve=pe.currentY,ge=pe.currentTime;te(ue,pe);const se=pe.currentX,me=pe.currentY,Ee=(pe.currentTime=De(ue))-ge;if(Ee>0&&Ee<100){const ke=(me-Ve)/Ee;pe.velocityX=(se-ie)/Ee*.7+.3*pe.velocityX,pe.velocityY=.7*ke+.3*pe.velocityY}pe.deltaX=se-pe.startX,pe.deltaY=me-pe.startY,pe.event=ue},te=(pe,ue)=>{let ie=0,Ve=0;if(pe){const ge=pe.changedTouches;if(ge&&ge.length>0){const se=ge[0];ie=se.clientX,Ve=se.clientY}else void 0!==pe.pageX&&(ie=pe.pageX,Ve=pe.pageY)}ue.currentX=ie,ue.currentY=Ve},De=pe=>pe.timeStamp||Date.now()},5384:(Ct,We,O)=>{"use strict";O.d(We,{m:()=>pe});var i=O(467),C=O(8476),ne=O(3113),X=O(4929),Me=O(5638),Ie=O(611),L=O(3503);const Pe=ue=>(0,L.c)().duration(ue?400:300),je=ue=>{let ie,Ve;const ge=ue.width+8,se=(0,L.c)(),me=(0,L.c)();ue.isEndSide?(ie=ge+"px",Ve="0px"):(ie=-ge+"px",Ve="0px"),se.addElement(ue.menuInnerEl).fromTo("transform",`translateX(${ie})`,`translateX(${Ve})`);const Ee="ios"===(0,Ie.b)(ue),Ce=Ee?.2:.25;return me.addElement(ue.backdropEl).fromTo("opacity",.01,Ce),Pe(Ee).addAnimation([se,me])},ye=ue=>{let ie,Ve;const ge=(0,Ie.b)(ue),se=ue.width;ue.isEndSide?(ie=-se+"px",Ve=se+"px"):(ie=se+"px",Ve=-se+"px");const me=(0,L.c)().addElement(ue.menuInnerEl).fromTo("transform",`translateX(${Ve})`,"translateX(0px)"),Ze=(0,L.c)().addElement(ue.contentEl).fromTo("transform","translateX(0px)",`translateX(${ie})`),Ee=(0,L.c)().addElement(ue.backdropEl).fromTo("opacity",.01,.32);return Pe("ios"===ge).addAnimation([me,Ze,Ee])},te=ue=>{const ie=(0,Ie.b)(ue),Ve=ue.width*(ue.isEndSide?-1:1)+"px",ge=(0,L.c)().addElement(ue.contentEl).fromTo("transform","translateX(0px)",`translateX(${Ve})`);return Pe("ios"===ie).addAnimation(ge)},pe=(()=>{const ue=new Map,ie=[],Ve=function(){var Ue=(0,i.A)(function*(H){const q=yield ke(H,!0);return!!q&&q.open()});return function(q){return Ue.apply(this,arguments)}}(),ge=function(){var Ue=(0,i.A)(function*(H){const q=yield void 0!==H?ke(H,!0):Ge();return void 0!==q&&q.close()});return function(q){return Ue.apply(this,arguments)}}(),se=function(){var Ue=(0,i.A)(function*(H){const q=yield ke(H,!0);return!!q&&q.toggle()});return function(q){return Ue.apply(this,arguments)}}(),me=function(){var Ue=(0,i.A)(function*(H,q){const ae=yield ke(q);return ae&&(ae.disabled=!H),ae});return function(q,ae){return Ue.apply(this,arguments)}}(),Ze=function(){var Ue=(0,i.A)(function*(H,q){const ae=yield ke(q);return ae&&(ae.swipeGesture=H),ae});return function(q,ae){return Ue.apply(this,arguments)}}(),Ee=function(){var Ue=(0,i.A)(function*(H){if(null!=H){const q=yield ke(H);return void 0!==q&&q.isOpen()}return void 0!==(yield Ge())});return function(q){return Ue.apply(this,arguments)}}(),Ce=function(){var Ue=(0,i.A)(function*(H){const q=yield ke(H);return!!q&&!q.disabled});return function(q){return Ue.apply(this,arguments)}}(),ke=function(){var Ue=(0,i.A)(function*(H,q=!1){if(yield at(),"start"===H||"end"===H){const $=ie.filter(Le=>Le.side===H&&!Le.disabled);if($.length>=1)return $.length>1&&q&&(0,X.p)(`menuController queried for a menu on the "${H}" side, but ${$.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`,$.map(Le=>Le.el)),$[0].el;const Oe=ie.filter(Le=>Le.side===H);if(Oe.length>=1)return Oe.length>1&&q&&(0,X.p)(`menuController queried for a menu on the "${H}" side, but ${Oe.length} menus were found. The first menu reference will be used. If this is not the behavior you want then pass the ID of the menu instead of its side.`,Oe.map(Le=>Le.el)),Oe[0].el}else if(null!=H)return ze($=>$.menuId===H);return ze($=>!$.disabled)||(ie.length>0?ie[0].el:void 0)});return function(q){return Ue.apply(this,arguments)}}(),Ge=function(){var Ue=(0,i.A)(function*(){return yield at(),Ke()});return function(){return Ue.apply(this,arguments)}}(),V=function(){var Ue=(0,i.A)(function*(){return yield at(),pt()});return function(){return Ue.apply(this,arguments)}}(),A=function(){var Ue=(0,i.A)(function*(){return yield at(),Rt()});return function(){return Ue.apply(this,arguments)}}(),Y=(Ue,H)=>{ue.set(Ue,H)},_e=function(){var Ue=(0,i.A)(function*(H,q,ae){if(Rt())return!1;if(q){const $=yield Ge();$&&H.el!==$&&(yield $.setOpen(!1,!1))}return H._setOpen(q,ae)});return function(q,ae,$){return Ue.apply(this,arguments)}}(),Ke=()=>ze(Ue=>Ue._isOpen),pt=()=>ie.map(Ue=>Ue.el),Rt=()=>ie.some(Ue=>Ue.isAnimating),ze=Ue=>{const H=ie.find(Ue);if(void 0!==H)return H.el},at=()=>Promise.all(Array.from(document.querySelectorAll("ion-menu")).map(Ue=>new Promise(H=>(0,Me.c)(Ue,H))));return Y("reveal",te),Y("push",ye),Y("overlay",je),null==C.d||C.d.addEventListener("ionBackButton",Ue=>{const H=Ke();H&&Ue.detail.register(ne.MENU_BACK_BUTTON_PRIORITY,()=>H.close())}),{registerAnimation:Y,get:ke,getMenus:V,getOpen:Ge,isEnabled:Ce,swipeGesture:Ze,isAnimating:A,isOpen:Ee,enable:me,toggle:se,close:ge,open:Ve,_getOpenSync:Ke,_createAnimation:(Ue,H)=>{const q=ue.get(Ue);if(!q)throw new Error("animation not registered");return q(H)},_register:Ue=>{ie.indexOf(Ue)<0&&ie.push(Ue)},_unregister:Ue=>{const H=ie.indexOf(Ue);H>-1&&ie.splice(H,1)},_setOpen:_e}})()},4929:(Ct,We,O)=>{"use strict";O.d(We,{a:()=>C,b:()=>ne,p:()=>i});const i=(X,...Me)=>console.warn(`[Ionic Warning]: ${X}`,...Me),C=(X,...Me)=>console.error(`[Ionic Error]: ${X}`,...Me),ne=(X,...Me)=>console.error(`<${X.tagName.toLowerCase()}> must be used inside ${Me.join(" or ")}.`)},4363:(Ct,We,O)=>{"use strict";O.d(We,{B:()=>De,H:()=>Rt,a:()=>ur,b:()=>Tr,c:()=>Le,d:()=>vt,e:()=>J,f:()=>Tt,g:()=>Xe,h:()=>Ke,i:()=>Et,j:()=>pe,r:()=>xr,w:()=>He});var i=O(467);let X,Me,Ie,L=!1,Pe=!1,je=!1,ye=!1,te=!1;const De={isDev:!1,isBrowser:!0,isServer:!1,isTesting:!1},pe=b=>{const x=new URL(b,tn.$resourcesUrl$);return x.origin!==Zn.location.origin?x.href:x.pathname},Ze="s-id",Ee="sty-id",Ge="slot-fb{display:contents}slot-fb[hidden]{display:none}",V="http://www.w3.org/1999/xlink",A={},_e=b=>"object"==(b=typeof b)||"function"===b;function Re(b){var x,G,Q;return null!==(Q=null===(G=null===(x=b.head)||void 0===x?void 0:x.querySelector('meta[name="csp-nonce"]'))||void 0===G?void 0:G.getAttribute("content"))&&void 0!==Q?Q:void 0}const Ke=(b,x,...G)=>{let Q=null,Fe=null,be=null,st=!1,nt=!1;const lt=[],ot=mt=>{for(let Dt=0;Dt<mt.length;Dt++)Q=mt[Dt],Array.isArray(Q)?ot(Q):null!=Q&&"boolean"!=typeof Q&&((st="function"!=typeof b&&!_e(Q))&&(Q=String(Q)),st&&nt?lt[lt.length-1].$text$+=Q:lt.push(st?pt(null,Q):Q),nt=st)};if(ot(G),x){x.key&&(Fe=x.key),x.name&&(be=x.name);{const mt=x.className||x.class;mt&&(x.class="object"!=typeof mt?mt:Object.keys(mt).filter(Dt=>mt[Dt]).join(" "))}}if("function"==typeof b)return b(null===x?{}:x,lt,at);const Qe=pt(b,null);return Qe.$attrs$=x,lt.length>0&&(Qe.$children$=lt),Qe.$key$=Fe,Qe.$name$=be,Qe},pt=(b,x)=>({$flags$:0,$tag$:b,$text$:x,$elm$:null,$children$:null,$attrs$:null,$key$:null,$name$:null}),Rt={},at={forEach:(b,x)=>b.map(Ue).forEach(x),map:(b,x)=>b.map(Ue).map(x).map(H)},Ue=b=>({vattrs:b.$attrs$,vchildren:b.$children$,vkey:b.$key$,vname:b.$name$,vtag:b.$tag$,vtext:b.$text$}),H=b=>{if("function"==typeof b.vtag){const G=Object.assign({},b.vattrs);return b.vkey&&(G.key=b.vkey),b.vname&&(G.name=b.vname),Ke(b.vtag,G,...b.vchildren||[])}const x=pt(b.vtag,b.vtext);return x.$attrs$=b.vattrs,x.$children$=b.vchildren,x.$key$=b.vkey,x.$name$=b.vname,x},ae=(b,x,G,Q,Fe,be,st)=>{let nt,lt,ot,Qe;if(1===be.nodeType){for(nt=be.getAttribute("c-id"),nt&&(lt=nt.split("."),(lt[0]===st||"0"===lt[0])&&(ot={$flags$:0,$hostId$:lt[0],$nodeId$:lt[1],$depth$:lt[2],$index$:lt[3],$tag$:be.tagName.toLowerCase(),$elm$:be,$attrs$:null,$children$:null,$key$:null,$name$:null,$text$:null},x.push(ot),be.removeAttribute("c-id"),b.$children$||(b.$children$=[]),b.$children$[ot.$index$]=ot,b=ot,Q&&"0"===ot.$depth$&&(Q[ot.$index$]=ot.$elm$))),Qe=be.childNodes.length-1;Qe>=0;Qe--)ae(b,x,G,Q,Fe,be.childNodes[Qe],st);if(be.shadowRoot)for(Qe=be.shadowRoot.childNodes.length-1;Qe>=0;Qe--)ae(b,x,G,Q,Fe,be.shadowRoot.childNodes[Qe],st)}else if(8===be.nodeType)lt=be.nodeValue.split("."),(lt[1]===st||"0"===lt[1])&&(nt=lt[0],ot={$flags$:0,$hostId$:lt[1],$nodeId$:lt[2],$depth$:lt[3],$index$:lt[4],$elm$:be,$attrs$:null,$children$:null,$key$:null,$name$:null,$tag$:null,$text$:null},"t"===nt?(ot.$elm$=be.nextSibling,ot.$elm$&&3===ot.$elm$.nodeType&&(ot.$text$=ot.$elm$.textContent,x.push(ot),be.remove(),b.$children$||(b.$children$=[]),b.$children$[ot.$index$]=ot,Q&&"0"===ot.$depth$&&(Q[ot.$index$]=ot.$elm$))):ot.$hostId$===st&&("s"===nt?(ot.$tag$="slot",be["s-sn"]=lt[5]?ot.$name$=lt[5]:"",be["s-sr"]=!0,Q&&(ot.$elm$=Jt.createElement(ot.$tag$),ot.$name$&&ot.$elm$.setAttribute("name",ot.$name$),be.parentNode.insertBefore(ot.$elm$,be),be.remove(),"0"===ot.$depth$&&(Q[ot.$index$]=ot.$elm$)),G.push(ot),b.$children$||(b.$children$=[]),b.$children$[ot.$index$]=ot):"r"===nt&&(Q?be.remove():(Fe["s-cr"]=be,be["s-cn"]=!0))));else if(b&&"style"===b.$tag$){const mt=pt(null,be.textContent);mt.$elm$=be,mt.$index$="0",b.$children$=[mt]}},$=(b,x)=>{if(1===b.nodeType){let G=0;for(;G<b.childNodes.length;G++)$(b.childNodes[G],x);if(b.shadowRoot)for(G=0;G<b.shadowRoot.childNodes.length;G++)$(b.shadowRoot.childNodes[G],x)}else if(8===b.nodeType){const G=b.nodeValue.split(".");"o"===G[0]&&(x.set(G[1]+"."+G[2],b),b.nodeValue="",b["s-en"]=G[3])}},Le=b=>Bt.push(b),Xe=b=>pn(b).$modeName$,Tt=b=>pn(b).$hostElement$,vt=(b,x,G)=>{const Q=Tt(b);return{emit:Fe=>xt(Q,x,{bubbles:!!(4&G),composed:!!(2&G),cancelable:!!(1&G),detail:Fe})}},xt=(b,x,G)=>{const Q=tn.ce(x,G);return b.dispatchEvent(Q),Q},Pt=new WeakMap,Ft=(b,x,G)=>{let Q=Er.get(b);B&&G?(Q=Q||new CSSStyleSheet,"string"==typeof Q?Q=x:Q.replaceSync(x)):Q=x,Er.set(b,Q)},Be=(b,x,G)=>{var Q;const Fe=he(x,G),be=Er.get(Fe);if(b=11===b.nodeType?b:Jt,be)if("string"==typeof be){let nt,st=Pt.get(b=b.head||b);if(st||Pt.set(b,st=new Set),!st.has(Fe)){if(b.host&&(nt=b.querySelector(`[${Ee}="${Fe}"]`)))nt.innerHTML=be;else{nt=Jt.createElement("style"),nt.innerHTML=be;const lt=null!==(Q=tn.$nonce$)&&void 0!==Q?Q:Re(Jt);null!=lt&&nt.setAttribute("nonce",lt),b.insertBefore(nt,b.querySelector("link"))}4&x.$flags$&&(nt.innerHTML+=Ge),st&&st.add(Fe)}}else b.adoptedStyleSheets.includes(be)||(b.adoptedStyleSheets=[...b.adoptedStyleSheets,be]);return Fe},he=(b,x)=>"sc-"+(x&&32&b.$flags$?b.$tagName$+"-"+x:b.$tagName$),Ae=b=>b.replace(/\/\*!@([^\/]+)\*\/[^\{]+\{/g,"$1{"),ht=(b,x,G,Q,Fe,be)=>{if(G!==Q){let st=Xn(b,x),nt=x.toLowerCase();if("class"===x){const lt=b.classList,ot=Ht(G),Qe=Ht(Q);lt.remove(...ot.filter(mt=>mt&&!Qe.includes(mt))),lt.add(...Qe.filter(mt=>mt&&!ot.includes(mt)))}else if("style"===x){for(const lt in G)(!Q||null==Q[lt])&&(lt.includes("-")?b.style.removeProperty(lt):b.style[lt]="");for(const lt in Q)(!G||Q[lt]!==G[lt])&&(lt.includes("-")?b.style.setProperty(lt,Q[lt]):b.style[lt]=Q[lt])}else if("key"!==x)if("ref"===x)Q&&Q(b);else if(st||"o"!==x[0]||"n"!==x[1]){const lt=_e(Q);if((st||lt&&null!==Q)&&!Fe)try{if(b.tagName.includes("-"))b[x]=Q;else{const Qe=Q??"";"list"===x?st=!1:(null==G||b[x]!=Qe)&&(b[x]=Qe)}}catch{}let ot=!1;nt!==(nt=nt.replace(/^xlink\:?/,""))&&(x=nt,ot=!0),null==Q||!1===Q?(!1!==Q||""===b.getAttribute(x))&&(ot?b.removeAttributeNS(V,x):b.removeAttribute(x)):(!st||4&be||Fe)&&!lt&&(Q=!0===Q?"":Q,ot?b.setAttributeNS(V,x,Q):b.setAttribute(x,Q))}else if(x="-"===x[2]?x.slice(3):Xn(Zn,nt)?nt.slice(2):nt[2]+x.slice(3),G||Q){const lt=x.endsWith(on);x=x.replace($t,""),G&&tn.rel(b,x,G,lt),Q&&tn.ael(b,x,Q,lt)}}},Lt=/\s/,Ht=b=>b?b.split(Lt):[],on="Capture",$t=new RegExp(on+"$"),Wt=(b,x,G,Q)=>{const Fe=11===x.$elm$.nodeType&&x.$elm$.host?x.$elm$.host:x.$elm$,be=b&&b.$attrs$||A,st=x.$attrs$||A;for(Q of yt(Object.keys(be)))Q in st||ht(Fe,Q,be[Q],void 0,G,x.$flags$);for(Q of yt(Object.keys(st)))ht(Fe,Q,be[Q],st[Q],G,x.$flags$)};function yt(b){return b.includes("ref")?[...b.filter(x=>"ref"!==x),"ref"]:b}const At=(b,x,G,Q)=>{var Fe;const be=x.$children$[G];let nt,lt,ot,st=0;if(L||(je=!0,"slot"===be.$tag$&&(X&&Q.classList.add(X+"-s"),be.$flags$|=be.$children$?2:1)),null!==be.$text$)nt=be.$elm$=Jt.createTextNode(be.$text$);else if(1&be.$flags$)nt=be.$elm$=Jt.createTextNode("");else{if(ye||(ye="svg"===be.$tag$),nt=be.$elm$=Jt.createElementNS(ye?"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",2&be.$flags$?"slot-fb":be.$tag$),ye&&"foreignObject"===be.$tag$&&(ye=!1),Wt(null,be,ye),(b=>null!=b)(X)&&nt["s-si"]!==X&&nt.classList.add(nt["s-si"]=X),be.$children$)for(st=0;st<be.$children$.length;++st)lt=At(b,be,st,nt),lt&&nt.appendChild(lt);"svg"===be.$tag$?ye=!1:"foreignObject"===nt.tagName&&(ye=!0)}return nt["s-hn"]=Ie,3&be.$flags$&&(nt["s-sr"]=!0,nt["s-cr"]=Me,nt["s-sn"]=be.$name$||"",nt["s-rf"]=null===(Fe=be.$attrs$)||void 0===Fe?void 0:Fe.ref,ot=b&&b.$children$&&b.$children$[G],ot&&ot.$tag$===be.$tag$&&b.$elm$&&dn(b.$elm$,!1)),nt},dn=(b,x)=>{tn.$flags$|=1;const G=Array.from(b.childNodes);for(let Q=G.length-1;Q>=0;Q--){const Fe=G[Q];Fe["s-hn"]!==Ie&&Fe["s-ol"]&&(Nt(Fe).insertBefore(Fe,yr(Fe)),Fe["s-ol"].remove(),Fe["s-ol"]=void 0,Fe["s-sh"]=void 0,je=!0),x&&dn(Fe,x)}tn.$flags$&=-2},On=(b,x,G,Q,Fe,be)=>{let nt,st=b["s-cr"]&&b["s-cr"].parentNode||b;for(st.shadowRoot&&st.tagName===Ie&&(st=st.shadowRoot);Fe<=be;++Fe)Q[Fe]&&(nt=At(null,G,Fe,b),nt&&(Q[Fe].$elm$=nt,st.insertBefore(nt,yr(x))))},bn=(b,x,G)=>{for(let Q=x;Q<=G;++Q){const Fe=b[Q];if(Fe){const be=Fe.$elm$;$n(Fe),be&&(Pe=!0,be["s-ol"]?be["s-ol"].remove():dn(be,!0),be.remove())}}},hn=(b,x,G=!1)=>b.$tag$===x.$tag$&&("slot"===b.$tag$?b.$name$===x.$name$:!!G||b.$key$===x.$key$),yr=b=>b&&b["s-ol"]||b,Nt=b=>(b["s-ol"]?b["s-ol"]:b).parentNode,Ln=(b,x,G=!1)=>{const Q=x.$elm$=b.$elm$,Fe=b.$children$,be=x.$children$,st=x.$tag$,nt=x.$text$;let lt;null===nt?(ye="svg"===st||"foreignObject"!==st&&ye,"slot"===st&&!L||Wt(b,x,ye),null!==Fe&&null!==be?((b,x,G,Q,Fe=!1)=>{let f,N,be=0,st=0,nt=0,lt=0,ot=x.length-1,Qe=x[0],mt=x[ot],Dt=Q.length-1,Ot=Q[0],mn=Q[Dt];for(;be<=ot&&st<=Dt;)if(null==Qe)Qe=x[++be];else if(null==mt)mt=x[--ot];else if(null==Ot)Ot=Q[++st];else if(null==mn)mn=Q[--Dt];else if(hn(Qe,Ot,Fe))Ln(Qe,Ot,Fe),Qe=x[++be],Ot=Q[++st];else if(hn(mt,mn,Fe))Ln(mt,mn,Fe),mt=x[--ot],mn=Q[--Dt];else if(hn(Qe,mn,Fe))("slot"===Qe.$tag$||"slot"===mn.$tag$)&&dn(Qe.$elm$.parentNode,!1),Ln(Qe,mn,Fe),b.insertBefore(Qe.$elm$,mt.$elm$.nextSibling),Qe=x[++be],mn=Q[--Dt];else if(hn(mt,Ot,Fe))("slot"===Qe.$tag$||"slot"===mn.$tag$)&&dn(mt.$elm$.parentNode,!1),Ln(mt,Ot,Fe),b.insertBefore(mt.$elm$,Qe.$elm$),mt=x[--ot],Ot=Q[++st];else{for(nt=-1,lt=be;lt<=ot;++lt)if(x[lt]&&null!==x[lt].$key$&&x[lt].$key$===Ot.$key$){nt=lt;break}nt>=0?(N=x[nt],N.$tag$!==Ot.$tag$?f=At(x&&x[st],G,nt,b):(Ln(N,Ot,Fe),x[nt]=void 0,f=N.$elm$),Ot=Q[++st]):(f=At(x&&x[st],G,st,b),Ot=Q[++st]),f&&Nt(Qe.$elm$).insertBefore(f,yr(Qe.$elm$))}be>ot?On(b,null==Q[Dt+1]?null:Q[Dt+1].$elm$,G,Q,st,Dt):st>Dt&&bn(x,be,ot)})(Q,Fe,x,be,G):null!==be?(null!==b.$text$&&(Q.textContent=""),On(Q,null,x,be,0,be.length-1)):null!==Fe&&bn(Fe,0,Fe.length-1),ye&&"svg"===st&&(ye=!1)):(lt=Q["s-cr"])?lt.parentNode.textContent=nt:b.$text$!==nt&&(Q.data=nt)},Zt=b=>{const x=b.childNodes;for(const G of x)if(1===G.nodeType){if(G["s-sr"]){const Q=G["s-sn"];G.hidden=!1;for(const Fe of x)if(Fe!==G)if(Fe["s-hn"]!==G["s-hn"]||""!==Q){if(1===Fe.nodeType&&(Q===Fe.getAttribute("slot")||Q===Fe["s-sn"])){G.hidden=!0;break}}else if(1===Fe.nodeType||3===Fe.nodeType&&""!==Fe.textContent.trim()){G.hidden=!0;break}}Zt(G)}},Vt=[],Wn=b=>{let x,G,Q;for(const Fe of b.childNodes){if(Fe["s-sr"]&&(x=Fe["s-cr"])&&x.parentNode){G=x.parentNode.childNodes;const be=Fe["s-sn"];for(Q=G.length-1;Q>=0;Q--)if(x=G[Q],!x["s-cn"]&&!x["s-nr"]&&x["s-hn"]!==Fe["s-hn"])if(Mt(x,be)){let st=Vt.find(nt=>nt.$nodeToRelocate$===x);Pe=!0,x["s-sn"]=x["s-sn"]||be,st?(st.$nodeToRelocate$["s-sh"]=Fe["s-hn"],st.$slotRefNode$=Fe):(x["s-sh"]=Fe["s-hn"],Vt.push({$slotRefNode$:Fe,$nodeToRelocate$:x})),x["s-sr"]&&Vt.map(nt=>{Mt(nt.$nodeToRelocate$,x["s-sn"])&&(st=Vt.find(lt=>lt.$nodeToRelocate$===x),st&&!nt.$slotRefNode$&&(nt.$slotRefNode$=st.$slotRefNode$))})}else Vt.some(st=>st.$nodeToRelocate$===x)||Vt.push({$nodeToRelocate$:x})}1===Fe.nodeType&&Wn(Fe)}},Mt=(b,x)=>1===b.nodeType?null===b.getAttribute("slot")&&""===x||b.getAttribute("slot")===x:b["s-sn"]===x||""===x,$n=b=>{b.$attrs$&&b.$attrs$.ref&&b.$attrs$.ref(null),b.$children$&&b.$children$.map($n)},tr=(b,x)=>{x&&!b.$onRenderResolve$&&x["s-p"]&&x["s-p"].push(new Promise(G=>b.$onRenderResolve$=G))},nr=(b,x)=>{if(b.$flags$|=16,!(4&b.$flags$))return tr(b,b.$ancestorComponent$),He(()=>le(b,x));b.$flags$|=512},le=(b,x)=>{const Q=b.$lazyInstance$;let Fe;return x&&(b.$flags$|=256,b.$queuedListeners$&&(b.$queuedListeners$.map(([be,st])=>kt(Q,be,st)),b.$queuedListeners$=void 0),Fe=kt(Q,"componentWillLoad")),Fe=Se(Fe,()=>kt(Q,"componentWillRender")),Se(Fe,()=>ve(b,Q,x))},Se=(b,x)=>z(b)?b.then(x):x(),z=b=>b instanceof Promise||b&&b.then&&"function"==typeof b.then,ve=function(){var b=(0,i.A)(function*(x,G,Q){var Fe;const be=x.$hostElement$,nt=be["s-rc"];Q&&(b=>{const x=b.$cmpMeta$,G=b.$hostElement$,Q=x.$flags$,be=Be(G.shadowRoot?G.shadowRoot:G.getRootNode(),x,b.$modeName$);10&Q&&(G["s-sc"]=be,G.classList.add(be+"-h"),2&Q&&G.classList.add(be+"-s"))})(x);et(x,G,be,Q),nt&&(nt.map(ot=>ot()),be["s-rc"]=void 0);{const ot=null!==(Fe=be["s-p"])&&void 0!==Fe?Fe:[],Qe=()=>gt(x);0===ot.length?Qe():(Promise.all(ot).then(Qe),x.$flags$|=4,ot.length=0)}});return function(G,Q,Fe){return b.apply(this,arguments)}}(),et=(b,x,G,Q)=>{try{x=x.render&&x.render(),b.$flags$&=-17,b.$flags$|=2,((b,x,G=!1)=>{var Q,Fe,be,st;const nt=b.$hostElement$,lt=b.$cmpMeta$,ot=b.$vnode$||pt(null,null),Qe=(b=>b&&b.$tag$===Rt)(x)?x:Ke(null,null,x);if(Ie=nt.tagName,lt.$attrsToReflect$&&(Qe.$attrs$=Qe.$attrs$||{},lt.$attrsToReflect$.map(([mt,Dt])=>Qe.$attrs$[Dt]=nt[mt])),G&&Qe.$attrs$)for(const mt of Object.keys(Qe.$attrs$))nt.hasAttribute(mt)&&!["key","ref","style","class"].includes(mt)&&(Qe.$attrs$[mt]=nt[mt]);if(Qe.$tag$=null,Qe.$flags$|=4,b.$vnode$=Qe,Qe.$elm$=ot.$elm$=nt.shadowRoot||nt,X=nt["s-sc"],L=!!(1&lt.$flags$),Me=nt["s-cr"],Pe=!1,Ln(ot,Qe,G),tn.$flags$|=1,je){Wn(Qe.$elm$);for(const mt of Vt){const Dt=mt.$nodeToRelocate$;if(!Dt["s-ol"]){const Ot=Jt.createTextNode("");Ot["s-nr"]=Dt,Dt.parentNode.insertBefore(Dt["s-ol"]=Ot,Dt)}}for(const mt of Vt){const Dt=mt.$nodeToRelocate$,Ot=mt.$slotRefNode$;if(Ot){const mn=Ot.parentNode;let f=Ot.nextSibling;{let N=null===(Q=Dt["s-ol"])||void 0===Q?void 0:Q.previousSibling;for(;N;){let m=null!==(Fe=N["s-nr"])&&void 0!==Fe?Fe:null;if(m&&m["s-sn"]===Dt["s-sn"]&&mn===m.parentNode){for(m=m.nextSibling;m===Dt||m?.["s-sr"];)m=m?.nextSibling;if(!m||!m["s-nr"]){f=m;break}}N=N.previousSibling}}(!f&&mn!==Dt.parentNode||Dt.nextSibling!==f)&&Dt!==f&&(!Dt["s-hn"]&&Dt["s-ol"]&&(Dt["s-hn"]=Dt["s-ol"].parentNode.nodeName),mn.insertBefore(Dt,f),1===Dt.nodeType&&(Dt.hidden=null!==(be=Dt["s-ih"])&&void 0!==be&&be)),Dt&&"function"==typeof Ot["s-rf"]&&Ot["s-rf"](Dt)}else 1===Dt.nodeType&&(G&&(Dt["s-ih"]=null!==(st=Dt.hidden)&&void 0!==st&&st),Dt.hidden=!0)}}Pe&&Zt(Qe.$elm$),tn.$flags$&=-2,Vt.length=0,Me=void 0})(b,x,Q)}catch(Fe){Bn(Fe,b.$hostElement$)}return null},gt=b=>{const G=b.$hostElement$,Fe=b.$lazyInstance$,be=b.$ancestorComponent$;kt(Fe,"componentDidRender"),64&b.$flags$?kt(Fe,"componentDidUpdate"):(b.$flags$|=64,ln(G),kt(Fe,"componentDidLoad"),b.$onReadyResolve$(G),be||Qt()),b.$onInstanceResolve$(G),b.$onRenderResolve$&&(b.$onRenderResolve$(),b.$onRenderResolve$=void 0),512&b.$flags$&&rt(()=>nr(b,!1)),b.$flags$&=-517},Et=b=>{{const x=pn(b),G=x.$hostElement$.isConnected;return G&&2==(18&x.$flags$)&&nr(x,!1),G}},Qt=b=>{ln(Jt.documentElement),rt(()=>xt(Zn,"appload",{detail:{namespace:"ionic"}}))},kt=(b,x,G)=>{if(b&&b[x])try{return b[x](G)}catch(Q){Bn(Q)}},ln=b=>b.classList.add("hydrated"),rr=(b,x,G)=>{var Q;const Fe=b.prototype;if(x.$members$){b.watchers&&(x.$watchers$=b.watchers);const be=Object.entries(x.$members$);if(be.map(([st,[nt]])=>{31&nt||2&G&&32&nt?Object.defineProperty(Fe,st,{get(){return((b,x)=>pn(this).$instanceValues$.get(x))(0,st)},set(lt){((b,x,G,Q)=>{const Fe=pn(b),be=Fe.$hostElement$,st=Fe.$instanceValues$.get(x),nt=Fe.$flags$,lt=Fe.$lazyInstance$;G=((b,x)=>null==b||_e(b)?b:4&x?"false"!==b&&(""===b||!!b):2&x?parseFloat(b):1&x?String(b):b)(G,Q.$members$[x][0]);const ot=Number.isNaN(st)&&Number.isNaN(G);if((!(8&nt)||void 0===st)&&G!==st&&!ot&&(Fe.$instanceValues$.set(x,G),lt)){if(Q.$watchers$&&128&nt){const mt=Q.$watchers$[x];mt&&mt.map(Dt=>{try{lt[Dt](G,st,x)}catch(Ot){Bn(Ot,be)}})}2==(18&nt)&&nr(Fe,!1)}})(this,st,lt,x)},configurable:!0,enumerable:!0}):1&G&&64&nt&&Object.defineProperty(Fe,st,{value(...lt){var ot;const Qe=pn(this);return null===(ot=Qe?.$onInstancePromise$)||void 0===ot?void 0:ot.then(()=>{var mt;return null===(mt=Qe.$lazyInstance$)||void 0===mt?void 0:mt[st](...lt)})}})}),1&G){const st=new Map;Fe.attributeChangedCallback=function(nt,lt,ot){tn.jmp(()=>{var Qe;const mt=st.get(nt);if(this.hasOwnProperty(mt))ot=this[mt],delete this[mt];else{if(Fe.hasOwnProperty(mt)&&"number"==typeof this[mt]&&this[mt]==ot)return;if(null==mt){const Dt=pn(this),Ot=Dt?.$flags$;if(Ot&&!(8&Ot)&&128&Ot&&ot!==lt){const mn=Dt.$lazyInstance$,f=null===(Qe=x.$watchers$)||void 0===Qe?void 0:Qe[nt];f?.forEach(N=>{null!=mn[N]&&mn[N].call(mn,ot,lt,nt)})}return}}this[mt]=(null!==ot||"boolean"!=typeof this[mt])&&ot})},b.observedAttributes=Array.from(new Set([...Object.keys(null!==(Q=x.$watchers$)&&void 0!==Q?Q:{}),...be.filter(([nt,lt])=>15&lt[0]).map(([nt,lt])=>{var ot;const Qe=lt[1]||nt;return st.set(Qe,nt),512&lt[0]&&(null===(ot=x.$attrsToReflect$)||void 0===ot||ot.push([nt,Qe])),Qe})]))}}return b},zr=function(){var b=(0,i.A)(function*(x,G,Q,Fe){let be;if(!(32&G.$flags$)){if(G.$flags$|=32,Q.$lazyBundleId$){if(be=_n(Q),be.then){const Qe=()=>{};be=yield be,Qe()}be.isProxied||(Q.$watchers$=be.watchers,rr(be,Q,2),be.isProxied=!0);const ot=()=>{};G.$flags$|=8;try{new be(G)}catch(Qe){Bn(Qe)}G.$flags$&=-9,G.$flags$|=128,ot(),Wr(G.$lazyInstance$)}else be=x.constructor,customElements.whenDefined(Q.$tagName$).then(()=>G.$flags$|=128);if(be.style){let ot=be.style;"string"!=typeof ot&&(ot=ot[G.$modeName$=(b=>Bt.map(x=>x(b)).find(x=>!!x))(x)]);const Qe=he(Q,G.$modeName$);if(!Er.has(Qe)){const mt=()=>{};Ft(Qe,ot,!!(1&Q.$flags$)),mt()}}}const st=G.$ancestorComponent$,nt=()=>nr(G,!0);st&&st["s-rc"]?st["s-rc"].push(nt):nt()});return function(G,Q,Fe,be){return b.apply(this,arguments)}}(),Wr=b=>{kt(b,"connectedCallback")},_t=b=>{const x=b["s-cr"]=Jt.createComment("");x["s-cn"]=!0,b.insertBefore(x,b.firstChild)},eo=b=>{kt(b,"disconnectedCallback")},cr=function(){var b=(0,i.A)(function*(x){if(!(1&tn.$flags$)){const G=pn(x);G.$rmListeners$&&(G.$rmListeners$.map(Q=>Q()),G.$rmListeners$=void 0),G?.$lazyInstance$?eo(G.$lazyInstance$):G?.$onReadyPromise$&&G.$onReadyPromise$.then(()=>eo(G.$lazyInstance$))}});return function(G){return b.apply(this,arguments)}}(),Tr=(b,x={})=>{var G;const Fe=[],be=x.exclude||[],st=Zn.customElements,nt=Jt.head,lt=nt.querySelector("meta[charset]"),ot=Jt.createElement("style"),Qe=[],mt=Jt.querySelectorAll(`[${Ee}]`);let Dt,Ot=!0,mn=0;for(Object.assign(tn,x),tn.$resourcesUrl$=new URL(x.resourcesUrl||"./",Jt.baseURI).href,tn.$flags$|=2;mn<mt.length;mn++)Ft(mt[mn].getAttribute(Ee),Ae(mt[mn].innerHTML),!0);let f=!1;if(b.map(N=>{N[1].map(m=>{var F;const oe={$flags$:m[0],$tagName$:m[1],$members$:m[2],$listeners$:m[3]};4&oe.$flags$&&(f=!0),oe.$members$=m[2],oe.$listeners$=m[3],oe.$attrsToReflect$=[],oe.$watchers$=null!==(F=m[4])&&void 0!==F?F:{};const Xt=oe.$tagName$,Kn=class extends HTMLElement{constructor(xn){super(xn),Nr(xn=this,oe),1&oe.$flags$&&xn.attachShadow({mode:"open",delegatesFocus:!!(16&oe.$flags$)})}connectedCallback(){Dt&&(clearTimeout(Dt),Dt=null),Ot?Qe.push(this):tn.jmp(()=>(b=>{if(!(1&tn.$flags$)){const x=pn(b),G=x.$cmpMeta$,Q=()=>{};if(1&x.$flags$)Cr(b,x,G.$listeners$),x?.$lazyInstance$?Wr(x.$lazyInstance$):x?.$onReadyPromise$&&x.$onReadyPromise$.then(()=>Wr(x.$lazyInstance$));else{let Fe;if(x.$flags$|=1,Fe=b.getAttribute(Ze),Fe){if(1&G.$flags$){const be=Be(b.shadowRoot,G,b.getAttribute("s-mode"));b.classList.remove(be+"-h",be+"-s")}((b,x,G,Q)=>{const be=b.shadowRoot,st=[],lt=be?[]:null,ot=Q.$vnode$=pt(x,null);tn.$orgLocNodes$||$(Jt.body,tn.$orgLocNodes$=new Map),b[Ze]=G,b.removeAttribute(Ze),ae(ot,st,[],lt,b,b,G),st.map(Qe=>{const mt=Qe.$hostId$+"."+Qe.$nodeId$,Dt=tn.$orgLocNodes$.get(mt),Ot=Qe.$elm$;Dt&&br&&""===Dt["s-en"]&&Dt.parentNode.insertBefore(Ot,Dt.nextSibling),be||(Ot["s-hn"]=x,Dt&&(Ot["s-ol"]=Dt,Ot["s-ol"]["s-nr"]=Ot)),tn.$orgLocNodes$.delete(mt)}),be&&lt.map(Qe=>{Qe&&be.appendChild(Qe)})})(b,G.$tagName$,Fe,x)}Fe||12&G.$flags$&&_t(b);{let be=b;for(;be=be.parentNode||be.host;)if(1===be.nodeType&&be.hasAttribute("s-id")&&be["s-p"]||be["s-p"]){tr(x,x.$ancestorComponent$=be);break}}G.$members$&&Object.entries(G.$members$).map(([be,[st]])=>{if(31&st&&b.hasOwnProperty(be)){const nt=b[be];delete b[be],b[be]=nt}}),zr(b,x,G)}Q()}})(this))}disconnectedCallback(){tn.jmp(()=>cr(this))}componentOnReady(){return pn(this).$onReadyPromise$}};oe.$lazyBundleId$=N[0],!be.includes(Xt)&&!st.get(Xt)&&(Fe.push(Xt),st.define(Xt,rr(Kn,oe,1)))})}),Fe.length>0&&(f&&(ot.textContent+=Ge),ot.textContent+=Fe+"{visibility:hidden}.hydrated{visibility:inherit}",ot.innerHTML.length)){ot.setAttribute("data-styles","");const N=null!==(G=tn.$nonce$)&&void 0!==G?G:Re(Jt);null!=N&&ot.setAttribute("nonce",N),nt.insertBefore(ot,lt?lt.nextSibling:nt.firstChild)}Ot=!1,Qe.length?Qe.map(N=>N.connectedCallback()):tn.jmp(()=>Dt=setTimeout(Qt,30))},Cr=(b,x,G,Q)=>{G&&G.map(([Fe,be,st])=>{const nt=Yt(b,Fe),lt=Ar(x,st),ot=Rr(Fe);tn.ael(nt,be,lt,ot),(x.$rmListeners$=x.$rmListeners$||[]).push(()=>tn.rel(nt,be,lt,ot))})},Ar=(b,x)=>G=>{try{256&b.$flags$?b.$lazyInstance$[x](G):(b.$queuedListeners$=b.$queuedListeners$||[]).push([x,G])}catch(Q){Bn(Q)}},Yt=(b,x)=>4&x?Jt:8&x?Zn:16&x?Jt.body:b,Rr=b=>sr?{passive:!!(1&b),capture:!!(2&b)}:!!(2&b),Vn=new WeakMap,pn=b=>Vn.get(b),xr=(b,x)=>Vn.set(x.$lazyInstance$=b,x),Nr=(b,x)=>{const G={$flags$:0,$hostElement$:b,$cmpMeta$:x,$instanceValues$:new Map};return G.$onInstancePromise$=new Promise(Q=>G.$onInstanceResolve$=Q),G.$onReadyPromise$=new Promise(Q=>G.$onReadyResolve$=Q),b["s-p"]=[],b["s-rc"]=[],Cr(b,G,x.$listeners$),Vn.set(b,G)},Xn=(b,x)=>x in b,Bn=(b,x)=>(0,console.error)(b,x),Un=new Map,_n=(b,x,G)=>{const Q=b.$tagName$.replace(/-/g,"_"),Fe=b.$lazyBundleId$,be=Un.get(Fe);return be?be[Q]:O(8996)(`./${Fe}.entry.js`).then(st=>(Un.set(Fe,st),st[Q]),Bn)},Er=new Map,Bt=[],Zn=typeof window<"u"?window:{},Jt=Zn.document||{head:{}},tn={$flags$:0,$resourcesUrl$:"",jmp:b=>b(),raf:b=>requestAnimationFrame(b),ael:(b,x,G,Q)=>b.addEventListener(x,G,Q),rel:(b,x,G,Q)=>b.removeEventListener(x,G,Q),ce:(b,x)=>new CustomEvent(b,x)},ur=b=>{Object.assign(tn,b)},br=!0,sr=(()=>{let b=!1;try{Jt.addEventListener("e",null,Object.defineProperty({},"passive",{get(){b=!0}}))}catch{}return b})(),B=(()=>{try{return new CSSStyleSheet,"function"==typeof(new CSSStyleSheet).replaceSync}catch{}return!1})(),j=[],T=[],U=(b,x)=>G=>{b.push(G),te||(te=!0,x&&4&tn.$flags$?rt(qe):tn.raf(qe))},fe=b=>{for(let x=0;x<b.length;x++)try{b[x](performance.now())}catch(G){Bn(G)}b.length=0},qe=()=>{fe(j),fe(T),(te=j.length>0)&&tn.raf(qe)},rt=b=>Promise.resolve(void 0).then(b),J=U(j,!1),He=U(T,!0)},8476:(Ct,We,O)=>{"use strict";O.d(We,{d:()=>C,w:()=>i});const i=typeof window<"u"?window:void 0,C=typeof document<"u"?document:void 0},7555:(Ct,We,O)=>{"use strict";O.d(We,{b:()=>Ie,c:()=>L,d:()=>Pe,e:()=>V,g:()=>Z,l:()=>ke,s:()=>A,t:()=>te,w:()=>Ge});var i=O(467),C=O(4363),ne=O(5638);const Ie="ionViewWillLeave",L="ionViewDidLeave",Pe="ionViewWillUnload",te=de=>new Promise((_e,Re)=>{(0,C.w)(()=>{De(de),pe(de).then(Ke=>{Ke.animation&&Ke.animation.destroy(),ue(de),_e(Ke)},Ke=>{ue(de),Re(Ke)})})}),De=de=>{const _e=de.enteringEl,Re=de.leavingEl;Y(_e,Re,de.direction),de.showGoBack?_e.classList.add("can-go-back"):_e.classList.remove("can-go-back"),A(_e,!1),_e.style.setProperty("pointer-events","none"),Re&&(A(Re,!1),Re.style.setProperty("pointer-events","none"))},pe=function(){var de=(0,i.A)(function*(_e){const Re=yield ie(_e);return Re&&C.B.isBrowser?Ve(Re,_e):ge(_e)});return function(Re){return de.apply(this,arguments)}}(),ue=de=>{const _e=de.enteringEl,Re=de.leavingEl;_e.classList.remove("ion-page-invisible"),_e.style.removeProperty("pointer-events"),void 0!==Re&&(Re.classList.remove("ion-page-invisible"),Re.style.removeProperty("pointer-events"))},ie=function(){var de=(0,i.A)(function*(_e){return _e.leavingEl&&_e.animated&&0!==_e.duration?_e.animationBuilder?_e.animationBuilder:"ios"===_e.mode?(yield Promise.resolve().then(O.bind(O,4569))).iosTransitionAnimation:(yield Promise.resolve().then(O.bind(O,2942))).mdTransitionAnimation:void 0});return function(Re){return de.apply(this,arguments)}}(),Ve=function(){var de=(0,i.A)(function*(_e,Re){yield se(Re,!0);const Ke=_e(Re.baseEl,Re);Ee(Re.enteringEl,Re.leavingEl);const pt=yield Ze(Ke,Re);return Re.progressCallback&&Re.progressCallback(void 0),pt&&Ce(Re.enteringEl,Re.leavingEl),{hasCompleted:pt,animation:Ke}});return function(Re,Ke){return de.apply(this,arguments)}}(),ge=function(){var de=(0,i.A)(function*(_e){const Re=_e.enteringEl,Ke=_e.leavingEl;return yield se(_e,!1),Ee(Re,Ke),Ce(Re,Ke),{hasCompleted:!0}});return function(Re){return de.apply(this,arguments)}}(),se=function(){var de=(0,i.A)(function*(_e,Re){(void 0!==_e.deepWait?_e.deepWait:Re)&&(yield Promise.all([V(_e.enteringEl),V(_e.leavingEl)])),yield me(_e.viewIsReady,_e.enteringEl)});return function(Re,Ke){return de.apply(this,arguments)}}(),me=function(){var de=(0,i.A)(function*(_e,Re){_e&&(yield _e(Re))});return function(Re,Ke){return de.apply(this,arguments)}}(),Ze=(de,_e)=>{const Re=_e.progressCallback,Ke=new Promise(pt=>{de.onFinish(Rt=>pt(1===Rt))});return Re?(de.progressStart(!0),Re(de)):de.play(),Ke},Ee=(de,_e)=>{ke(_e,Ie),ke(de,"ionViewWillEnter")},Ce=(de,_e)=>{ke(de,"ionViewDidEnter"),ke(_e,L)},ke=(de,_e)=>{if(de){const Re=new CustomEvent(_e,{bubbles:!1,cancelable:!1});de.dispatchEvent(Re)}},Ge=()=>new Promise(de=>(0,ne.r)(()=>(0,ne.r)(()=>de()))),V=function(){var de=(0,i.A)(function*(_e){const Re=_e;if(Re){if(null!=Re.componentOnReady){if(null!=(yield Re.componentOnReady()))return}else if(null!=Re.__registerHost)return void(yield new Promise(pt=>(0,ne.r)(pt)));yield Promise.all(Array.from(Re.children).map(V))}});return function(Re){return de.apply(this,arguments)}}(),A=(de,_e)=>{_e?(de.setAttribute("aria-hidden","true"),de.classList.add("ion-page-hidden")):(de.hidden=!1,de.removeAttribute("aria-hidden"),de.classList.remove("ion-page-hidden"))},Y=(de,_e,Re)=>{void 0!==de&&(de.style.zIndex="back"===Re?"99":"101"),void 0!==_e&&(_e.style.zIndex="100")},Z=de=>de.classList.contains("ion-page")?de:de.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")||de},611:(Ct,We,O)=>{"use strict";O.d(We,{a:()=>te,b:()=>pt,c:()=>ne,i:()=>Rt});var i=O(4363);class C{constructor(){this.m=new Map}reset(at){this.m=new Map(Object.entries(at))}get(at,Ue){const H=this.m.get(at);return void 0!==H?H:Ue}getBoolean(at,Ue=!1){const H=this.m.get(at);return void 0===H?Ue:"string"==typeof H?"true"===H:!!H}getNumber(at,Ue){const H=parseFloat(this.m.get(at));return isNaN(H)?void 0!==Ue?Ue:NaN:H}set(at,Ue){this.m.set(at,Ue)}}const ne=new C,je="ionic-persist-config",te=(ze,at)=>("string"==typeof ze&&(at=ze,ze=void 0),(ze=>De(ze))(ze).includes(at)),De=(ze=window)=>{if(typeof ze>"u")return[];ze.Ionic=ze.Ionic||{};let at=ze.Ionic.platforms;return null==at&&(at=ze.Ionic.platforms=pe(ze),at.forEach(Ue=>ze.document.documentElement.classList.add(`plt-${Ue}`))),at},pe=ze=>{const at=ne.get("platform");return Object.keys(Re).filter(Ue=>{const H=at?.[Ue];return"function"==typeof H?H(ze):Re[Ue](ze)})},ie=ze=>!!(de(ze,/iPad/i)||de(ze,/Macintosh/i)&&Ce(ze)),se=ze=>de(ze,/android|sink/i),Ce=ze=>_e(ze,"(any-pointer:coarse)"),Ge=ze=>V(ze)||A(ze),V=ze=>!!(ze.cordova||ze.phonegap||ze.PhoneGap),A=ze=>{const at=ze.Capacitor;return!!at?.isNative},de=(ze,at)=>at.test(ze.navigator.userAgent),_e=(ze,at)=>{var Ue;return null===(Ue=ze.matchMedia)||void 0===Ue?void 0:Ue.call(ze,at).matches},Re={ipad:ie,iphone:ze=>de(ze,/iPhone/i),ios:ze=>de(ze,/iPhone|iPod/i)||ie(ze),android:se,phablet:ze=>{const at=ze.innerWidth,Ue=ze.innerHeight,H=Math.min(at,Ue),q=Math.max(at,Ue);return H>390&&H<520&&q>620&&q<800},tablet:ze=>{const at=ze.innerWidth,Ue=ze.innerHeight,H=Math.min(at,Ue),q=Math.max(at,Ue);return ie(ze)||(ze=>se(ze)&&!de(ze,/mobile/i))(ze)||H>460&&H<820&&q>780&&q<1400},cordova:V,capacitor:A,electron:ze=>de(ze,/electron/i),pwa:ze=>{var at;return!!(null!==(at=ze.matchMedia)&&void 0!==at&&at.call(ze,"(display-mode: standalone)").matches||ze.navigator.standalone)},mobile:Ce,mobileweb:ze=>Ce(ze)&&!Ge(ze),desktop:ze=>!Ce(ze),hybrid:Ge};let Ke;const pt=ze=>ze&&(0,i.g)(ze)||Ke,Rt=(ze={})=>{if(typeof window>"u")return;const at=window.document,Ue=window,H=Ue.Ionic=Ue.Ionic||{},q={};ze._ael&&(q.ael=ze._ael),ze._rel&&(q.rel=ze._rel),ze._ce&&(q.ce=ze._ce),(0,i.a)(q);const ae=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(ze=>{try{const at=ze.sessionStorage.getItem(je);return null!==at?JSON.parse(at):{}}catch{return{}}})(Ue)),{persistConfig:!1}),H.config),(ze=>{const at={};return ze.location.search.slice(1).split("&").map(Ue=>Ue.split("=")).map(([Ue,H])=>[decodeURIComponent(Ue),decodeURIComponent(H)]).filter(([Ue])=>((ze,at)=>ze.substr(0,at.length)===at)(Ue,"ionic:")).map(([Ue,H])=>[Ue.slice(6),H]).forEach(([Ue,H])=>{at[Ue]=H}),at})(Ue)),ze);ne.reset(ae),ne.getBoolean("persistConfig")&&((ze,at)=>{try{ze.sessionStorage.setItem(je,JSON.stringify(at))}catch{return}})(Ue,ae),De(Ue),H.config=ne,H.mode=Ke=ne.get("mode",at.documentElement.getAttribute("mode")||(te(Ue,"ios")?"ios":"md")),ne.set("mode",Ke),at.documentElement.setAttribute("mode",Ke),at.documentElement.classList.add(Ke),ne.getBoolean("_testing")&&ne.set("animated",!1);const $=Le=>{var Xe;return null===(Xe=Le.tagName)||void 0===Xe?void 0:Xe.startsWith("ION-")},Oe=Le=>["ios","md"].includes(Le);(0,i.c)(Le=>{for(;Le;){const Xe=Le.mode||Le.getAttribute("mode");if(Xe){if(Oe(Xe))return Xe;$(Le)&&console.warn('Invalid ionic mode: "'+Xe+'", expected: "ios" or "md"')}Le=Le.parentElement}return Ke})}},4569:(Ct,We,O)=>{"use strict";O.r(We),O.d(We,{iosTransitionAnimation:()=>pe,shadow:()=>L});var i=O(3503),C=O(7555);O(8476),O(4363);const Ie=ie=>document.querySelector(`${ie}.ion-cloned-element`),L=ie=>ie.shadowRoot||ie,Pe=ie=>{const Ve="ION-TABS"===ie.tagName?ie:ie.querySelector("ion-tabs"),ge="ion-content ion-header:not(.header-collapse-condense-inactive) ion-title.title-large";if(null!=Ve){const se=Ve.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");return null!=se?se.querySelector(ge):null}return ie.querySelector(ge)},je=(ie,Ve)=>{const ge="ION-TABS"===ie.tagName?ie:ie.querySelector("ion-tabs");let se=[];if(null!=ge){const me=ge.querySelector("ion-tab:not(.tab-hidden), .ion-page:not(.ion-page-hidden)");null!=me&&(se=me.querySelectorAll("ion-buttons"))}else se=ie.querySelectorAll("ion-buttons");for(const me of se){const Ze=me.closest("ion-header"),Ee=Ze&&!Ze.classList.contains("header-collapse-condense-inactive"),Ce=me.querySelector("ion-back-button"),ke=me.classList.contains("buttons-collapse");if(null!==Ce&&("start"===me.slot||""===me.slot)&&(ke&&Ee&&Ve||!ke))return Ce}return null},te=(ie,Ve,ge,se,me,Ze,Ee,Ce,ke)=>{var Ge,V;const A=Ve?`calc(100% - ${me.right+4}px)`:me.left-4+"px",Y=Ve?"right":"left",Z=Ve?"left":"right",de=Ve?"right":"left",_e=(null===(Ge=Ze.textContent)||void 0===Ge?void 0:Ge.trim())===(null===(V=Ce.textContent)||void 0===V?void 0:V.trim()),Ke=(ke.height-ue)/Ee.height,pt=_e?`scale(${ke.width/Ee.width}, ${Ke})`:`scale(${Ke})`,Rt="scale(1)",at=L(se).querySelector("ion-icon").getBoundingClientRect(),Ue=Ve?at.width/2-(at.right-me.right)+"px":me.left-at.width/2+"px",H=Ve?`-${window.innerWidth-me.right}px`:`${me.left}px`,q=`${ke.top}px`,ae=`${me.top}px`,Le=ge?[{offset:0,transform:`translate3d(${H}, ${ae}, 0)`},{offset:1,transform:`translate3d(${Ue}, ${q}, 0)`}]:[{offset:0,transform:`translate3d(${Ue}, ${q}, 0)`},{offset:1,transform:`translate3d(${H}, ${ae}, 0)`}],Tt=ge?[{offset:0,opacity:1,transform:Rt},{offset:1,opacity:0,transform:pt}]:[{offset:0,opacity:0,transform:pt},{offset:1,opacity:1,transform:Rt}],Pt=ge?[{offset:0,opacity:1,transform:"scale(1)"},{offset:.2,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:0,transform:"scale(0.6)"}]:[{offset:0,opacity:0,transform:"scale(0.6)"},{offset:.6,opacity:0,transform:"scale(0.6)"},{offset:1,opacity:1,transform:"scale(1)"}],Ft=(0,i.c)(),Be=(0,i.c)(),xe=(0,i.c)(),he=Ie("ion-back-button"),Ae=L(he).querySelector(".button-text"),ht=L(he).querySelector("ion-icon");he.text=se.text,he.mode=se.mode,he.icon=se.icon,he.color=se.color,he.disabled=se.disabled,he.style.setProperty("display","block"),he.style.setProperty("position","fixed"),Be.addElement(ht),Ft.addElement(Ae),xe.addElement(he),xe.beforeStyles({position:"absolute",top:"0px",[de]:"0px"}).keyframes(Le),Ft.beforeStyles({"transform-origin":`${Y} top`}).beforeAddWrite(()=>{se.style.setProperty("display","none"),he.style.setProperty(Y,A)}).afterAddWrite(()=>{se.style.setProperty("display",""),he.style.setProperty("display","none"),he.style.removeProperty(Y)}).keyframes(Tt),Be.beforeStyles({"transform-origin":`${Z} center`}).keyframes(Pt),ie.addAnimation([Ft,Be,xe])},De=(ie,Ve,ge,se,me,Ze,Ee,Ce)=>{var ke,Ge;const V=Ve?"right":"left",A=Ve?`calc(100% - ${me.right}px)`:`${me.left}px`,Z=`${me.top}px`,_e=Ve?`-${window.innerWidth-Ce.right-8}px`:Ce.x-8+"px",Ke=Ce.y-2+"px",pt=(null===(ke=Ee.textContent)||void 0===ke?void 0:ke.trim())===(null===(Ge=se.textContent)||void 0===Ge?void 0:Ge.trim()),ze=Ce.height/(Ze.height-ue),at="scale(1)",Ue=pt?`scale(${Ce.width/Ze.width}, ${ze})`:`scale(${ze})`,ae=ge?[{offset:0,opacity:0,transform:`translate3d(${_e}, ${Ke}, 0) ${Ue}`},{offset:.1,opacity:0},{offset:1,opacity:1,transform:`translate3d(0px, ${Z}, 0) ${at}`}]:[{offset:0,opacity:.99,transform:`translate3d(0px, ${Z}, 0) ${at}`},{offset:.6,opacity:0},{offset:1,opacity:0,transform:`translate3d(${_e}, ${Ke}, 0) ${Ue}`}],$=Ie("ion-title"),Oe=(0,i.c)();$.innerText=se.innerText,$.size=se.size,$.color=se.color,Oe.addElement($),Oe.beforeStyles({"transform-origin":`${V} top`,height:`${me.height}px`,display:"",position:"relative",[V]:A}).beforeAddWrite(()=>{se.style.setProperty("opacity","0")}).afterAddWrite(()=>{se.style.setProperty("opacity",""),$.style.setProperty("display","none")}).keyframes(ae),ie.addAnimation(Oe)},pe=(ie,Ve)=>{var ge;try{const se="cubic-bezier(0.32,0.72,0,1)",me="opacity",Ze="transform",Ee="0%",ke="rtl"===ie.ownerDocument.dir,Ge=ke?"-99.5%":"99.5%",V=ke?"33%":"-33%",A=Ve.enteringEl,Y=Ve.leavingEl,Z="back"===Ve.direction,de=A.querySelector(":scope > ion-content"),_e=A.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *"),Re=A.querySelectorAll(":scope > ion-header > ion-toolbar"),Ke=(0,i.c)(),pt=(0,i.c)();if(Ke.addElement(A).duration((null!==(ge=Ve.duration)&&void 0!==ge?ge:0)||540).easing(Ve.easing||se).fill("both").beforeRemoveClass("ion-page-invisible"),Y&&null!=ie){const Ue=(0,i.c)();Ue.addElement(ie),Ke.addAnimation(Ue)}if(de||0!==Re.length||0!==_e.length?(pt.addElement(de),pt.addElement(_e)):pt.addElement(A.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),Ke.addAnimation(pt),Z?pt.beforeClearStyles([me]).fromTo("transform",`translateX(${V})`,`translateX(${Ee})`).fromTo(me,.8,1):pt.beforeClearStyles([me]).fromTo("transform",`translateX(${Ge})`,`translateX(${Ee})`),de){const Ue=L(de).querySelector(".transition-effect");if(Ue){const H=Ue.querySelector(".transition-cover"),q=Ue.querySelector(".transition-shadow"),ae=(0,i.c)(),$=(0,i.c)(),Oe=(0,i.c)();ae.addElement(Ue).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),$.addElement(H).beforeClearStyles([me]).fromTo(me,0,.1),Oe.addElement(q).beforeClearStyles([me]).fromTo(me,.03,.7),ae.addAnimation([$,Oe]),pt.addAnimation([ae])}}const Rt=A.querySelector("ion-header.header-collapse-condense"),{forward:ze,backward:at}=((ie,Ve,ge,se,me)=>{const Ze=je(se,ge),Ee=Pe(me),Ce=Pe(se),ke=je(me,ge),Ge=null!==Ze&&null!==Ee&&!ge,V=null!==Ce&&null!==ke&&ge;if(Ge){const A=Ee.getBoundingClientRect(),Y=Ze.getBoundingClientRect(),Z=L(Ze).querySelector(".button-text"),de=Z.getBoundingClientRect(),Re=L(Ee).querySelector(".toolbar-title").getBoundingClientRect();De(ie,Ve,ge,Ee,A,Re,Z,de),te(ie,Ve,ge,Ze,Y,Z,de,Ee,Re)}else if(V){const A=Ce.getBoundingClientRect(),Y=ke.getBoundingClientRect(),Z=L(ke).querySelector(".button-text"),de=Z.getBoundingClientRect(),Re=L(Ce).querySelector(".toolbar-title").getBoundingClientRect();De(ie,Ve,ge,Ce,A,Re,Z,de),te(ie,Ve,ge,ke,Y,Z,de,Ce,Re)}return{forward:Ge,backward:V}})(Ke,ke,Z,A,Y);if(Re.forEach(Ue=>{const H=(0,i.c)();H.addElement(Ue),Ke.addAnimation(H);const q=(0,i.c)();q.addElement(Ue.querySelector("ion-title"));const ae=(0,i.c)(),$=Array.from(Ue.querySelectorAll("ion-buttons,[menuToggle]")),Oe=Ue.closest("ion-header"),Le=Oe?.classList.contains("header-collapse-condense-inactive");let Xe;Xe=$.filter(Z?Pt=>{const Ft=Pt.classList.contains("buttons-collapse");return Ft&&!Le||!Ft}:Pt=>!Pt.classList.contains("buttons-collapse")),ae.addElement(Xe);const It=(0,i.c)();It.addElement(Ue.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])"));const Tt=(0,i.c)();Tt.addElement(L(Ue).querySelector(".toolbar-background"));const vt=(0,i.c)(),xt=Ue.querySelector("ion-back-button");if(xt&&vt.addElement(xt),H.addAnimation([q,ae,It,Tt,vt]),ae.fromTo(me,.01,1),It.fromTo(me,.01,1),Z)Le||q.fromTo("transform",`translateX(${V})`,`translateX(${Ee})`).fromTo(me,.01,1),It.fromTo("transform",`translateX(${V})`,`translateX(${Ee})`),vt.fromTo(me,.01,1);else if(Rt||q.fromTo("transform",`translateX(${Ge})`,`translateX(${Ee})`).fromTo(me,.01,1),It.fromTo("transform",`translateX(${Ge})`,`translateX(${Ee})`),Tt.beforeClearStyles([me,"transform"]),Oe?.translucent?Tt.fromTo("transform",ke?"translateX(-100%)":"translateX(100%)","translateX(0px)"):Tt.fromTo(me,.01,"var(--opacity)"),ze||vt.fromTo(me,.01,1),xt&&!ze){const Ft=(0,i.c)();Ft.addElement(L(xt).querySelector(".button-text")).fromTo("transform",ke?"translateX(-100px)":"translateX(100px)","translateX(0px)"),H.addAnimation(Ft)}}),Y){const Ue=(0,i.c)(),H=Y.querySelector(":scope > ion-content"),q=Y.querySelectorAll(":scope > ion-header > ion-toolbar"),ae=Y.querySelectorAll(":scope > ion-header > *:not(ion-toolbar), :scope > ion-footer > *");if(H||0!==q.length||0!==ae.length?(Ue.addElement(H),Ue.addElement(ae)):Ue.addElement(Y.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),Ke.addAnimation(Ue),Z){Ue.beforeClearStyles([me]).fromTo("transform",`translateX(${Ee})`,ke?"translateX(-100%)":"translateX(100%)");const $=(0,C.g)(Y);Ke.afterAddWrite(()=>{"normal"===Ke.getDirection()&&$.style.setProperty("display","none")})}else Ue.fromTo("transform",`translateX(${Ee})`,`translateX(${V})`).fromTo(me,1,.8);if(H){const $=L(H).querySelector(".transition-effect");if($){const Oe=$.querySelector(".transition-cover"),Le=$.querySelector(".transition-shadow"),Xe=(0,i.c)(),It=(0,i.c)(),Tt=(0,i.c)();Xe.addElement($).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),It.addElement(Oe).beforeClearStyles([me]).fromTo(me,.1,0),Tt.addElement(Le).beforeClearStyles([me]).fromTo(me,.7,.03),Xe.addAnimation([It,Tt]),Ue.addAnimation([Xe])}}q.forEach($=>{const Oe=(0,i.c)();Oe.addElement($);const Le=(0,i.c)();Le.addElement($.querySelector("ion-title"));const Xe=(0,i.c)(),It=$.querySelectorAll("ion-buttons,[menuToggle]"),Tt=$.closest("ion-header"),vt=Tt?.classList.contains("header-collapse-condense-inactive"),xt=Array.from(It).filter(Ae=>{const ht=Ae.classList.contains("buttons-collapse");return ht&&!vt||!ht});Xe.addElement(xt);const Pt=(0,i.c)(),Ft=$.querySelectorAll(":scope > *:not(ion-title):not(ion-buttons):not([menuToggle])");Ft.length>0&&Pt.addElement(Ft);const Be=(0,i.c)();Be.addElement(L($).querySelector(".toolbar-background"));const xe=(0,i.c)(),he=$.querySelector("ion-back-button");if(he&&xe.addElement(he),Oe.addAnimation([Le,Xe,Pt,xe,Be]),Ke.addAnimation(Oe),xe.fromTo(me,.99,0),Xe.fromTo(me,.99,0),Pt.fromTo(me,.99,0),Z){if(vt||Le.fromTo("transform",`translateX(${Ee})`,ke?"translateX(-100%)":"translateX(100%)").fromTo(me,.99,0),Pt.fromTo("transform",`translateX(${Ee})`,ke?"translateX(-100%)":"translateX(100%)"),Be.beforeClearStyles([me,"transform"]),Tt?.translucent?Be.fromTo("transform","translateX(0px)",ke?"translateX(-100%)":"translateX(100%)"):Be.fromTo(me,"var(--opacity)",0),he&&!at){const ht=(0,i.c)();ht.addElement(L(he).querySelector(".button-text")).fromTo("transform",`translateX(${Ee})`,`translateX(${(ke?-124:124)+"px"})`),Oe.addAnimation(ht)}}else vt||Le.fromTo("transform",`translateX(${Ee})`,`translateX(${V})`).fromTo(me,.99,0).afterClearStyles([Ze,me]),Pt.fromTo("transform",`translateX(${Ee})`,`translateX(${V})`).afterClearStyles([Ze,me]),xe.afterClearStyles([me]),Le.afterClearStyles([me]),Xe.afterClearStyles([me])})}return Ke}catch(se){throw se}},ue=10},2942:(Ct,We,O)=>{"use strict";O.r(We),O.d(We,{mdTransitionAnimation:()=>Me});var i=O(3503),C=O(7555);O(8476),O(4363);const Me=(Ie,L)=>{var Pe,je,ye;const pe="back"===L.direction,ie=L.leavingEl,Ve=(0,C.g)(L.enteringEl),ge=Ve.querySelector("ion-toolbar"),se=(0,i.c)();if(se.addElement(Ve).fill("both").beforeRemoveClass("ion-page-invisible"),pe?se.duration((null!==(Pe=L.duration)&&void 0!==Pe?Pe:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)"):se.duration((null!==(je=L.duration)&&void 0!==je?je:0)||280).easing("cubic-bezier(0.36,0.66,0.04,1)").fromTo("transform","translateY(40px)","translateY(0px)").fromTo("opacity",.01,1),ge){const me=(0,i.c)();me.addElement(ge),se.addAnimation(me)}if(ie&&pe){se.duration((null!==(ye=L.duration)&&void 0!==ye?ye:0)||200).easing("cubic-bezier(0.47,0,0.745,0.715)");const me=(0,i.c)();me.addElement((0,C.g)(ie)).onFinish(Ze=>{1===Ze&&me.elements.length>0&&me.elements[0].style.setProperty("display","none")}).fromTo("transform","translateY(0px)","translateY(40px)").fromTo("opacity",1,0),se.addAnimation(me)}return se}},5516:(Ct,We,O)=>{"use strict";O.d(We,{B:()=>Xe,G:()=>It,O:()=>Tt,a:()=>De,b:()=>pe,c:()=>ge,d:()=>vt,e:()=>xt,f:()=>Rt,g:()=>at,h:()=>q,i:()=>$,j:()=>me,k:()=>Ze,m:()=>ie,n:()=>Ge,o:()=>Ke,s:()=>Le});var i=O(467),C=O(8476),ne=O(3113),X=O(611),Me=O(5938),Ie=O(5638),L=O(4929);let Pe=0,je=0;const ye=new WeakMap,te=Be=>({create:xe=>Ee(Be,xe),dismiss:(xe,he,Ae)=>de(document,xe,he,Be,Ae),getTop:()=>(0,i.A)(function*(){return Ke(document,Be)})()}),De=te("ion-alert"),pe=te("ion-action-sheet"),ie=te("ion-modal"),ge=te("ion-popover"),me=Be=>{typeof document<"u"&&Z(document);const xe=Pe++;Be.overlayIndex=xe},Ze=Be=>(Be.hasAttribute("id")||(Be.id="ion-overlay-"+ ++je),Be.id),Ee=(Be,xe)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(Be).then(()=>{const he=document.createElement(Be);return he.classList.add("overlay-hidden"),Object.assign(he,Object.assign(Object.assign({},xe),{hasController:!0})),Ue(document).appendChild(he),new Promise(Ae=>(0,Ie.c)(he,Ae))}):Promise.resolve(),Ce='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',Ge=(Be,xe)=>{const he=Be.querySelector(Ce);A(he,xe)},V=(Be,xe)=>{const he=Array.from(Be.querySelectorAll(Ce));A(he.length>0?he[he.length-1]:null,xe)},A=(Be,xe)=>{let he=Be;const Ae=Be?.shadowRoot;Ae&&(he=Ae.querySelector(Ce)||Be),he?(0,Ie.f)(he):xe.focus()},Z=Be=>{0===Pe&&(Pe=1,Be.addEventListener("focus",xe=>{((Be,xe)=>{const he=Ke(xe,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover"),Ae=Be.target;he&&Ae&&!he.classList.contains("ion-disable-focus-trap")&&(he.shadowRoot?(()=>{if(he.contains(Ae))he.lastFocus=Ae;else if("ION-TOAST"===Ae.tagName)A(he.lastFocus,he);else{const Ht=he.lastFocus;Ge(he,he),Ht===xe.activeElement&&V(he,he),he.lastFocus=xe.activeElement}})():(()=>{if(he===Ae)he.lastFocus=void 0;else if("ION-TOAST"===Ae.tagName)A(he.lastFocus,he);else{const Ht=(0,Ie.g)(he);if(!Ht.contains(Ae))return;const on=Ht.querySelector(".ion-overlay-wrapper");if(!on)return;if(on.contains(Ae)||Ae===Ht.querySelector("ion-backdrop"))he.lastFocus=Ae;else{const $t=he.lastFocus;Ge(on,he),$t===xe.activeElement&&V(on,he),he.lastFocus=xe.activeElement}}})())})(xe,Be)},!0),Be.addEventListener("ionBackButton",xe=>{const he=Ke(Be);he?.backdropDismiss&&xe.detail.register(ne.OVERLAY_BACK_BUTTON_PRIORITY,()=>{he.dismiss(void 0,Xe)})}),(0,ne.shouldUseCloseWatcher)()||Be.addEventListener("keydown",xe=>{if("Escape"===xe.key){const he=Ke(Be);he?.backdropDismiss&&he.dismiss(void 0,Xe)}}))},de=(Be,xe,he,Ae,ht)=>{const Lt=Ke(Be,Ae,ht);return Lt?Lt.dismiss(xe,he):Promise.reject("overlay does not exist")},Re=(Be,xe)=>((Be,xe)=>(void 0===xe&&(xe="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker,ion-popover,ion-toast"),Array.from(Be.querySelectorAll(xe)).filter(he=>he.overlayIndex>0)))(Be,xe).filter(he=>!(Be=>Be.classList.contains("overlay-hidden"))(he)),Ke=(Be,xe,he)=>{const Ae=Re(Be,xe);return void 0===he?Ae[Ae.length-1]:Ae.find(ht=>ht.id===he)},pt=(Be=!1)=>{const he=Ue(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");he&&(Be?he.setAttribute("aria-hidden","true"):he.removeAttribute("aria-hidden"))},Rt=function(){var Be=(0,i.A)(function*(xe,he,Ae,ht,Lt){var Ht,on;if(xe.presented)return;pt(!0),Pt(xe.el),xe.presented=!0,xe.willPresent.emit(),null===(Ht=xe.willPresentShorthand)||void 0===Ht||Ht.emit();const $t=(0,X.b)(xe),Wt=xe.enterAnimation?xe.enterAnimation:X.c.get(he,"ios"===$t?Ae:ht);(yield H(xe,Wt,xe.el,Lt))&&(xe.didPresent.emit(),null===(on=xe.didPresentShorthand)||void 0===on||on.emit()),"ION-TOAST"!==xe.el.tagName&&ze(xe.el),xe.keyboardClose&&(null===document.activeElement||!xe.el.contains(document.activeElement))&&xe.el.focus(),xe.el.removeAttribute("aria-hidden")});return function(he,Ae,ht,Lt,Ht){return Be.apply(this,arguments)}}(),ze=function(){var Be=(0,i.A)(function*(xe){let he=document.activeElement;if(!he)return;const Ae=he?.shadowRoot;Ae&&(he=Ae.querySelector(Ce)||he),yield xe.onDidDismiss(),(null===document.activeElement||document.activeElement===document.body)&&he.focus()});return function(he){return Be.apply(this,arguments)}}(),at=function(){var Be=(0,i.A)(function*(xe,he,Ae,ht,Lt,Ht,on){var $t,Wt;if(!xe.presented)return!1;void 0!==C.d&&1===Re(C.d).length&&pt(!1),xe.presented=!1;try{xe.el.style.setProperty("pointer-events","none"),xe.willDismiss.emit({data:he,role:Ae}),null===($t=xe.willDismissShorthand)||void 0===$t||$t.emit({data:he,role:Ae});const yt=(0,X.b)(xe),At=xe.leaveAnimation?xe.leaveAnimation:X.c.get(ht,"ios"===yt?Lt:Ht);Ae!==It&&(yield H(xe,At,xe.el,on)),xe.didDismiss.emit({data:he,role:Ae}),null===(Wt=xe.didDismissShorthand)||void 0===Wt||Wt.emit({data:he,role:Ae}),(ye.get(xe)||[]).forEach(On=>On.destroy()),ye.delete(xe),xe.el.classList.add("overlay-hidden"),xe.el.style.removeProperty("pointer-events"),void 0!==xe.el.lastFocus&&(xe.el.lastFocus=void 0)}catch(yt){console.error(yt)}return xe.el.remove(),Ft(),!0});return function(he,Ae,ht,Lt,Ht,on,$t){return Be.apply(this,arguments)}}(),Ue=Be=>Be.querySelector("ion-app")||Be.body,H=function(){var Be=(0,i.A)(function*(xe,he,Ae,ht){Ae.classList.remove("overlay-hidden");const Ht=he(xe.el,ht);(!xe.animated||!X.c.getBoolean("animated",!0))&&Ht.duration(0),xe.keyboardClose&&Ht.beforeAddWrite(()=>{const $t=Ae.ownerDocument.activeElement;$t?.matches("input,ion-input, ion-textarea")&&$t.blur()});const on=ye.get(xe)||[];return ye.set(xe,[...on,Ht]),yield Ht.play(),!0});return function(he,Ae,ht,Lt){return Be.apply(this,arguments)}}(),q=(Be,xe)=>{let he;const Ae=new Promise(ht=>he=ht);return ae(Be,xe,ht=>{he(ht.detail)}),Ae},ae=(Be,xe,he)=>{const Ae=ht=>{(0,Ie.b)(Be,xe,Ae),he(ht)};(0,Ie.a)(Be,xe,Ae)},$=Be=>"cancel"===Be||Be===Xe,Oe=Be=>Be(),Le=(Be,xe)=>{if("function"==typeof Be)return X.c.get("_zoneGate",Oe)(()=>{try{return Be(xe)}catch(Ae){throw Ae}})},Xe="backdrop",It="gesture",Tt=39,vt=Be=>{let he,xe=!1;const Ae=(0,Me.C)(),ht=(on=!1)=>{if(he&&!on)return{delegate:he,inline:xe};const{el:$t,hasController:Wt,delegate:yt}=Be;return xe=null!==$t.parentNode&&!Wt,he=xe?yt||Ae:yt,{inline:xe,delegate:he}};return{attachViewToDom:function(){var on=(0,i.A)(function*($t){const{delegate:Wt}=ht(!0);if(Wt)return yield Wt.attachViewToDom(Be.el,$t);const{hasController:yt}=Be;if(yt&&void 0!==$t)throw new Error("framework delegate is missing");return null});return function(Wt){return on.apply(this,arguments)}}(),removeViewFromDom:()=>{const{delegate:on}=ht();on&&void 0!==Be.el&&on.removeViewFromDom(Be.el.parentElement,Be.el)}}},xt=()=>{let Be;const xe=()=>{Be&&(Be(),Be=void 0)};return{addClickListener:(Ae,ht)=>{xe();const Lt=void 0!==ht?document.getElementById(ht):null;Lt?Be=((on,$t)=>{const Wt=()=>{$t.present()};return on.addEventListener("click",Wt),()=>{on.removeEventListener("click",Wt)}})(Lt,Ae):(0,L.p)(`A trigger element with the ID "${ht}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,Ae)},removeClickListener:xe}},Pt=Be=>{var xe;if(void 0===C.d)return;const he=Re(C.d);for(let Ae=he.length-1;Ae>=0;Ae--){const ht=he[Ae],Lt=null!==(xe=he[Ae+1])&&void 0!==xe?xe:Be;(Lt.hasAttribute("aria-hidden")||"ION-TOAST"!==Lt.tagName)&&ht.setAttribute("aria-hidden","true")}},Ft=()=>{if(void 0===C.d)return;const Be=Re(C.d);for(let xe=Be.length-1;xe>=0;xe--){const he=Be[xe];if(he.removeAttribute("aria-hidden"),"ION-TOAST"!==he.tagName)break}}},7206:(Ct,We,O)=>{"use strict";var i=O(345),C=O(70),ne=O(4517),X=O(8974),Me=O(4438);let Ie=(()=>{class ye{constructor(){}static{this.\u0275fac=function(pe){return new(pe||ye)}}static{this.\u0275cmp=Me.VBU({type:ye,selectors:[["app-root"]],decls:2,vars:0,template:function(pe,ue){1&pe&&(Me.j41(0,"ion-app"),Me.nrm(1,"ion-router-outlet"),Me.k0s())},dependencies:[X.U1,X.Rg]})}}return ye})();const L=[{path:"home",loadChildren:()=>O.e(5075).then(O.bind(O,5075)).then(ye=>ye.HomePageModule)},{path:"",redirectTo:"home",pathMatch:"full"}];let Pe=(()=>{class ye{static{this.\u0275fac=function(pe){return new(pe||ye)}}static{this.\u0275mod=Me.$C({type:ye})}static{this.\u0275inj=Me.G2t({imports:[C.iI.forRoot(L,{preloadingStrategy:C.Kp}),C.iI]})}}return ye})(),je=(()=>{class ye{static{this.\u0275fac=function(pe){return new(pe||ye)}}static{this.\u0275mod=Me.$C({type:ye,bootstrap:[Ie]})}static{this.\u0275inj=Me.G2t({providers:[{provide:C.b,useClass:ne.jM}],imports:[i.Bb,X.bv.forRoot(),Pe]})}}return ye})();i.sG().bootstrapModule(je).catch(ye=>console.log(ye))},4412:(Ct,We,O)=>{"use strict";O.d(We,{t:()=>C});var i=O(1413);class C extends i.B{constructor(X){super(),this._value=X}get value(){return this.getValue()}_subscribe(X){const Me=super._subscribe(X);return!Me.closed&&X.next(this._value),Me}getValue(){const{hasError:X,thrownError:Me,_value:Ie}=this;if(X)throw Me;return this._throwIfClosed(),Ie}next(X){super.next(this._value=X)}}},1985:(Ct,We,O)=>{"use strict";O.d(We,{c:()=>Pe});var i=O(7707),C=O(8359),ne=O(3494),X=O(1203),Me=O(1026),Ie=O(8071),L=O(9786);let Pe=(()=>{class De{constructor(ue){ue&&(this._subscribe=ue)}lift(ue){const ie=new De;return ie.source=this,ie.operator=ue,ie}subscribe(ue,ie,Ve){const ge=function te(De){return De&&De instanceof i.vU||function ye(De){return De&&(0,Ie.T)(De.next)&&(0,Ie.T)(De.error)&&(0,Ie.T)(De.complete)}(De)&&(0,C.Uv)(De)}(ue)?ue:new i.Ms(ue,ie,Ve);return(0,L.Y)(()=>{const{operator:se,source:me}=this;ge.add(se?se.call(ge,me):me?this._subscribe(ge):this._trySubscribe(ge))}),ge}_trySubscribe(ue){try{return this._subscribe(ue)}catch(ie){ue.error(ie)}}forEach(ue,ie){return new(ie=je(ie))((Ve,ge)=>{const se=new i.Ms({next:me=>{try{ue(me)}catch(Ze){ge(Ze),se.unsubscribe()}},error:ge,complete:Ve});this.subscribe(se)})}_subscribe(ue){var ie;return null===(ie=this.source)||void 0===ie?void 0:ie.subscribe(ue)}[ne.s](){return this}pipe(...ue){return(0,X.m)(ue)(this)}toPromise(ue){return new(ue=je(ue))((ie,Ve)=>{let ge;this.subscribe(se=>ge=se,se=>Ve(se),()=>ie(ge))})}}return De.create=pe=>new De(pe),De})();function je(De){var pe;return null!==(pe=De??Me.$.Promise)&&void 0!==pe?pe:Promise}},1413:(Ct,We,O)=>{"use strict";O.d(We,{B:()=>L});var i=O(1985),C=O(8359);const X=(0,O(1853).L)(je=>function(){je(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Me=O(7908),Ie=O(9786);let L=(()=>{class je extends i.c{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(te){const De=new Pe(this,this);return De.operator=te,De}_throwIfClosed(){if(this.closed)throw new X}next(te){(0,Ie.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const De of this.currentObservers)De.next(te)}})}error(te){(0,Ie.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=te;const{observers:De}=this;for(;De.length;)De.shift().error(te)}})}complete(){(0,Ie.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:te}=this;for(;te.length;)te.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var te;return(null===(te=this.observers)||void 0===te?void 0:te.length)>0}_trySubscribe(te){return this._throwIfClosed(),super._trySubscribe(te)}_subscribe(te){return this._throwIfClosed(),this._checkFinalizedStatuses(te),this._innerSubscribe(te)}_innerSubscribe(te){const{hasError:De,isStopped:pe,observers:ue}=this;return De||pe?C.Kn:(this.currentObservers=null,ue.push(te),new C.yU(()=>{this.currentObservers=null,(0,Me.o)(ue,te)}))}_checkFinalizedStatuses(te){const{hasError:De,thrownError:pe,isStopped:ue}=this;De?te.error(pe):ue&&te.complete()}asObservable(){const te=new i.c;return te.source=this,te}}return je.create=(ye,te)=>new Pe(ye,te),je})();class Pe extends L{constructor(ye,te){super(),this.destination=ye,this.source=te}next(ye){var te,De;null===(De=null===(te=this.destination)||void 0===te?void 0:te.next)||void 0===De||De.call(te,ye)}error(ye){var te,De;null===(De=null===(te=this.destination)||void 0===te?void 0:te.error)||void 0===De||De.call(te,ye)}complete(){var ye,te;null===(te=null===(ye=this.destination)||void 0===ye?void 0:ye.complete)||void 0===te||te.call(ye)}_subscribe(ye){var te,De;return null!==(De=null===(te=this.source)||void 0===te?void 0:te.subscribe(ye))&&void 0!==De?De:C.Kn}}},7707:(Ct,We,O)=>{"use strict";O.d(We,{Ms:()=>Ve,vU:()=>De});var i=O(8071),C=O(8359),ne=O(1026),X=O(5334),Me=O(5343);const Ie=je("C",void 0,void 0);function je(Ee,Ce,ke){return{kind:Ee,value:Ce,error:ke}}var ye=O(9270),te=O(9786);class De extends C.yU{constructor(Ce){super(),this.isStopped=!1,Ce?(this.destination=Ce,(0,C.Uv)(Ce)&&Ce.add(this)):this.destination=Ze}static create(Ce,ke,Ge){return new Ve(Ce,ke,Ge)}next(Ce){this.isStopped?me(function Pe(Ee){return je("N",Ee,void 0)}(Ce),this):this._next(Ce)}error(Ce){this.isStopped?me(function L(Ee){return je("E",void 0,Ee)}(Ce),this):(this.isStopped=!0,this._error(Ce))}complete(){this.isStopped?me(Ie,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(Ce){this.destination.next(Ce)}_error(Ce){try{this.destination.error(Ce)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const pe=Function.prototype.bind;function ue(Ee,Ce){return pe.call(Ee,Ce)}class ie{constructor(Ce){this.partialObserver=Ce}next(Ce){const{partialObserver:ke}=this;if(ke.next)try{ke.next(Ce)}catch(Ge){ge(Ge)}}error(Ce){const{partialObserver:ke}=this;if(ke.error)try{ke.error(Ce)}catch(Ge){ge(Ge)}else ge(Ce)}complete(){const{partialObserver:Ce}=this;if(Ce.complete)try{Ce.complete()}catch(ke){ge(ke)}}}class Ve extends De{constructor(Ce,ke,Ge){let V;if(super(),(0,i.T)(Ce)||!Ce)V={next:Ce??void 0,error:ke??void 0,complete:Ge??void 0};else{let A;this&&ne.$.useDeprecatedNextContext?(A=Object.create(Ce),A.unsubscribe=()=>this.unsubscribe(),V={next:Ce.next&&ue(Ce.next,A),error:Ce.error&&ue(Ce.error,A),complete:Ce.complete&&ue(Ce.complete,A)}):V=Ce}this.destination=new ie(V)}}function ge(Ee){ne.$.useDeprecatedSynchronousErrorHandling?(0,te.l)(Ee):(0,X.m)(Ee)}function me(Ee,Ce){const{onStoppedNotification:ke}=ne.$;ke&&ye.f.setTimeout(()=>ke(Ee,Ce))}const Ze={closed:!0,next:Me.l,error:function se(Ee){throw Ee},complete:Me.l}},8359:(Ct,We,O)=>{"use strict";O.d(We,{Kn:()=>Ie,yU:()=>Me,Uv:()=>L});var i=O(8071);const ne=(0,O(1853).L)(je=>function(te){je(this),this.message=te?`${te.length} errors occurred during unsubscription:\n${te.map((De,pe)=>`${pe+1}) ${De.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=te});var X=O(7908);class Me{constructor(ye){this.initialTeardown=ye,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let ye;if(!this.closed){this.closed=!0;const{_parentage:te}=this;if(te)if(this._parentage=null,Array.isArray(te))for(const ue of te)ue.remove(this);else te.remove(this);const{initialTeardown:De}=this;if((0,i.T)(De))try{De()}catch(ue){ye=ue instanceof ne?ue.errors:[ue]}const{_finalizers:pe}=this;if(pe){this._finalizers=null;for(const ue of pe)try{Pe(ue)}catch(ie){ye=ye??[],ie instanceof ne?ye=[...ye,...ie.errors]:ye.push(ie)}}if(ye)throw new ne(ye)}}add(ye){var te;if(ye&&ye!==this)if(this.closed)Pe(ye);else{if(ye instanceof Me){if(ye.closed||ye._hasParent(this))return;ye._addParent(this)}(this._finalizers=null!==(te=this._finalizers)&&void 0!==te?te:[]).push(ye)}}_hasParent(ye){const{_parentage:te}=this;return te===ye||Array.isArray(te)&&te.includes(ye)}_addParent(ye){const{_parentage:te}=this;this._parentage=Array.isArray(te)?(te.push(ye),te):te?[te,ye]:ye}_removeParent(ye){const{_parentage:te}=this;te===ye?this._parentage=null:Array.isArray(te)&&(0,X.o)(te,ye)}remove(ye){const{_finalizers:te}=this;te&&(0,X.o)(te,ye),ye instanceof Me&&ye._removeParent(this)}}Me.EMPTY=(()=>{const je=new Me;return je.closed=!0,je})();const Ie=Me.EMPTY;function L(je){return je instanceof Me||je&&"closed"in je&&(0,i.T)(je.remove)&&(0,i.T)(je.add)&&(0,i.T)(je.unsubscribe)}function Pe(je){(0,i.T)(je)?je():je.unsubscribe()}},1026:(Ct,We,O)=>{"use strict";O.d(We,{$:()=>i});const i={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},4572:(Ct,We,O)=>{"use strict";O.d(We,{z:()=>ye});var i=O(1985),C=O(3073),ne=O(6648),X=O(3669),Me=O(6450),Ie=O(3794),L=O(8496),Pe=O(4360),je=O(5225);function ye(...pe){const ue=(0,Ie.lI)(pe),ie=(0,Ie.ms)(pe),{args:Ve,keys:ge}=(0,C.D)(pe);if(0===Ve.length)return(0,ne.H)([],ue);const se=new i.c(function te(pe,ue,ie=X.D){return Ve=>{De(ue,()=>{const{length:ge}=pe,se=new Array(ge);let me=ge,Ze=ge;for(let Ee=0;Ee<ge;Ee++)De(ue,()=>{const Ce=(0,ne.H)(pe[Ee],ue);let ke=!1;Ce.subscribe((0,Pe._)(Ve,Ge=>{se[Ee]=Ge,ke||(ke=!0,Ze--),Ze||Ve.next(ie(se.slice()))},()=>{--me||Ve.complete()}))},Ve)},Ve)}}(Ve,ue,ge?me=>(0,L.e)(ge,me):X.D));return ie?se.pipe((0,Me.I)(ie)):se}function De(pe,ue,ie){pe?(0,je.N)(ie,pe,ue):ue()}},6648:(Ct,We,O)=>{"use strict";O.d(We,{H:()=>Ge});var i=O(8750),C=O(5225),ne=O(9974),X=O(4360);function Me(V,A=0){return(0,ne.N)((Y,Z)=>{Y.subscribe((0,X._)(Z,de=>(0,C.N)(Z,V,()=>Z.next(de),A),()=>(0,C.N)(Z,V,()=>Z.complete(),A),de=>(0,C.N)(Z,V,()=>Z.error(de),A)))})}function Ie(V,A=0){return(0,ne.N)((Y,Z)=>{Z.add(V.schedule(()=>Y.subscribe(Z),A))})}var je=O(1985),te=O(4761),De=O(8071);function ue(V,A){if(!V)throw new Error("Iterable cannot be null");return new je.c(Y=>{(0,C.N)(Y,A,()=>{const Z=V[Symbol.asyncIterator]();(0,C.N)(Y,A,()=>{Z.next().then(de=>{de.done?Y.complete():Y.next(de.value)})},0,!0)})})}var ie=O(5055),Ve=O(9858),ge=O(7441),se=O(5397),me=O(7953),Ze=O(591),Ee=O(5196);function Ge(V,A){return A?function ke(V,A){if(null!=V){if((0,ie.l)(V))return function L(V,A){return(0,i.Tg)(V).pipe(Ie(A),Me(A))}(V,A);if((0,ge.X)(V))return function ye(V,A){return new je.c(Y=>{let Z=0;return A.schedule(function(){Z===V.length?Y.complete():(Y.next(V[Z++]),Y.closed||this.schedule())})})}(V,A);if((0,Ve.y)(V))return function Pe(V,A){return(0,i.Tg)(V).pipe(Ie(A),Me(A))}(V,A);if((0,me.T)(V))return ue(V,A);if((0,se.x)(V))return function pe(V,A){return new je.c(Y=>{let Z;return(0,C.N)(Y,A,()=>{Z=V[te.l](),(0,C.N)(Y,A,()=>{let de,_e;try{({value:de,done:_e}=Z.next())}catch(Re){return void Y.error(Re)}_e?Y.complete():Y.next(de)},0,!0)}),()=>(0,De.T)(Z?.return)&&Z.return()})}(V,A);if((0,Ee.U)(V))return function Ce(V,A){return ue((0,Ee.C)(V),A)}(V,A)}throw(0,Ze.L)(V)}(V,A):(0,i.Tg)(V)}},3726:(Ct,We,O)=>{"use strict";O.d(We,{R:()=>ye});var i=O(8750),C=O(1985),ne=O(1397),X=O(7441),Me=O(8071),Ie=O(6450);const L=["addListener","removeListener"],Pe=["addEventListener","removeEventListener"],je=["on","off"];function ye(ie,Ve,ge,se){if((0,Me.T)(ge)&&(se=ge,ge=void 0),se)return ye(ie,Ve,ge).pipe((0,Ie.I)(se));const[me,Ze]=function ue(ie){return(0,Me.T)(ie.addEventListener)&&(0,Me.T)(ie.removeEventListener)}(ie)?Pe.map(Ee=>Ce=>ie[Ee](Ve,Ce,ge)):function De(ie){return(0,Me.T)(ie.addListener)&&(0,Me.T)(ie.removeListener)}(ie)?L.map(te(ie,Ve)):function pe(ie){return(0,Me.T)(ie.on)&&(0,Me.T)(ie.off)}(ie)?je.map(te(ie,Ve)):[];if(!me&&(0,X.X)(ie))return(0,ne.Z)(Ee=>ye(Ee,Ve,ge))((0,i.Tg)(ie));if(!me)throw new TypeError("Invalid event target");return new C.c(Ee=>{const Ce=(...ke)=>Ee.next(1<ke.length?ke:ke[0]);return me(Ce),()=>Ze(Ce)})}function te(ie,Ve){return ge=>se=>ie[ge](Ve,se)}},8750:(Ct,We,O)=>{"use strict";O.d(We,{Tg:()=>pe});var i=O(1635),C=O(7441),ne=O(9858),X=O(1985),Me=O(5055),Ie=O(7953),L=O(591),Pe=O(5397),je=O(5196),ye=O(8071),te=O(5334),De=O(3494);function pe(Ee){if(Ee instanceof X.c)return Ee;if(null!=Ee){if((0,Me.l)(Ee))return function ue(Ee){return new X.c(Ce=>{const ke=Ee[De.s]();if((0,ye.T)(ke.subscribe))return ke.subscribe(Ce);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(Ee);if((0,C.X)(Ee))return function ie(Ee){return new X.c(Ce=>{for(let ke=0;ke<Ee.length&&!Ce.closed;ke++)Ce.next(Ee[ke]);Ce.complete()})}(Ee);if((0,ne.y)(Ee))return function Ve(Ee){return new X.c(Ce=>{Ee.then(ke=>{Ce.closed||(Ce.next(ke),Ce.complete())},ke=>Ce.error(ke)).then(null,te.m)})}(Ee);if((0,Ie.T)(Ee))return se(Ee);if((0,Pe.x)(Ee))return function ge(Ee){return new X.c(Ce=>{for(const ke of Ee)if(Ce.next(ke),Ce.closed)return;Ce.complete()})}(Ee);if((0,je.U)(Ee))return function me(Ee){return se((0,je.C)(Ee))}(Ee)}throw(0,L.L)(Ee)}function se(Ee){return new X.c(Ce=>{(function Ze(Ee,Ce){var ke,Ge,V,A;return(0,i.sH)(this,void 0,void 0,function*(){try{for(ke=(0,i.xN)(Ee);!(Ge=yield ke.next()).done;)if(Ce.next(Ge.value),Ce.closed)return}catch(Y){V={error:Y}}finally{try{Ge&&!Ge.done&&(A=ke.return)&&(yield A.call(ke))}finally{if(V)throw V.error}}Ce.complete()})})(Ee,Ce).catch(ke=>Ce.error(ke))})}},7673:(Ct,We,O)=>{"use strict";O.d(We,{of:()=>ne});var i=O(3794),C=O(6648);function ne(...X){const Me=(0,i.lI)(X);return(0,C.H)(X,Me)}},4360:(Ct,We,O)=>{"use strict";O.d(We,{_:()=>C});var i=O(7707);function C(X,Me,Ie,L,Pe){return new ne(X,Me,Ie,L,Pe)}class ne extends i.vU{constructor(Me,Ie,L,Pe,je,ye){super(Me),this.onFinalize=je,this.shouldUnsubscribe=ye,this._next=Ie?function(te){try{Ie(te)}catch(De){Me.error(De)}}:super._next,this._error=Pe?function(te){try{Pe(te)}catch(De){Me.error(De)}finally{this.unsubscribe()}}:super._error,this._complete=L?function(){try{L()}catch(te){Me.error(te)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var Me;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:Ie}=this;super.unsubscribe(),!Ie&&(null===(Me=this.onFinalize)||void 0===Me||Me.call(this))}}}},5964:(Ct,We,O)=>{"use strict";O.d(We,{p:()=>ne});var i=O(9974),C=O(4360);function ne(X,Me){return(0,i.N)((Ie,L)=>{let Pe=0;Ie.subscribe((0,C._)(L,je=>X.call(Me,je,Pe++)&&L.next(je)))})}},6354:(Ct,We,O)=>{"use strict";O.d(We,{T:()=>ne});var i=O(9974),C=O(4360);function ne(X,Me){return(0,i.N)((Ie,L)=>{let Pe=0;Ie.subscribe((0,C._)(L,je=>{L.next(X.call(Me,je,Pe++))}))})}},1397:(Ct,We,O)=>{"use strict";O.d(We,{Z:()=>Pe});var i=O(6354),C=O(8750),ne=O(9974),X=O(5225),Me=O(4360),L=O(8071);function Pe(je,ye,te=1/0){return(0,L.T)(ye)?Pe((De,pe)=>(0,i.T)((ue,ie)=>ye(De,ue,pe,ie))((0,C.Tg)(je(De,pe))),te):("number"==typeof ye&&(te=ye),(0,ne.N)((De,pe)=>function Ie(je,ye,te,De,pe,ue,ie,Ve){const ge=[];let se=0,me=0,Ze=!1;const Ee=()=>{Ze&&!ge.length&&!se&&ye.complete()},Ce=Ge=>se<De?ke(Ge):ge.push(Ge),ke=Ge=>{ue&&ye.next(Ge),se++;let V=!1;(0,C.Tg)(te(Ge,me++)).subscribe((0,Me._)(ye,A=>{pe?.(A),ue?Ce(A):ye.next(A)},()=>{V=!0},void 0,()=>{if(V)try{for(se--;ge.length&&se<De;){const A=ge.shift();ie?(0,X.N)(ye,ie,()=>ke(A)):ke(A)}Ee()}catch(A){ye.error(A)}}))};return je.subscribe((0,Me._)(ye,Ce,()=>{Ze=!0,Ee()})),()=>{Ve?.()}}(De,pe,je,te)))}},5558:(Ct,We,O)=>{"use strict";O.d(We,{n:()=>X});var i=O(8750),C=O(9974),ne=O(4360);function X(Me,Ie){return(0,C.N)((L,Pe)=>{let je=null,ye=0,te=!1;const De=()=>te&&!je&&Pe.complete();L.subscribe((0,ne._)(Pe,pe=>{je?.unsubscribe();let ue=0;const ie=ye++;(0,i.Tg)(Me(pe,ie)).subscribe(je=(0,ne._)(Pe,Ve=>Pe.next(Ie?Ie(pe,Ve,ie,ue++):Ve),()=>{je=null,De()}))},()=>{te=!0,De()}))})}},9270:(Ct,We,O)=>{"use strict";O.d(We,{f:()=>i});const i={setTimeout(C,ne,...X){const{delegate:Me}=i;return Me?.setTimeout?Me.setTimeout(C,ne,...X):setTimeout(C,ne,...X)},clearTimeout(C){const{delegate:ne}=i;return(ne?.clearTimeout||clearTimeout)(C)},delegate:void 0}},4761:(Ct,We,O)=>{"use strict";O.d(We,{l:()=>C});const C=function i(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}()},3494:(Ct,We,O)=>{"use strict";O.d(We,{s:()=>i});const i="function"==typeof Symbol&&Symbol.observable||"@@observable"},3794:(Ct,We,O)=>{"use strict";O.d(We,{ms:()=>X,lI:()=>Me});var i=O(8071);function ne(L){return L[L.length-1]}function X(L){return(0,i.T)(ne(L))?L.pop():void 0}function Me(L){return function C(L){return L&&(0,i.T)(L.schedule)}(ne(L))?L.pop():void 0}},3073:(Ct,We,O)=>{"use strict";O.d(We,{D:()=>Me});const{isArray:i}=Array,{getPrototypeOf:C,prototype:ne,keys:X}=Object;function Me(L){if(1===L.length){const Pe=L[0];if(i(Pe))return{args:Pe,keys:null};if(function Ie(L){return L&&"object"==typeof L&&C(L)===ne}(Pe)){const je=X(Pe);return{args:je.map(ye=>Pe[ye]),keys:je}}}return{args:L,keys:null}}},7908:(Ct,We,O)=>{"use strict";function i(C,ne){if(C){const X=C.indexOf(ne);0<=X&&C.splice(X,1)}}O.d(We,{o:()=>i})},1853:(Ct,We,O)=>{"use strict";function i(C){const X=C(Me=>{Error.call(Me),Me.stack=(new Error).stack});return X.prototype=Object.create(Error.prototype),X.prototype.constructor=X,X}O.d(We,{L:()=>i})},8496:(Ct,We,O)=>{"use strict";function i(C,ne){return C.reduce((X,Me,Ie)=>(X[Me]=ne[Ie],X),{})}O.d(We,{e:()=>i})},9786:(Ct,We,O)=>{"use strict";O.d(We,{Y:()=>ne,l:()=>X});var i=O(1026);let C=null;function ne(Me){if(i.$.useDeprecatedSynchronousErrorHandling){const Ie=!C;if(Ie&&(C={errorThrown:!1,error:null}),Me(),Ie){const{errorThrown:L,error:Pe}=C;if(C=null,L)throw Pe}}else Me()}function X(Me){i.$.useDeprecatedSynchronousErrorHandling&&C&&(C.errorThrown=!0,C.error=Me)}},5225:(Ct,We,O)=>{"use strict";function i(C,ne,X,Me=0,Ie=!1){const L=ne.schedule(function(){X(),Ie?C.add(this.schedule(null,Me)):this.unsubscribe()},Me);if(C.add(L),!Ie)return L}O.d(We,{N:()=>i})},3669:(Ct,We,O)=>{"use strict";function i(C){return C}O.d(We,{D:()=>i})},7441:(Ct,We,O)=>{"use strict";O.d(We,{X:()=>i});const i=C=>C&&"number"==typeof C.length&&"function"!=typeof C},7953:(Ct,We,O)=>{"use strict";O.d(We,{T:()=>C});var i=O(8071);function C(ne){return Symbol.asyncIterator&&(0,i.T)(ne?.[Symbol.asyncIterator])}},8071:(Ct,We,O)=>{"use strict";function i(C){return"function"==typeof C}O.d(We,{T:()=>i})},5055:(Ct,We,O)=>{"use strict";O.d(We,{l:()=>ne});var i=O(3494),C=O(8071);function ne(X){return(0,C.T)(X[i.s])}},5397:(Ct,We,O)=>{"use strict";O.d(We,{x:()=>ne});var i=O(4761),C=O(8071);function ne(X){return(0,C.T)(X?.[i.l])}},9858:(Ct,We,O)=>{"use strict";O.d(We,{y:()=>C});var i=O(8071);function C(ne){return(0,i.T)(ne?.then)}},5196:(Ct,We,O)=>{"use strict";O.d(We,{C:()=>ne,U:()=>X});var i=O(1635),C=O(8071);function ne(Me){return(0,i.AQ)(this,arguments,function*(){const L=Me.getReader();try{for(;;){const{value:Pe,done:je}=yield(0,i.N3)(L.read());if(je)return yield(0,i.N3)(void 0);yield yield(0,i.N3)(Pe)}}finally{L.releaseLock()}})}function X(Me){return(0,C.T)(Me?.getReader)}},9974:(Ct,We,O)=>{"use strict";O.d(We,{N:()=>ne,S:()=>C});var i=O(8071);function C(X){return(0,i.T)(X?.lift)}function ne(X){return Me=>{if(C(Me))return Me.lift(function(Ie){try{return X(Ie,this)}catch(L){this.error(L)}});throw new TypeError("Unable to lift unknown Observable type")}}},6450:(Ct,We,O)=>{"use strict";O.d(We,{I:()=>X});var i=O(6354);const{isArray:C}=Array;function X(Me){return(0,i.T)(Ie=>function ne(Me,Ie){return C(Ie)?Me(...Ie):Me(Ie)}(Me,Ie))}},5343:(Ct,We,O)=>{"use strict";function i(){}O.d(We,{l:()=>i})},1203:(Ct,We,O)=>{"use strict";O.d(We,{F:()=>C,m:()=>ne});var i=O(3669);function C(...X){return ne(X)}function ne(X){return 0===X.length?i.D:1===X.length?X[0]:function(Ie){return X.reduce((L,Pe)=>Pe(L),Ie)}}},5334:(Ct,We,O)=>{"use strict";O.d(We,{m:()=>ne});var i=O(1026),C=O(9270);function ne(X){C.f.setTimeout(()=>{const{onUnhandledError:Me}=i.$;if(!Me)throw X;Me(X)})}},591:(Ct,We,O)=>{"use strict";function i(C){return new TypeError(`You provided ${null!==C&&"object"==typeof C?"an invalid object":`'${C}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}O.d(We,{L:()=>i})},8996:(Ct,We,O)=>{var i={"./ion-accordion_2.entry.js":[2375,2076,2375],"./ion-action-sheet.entry.js":[8814,2076,8814],"./ion-alert.entry.js":[5222,2076,5222],"./ion-app_8.entry.js":[7720,2076,7720],"./ion-avatar_3.entry.js":[1049,1049],"./ion-back-button.entry.js":[3162,2076,3162],"./ion-backdrop.entry.js":[7240,7240],"./ion-breadcrumb_2.entry.js":[8314,2076,8314],"./ion-button_2.entry.js":[4591,4591],"./ion-card_5.entry.js":[8584,8584],"./ion-checkbox.entry.js":[3511,2076,3511],"./ion-chip.entry.js":[6024,6024],"./ion-col_3.entry.js":[5100,5100],"./ion-datetime-button.entry.js":[7428,771,7428],"./ion-datetime_3.entry.js":[5266,771,2076,2885],"./ion-fab_3.entry.js":[4463,2076,4463],"./ion-img.entry.js":[4183,4183],"./ion-infinite-scroll_2.entry.js":[4171,2076,4171],"./ion-input.entry.js":[9344,2076,9344],"./ion-item-option_3.entry.js":[5949,2076,5949],"./ion-item_8.entry.js":[3506,2076,3506],"./ion-loading.entry.js":[7372,2076,7372],"./ion-menu_3.entry.js":[2075,2076,2075],"./ion-modal.entry.js":[441,2076,441],"./ion-nav_2.entry.js":[5712,2076,5712],"./ion-picker-column-internal.entry.js":[1433,2076,1433],"./ion-picker-internal.entry.js":[2628,2628],"./ion-popover.entry.js":[6433,2076,6433],"./ion-progress-bar.entry.js":[9977,9977],"./ion-radio_2.entry.js":[8066,2076,8066],"./ion-range.entry.js":[8477,2076,8477],"./ion-refresher_2.entry.js":[5197,2076,5197],"./ion-reorder_2.entry.js":[7030,2076,7030],"./ion-ripple-effect.entry.js":[964,964],"./ion-route_4.entry.js":[8970,8970],"./ion-searchbar.entry.js":[8193,2076,8193],"./ion-segment_2.entry.js":[2560,2076,2560],"./ion-select_3.entry.js":[7076,2076,7076],"./ion-spinner.entry.js":[8805,2076,8805],"./ion-split-pane.entry.js":[5887,5887],"./ion-tab-bar_2.entry.js":[4406,2076,4406],"./ion-tab_2.entry.js":[1102,1102],"./ion-text.entry.js":[1577,1577],"./ion-textarea.entry.js":[2348,2076,2348],"./ion-toast.entry.js":[2415,2076,2415],"./ion-toggle.entry.js":[3814,2076,3814]};function C(ne){if(!O.o(i,ne))return Promise.resolve().then(()=>{var Ie=new Error("Cannot find module '"+ne+"'");throw Ie.code="MODULE_NOT_FOUND",Ie});var X=i[ne],Me=X[0];return Promise.all(X.slice(1).map(O.e)).then(()=>O(Me))}C.keys=()=>Object.keys(i),C.id=8996,Ct.exports=C},177:(Ct,We,O)=>{"use strict";O.d(We,{AJ:()=>ai,MD:()=>to,N0:()=>yo,QT:()=>ne,Sm:()=>Ve,T3:()=>Q,VF:()=>Me,Vy:()=>_i,Xr:()=>ui,ZD:()=>X,_b:()=>Un,aZ:()=>se,bT:()=>dr,fw:()=>ge,hb:()=>ue,hj:()=>je,qQ:()=>L});var i=O(4438);let C=null;function ne(){return C}function X(h){C??=h}class Me{}const L=new i.nKC("");let Pe=(()=>{class h{historyGo(y){throw new Error("")}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=i.jDH({token:h,factory:()=>(0,i.WQX)(ye),providedIn:"platform"})}}return h})();const je=new i.nKC("");let ye=(()=>{class h extends Pe{constructor(){super(),this._doc=(0,i.WQX)(L),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return ne().getBaseHref(this._doc)}onPopState(y){const I=ne().getGlobalEventTarget(this._doc,"window");return I.addEventListener("popstate",y,!1),()=>I.removeEventListener("popstate",y)}onHashChange(y){const I=ne().getGlobalEventTarget(this._doc,"window");return I.addEventListener("hashchange",y,!1),()=>I.removeEventListener("hashchange",y)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(y){this._location.pathname=y}pushState(y,I,K){this._history.pushState(y,I,K)}replaceState(y,I,K){this._history.replaceState(y,I,K)}forward(){this._history.forward()}back(){this._history.back()}historyGo(y=0){this._history.go(y)}getState(){return this._history.state}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=i.jDH({token:h,factory:()=>new h,providedIn:"platform"})}}return h})();function te(h,M){if(0==h.length)return M;if(0==M.length)return h;let y=0;return h.endsWith("/")&&y++,M.startsWith("/")&&y++,2==y?h+M.substring(1):1==y?h+M:h+"/"+M}function De(h){const M=h.match(/#|\?|$/),y=M&&M.index||h.length;return h.slice(0,y-("/"===h[y-1]?1:0))+h.slice(y)}function pe(h){return h&&"?"!==h[0]?"?"+h:h}let ue=(()=>{class h{historyGo(y){throw new Error("")}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=i.jDH({token:h,factory:()=>(0,i.WQX)(Ve),providedIn:"root"})}}return h})();const ie=new i.nKC("");let Ve=(()=>{class h extends ue{constructor(y,I){super(),this._platformLocation=y,this._removeListenerFns=[],this._baseHref=I??this._platformLocation.getBaseHrefFromDOM()??(0,i.WQX)(L).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(y){this._removeListenerFns.push(this._platformLocation.onPopState(y),this._platformLocation.onHashChange(y))}getBaseHref(){return this._baseHref}prepareExternalUrl(y){return te(this._baseHref,y)}path(y=!1){const I=this._platformLocation.pathname+pe(this._platformLocation.search),K=this._platformLocation.hash;return K&&y?`${I}${K}`:I}pushState(y,I,K,$e){const tt=this.prepareExternalUrl(K+pe($e));this._platformLocation.pushState(y,I,tt)}replaceState(y,I,K,$e){const tt=this.prepareExternalUrl(K+pe($e));this._platformLocation.replaceState(y,I,tt)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(y=0){this._platformLocation.historyGo?.(y)}static{this.\u0275fac=function(I){return new(I||h)(i.KVO(Pe),i.KVO(ie,8))}}static{this.\u0275prov=i.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})(),ge=(()=>{class h extends ue{constructor(y,I){super(),this._platformLocation=y,this._baseHref="",this._removeListenerFns=[],null!=I&&(this._baseHref=I)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(y){this._removeListenerFns.push(this._platformLocation.onPopState(y),this._platformLocation.onHashChange(y))}getBaseHref(){return this._baseHref}path(y=!1){const I=this._platformLocation.hash??"#";return I.length>0?I.substring(1):I}prepareExternalUrl(y){const I=te(this._baseHref,y);return I.length>0?"#"+I:I}pushState(y,I,K,$e){let tt=this.prepareExternalUrl(K+pe($e));0==tt.length&&(tt=this._platformLocation.pathname),this._platformLocation.pushState(y,I,tt)}replaceState(y,I,K,$e){let tt=this.prepareExternalUrl(K+pe($e));0==tt.length&&(tt=this._platformLocation.pathname),this._platformLocation.replaceState(y,I,tt)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(y=0){this._platformLocation.historyGo?.(y)}static{this.\u0275fac=function(I){return new(I||h)(i.KVO(Pe),i.KVO(ie,8))}}static{this.\u0275prov=i.jDH({token:h,factory:h.\u0275fac})}}return h})(),se=(()=>{class h{constructor(y){this._subject=new i.bkB,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=y;const I=this._locationStrategy.getBaseHref();this._basePath=function Ce(h){if(new RegExp("^(https?:)?//").test(h)){const[,y]=h.split(/\/\/[^\/]+/);return y}return h}(De(Ee(I))),this._locationStrategy.onPopState(K=>{this._subject.emit({url:this.path(!0),pop:!0,state:K.state,type:K.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(y=!1){return this.normalize(this._locationStrategy.path(y))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(y,I=""){return this.path()==this.normalize(y+pe(I))}normalize(y){return h.stripTrailingSlash(function Ze(h,M){if(!h||!M.startsWith(h))return M;const y=M.substring(h.length);return""===y||["/",";","?","#"].includes(y[0])?y:M}(this._basePath,Ee(y)))}prepareExternalUrl(y){return y&&"/"!==y[0]&&(y="/"+y),this._locationStrategy.prepareExternalUrl(y)}go(y,I="",K=null){this._locationStrategy.pushState(K,"",y,I),this._notifyUrlChangeListeners(this.prepareExternalUrl(y+pe(I)),K)}replaceState(y,I="",K=null){this._locationStrategy.replaceState(K,"",y,I),this._notifyUrlChangeListeners(this.prepareExternalUrl(y+pe(I)),K)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(y=0){this._locationStrategy.historyGo?.(y)}onUrlChange(y){return this._urlChangeListeners.push(y),this._urlChangeSubscription??=this.subscribe(I=>{this._notifyUrlChangeListeners(I.url,I.state)}),()=>{const I=this._urlChangeListeners.indexOf(y);this._urlChangeListeners.splice(I,1),0===this._urlChangeListeners.length&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(y="",I){this._urlChangeListeners.forEach(K=>K(y,I))}subscribe(y,I,K){return this._subject.subscribe({next:y,error:I,complete:K})}static{this.normalizeQueryParams=pe}static{this.joinWithSlash=te}static{this.stripTrailingSlash=De}static{this.\u0275fac=function(I){return new(I||h)(i.KVO(ue))}}static{this.\u0275prov=i.jDH({token:h,factory:()=>function me(){return new se((0,i.KVO)(ue))}(),providedIn:"root"})}}return h})();function Ee(h){return h.replace(/\/index.html$/,"")}function Un(h,M){M=encodeURIComponent(M);for(const y of h.split(";")){const I=y.indexOf("="),[K,$e]=-1==I?[y,""]:[y.slice(0,I),y.slice(I+1)];if(K.trim()===M)return decodeURIComponent($e)}return null}let dr=(()=>{class h{constructor(y,I){this._viewContainer=y,this._context=new B,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=I}set ngIf(y){this._context.$implicit=this._context.ngIf=y,this._updateView()}set ngIfThen(y){j("ngIfThen",y),this._thenTemplateRef=y,this._thenViewRef=null,this._updateView()}set ngIfElse(y){j("ngIfElse",y),this._elseTemplateRef=y,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(y,I){return!0}static{this.\u0275fac=function(I){return new(I||h)(i.rXU(i.c1b),i.rXU(i.C4Q))}}static{this.\u0275dir=i.FsC({type:h,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return h})();class B{constructor(){this.$implicit=null,this.ngIf=null}}function j(h,M){if(M&&!M.createEmbeddedView)throw new Error(`${h} must be a TemplateRef, but received '${(0,i.Tbb)(M)}'.`)}let Q=(()=>{class h{constructor(y){this._viewContainerRef=y,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(y){if(this._shouldRecreateView(y)){const I=this._viewContainerRef;if(this._viewRef&&I.remove(I.indexOf(this._viewRef)),!this.ngTemplateOutlet)return void(this._viewRef=null);const K=this._createContextForwardProxy();this._viewRef=I.createEmbeddedView(this.ngTemplateOutlet,K,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(y){return!!y.ngTemplateOutlet||!!y.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(y,I,K)=>!!this.ngTemplateOutletContext&&Reflect.set(this.ngTemplateOutletContext,I,K),get:(y,I,K)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,I,K)}})}static{this.\u0275fac=function(I){return new(I||h)(i.rXU(i.c1b))}}static{this.\u0275dir=i.FsC({type:h,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[i.OA$]})}}return h})(),to=(()=>{class h{static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275mod=i.$C({type:h})}static{this.\u0275inj=i.G2t({})}}return h})();const ai="browser",To="server";function _i(h){return h===To}let ui=(()=>{class h{static{this.\u0275prov=(0,i.jDH)({token:h,providedIn:"root",factory:()=>function lo(h){return h===ai}((0,i.WQX)(i.Agw))?new fr((0,i.WQX)(L),window):new wi})}}return h})();class fr{constructor(M,y){this.document=M,this.window=y,this.offset=()=>[0,0]}setOffset(M){this.offset=Array.isArray(M)?()=>M:M}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(M){this.window.scrollTo(M[0],M[1])}scrollToAnchor(M){const y=function ar(h,M){const y=h.getElementById(M)||h.getElementsByName(M)[0];if(y)return y;if("function"==typeof h.createTreeWalker&&h.body&&"function"==typeof h.body.attachShadow){const I=h.createTreeWalker(h.body,NodeFilter.SHOW_ELEMENT);let K=I.currentNode;for(;K;){const $e=K.shadowRoot;if($e){const tt=$e.getElementById(M)||$e.querySelector(`[name="${M}"]`);if(tt)return tt}K=I.nextNode()}}return null}(this.document,M);y&&(this.scrollToElement(y),y.focus())}setHistoryScrollRestoration(M){this.window.history.scrollRestoration=M}scrollToElement(M){const y=M.getBoundingClientRect(),I=y.left+this.window.pageXOffset,K=y.top+this.window.pageYOffset,$e=this.offset();this.window.scrollTo(I-$e[0],K-$e[1])}}class wi{setOffset(M){}getScrollPosition(){return[0,0]}scrollToPosition(M){}scrollToAnchor(M){}setHistoryScrollRestoration(M){}}class yo{}},4438:(Ct,We,O)=>{"use strict";O.d(We,{iLQ:()=>nh,sZ2:()=>Mp,hnV:()=>$D,Hbi:()=>yA,o8S:()=>ji,BIS:()=>YE,gRc:()=>YD,Ql9:()=>zT,Ocv:()=>JT,Z63:()=>Ro,aKT:()=>ja,uvJ:()=>Qr,zcH:()=>Ci,bkB:()=>ti,$GK:()=>Qe,nKC:()=>Bt,zZn:()=>ho,_q3:()=>uh,MKu:()=>dh,xe9:()=>lu,Co$:()=>Um,Vns:()=>ps,SKi:()=>Dr,Xx1:()=>lo,Agw:()=>Ku,PLl:()=>Tp,sFG:()=>EI,_9s:()=>Qg,czy:()=>Dc,kdw:()=>ci,C4Q:()=>al,NYb:()=>$T,giA:()=>kD,RxE:()=>TD,c1b:()=>$c,gXe:()=>co,mal:()=>rm,L39:()=>XA,a0P:()=>tR,Ol2:()=>uf,w6W:()=>C_,oH4:()=>XD,Rfq:()=>nr,WQX:()=>Ut,QuC:()=>Ur,fpN:()=>vA,HJs:()=>nR,N4e:()=>_o,O8t:()=>qA,H3F:()=>AD,H8p:()=>es,KH2:()=>fa,TgB:()=>Xs,wOt:()=>Ae,WHO:()=>PD,e01:()=>FD,H5H:()=>Lf,Zy3:()=>ht,mq5:()=>Hv,JZv:()=>Nt,LfX:()=>pn,plB:()=>$i,jNT:()=>eh,zjR:()=>LD,TL$:()=>qE,Tbb:()=>Mt,Vt3:()=>af,Mj6:()=>Lr,GFd:()=>Bm,OA$:()=>Ne,Jv_:()=>iD,aNF:()=>sD,R7$:()=>Dg,BMQ:()=>Ef,AVh:()=>Mf,wni:()=>xy,VBU:()=>Da,FsC:()=>ba,jDH:()=>Yt,G2t:()=>Or,$C:()=>Es,EJ8:()=>Qi,rXU:()=>js,nrm:()=>Nf,eu8:()=>kf,k0s:()=>Zc,j41:()=>Yc,RV6:()=>Bv,xGo:()=>Qh,KVO:()=>qn,kS0:()=>rc,QTQ:()=>Eg,bIt:()=>Bf,lsd:()=>Fy,XpG:()=>Cy,SdG:()=>by,NAR:()=>Ey,Y8G:()=>wf,mGM:()=>Py,Njj:()=>Mh,eBV:()=>Sh,n$t:()=>Qp,DNE:()=>Ks,EFF:()=>zy,GBs:()=>Ny}),O(467);let ne=null,Me=1;const Ie=Symbol("SIGNAL");function L(e){const t=ne;return ne=e,t}function ue(e){if((!Ge(e)||e.dirty)&&(e.dirty||e.lastCleanEpoch!==Me)){if(!e.producerMustRecompute(e)&&!Ze(e))return e.dirty=!1,void(e.lastCleanEpoch=Me);e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Me}}function Ze(e){V(e);for(let t=0;t<e.producerNode.length;t++){const n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(ue(n),r!==n.version))return!0}return!1}function ke(e,t){if(function A(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}(e),V(e),1===e.liveConsumerNode.length)for(let r=0;r<e.producerNode.length;r++)ke(e.producerNode[r],e.producerIndexOfThis[r]);const n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){const r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];V(o),o.producerIndexOfThis[r]=t}}function Ge(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function V(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}let pt=null;var xt=O(1413),Pt=O(8359),Ft=O(4412),Be=O(6354);const he="https://g.co/ng/security#xss";class Ae extends Error{constructor(t,n){super(ht(t,n)),this.code=t}}function ht(e,t){return`NG0${Math.abs(e)}${t?": "+t:""}`}function yt(e){return{toString:e}.toString()}const dn="__parameters__";function hn(e,t,n){return yt(()=>{const r=function er(e){return function(...n){if(e){const r=e(...n);for(const o in r)this[o]=r[o]}}}(t);function o(...s){if(this instanceof o)return r.apply(this,s),this;const a=new o(...s);return l.annotation=a,l;function l(d,D,S){const k=d.hasOwnProperty(dn)?d[dn]:Object.defineProperty(d,dn,{value:[]})[dn];for(;k.length<=S;)k.push(null);return(k[S]=k[S]||[]).push(a),d}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}const Nt=globalThis;function Vt(e){for(let t in e)if(e[t]===Vt)return t;throw Error("Could not find renamed property on target object.")}function Wn(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Mt(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(Mt).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function $n(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const tr=Vt({__forward_ref__:Vt});function nr(e){return e.__forward_ref__=nr,e.toString=function(){return Mt(this())},e}function le(e){return Se(e)?e():e}function Se(e){return"function"==typeof e&&e.hasOwnProperty(tr)&&e.__forward_ref__===nr}function Yt(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Or(e){return{providers:e.providers||[],imports:e.imports||[]}}function Vn(e){return xr(e,Bn)||xr(e,_n)}function pn(e){return null!==Vn(e)}function xr(e,t){return e.hasOwnProperty(t)?e[t]:null}function Xn(e){return e&&(e.hasOwnProperty(Un)||e.hasOwnProperty(Er))?e[Un]:null}const Bn=Vt({\u0275prov:Vt}),Un=Vt({\u0275inj:Vt}),_n=Vt({ngInjectableDef:Vt}),Er=Vt({ngInjectorDef:Vt});class Bt{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=Yt({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}function U(e){return e&&!!e.\u0275providers}const fe=Vt({\u0275cmp:Vt}),qe=Vt({\u0275dir:Vt}),rt=Vt({\u0275pipe:Vt}),J=Vt({\u0275mod:Vt}),He=Vt({\u0275fac:Vt}),b=Vt({__NG_ELEMENT_ID__:Vt}),x=Vt({__NG_ENV_ID__:Vt});function G(e){return"string"==typeof e?e:null==e?"":String(e)}function ot(e,t){throw new Ae(-201,!1)}var Qe=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(Qe||{});let mt;function Dt(){return mt}function Ot(e){const t=mt;return mt=e,t}function mn(e,t,n){const r=Vn(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&Qe.Optional?null:void 0!==t?t:void ot()}const m={},F="__NG_DI_FLAG__",oe="ngTempTokenPath",Kn=/\n/gm,Xr="__source";let Ir;function jn(e){const t=Ir;return Ir=e,t}function mo(e,t=Qe.Default){if(void 0===Ir)throw new Ae(-203,!1);return null===Ir?mn(e,void 0,t):Ir.get(e,t&Qe.Optional?null:void 0,t)}function qn(e,t=Qe.Default){return(Dt()||mo)(le(e),t)}function Ut(e,t=Qe.Default){return qn(e,Vr(t))}function Vr(e){return typeof e>"u"||"number"==typeof e?e:(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ho(e){const t=[];for(let n=0;n<e.length;n++){const r=le(e[n]);if(Array.isArray(r)){if(0===r.length)throw new Ae(900,!1);let o,s=Qe.Default;for(let a=0;a<r.length;a++){const l=r[a],d=ai(l);"number"==typeof d?-1===d?o=l.token:s|=d:o=l}t.push(qn(o,s))}else t.push(qn(r))}return t}function to(e,t){return e[F]=t,e.prototype[F]=t,e}function ai(e){return e[F]}const lo=to(hn("Optional"),8),ci=to(hn("SkipSelf"),4);function Kr(e,t){return e.hasOwnProperty(He)?e[He]:null}function ar(e,t){e.forEach(n=>Array.isArray(n)?ar(n,t):t(n))}function wi(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function yo(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function hr(e,t,n){let r=Br(e,t);return r>=0?e[1|r]=n:(r=~r,function Gi(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(1===o)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;)e[o]=e[o-2],o--;e[t]=n,e[t+1]=r}}(e,r,t,n)),r}function di(e,t){const n=Br(e,t);if(n>=0)return e[1|n]}function Br(e,t){return function zi(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){const s=r+(o-r>>1),a=e[s<<n];if(t===a)return s<<n;a>t?o=s:r=s+1}return~(o<<n)}(e,t,1)}const qr={},un=[],Ro=new Bt(""),Si=new Bt("",-1),Mi=new Bt("");class Oo{get(t,n=m){if(n===m){const r=new Error(`NullInjectorError: No provider for ${Mt(t)}!`);throw r.name="NullInjectorError",r}return n}}var Pr=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Pr||{}),co=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(co||{}),Lr=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Lr||{});function ya(e,t,n){let r=e.length;for(;;){const o=e.indexOf(t,n);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){const s=t.length;if(o+s===r||e.charCodeAt(o+s)<=32)return o}n=o+1}}function Ti(e,t,n){let r=0;for(;r<n.length;){const o=n[r];if("number"==typeof o){if(0!==o)break;r++;const s=n[r++],a=n[r++],l=n[r++];e.setAttribute(t,a,l,s)}else{const s=o,a=n[++r];Xi(s)?e.setProperty(t,s,a):e.setAttribute(t,s,a),r++}}return r}function Wi(e){return 3===e||4===e||6===e}function Xi(e){return 64===e.charCodeAt(0)}function Do(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const o=t[r];"number"==typeof o?n=o:0===n||Ki(e,n,o,null,-1===n||2===n?t[++r]:null)}}return e}function Ki(e,t,n,r,o){let s=0,a=e.length;if(-1===t)a=-1;else for(;s<e.length;){const l=e[s++];if("number"==typeof l){if(l===t){a=-1;break}if(l>t){a=s-1;break}}}for(;s<e.length;){const l=e[s];if("number"==typeof l)break;if(l===n){if(null===r)return void(null!==o&&(e[s+1]=o));if(r===e[s+1])return void(e[s+2]=o)}s++,null!==r&&s++,null!==o&&s++}-1!==a&&(e.splice(a,0,t),s=a+1),e.splice(s++,0,n),null!==r&&e.splice(s++,0,r),null!==o&&e.splice(s++,0,o)}const qi="ng-template";function Co(e,t,n,r){let o=0;if(r){for(;o<t.length&&"string"==typeof t[o];o+=2)if("class"===t[o]&&-1!==ya(t[o+1].toLowerCase(),n,0))return!0}else if(xo(e))return!1;if(o=t.indexOf(1,o),o>-1){let s;for(;++o<t.length&&"string"==typeof(s=t[o]);)if(s.toLowerCase()===n)return!0}return!1}function xo(e){return 4===e.type&&e.value!==qi}function _(e,t,n){return t===(4!==e.type||n?e.value:qi)}function w(e,t,n){let r=4;const o=e.attrs,s=null!==o?function Gt(e){for(let t=0;t<e.length;t++)if(Wi(e[t]))return t;return e.length}(o):0;let a=!1;for(let l=0;l<t.length;l++){const d=t[l];if("number"!=typeof d){if(!a)if(4&r){if(r=2|1&r,""!==d&&!_(e,d,n)||""===d&&1===t.length){if(v(r))return!1;a=!0}}else if(8&r){if(null===o||!Co(e,o,d,n)){if(v(r))return!1;a=!0}}else{const D=t[++l],S=P(d,o,xo(e),n);if(-1===S){if(v(r))return!1;a=!0;continue}if(""!==D){let k;if(k=S>s?"":o[S+1].toLowerCase(),2&r&&D!==k){if(v(r))return!1;a=!0}}}}else{if(!a&&!v(r)&&!v(d))return!1;if(a&&v(d))continue;a=!1,r=d|1&r}}return v(r)||a}function v(e){return!(1&e)}function P(e,t,n,r){if(null===t)return-1;let o=0;if(r||!n){let s=!1;for(;o<t.length;){const a=t[o];if(a===e)return o;if(3===a||6===a)s=!0;else{if(1===a||2===a){let l=t[++o];for(;"string"==typeof l;)l=t[++o];continue}if(4===a)break;if(0===a){o+=4;continue}}o+=s?1:2}return-1}return function nn(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function re(e,t,n=!1){for(let r=0;r<t.length;r++)if(w(e,t[r],n))return!0;return!1}function Eo(e,t){e:for(let n=0;n<t.length;n++){const r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function bo(e,t){return e?":not("+t.trim()+")":t}function Xo(e){let t=e[0],n=1,r=2,o="",s=!1;for(;n<e.length;){let a=e[n];if("string"==typeof a)if(2&r){const l=e[++n];o+="["+a+(l.length>0?'="'+l+'"':"")+"]"}else 8&r?o+="."+a:4&r&&(o+=" "+a);else""!==o&&!v(a)&&(t+=bo(s,o),o=""),r=a,s=s||!v(r);n++}return""!==o&&(t+=bo(s,o)),t}function Da(e){return yt(()=>{const t=Yi(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Pr.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||co.Emulated,styles:e.styles||un,_:null,schemas:e.schemas||null,tView:null,id:""};Io(n);const r=e.dependencies;return n.directiveDefs=Ai(r,!1),n.pipeDefs=Ai(r,!0),n.id=function Ll(e){let t=0;const n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const o of n)t=Math.imul(31,t)+o.charCodeAt(0)|0;return t+=2147483648,"c"+t}(n),n})}function Fl(e){return sn(e)||or(e)}function Ca(e){return null!==e}function Es(e){return yt(()=>({type:e.type,bootstrap:e.bootstrap||un,declarations:e.declarations||un,imports:e.imports||un,exports:e.exports||un,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Ea(e,t){if(null==e)return qr;const n={};for(const r in e)if(e.hasOwnProperty(r)){const o=e[r];let s,a,l=Lr.None;Array.isArray(o)?(l=o[0],s=o[1],a=o[2]??s):(s=o,a=o),t?(n[s]=l!==Lr.None?[r,l]:r,t[s]=a):n[s]=r}return n}function ba(e){return yt(()=>{const t=Yi(e);return Io(t),t})}function Qi(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function sn(e){return e[fe]||null}function or(e){return e[qe]||null}function Jn(e){return e[rt]||null}function Ur(e){const t=sn(e)||or(e)||Jn(e);return null!==t&&t.standalone}function pr(e,t){const n=e[J]||null;if(!n&&!0===t)throw new Error(`Type ${Mt(e)} does not have '\u0275mod' property.`);return n}function Yi(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||qr,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||un,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Ea(e.inputs,t),outputs:Ea(e.outputs),debugInfo:null}}function Io(e){e.features?.forEach(t=>t(e))}function Ai(e,t){if(!e)return null;const n=t?Jn:Fl;return()=>("function"==typeof e?e():e).map(r=>n(r)).filter(Ca)}function Ri(...e){return{\u0275providers:Oi(0,e),\u0275fromNgModule:!0}}function Oi(e,...t){const n=[],r=new Set;let o;const s=a=>{n.push(a)};return ar(t,a=>{const l=a;Ji(l,s,[],r)&&(o||=[],o.push(l))}),void 0!==o&&xi(o,s),n}function xi(e,t){for(let n=0;n<e.length;n++){const{ngModule:r,providers:o}=e[n];bs(o,s=>{t(s,r)})}}function Ji(e,t,n,r){if(!(e=le(e)))return!1;let o=null,s=Xn(e);const a=!s&&sn(e);if(s||a){if(a&&!a.standalone)return!1;o=e}else{const d=e.ngModule;if(s=Xn(d),!s)return!1;o=d}const l=r.has(o);if(a){if(l)return!1;if(r.add(o),a.dependencies){const d="function"==typeof a.dependencies?a.dependencies():a.dependencies;for(const D of d)Ji(D,t,n,r)}}else{if(!s)return!1;{if(null!=s.imports&&!l){let D;r.add(o);try{ar(s.imports,S=>{Ji(S,t,n,r)&&(D||=[],D.push(S))})}finally{}void 0!==D&&xi(D,t)}if(!l){const D=Kr(o)||(()=>new o);t({provide:o,useFactory:D,deps:un},o),t({provide:Mi,useValue:o,multi:!0},o),t({provide:Ro,useValue:()=>qn(o),multi:!0},o)}const d=s.providers;if(null!=d&&!l){const D=e;bs(d,S=>{t(S,D)})}}}return o!==e&&void 0!==e.providers}function bs(e,t){for(let n of e)U(n)&&(n=n.\u0275providers),Array.isArray(n)?bs(n,t):t(n)}const Vl=Vt({provide:String,useValue:Vt});function Is(e){return null!==e&&"object"==typeof e&&Vl in e}function Fo(e){return"function"==typeof e}const es=new Bt(""),ts={},wa={};let _s;function Pi(){return void 0===_s&&(_s=new Oo),_s}class Qr{}class Ko extends Qr{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,ws(t,a=>this.processProvider(a)),this.records.set(Si,fi(void 0,this)),o.has("environment")&&this.records.set(Qr,fi(void 0,this));const s=this.records.get(es);null!=s&&"string"==typeof s.value&&this.scopes.add(s.value),this.injectorDefTypes=new Set(this.get(Mi,un,Qe.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;const t=L(null);try{for(const r of this._ngOnDestroyHooks)r.ngOnDestroy();const n=this._onDestroyHooks;this._onDestroyHooks=[];for(const r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),L(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();const n=jn(this),r=Ot(void 0);try{return t()}finally{jn(n),Ot(r)}}get(t,n=m,r=Qe.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(x))return t[x](this);r=Vr(r);const s=jn(this),a=Ot(void 0);try{if(!(r&Qe.SkipSelf)){let d=this.records.get(t);if(void 0===d){const D=function Hl(e){return"function"==typeof e||"object"==typeof e&&e instanceof Bt}(t)&&Vn(t);d=D&&this.injectableDefInScope(D)?fi(Sa(t),ts):null,this.records.set(t,d)}if(null!=d)return this.hydrate(t,d)}return(r&Qe.Self?Pi():this.parent).get(t,n=r&Qe.Optional&&n===m?null:n)}catch(l){if("NullInjectorError"===l.name){if((l[oe]=l[oe]||[]).unshift(Mt(t)),s)throw l;return function To(e,t,n,r){const o=e[oe];throw t[Xr]&&o.unshift(t[Xr]),e.message=function Ii(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let o=Mt(t);if(Array.isArray(t))o=t.map(Mt).join(" -> ");else if("object"==typeof t){let s=[];for(let a in t)if(t.hasOwnProperty(a)){let l=t[a];s.push(a+":"+("string"==typeof l?JSON.stringify(l):Mt(l)))}o=`{${s.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Kn,"\n  ")}`}("\n"+e.message,o,n,r),e.ngTokenPath=o,e[oe]=null,e}(l,t,"R3InjectorError",this.source)}throw l}finally{Ot(a),jn(s)}}resolveInjectorInitializers(){const t=L(null),n=jn(this),r=Ot(void 0);try{const s=this.get(Ro,un,Qe.Self);for(const a of s)a()}finally{jn(n),Ot(r),L(t)}}toString(){const t=[],n=this.records;for(const r of n.keys())t.push(Mt(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new Ae(205,!1)}processProvider(t){let n=Fo(t=le(t))?t:le(t&&t.provide);const r=function Ul(e){return Is(e)?fi(void 0,e.useValue):fi(Ma(e),ts)}(t);if(!Fo(t)&&!0===t.multi){let o=this.records.get(n);o||(o=fi(void 0,ts,!0),o.factory=()=>Ho(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){const r=L(null);try{return n.value===ts&&(n.value=wa,n.value=n.factory()),"object"==typeof n.value&&n.value&&function jl(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{L(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;const n=le(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){const n=this._onDestroyHooks.indexOf(t);-1!==n&&this._onDestroyHooks.splice(n,1)}}function Sa(e){const t=Vn(e),n=null!==t?t.factory:Kr(e);if(null!==n)return n;if(e instanceof Bt)throw new Ae(204,!1);if(e instanceof Function)return function Bl(e){if(e.length>0)throw new Ae(204,!1);const n=function Nr(e){return e&&(e[Bn]||e[_n])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new Ae(204,!1)}function Ma(e,t,n){let r;if(Fo(e)){const o=le(e);return Kr(o)||Sa(o)}if(Is(e))r=()=>le(e.useValue);else if(function Ni(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...Ho(e.deps||[]));else if(function Ia(e){return!(!e||!e.useExisting)}(e))r=()=>qn(le(e.useExisting));else{const o=le(e&&(e.useClass||e.provide));if(!function Ta(e){return!!e.deps}(e))return Kr(o)||Sa(o);r=()=>new o(...Ho(e.deps))}return r}function fi(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function ws(e,t){for(const n of e)Array.isArray(n)?ws(n,t):n&&U(n)?ws(n.\u0275providers,t):t(n)}function _o(e,t){e instanceof Ko&&e.assertNotDestroyed();const r=jn(e),o=Ot(void 0);try{return t()}finally{jn(r),Ot(o)}}function Ss(){return void 0!==Dt()||null!=function jo(){return Ir}()}const gn=0,ct=1,St=2,En=3,Qn=4,Nn=5,lr=6,wo=7,Pn=8,Dn=9,uo=10,zt=11,qo=12,Gl=13,Fi=14,Gn=15,rs=16,os=17,ko=18,hi=19,Aa=20,pi=21,is=22,Fr=23,qt=25,zl=1,Lo=7,gi=9,Yn=10;var Ra=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(Ra||{});function kr(e){return Array.isArray(e)&&"object"==typeof e[zl]}function $r(e){return Array.isArray(e)&&!0===e[zl]}function Ts(e){return!!(4&e.flags)}function mi(e){return e.componentOffset>-1}function Oa(e){return!(1&~e.flags)}function So(e){return!!e.template}function Wl(e){return!!(512&e[St])}class R{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function W(e,t,n,r){null!==t?t.applyValueToInputSignal(t,r):e[n]=r}function Ne(){return we}function we(e){return e.type.prototype.ngOnChanges&&(e.setInput=en),dt}function dt(){const e=ir(this),t=e?.current;if(t){const n=e.previous;if(n===qr)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function en(e,t,n,r,o){const s=this.declaredInputs[r],a=ir(e)||function as(e,t){return e[an]=t}(e,{previous:qr,current:null}),l=a.current||(a.current={}),d=a.previous,D=d[s];l[s]=new R(D&&D.currentValue,n,d===qr),W(e,t,o,n)}Ne.ngInherit=!0;const an="__ngSimpleChanges__";function ir(e){return e[an]||null}const ro=function(e,t,n){};let Eh=!1;function Fn(e){for(;Array.isArray(e);)e=e[gn];return e}function Yr(e,t){return Fn(t[e.index])}function Fa(e,t){return e.data[t]}function fo(e,t){const n=t[e];return kr(n)?n:n[gn]}function bu(e){return!(128&~e[St])}function Yo(e,t){return null==t?null:e[t]}function bh(e){e[os]=0}function $C(e){1024&e[St]||(e[St]|=1024,bu(e)&&ka(e))}function Iu(e){return!!(9216&e[St]||e[Fr]?.dirty)}function _u(e){e[uo].changeDetectionScheduler?.notify(1),Iu(e)?ka(e):64&e[St]&&(function PC(){return Eh}()?(e[St]|=1024,ka(e)):e[uo].changeDetectionScheduler?.notify())}function ka(e){e[uo].changeDetectionScheduler?.notify();let t=cs(e);for(;null!==t&&!(8192&t[St])&&(t[St]|=8192,bu(t));)t=cs(t)}function Kl(e,t){if(!(256&~e[St]))throw new Ae(911,!1);null===e[pi]&&(e[pi]=[]),e[pi].push(t)}function cs(e){const t=e[En];return $r(t)?t[En]:t}const Kt={lFrame:Ph(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function wh(){return Kt.bindingsEnabled}function Rs(){return null!==Kt.skipHydrationRootTNode}function Je(){return Kt.lFrame.lView}function fn(){return Kt.lFrame.tView}function Sh(e){return Kt.lFrame.contextLView=e,e[Pn]}function Mh(e){return Kt.lFrame.contextLView=null,e}function Rn(){let e=Th();for(;null!==e&&64===e.type;)e=e.parent;return e}function Th(){return Kt.lFrame.currentTNode}function Zo(e,t){const n=Kt.lFrame;n.currentTNode=e,n.isParent=t}function Su(){return Kt.lFrame.isParent}function Mu(){Kt.lFrame.isParent=!1}function $o(){return Kt.lFrame.bindingIndex++}function qC(e,t){const n=Kt.lFrame;n.bindingIndex=n.bindingRootIndex=e,Tu(t)}function Tu(e){Kt.lFrame.currentDirectiveIndex=e}function Ru(){return Kt.lFrame.currentQueryIndex}function ql(e){Kt.lFrame.currentQueryIndex=e}function YC(e){const t=e[ct];return 2===t.type?t.declTNode:1===t.type?e[Nn]:null}function xh(e,t,n){if(n&Qe.SkipSelf){let o=t,s=e;for(;!(o=o.parent,null!==o||n&Qe.Host||(o=YC(s),null===o||(s=s[Fi],10&o.type))););if(null===o)return!1;t=o,e=s}const r=Kt.lFrame=Nh();return r.currentTNode=t,r.lView=e,!0}function Ou(e){const t=Nh(),n=e[ct];Kt.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Nh(){const e=Kt.lFrame,t=null===e?null:e.child;return null===t?Ph(e):t}function Ph(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function Fh(){const e=Kt.lFrame;return Kt.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const kh=Fh;function xu(){const e=Fh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function jr(){return Kt.lFrame.selectedIndex}function us(e){Kt.lFrame.selectedIndex=e}function zn(){const e=Kt.lFrame;return Fa(e.tView,e.selectedIndex)}let $h=!0;function $a(){return $h}function Jo(e){$h=e}function Ql(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const s=e.data[n].type.prototype,{ngAfterContentInit:a,ngAfterContentChecked:l,ngAfterViewInit:d,ngAfterViewChecked:D,ngOnDestroy:S}=s;a&&(e.contentHooks??=[]).push(-n,a),l&&((e.contentHooks??=[]).push(n,l),(e.contentCheckHooks??=[]).push(n,l)),d&&(e.viewHooks??=[]).push(-n,d),D&&((e.viewHooks??=[]).push(n,D),(e.viewCheckHooks??=[]).push(n,D)),null!=S&&(e.destroyHooks??=[]).push(n,S)}}function Yl(e,t,n){Vh(e,t,3,n)}function Zl(e,t,n,r){(3&e[St])===n&&Vh(e,t,n,r)}function Nu(e,t){let n=e[St];(3&n)===t&&(n&=16383,n+=1,e[St]=n)}function Vh(e,t,n,r){const s=r??-1,a=t.length-1;let l=0;for(let d=void 0!==r?65535&e[os]:0;d<a;d++)if("number"==typeof t[d+1]){if(l=t[d],null!=r&&l>=r)break}else t[d]<0&&(e[os]+=65536),(l<s||-1==s)&&(oE(e,n,t,d),e[os]=(**********&e[os])+d+2),d++}function Bh(e,t){ro(4,e,t);const n=L(null);try{t.call(e)}finally{L(n),ro(5,e,t)}}function oE(e,t,n,r){const o=n[r]<0,s=n[r+1],l=e[o?-n[r]:n[r]];o?e[St]>>14<e[os]>>16&&(3&e[St])===t&&(e[St]+=16384,Bh(l,s)):Bh(l,s)}const Os=-1;class Va{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function Fu(e){return e!==Os}function Ba(e){return 32767&e}function Ua(e,t){let n=function cE(e){return e>>16}(e),r=t;for(;n>0;)r=r[Fi],n--;return r}let ku=!0;function Jl(e){const t=ku;return ku=e,t}const Uh=255,jh=5;let uE=0;const ei={};function ec(e,t){const n=Hh(e,t);if(-1!==n)return n;const r=t[ct];r.firstCreatePass&&(e.injectorIndex=t.length,Lu(r.data,e),Lu(t,null),Lu(r.blueprint,null));const o=tc(e,t),s=e.injectorIndex;if(Fu(o)){const a=Ba(o),l=Ua(o,t),d=l[ct].data;for(let D=0;D<8;D++)t[s+D]=l[a+D]|d[a+D]}return t[s+8]=o,s}function Lu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Hh(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function tc(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;null!==o;){if(r=Yh(o),null===r)return Os;if(n++,o=o[Fi],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return Os}function $u(e,t,n){!function dE(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(b)&&(r=n[b]),null==r&&(r=n[b]=uE++);const o=r&Uh;t.data[e+(o>>jh)]|=1<<o}(e,t,n)}function Gh(e,t,n){if(n&Qe.Optional||void 0!==e)return e;ot()}function zh(e,t,n,r){if(n&Qe.Optional&&void 0===r&&(r=null),!(n&(Qe.Self|Qe.Host))){const o=e[Dn],s=Ot(void 0);try{return o?o.get(t,r,n&Qe.Optional):mn(t,r,n&Qe.Optional)}finally{Ot(s)}}return Gh(r,0,n)}function Wh(e,t,n,r=Qe.Default,o){if(null!==e){if(2048&t[St]&&!(r&Qe.Self)){const a=function mE(e,t,n,r,o){let s=e,a=t;for(;null!==s&&null!==a&&2048&a[St]&&!(512&a[St]);){const l=Xh(s,a,n,r|Qe.Self,ei);if(l!==ei)return l;let d=s.parent;if(!d){const D=a[Aa];if(D){const S=D.get(n,ei,r);if(S!==ei)return S}d=Yh(a),a=a[Fi]}s=d}return o}(e,t,n,r,ei);if(a!==ei)return a}const s=Xh(e,t,n,r,ei);if(s!==ei)return s}return zh(t,n,r,o)}function Xh(e,t,n,r,o){const s=function pE(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(b)?e[b]:void 0;return"number"==typeof t?t>=0?t&Uh:gE:t}(n);if("function"==typeof s){if(!xh(t,e,r))return r&Qe.Host?Gh(o,0,r):zh(t,n,r,o);try{let a;if(a=s(r),null!=a||r&Qe.Optional)return a;ot()}finally{kh()}}else if("number"==typeof s){let a=null,l=Hh(e,t),d=Os,D=r&Qe.Host?t[Gn][Nn]:null;for((-1===l||r&Qe.SkipSelf)&&(d=-1===l?tc(e,t):t[l+8],d!==Os&&qh(r,!1)?(a=t[ct],l=Ba(d),t=Ua(d,t)):l=-1);-1!==l;){const S=t[ct];if(Kh(s,l,S.data)){const k=hE(l,t,n,a,r,D);if(k!==ei)return k}d=t[l+8],d!==Os&&qh(r,t[ct].data[l+8]===D)&&Kh(s,l,t)?(a=S,l=Ba(d),t=Ua(d,t)):l=-1}}return o}function hE(e,t,n,r,o,s){const a=t[ct],l=a.data[e+8],S=nc(l,a,n,null==r?mi(l)&&ku:r!=a&&!!(3&l.type),o&Qe.Host&&s===l);return null!==S?ds(t,a,S,l):ei}function nc(e,t,n,r,o){const s=e.providerIndexes,a=t.data,l=1048575&s,d=e.directiveStart,S=s>>20,ee=o?l+S:e.directiveEnd;for(let ce=r?l:l+S;ce<ee;ce++){const Te=a[ce];if(ce<d&&n===Te||ce>=d&&Te.type===n)return ce}if(o){const ce=a[d];if(ce&&So(ce)&&ce.type===n)return d}return null}function ds(e,t,n,r){let o=e[n];const s=t.data;if(function iE(e){return e instanceof Va}(o)){const a=o;a.resolving&&function st(e,t){throw t&&t.join(" > "),new Ae(-200,e)}(function Q(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():G(e)}(s[n]));const l=Jl(a.canSeeViewProviders);a.resolving=!0;const D=a.injectImpl?Ot(a.injectImpl):null;xh(e,r,Qe.Default);try{o=e[n]=a.factory(void 0,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&function rE(e,t,n){const{ngOnChanges:r,ngOnInit:o,ngDoCheck:s}=t.type.prototype;if(r){const a=we(t);(n.preOrderHooks??=[]).push(e,a),(n.preOrderCheckHooks??=[]).push(e,a)}o&&(n.preOrderHooks??=[]).push(0-e,o),s&&((n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s))}(n,s[n],t)}finally{null!==D&&Ot(D),Jl(l),a.resolving=!1,kh()}}return o}function Kh(e,t,n){return!!(n[t+(e>>jh)]&1<<e)}function qh(e,t){return!(e&Qe.Self||e&Qe.Host&&t)}class _r{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Wh(this._tNode,this._lView,t,Vr(r),n)}}function gE(){return new _r(Rn(),Je())}function Qh(e){return yt(()=>{const t=e.prototype.constructor,n=t[He]||Vu(t),r=Object.prototype;let o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){const s=o[He]||Vu(o);if(s&&s!==n)return s;o=Object.getPrototypeOf(o)}return s=>new s})}function Vu(e){return Se(e)?()=>{const t=Vu(le(e));return t&&t()}:Kr(e)}function Yh(e){const t=e[ct],n=t.type;return 2===n?t.declTNode:1===n?e[Nn]:null}function rc(e){return function fE(e,t){if("class"===t)return e.classes;if("style"===t)return e.styles;const n=e.attrs;if(n){const r=n.length;let o=0;for(;o<r;){const s=n[o];if(Wi(s))break;if(0===s)o+=2;else if("number"==typeof s)for(o++;o<r&&"string"==typeof n[o];)o++;else{if(s===t)return n[o+1];o+=2}}}return null}(Rn(),e)}function np(e,t=null,n=null,r){const o=rp(e,t,n,r);return o.resolveInjectorInitializers(),o}function rp(e,t=null,n=null,r,o=new Set){const s=[n||un,Ri(e)];return r=r||("object"==typeof e?void 0:Mt(e)),new Ko(s,t||Pi(),r||null,o)}let ho=(()=>{class e{static{this.THROW_IF_NOT_FOUND=m}static{this.NULL=new Oo}static create(n,r){if(Array.isArray(n))return np({name:""},r,n,"");{const o=n.name??"";return np({name:o},n.parent,n.providers,o)}}static{this.\u0275prov=Yt({token:e,providedIn:"any",factory:()=>qn(Si)})}static{this.__NG_ELEMENT_ID__=-1}}return e})();function Uu(e){return e.ngOriginalError}class Ci{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Uu(t);for(;n&&Uu(n);)n=Uu(n);return n||null}}const ip=new Bt("",{providedIn:"root",factory:()=>Ut(Ci).handleError.bind(void 0)});let Ps=(()=>{class e{static{this.__NG_ELEMENT_ID__=wE}static{this.__NG_ENV_ID__=n=>n}}return e})();class _E extends Ps{constructor(t){super(),this._lView=t}onDestroy(t){return Kl(this._lView,t),()=>function wu(e,t){if(null===e[pi])return;const n=e[pi].indexOf(t);-1!==n&&e[pi].splice(n,1)}(this._lView,t)}}function wE(){return new _E(Je())}function SE(){return Fs(Rn(),Je())}function Fs(e,t){return new ja(Yr(e,t))}let ja=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=SE}}return e})();function ap(e){return e instanceof ja?e.nativeElement:e}function ju(e){return t=>{setTimeout(e,void 0,t)}}const ti=class ME extends xt.B{constructor(t=!1){super(),this.destroyRef=void 0,this.__isAsync=t,Ss()&&(this.destroyRef=Ut(Ps,{optional:!0})??void 0)}emit(t){const n=L(null);try{super.next(t)}finally{L(n)}}subscribe(t,n,r){let o=t,s=n||(()=>null),a=r;if(t&&"object"==typeof t){const d=t;o=d.next?.bind(d),s=d.error?.bind(d),a=d.complete?.bind(d)}this.__isAsync&&(s=ju(s),o&&(o=ju(o)),a&&(a=ju(a)));const l=super.subscribe({next:o,error:s,complete:a});return t instanceof Pt.yU&&t.add(l),l}};function TE(){return this._results[Symbol.iterator]()}class Hu{get changes(){return this._changes??=new ti}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;const n=Hu.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=TE)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;const r=function fr(e){return e.flat(Number.POSITIVE_INFINITY)}(t);(this._changesDetected=!function ui(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],s=t[r];if(n&&(o=n(o),s=n(s)),s!==o)return!1}return!0}(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){void 0!==this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){void 0!==this._changes&&(this._changes.complete(),this._changes.unsubscribe())}}function ic(e){return!(128&~e.flags)}const Gu=new Map;let RE=0;const Wu="__ngContext__";function Hr(e,t){kr(t)?(e[Wu]=t[hi],function xE(e){Gu.set(e[hi],e)}(t)):e[Wu]=t}function vp(e){return Dp(e[qo])}function yp(e){return Dp(e[Qn])}function Dp(e){for(;null!==e&&!$r(e);)e=e[Qn];return e}let Xu;function qE(e){Xu=e}const Mp=new Bt("",{providedIn:"root",factory:()=>QE}),QE="ng",Tp=new Bt(""),Ku=new Bt("",{providedIn:"platform",factory:()=>"unknown"}),YE=new Bt("",{providedIn:"root",factory:()=>function ki(){if(void 0!==Xu)return Xu;if(typeof document<"u")return document;throw new Ae(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let Ap=()=>null;function nd(e,t,n=!1){return Ap(e,t,n)}const Pp=new Bt("",{providedIn:"root",factory:()=>!1});let gc;function $p(e){return function ad(){if(void 0===gc&&(gc=null,Nt.trustedTypes))try{gc=Nt.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return gc}()?.createScriptURL(e)||e}class Vp{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${he})`}}function Li(e){return e instanceof Vp?e.changingThisBreaksApplicationSecurity:e}function qa(e,t){const n=function mb(e){return e instanceof Vp&&e.getTypeName()||null}(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${he})`)}return n===t}const Cb=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;var Vs=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Vs||{});function Kp(e){const t=Ya();return t?t.sanitize(Vs.URL,e)||"":qa(e,"URL")?Li(e):function ld(e){return(e=String(e)).match(Cb)?e:"unsafe:"+e}(G(e))}function qp(e){const t=Ya();if(t)return $p(t.sanitize(Vs.RESOURCE_URL,e)||"");if(qa(e,"ResourceURL"))return $p(Li(e));throw new Ae(904,!1)}function Qp(e,t,n){return function Fb(e,t){return"src"===t&&("embed"===e||"frame"===e||"iframe"===e||"media"===e||"script"===e)||"href"===t&&("base"===e||"link"===e)?qp:Kp}(t,n)(e)}function Ya(){const e=Je();return e&&e[uo].sanitizer}const kb=/^>|^->|<!--|-->|--!>|<!-$/g,Lb=/(<|>)/g,$b="\u200b$1\u200b";function po(e){return e instanceof Function?e():e}var Dc=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Dc||{});let gd;function md(e,t){return gd(e,t)}function Us(e,t,n,r,o){if(null!=r){let s,a=!1;$r(r)?s=r:kr(r)&&(a=!0,r=r[gn]);const l=Fn(r);0===e&&null!==n?null==o?lg(t,n,l):fs(t,n,l,o||null,!0):1===e&&null!==n?fs(t,n,l,o||null,!0):2===e?function el(e,t,n){const r=bc(e,t);r&&function r0(e,t,n,r){e.removeChild(t,n,r)}(e,r,t,n)}(t,l,a):3===e&&t.destroyNode(l),null!=s&&function a0(e,t,n,r,o){const s=n[Lo];s!==Fn(n)&&Us(t,e,r,s,o);for(let l=Yn;l<n.length;l++){const d=n[l];_c(d[ct],d,e,t,r,s)}}(t,e,s,n,o)}}function yd(e,t){return e.createComment(function Yp(e){return e.replace(kb,t=>t.replace(Lb,$b))}(t))}function Cc(e,t,n){return e.createElement(t,n)}function ig(e,t){t[uo].changeDetectionScheduler?.notify(1),_c(e,t,t[zt],2,null,null)}function sg(e,t){const n=e[gi],r=n.indexOf(t);n.splice(r,1)}function Za(e,t){if(e.length<=Yn)return;const n=Yn+t,r=e[n];if(r){const o=r[rs];null!==o&&o!==e&&sg(o,r),t>0&&(e[n-1][Qn]=r[Qn]);const s=yo(e,Yn+t);!function Qb(e,t){ig(e,t),t[gn]=null,t[Nn]=null}(r[ct],r);const a=s[ko];null!==a&&a.detachView(s[ct]),r[En]=null,r[Qn]=null,r[St]&=-129}return r}function Ec(e,t){if(!(256&t[St])){const n=t[zt];n.destroyNode&&_c(e,t,n,3,null,null),function Zb(e){let t=e[qo];if(!t)return Dd(e[ct],e);for(;t;){let n=null;if(kr(t))n=t[qo];else{const r=t[Yn];r&&(n=r)}if(!n){for(;t&&!t[Qn]&&t!==e;)kr(t)&&Dd(t[ct],t),t=t[En];null===t&&(t=e),kr(t)&&Dd(t[ct],t),n=t&&t[Qn]}t=n}}(t)}}function Dd(e,t){if(256&t[St])return;const n=L(null);try{t[St]&=-129,t[St]|=256,t[Fr]&&function Ee(e){if(V(e),Ge(e))for(let t=0;t<e.producerNode.length;t++)ke(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}(t[Fr]),function n0(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const o=t[n[r]];if(!(o instanceof Va)){const s=n[r+1];if(Array.isArray(s))for(let a=0;a<s.length;a+=2){const l=o[s[a]],d=s[a+1];ro(4,l,d);try{d.call(l)}finally{ro(5,l,d)}}else{ro(4,o,s);try{s.call(o)}finally{ro(5,o,s)}}}}}(e,t),function t0(e,t){const n=e.cleanup,r=t[wo];if(null!==n)for(let s=0;s<n.length-1;s+=2)if("string"==typeof n[s]){const a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else n[s].call(r[n[s+1]]);null!==r&&(t[wo]=null);const o=t[pi];if(null!==o){t[pi]=null;for(let s=0;s<o.length;s++)(0,o[s])()}}(e,t),1===t[ct].type&&t[zt].destroy();const r=t[rs];if(null!==r&&$r(t[En])){r!==t[En]&&sg(r,t);const o=t[ko];null!==o&&o.detachView(e)}!function NE(e){Gu.delete(e[hi])}(t)}finally{L(n)}}function Cd(e,t,n){return function ag(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[gn];{const{componentOffset:o}=r;if(o>-1){const{encapsulation:s}=e.data[r.directiveStart+o];if(s===co.None||s===co.Emulated)return null}return Yr(r,n)}}(e,t.parent,n)}function fs(e,t,n,r,o){e.insertBefore(t,n,r,o)}function lg(e,t,n){e.appendChild(t,n)}function cg(e,t,n,r,o){null!==r?fs(e,t,n,r,o):lg(e,t,n)}function bc(e,t){return e.parentNode(t)}function ug(e,t,n){return fg(e,t,n)}let Ed,fg=function dg(e,t,n){return 40&e.type?Yr(e,n):null};function Ic(e,t,n,r){const o=Cd(e,r,t),s=t[zt],l=ug(r.parent||t[Nn],r,t);if(null!=o)if(Array.isArray(n))for(let d=0;d<n.length;d++)cg(s,o,n[d],l,!1);else cg(s,o,n,l,!1);void 0!==Ed&&Ed(s,r,t,n,o)}function Ja(e,t){if(null!==t){const n=t.type;if(3&n)return Yr(t,e);if(4&n)return bd(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return Ja(e,r);{const o=e[t.index];return $r(o)?bd(-1,o):Fn(o)}}if(32&n)return md(t,e)()||Fn(e[t.index]);{const r=pg(e,t);return null!==r?Array.isArray(r)?r[0]:Ja(cs(e[Gn]),r):Ja(e,t.next)}}return null}function pg(e,t){return null!==t?e[Gn][Nn].projection[t.projection]:null}function bd(e,t){const n=Yn+e+1;if(n<t.length){const r=t[n],o=r[ct].firstChild;if(null!==o)return Ja(r,o)}return t[Lo]}function Id(e,t,n,r,o,s,a){for(;null!=n;){const l=r[n.index],d=n.type;if(a&&0===t&&(l&&Hr(Fn(l),r),n.flags|=2),32&~n.flags)if(8&d)Id(e,t,n.child,r,o,s,!1),Us(t,e,o,l,s);else if(32&d){const D=md(n,r);let S;for(;S=D();)Us(t,e,o,S,s);Us(t,e,o,l,s)}else 16&d?mg(e,t,r,n,o,s):Us(t,e,o,l,s);n=a?n.projectionNext:n.next}}function _c(e,t,n,r,o,s){Id(n,r,e.firstChild,t,o,s,!1)}function mg(e,t,n,r,o,s){const a=n[Gn],d=a[Nn].projection[r.projection];if(Array.isArray(d))for(let D=0;D<d.length;D++)Us(t,e,o,d[D],s);else{let D=d;const S=a[En];ic(r)&&(D.flags|=128),Id(e,t,D,S,o,s,!0)}}function vg(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function yg(e,t,n){const{mergedAttrs:r,classes:o,styles:s}=n;null!==r&&Ti(e,t,r),null!==o&&vg(e,t,o),null!==s&&function c0(e,t,n){e.setAttribute(t,"style",n)}(e,t,s)}const rn={};function Dg(e=1){Cg(fn(),Je(),jr()+e,!1)}function Cg(e,t,n,r){if(!r)if(3&~t[St]){const s=e.preOrderHooks;null!==s&&Zl(t,s,0,n)}else{const s=e.preOrderCheckHooks;null!==s&&Yl(t,s,n)}us(n)}function js(e,t=Qe.Default){const n=Je();return null===n?qn(e,t):Wh(Rn(),n,le(e),t)}function Eg(){throw new Error("invalid")}function bg(e,t,n,r,o,s){const a=L(null);try{let l=null;o&Lr.SignalBased&&(l=t[r][Ie]),null!==l&&void 0!==l.transformFn&&(s=l.transformFn(s)),o&Lr.HasDecoratorInputTransform&&(s=e.inputTransforms[r].call(t,s)),null!==e.setInput?e.setInput(t,l,s,n,r):W(t,l,r,s)}finally{L(a)}}function wc(e,t,n,r,o,s,a,l,d,D,S){const k=t.blueprint.slice();return k[gn]=o,k[St]=204|r,(null!==D||e&&2048&e[St])&&(k[St]|=2048),bh(k),k[En]=k[Fi]=e,k[Pn]=n,k[uo]=a||e&&e[uo],k[zt]=l||e&&e[zt],k[Dn]=d||e&&e[Dn]||null,k[Nn]=s,k[hi]=function OE(){return RE++}(),k[lr]=S,k[Aa]=D,k[Gn]=2==t.type?e[Gn]:k,k}function Hs(e,t,n,r,o){let s=e.data[t];if(null===s)s=function _d(e,t,n,r,o){const s=Th(),a=Su(),d=e.data[t]=function v0(e,t,n,r,o,s){let a=t?t.injectorIndex:-1,l=0;return Rs()&&(l|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:a,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:l,providerIndexes:0,value:o,attrs:s,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,a?s:s&&s.parent,n,t,r,o);return null===e.firstChild&&(e.firstChild=d),null!==s&&(a?null==s.child&&null!==d.parent&&(s.child=d):null===s.next&&(s.next=d,d.prev=s)),d}(e,t,n,r,o),function KC(){return Kt.lFrame.inI18n}()&&(s.flags|=32);else if(64&s.type){s.type=n,s.value=r,s.attrs=o;const a=function La(){const e=Kt.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();s.injectorIndex=null===a?-1:a.injectorIndex}return Zo(s,!0),s}function tl(e,t,n,r){if(0===n)return-1;const o=t.length;for(let s=0;s<n;s++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ig(e,t,n,r,o){const s=jr(),a=2&r;try{us(-1),a&&t.length>qt&&Cg(e,t,qt,!1),ro(a?2:0,o),n(r,o)}finally{us(s),ro(a?3:1,o)}}function wd(e,t,n){if(Ts(t)){const r=L(null);try{const s=t.directiveEnd;for(let a=t.directiveStart;a<s;a++){const l=e.data[a];l.contentQueries&&l.contentQueries(1,n[a],a)}}finally{L(r)}}}function Sd(e,t,n){wh()&&(function _0(e,t,n,r){const o=n.directiveStart,s=n.directiveEnd;mi(n)&&function O0(e,t,n){const r=Yr(t,e),o=_g(n);let a=16;n.signals?a=4096:n.onPush&&(a=64);const l=Sc(e,wc(e,o,null,a,r,t,null,e[uo].rendererFactory.createRenderer(r,n),null,null,null));e[t.index]=l}(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||ec(n,t),Hr(r,t);const a=n.initialInputs;for(let l=o;l<s;l++){const d=e.data[l],D=ds(t,e,l,n);Hr(D,t),null!==a&&x0(0,l-o,D,d,0,a),So(d)&&(fo(n.index,t)[Pn]=ds(t,e,l,n))}}(e,t,n,Yr(n,t)),!(64&~n.flags)&&Ag(e,t,n))}function Md(e,t,n=Yr){const r=t.localNames;if(null!==r){let o=t.index+1;for(let s=0;s<r.length;s+=2){const a=r[s+1],l=-1===a?n(t,e):e[a];e[o++]=l}}}function _g(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=Td(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Td(e,t,n,r,o,s,a,l,d,D,S){const k=qt+r,ee=k+o,ce=function d0(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:rn);return n}(k,ee),Te="function"==typeof D?D():D;return ce[ct]={type:e,blueprint:ce,template:n,queries:null,viewQuery:l,declTNode:t,data:ce.slice().fill(null,k),bindingStartIndex:k,expandoStartIndex:ee,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof s?s():s,pipeRegistry:"function"==typeof a?a():a,firstChild:null,schemas:d,consts:Te,incompleteFirstPass:!1,ssrId:S}}let wg=()=>null;function Sg(e,t,n,r,o){for(let s in t){if(!t.hasOwnProperty(s))continue;const a=t[s];if(void 0===a)continue;r??={};let l,d=Lr.None;Array.isArray(a)?(l=a[0],d=a[1]):l=a;let D=s;if(null!==o){if(!o.hasOwnProperty(s))continue;D=o[s]}0===e?Mg(r,n,D,l,d):Mg(r,n,D,l)}return r}function Mg(e,t,n,r,o){let s;e.hasOwnProperty(n)?(s=e[n]).push(t,r):s=e[n]=[t,r],void 0!==o&&s.push(o)}function Ad(e,t,n,r){if(wh()){const o=null===r?null:{"":-1},s=function S0(e,t){const n=e.directiveRegistry;let r=null,o=null;if(n)for(let s=0;s<n.length;s++){const a=n[s];if(re(t,a.selectors,!1))if(r||(r=[]),So(a))if(null!==a.findHostDirectiveDefs){const l=[];o=o||new Map,a.findHostDirectiveDefs(a,l,o),r.unshift(...l,a),Rd(e,t,l.length)}else r.unshift(a),Rd(e,t,0);else o=o||new Map,a.findHostDirectiveDefs?.(a,r,o),r.push(a)}return null===r?null:[r,o]}(e,n);let a,l;null===s?a=l=null:[a,l]=s,null!==a&&Tg(e,t,n,a,o,l),o&&function M0(e,t,n){if(t){const r=e.localNames=[];for(let o=0;o<t.length;o+=2){const s=n[t[o+1]];if(null==s)throw new Ae(-301,!1);r.push(t[o],s)}}}(n,r,o)}n.mergedAttrs=Do(n.mergedAttrs,n.attrs)}function Tg(e,t,n,r,o,s){for(let D=0;D<r.length;D++)$u(ec(n,t),e,r[D].type);!function A0(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,r.length);for(let D=0;D<r.length;D++){const S=r[D];S.providersResolver&&S.providersResolver(S)}let a=!1,l=!1,d=tl(e,t,r.length,null);for(let D=0;D<r.length;D++){const S=r[D];n.mergedAttrs=Do(n.mergedAttrs,S.hostAttrs),R0(e,n,t,d,S),T0(d,S,o),null!==S.contentQueries&&(n.flags|=4),(null!==S.hostBindings||null!==S.hostAttrs||0!==S.hostVars)&&(n.flags|=64);const k=S.type.prototype;!a&&(k.ngOnChanges||k.ngOnInit||k.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),a=!0),!l&&(k.ngOnChanges||k.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),d++}!function y0(e,t,n){const o=t.directiveEnd,s=e.data,a=t.attrs,l=[];let d=null,D=null;for(let S=t.directiveStart;S<o;S++){const k=s[S],ee=n?n.get(k):null,Te=ee?ee.outputs:null;d=Sg(0,k.inputs,S,d,ee?ee.inputs:null),D=Sg(1,k.outputs,S,D,Te);const Ye=null===d||null===a||xo(t)?null:N0(d,S,a);l.push(Ye)}null!==d&&(d.hasOwnProperty("class")&&(t.flags|=8),d.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=l,t.inputs=d,t.outputs=D}(e,n,s)}function Ag(e,t,n){const r=n.directiveStart,o=n.directiveEnd,s=n.index,a=function QC(){return Kt.lFrame.currentDirectiveIndex}();try{us(s);for(let l=r;l<o;l++){const d=e.data[l],D=t[l];Tu(l),(null!==d.hostBindings||0!==d.hostVars||null!==d.hostAttrs)&&w0(d,D)}}finally{us(-1),Tu(a)}}function w0(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function Rd(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function T0(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;So(t)&&(n[""]=e)}}function R0(e,t,n,r,o){e.data[r]=o;const s=o.factory||(o.factory=Kr(o.type)),a=new Va(s,So(o),js);e.blueprint[r]=a,n[r]=a,function b0(e,t,n,r,o){const s=o.hostBindings;if(s){let a=e.hostBindingOpCodes;null===a&&(a=e.hostBindingOpCodes=[]);const l=~t.index;(function I0(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(a)!=l&&a.push(l),a.push(n,r,s)}}(e,t,r,tl(e,n,o.hostVars,rn),o)}function ni(e,t,n,r,o,s){const a=Yr(e,t);!function Od(e,t,n,r,o,s,a){if(null==s)e.removeAttribute(t,o,n);else{const l=null==a?G(s):a(s,r||"",o);e.setAttribute(t,o,l,n)}}(t[zt],a,s,e.value,n,r,o)}function x0(e,t,n,r,o,s){const a=s[t];if(null!==a)for(let l=0;l<a.length;)bg(r,n,a[l++],a[l++],a[l++],a[l++])}function N0(e,t,n){let r=null,o=0;for(;o<n.length;){const s=n[o];if(0!==s)if(5!==s){if("number"==typeof s)break;if(e.hasOwnProperty(s)){null===r&&(r=[]);const a=e[s];for(let l=0;l<a.length;l+=3)if(a[l]===t){r.push(s,a[l+1],a[l+2],n[o+1]);break}}o+=2}else o+=2;else o+=4}return r}function Rg(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Og(e,t){const n=e.contentQueries;if(null!==n){const r=L(null);try{for(let o=0;o<n.length;o+=2){const a=n[o+1];if(-1!==a){const l=e.data[a];ql(n[o]),l.contentQueries(2,t[a],a)}}}finally{L(r)}}}function Sc(e,t){return e[qo]?e[Gl][Qn]=t:e[qo]=t,e[Gl]=t,t}function xd(e,t,n){ql(0);const r=L(null);try{t(e,n)}finally{L(r)}}function xg(e){return e[wo]||(e[wo]=[])}function Ng(e){return e.cleanup||(e.cleanup=[])}function Mc(e,t){const n=e[Dn],r=n?n.get(Ci,null):null;r&&r.handleError(t)}function Nd(e,t,n,r,o){for(let s=0;s<n.length;){const a=n[s++],l=n[s++],d=n[s++];bg(e.data[a],t[a],r,l,d,o)}}function P0(e,t){const n=fo(t,e),r=n[ct];!function F0(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(r,n);const o=n[gn];null!==o&&null===n[lr]&&(n[lr]=nd(o,n[Dn])),Pd(r,n,n[Pn])}function Pd(e,t,n){Ou(t);try{const r=e.viewQuery;null!==r&&xd(1,r,n);const o=e.template;null!==o&&Ig(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[ko]?.finishViewCreation(e),e.staticContentQueries&&Og(e,t),e.staticViewQueries&&xd(2,e.viewQuery,n);const s=e.components;null!==s&&function k0(e,t){for(let n=0;n<t.length;n++)P0(e,t[n])}(t,s)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[St]&=-5,xu()}}function Gs(e,t){return!t||null===t.firstChild||ic(e)}function rl(e,t,n,r=!0){const o=t[ct];if(function Jb(e,t,n,r){const o=Yn+r,s=n.length;r>0&&(n[o-1][Qn]=t),r<s-Yn?(t[Qn]=n[o],wi(n,Yn+r,t)):(n.push(t),t[Qn]=null),t[En]=n;const a=t[rs];null!==a&&n!==a&&function e0(e,t){const n=e[gi];t[Gn]!==t[En][En][Gn]&&(e[St]|=Ra.HasTransplantedViews),null===n?e[gi]=[t]:n.push(t)}(a,t);const l=t[ko];null!==l&&l.insertView(e),_u(t),t[St]|=128}(o,t,e,n),r){const a=bd(n,e),l=t[zt],d=bc(l,e[Lo]);null!==d&&function Yb(e,t,n,r,o,s){r[gn]=o,r[Nn]=t,_c(e,r,n,1,o,s)}(o,e[Nn],l,t,d,a)}const s=t[lr];null!==s&&null!==s.firstChild&&(s.firstChild=null)}function ol(e,t,n,r,o=!1){for(;null!==n;){const s=t[n.index];null!==s&&r.push(Fn(s)),$r(s)&&kg(s,r);const a=n.type;if(8&a)ol(e,t,n.child,r);else if(32&a){const l=md(n,t);let d;for(;d=l();)r.push(d)}else if(16&a){const l=pg(t,n);if(Array.isArray(l))r.push(...l);else{const d=cs(t[Gn]);ol(d[ct],d,l,r,!0)}}n=o?n.projectionNext:n.next}return r}function kg(e,t){for(let n=Yn;n<e.length;n++){const r=e[n],o=r[ct].firstChild;null!==o&&ol(r[ct],r,o,t)}e[Lo]!==e[gn]&&t.push(e[Lo])}let Lg=[];const B0={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{},consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{ka(e.lView)},consumerOnSignalRead(){this.lView[Fr]=this}},$g=100;function Tc(e,t=!0,n=0){const r=e[uo],o=r.rendererFactory;o.begin?.();try{!function U0(e,t){kd(e,t);let n=0;for(;Iu(e);){if(n===$g)throw new Ae(103,!1);n++,kd(e,1)}}(e,n)}catch(a){throw t&&Mc(e,a),a}finally{o.end?.(),r.inlineEffectRunner?.flush()}}function j0(e,t,n,r){const o=t[St];if(!(256&~o))return;t[uo].inlineEffectRunner?.flush(),Ou(t);let a=null,l=null;(function H0(e){return 2!==e.type})(e)&&(l=function L0(e){return e[Fr]??function $0(e){const t=Lg.pop()??Object.create(B0);return t.lView=e,t}(e)}(t),a=function se(e){return e&&(e.nextProducerIndex=0),L(e)}(l));try{bh(t),function Rh(e){return Kt.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&Ig(e,t,n,2,r);const d=!(3&~o);if(d){const k=e.preOrderCheckHooks;null!==k&&Yl(t,k,null)}else{const k=e.preOrderHooks;null!==k&&Zl(t,k,0,null),Nu(t,0)}if(function G0(e){for(let t=vp(e);null!==t;t=yp(t)){if(!(t[St]&Ra.HasTransplantedViews))continue;const n=t[gi];for(let r=0;r<n.length;r++){$C(n[r])}}}(t),Vg(t,0),null!==e.contentQueries&&Og(e,t),d){const k=e.contentCheckHooks;null!==k&&Yl(t,k)}else{const k=e.contentHooks;null!==k&&Zl(t,k,1),Nu(t,1)}!function u0(e,t){const n=e.hostBindingOpCodes;if(null!==n)try{for(let r=0;r<n.length;r++){const o=n[r];if(o<0)us(~o);else{const s=o,a=n[++r],l=n[++r];qC(a,s),l(2,t[s])}}}finally{us(-1)}}(e,t);const D=e.components;null!==D&&Ug(t,D,0);const S=e.viewQuery;if(null!==S&&xd(2,S,r),d){const k=e.viewCheckHooks;null!==k&&Yl(t,k)}else{const k=e.viewHooks;null!==k&&Zl(t,k,2),Nu(t,2)}if(!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[is]){for(const k of t[is])k();t[is]=null}t[St]&=-73}catch(d){throw ka(t),d}finally{null!==l&&(function me(e,t){if(L(t),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(Ge(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)ke(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}(l,a),function V0(e){e.lView[Fr]!==e&&(e.lView=null,Lg.push(e))}(l)),xu()}}function Vg(e,t){for(let n=vp(e);null!==n;n=yp(n))for(let r=Yn;r<n.length;r++)Bg(n[r],t)}function z0(e,t,n){Bg(fo(t,e),n)}function Bg(e,t){bu(e)&&kd(e,t)}function kd(e,t){const r=e[ct],o=e[St],s=e[Fr];let a=!!(0===t&&16&o);if(a||=!!(64&o&&0===t),a||=!!(1024&o),a||=!(!s?.dirty||!Ze(s)),s&&(s.dirty=!1),e[St]&=-9217,a)j0(r,e,r.template,e[Pn]);else if(8192&o){Vg(e,1);const l=r.components;null!==l&&Ug(e,l,1)}}function Ug(e,t,n){for(let r=0;r<t.length;r++)z0(e,t[r],n)}function il(e){for(e[uo].changeDetectionScheduler?.notify();e;){e[St]|=64;const t=cs(e);if(Wl(e)&&!t)return e;e=t}return null}class sl{get rootNodes(){const t=this._lView,n=t[ct];return ol(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[Pn]}set context(t){this._lView[Pn]=t}get destroyed(){return!(256&~this._lView[St])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[En];if($r(t)){const n=t[8],r=n?n.indexOf(this):-1;r>-1&&(Za(t,r),yo(n,r))}this._attachedToViewContainer=!1}Ec(this._lView[ct],this._lView)}onDestroy(t){Kl(this._lView,t)}markForCheck(){il(this._cdRefInjectingView||this._lView)}detach(){this._lView[St]&=-129}reattach(){_u(this._lView),this._lView[St]|=128}detectChanges(){this._lView[St]|=1024,Tc(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new Ae(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,ig(this._lView[ct],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new Ae(902,!1);this._appRef=t,_u(this._lView)}}let al=(()=>{class e{static{this.__NG_ELEMENT_ID__=K0}}return e})();const W0=al,X0=class extends W0{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){const o=function nl(e,t,n,r){const o=L(null);try{const s=t.tView,d=wc(e,s,n,4096&e[St]?4096:16,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null);d[rs]=e[t.index];const S=e[ko];return null!==S&&(d[ko]=S.createEmbeddedView(s)),Pd(s,d,n),d}finally{L(o)}}(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new sl(o)}};function K0(){return Ac(Rn(),Je())}function Ac(e,t){return 4&e.type?new X0(t,e,Fs(e,t)):null}class jd{}class yI{}class Kg{}class CI{resolveComponentFactory(t){throw function DI(e){const t=Error(`No component factory found for ${Mt(e)}.`);return t.ngComponent=e,t}(t)}}let Pc=(()=>{class e{static{this.NULL=new CI}}return e})();class Qg{}let EI=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>function bI(){const e=Je(),n=fo(Rn().index,e);return(kr(n)?n:e)[zt]}()}}return e})(),II=(()=>{class e{static{this.\u0275prov=Yt({token:e,providedIn:"root",factory:()=>null})}}return e})();const Hd={},Yg=new Set;function ri(e){Yg.has(e)||(Yg.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function Zg(...e){}class Dr{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new ti(!1),this.onMicrotaskEmpty=new ti(!1),this.onStable=new ti(!1),this.onError=new ti(!1),typeof Zone>"u")throw new Ae(908,!1);Zone.assertZonePatched();const o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=function _I(){const e="function"==typeof Nt.requestAnimationFrame;let t=Nt[e?"requestAnimationFrame":"setTimeout"],n=Nt[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);const o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}().nativeRequestAnimationFrame,function MI(e){const t=()=>{!function SI(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(Nt,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,zd(e),e.isCheckStableRunning=!0,Gd(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),zd(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,s,a,l)=>{if(function TI(e){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0].data?.__ignore_ng_zone__}(l))return n.invokeTask(o,s,a,l);try{return Jg(e),n.invokeTask(o,s,a,l)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===s.type||e.shouldCoalesceRunChangeDetection)&&t(),em(e)}},onInvoke:(n,r,o,s,a,l,d)=>{try{return Jg(e),n.invoke(o,s,a,l,d)}finally{e.shouldCoalesceRunChangeDetection&&t(),em(e)}},onHasTask:(n,r,o,s)=>{n.hasTask(o,s),r===o&&("microTask"==s.change?(e._hasPendingMicrotasks=s.microTask,zd(e),Gd(e)):"macroTask"==s.change&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(n,r,o,s)=>(n.handleError(o,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}(o)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!Dr.isInAngularZone())throw new Ae(909,!1)}static assertNotInAngularZone(){if(Dr.isInAngularZone())throw new Ae(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){const s=this._inner,a=s.scheduleEventTask("NgZoneEvent: "+o,t,wI,Zg,Zg);try{return s.runTask(a,n,r)}finally{s.cancelTask(a)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const wI={};function Gd(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function zd(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function Jg(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function em(e){e._nesting--,Gd(e)}class tm{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new ti,this.onMicrotaskEmpty=new ti,this.onStable=new ti,this.onError=new ti}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}}var hs=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(hs||{});const nm={destroy(){}};function rm(e,t){!t&&function ns(e){if(!Ss())throw new Ae(-203,!1)}();const n=t?.injector??Ut(ho);if(!function Vo(e){return"browser"===(e??Ut(ho)).get(Ku)}(n))return nm;ri("NgAfterNextRender");const r=n.get(fl),o=r.handler??=new im,s=t?.phase??hs.MixedReadWrite,a=()=>{o.unregister(d),l()},l=n.get(Ps).onDestroy(a),d=_o(n,()=>new om(s,()=>{a(),e()}));return o.register(d),{destroy:a}}class om{constructor(t,n){this.phase=t,this.callbackFn=n,this.zone=Ut(Dr),this.errorHandler=Ut(Ci,{optional:!0}),Ut(jd,{optional:!0})?.notify(1)}invoke(){try{this.zone.runOutsideAngular(this.callbackFn)}catch(t){this.errorHandler?.handleError(t)}}}class im{constructor(){this.executingCallbacks=!1,this.buckets={[hs.EarlyRead]:new Set,[hs.Write]:new Set,[hs.MixedReadWrite]:new Set,[hs.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(const t of Object.values(this.buckets))for(const n of t)n.invoke();this.executingCallbacks=!1;for(const t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(const t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}}let fl=(()=>{class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){const n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(const r of n)r()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static{this.\u0275prov=Yt({token:e,providedIn:"root",factory:()=>new e})}}return e})();function $i(e){return!!pr(e)}function kc(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,s=0;if(null!==t)for(let a=0;a<t.length;a++){const l=t[a];"number"==typeof l?s=l:1==s?o=$n(o,l):2==s&&(r=$n(r,l+": "+t[++a]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}class lm extends Pc{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=sn(t);return new gl(n,this.ngModule)}}function cm(e){const t=[];for(const n in e){if(!e.hasOwnProperty(n))continue;const r=e[n];void 0!==r&&t.push({propName:Array.isArray(r)?r[0]:r,templateName:n})}return t}class Lc{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Vr(r);const o=this.injector.get(t,Hd,r);return o!==Hd||n===Hd?o:this.parentInjector.get(t,n,r)}}class gl extends Kg{get inputs(){const t=this.componentDef,n=t.inputTransforms,r=cm(t.inputs);if(null!==n)for(const o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return cm(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function No(e){return e.map(Xo).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){const s=L(null);try{let a=(o=o||this.ngModule)instanceof Qr?o:o?.injector;a&&null!==this.componentDef.getStandaloneInjector&&(a=this.componentDef.getStandaloneInjector(a)||a);const l=a?new Lc(t,a):t,d=l.get(Qg,null);if(null===d)throw new Ae(407,!1);const D=l.get(II,null),ee={rendererFactory:d,sanitizer:D,inlineEffectRunner:null,afterRenderEventManager:l.get(fl,null),changeDetectionScheduler:l.get(jd,null)},ce=d.createRenderer(null,this.componentDef),Te=this.componentDef.selectors[0][0]||"div",Ye=r?function f0(e,t,n,r){const s=r.get(Pp,!1)||n===co.ShadowDom,a=e.selectRootElement(t,s);return function h0(e){wg(e)}(a),a}(ce,r,this.componentDef.encapsulation,l):Cc(ce,Te,function NI(e){const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":null}(Te));let ut=512;this.componentDef.signals?ut|=4096:this.componentDef.onPush||(ut|=16);let ft=null;null!==Ye&&(ft=nd(Ye,l,!0));const it=Td(0,null,null,1,0,null,null,null,null,null,null),jt=wc(null,it,null,ut,null,null,ee,ce,l,null,ft);let yn,kn;Ou(jt);try{const Mr=this.componentDef;let ao,va=null;Mr.findHostDirectiveDefs?(ao=[],va=new Map,Mr.findHostDirectiveDefs(Mr,ao,va),ao.push(Mr)):ao=[Mr];const TC=function FI(e,t){const n=e[ct],r=qt;return e[r]=t,Hs(n,r,2,"#host",null)}(jt,Ye),rR=function kI(e,t,n,r,o,s,a){const l=o[ct];!function LI(e,t,n,r){for(const o of e)t.mergedAttrs=Do(t.mergedAttrs,o.hostAttrs);null!==t.mergedAttrs&&(kc(t,t.mergedAttrs,!0),null!==n&&yg(r,n,t))}(r,e,t,a);let d=null;null!==t&&(d=nd(t,o[Dn]));const D=s.rendererFactory.createRenderer(t,n);let S=16;n.signals?S=4096:n.onPush&&(S=64);const k=wc(o,_g(n),null,S,o[e.index],e,s,D,null,null,d);return l.firstCreatePass&&Rd(l,e,r.length-1),Sc(o,k),o[e.index]=k}(TC,Ye,Mr,ao,jt,ee,ce);kn=Fa(it,qt),Ye&&function VI(e,t,n,r){if(r)Ti(e,n,["ng-version","17.3.12"]);else{const{attrs:o,classes:s}=function Po(e){const t=[],n=[];let r=1,o=2;for(;r<e.length;){let s=e[r];if("string"==typeof s)2===o?""!==s&&t.push(s,e[++r]):8===o&&n.push(s);else{if(!v(o))break;o=s}r++}return{attrs:t,classes:n}}(t.selectors[0]);o&&Ti(e,n,o),s&&s.length>0&&vg(e,n,s.join(" "))}}(ce,Mr,Ye,r),void 0!==n&&function BI(e,t,n){const r=e.projection=[];for(let o=0;o<t.length;o++){const s=n[o];r.push(null!=s?Array.from(s):null)}}(kn,this.ngContentSelectors,n),yn=function $I(e,t,n,r,o,s){const a=Rn(),l=o[ct],d=Yr(a,o);Tg(l,o,a,n,null,r);for(let S=0;S<n.length;S++)Hr(ds(o,l,a.directiveStart+S,a),o);Ag(l,o,a),d&&Hr(d,o);const D=ds(o,l,a.directiveStart+a.componentOffset,a);if(e[Pn]=o[Pn]=D,null!==s)for(const S of s)S(D,t);return wd(l,a,o),D}(rR,Mr,ao,va,jt,[UI]),Pd(it,jt,null)}finally{xu()}return new PI(this.componentType,yn,Fs(kn,jt),jt,kn)}finally{L(s)}}}class PI extends yI{constructor(t,n,r,o,s){super(),this.location=r,this._rootLView=o,this._tNode=s,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new sl(o,void 0,!1),this.componentType=t}setInput(t,n){const r=this._tNode.inputs;let o;if(null!==r&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;const s=this._rootLView;Nd(s[ct],s,o,t,n),this.previousInputValues.set(t,n),il(fo(this._tNode.index,s))}}get injector(){return new _r(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function UI(){const e=Rn();Ql(Je()[ct],e)}let $c=(()=>{class e{static{this.__NG_ELEMENT_ID__=jI}}return e})();function jI(){return fm(Rn(),Je())}const HI=$c,um=class extends HI{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Fs(this._hostTNode,this._hostLView)}get injector(){return new _r(this._hostTNode,this._hostLView)}get parentInjector(){const t=tc(this._hostTNode,this._hostLView);if(Fu(t)){const n=Ua(t,this._hostLView),r=Ba(t);return new _r(n[ct].data[r+8],n)}return new _r(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=dm(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-Yn}createEmbeddedView(t,n,r){let o,s;"number"==typeof r?o=r:null!=r&&(o=r.index,s=r.injector);const l=t.createEmbeddedViewImpl(n||{},s,null);return this.insertImpl(l,o,Gs(this._hostTNode,null)),l}createComponent(t,n,r,o,s){const a=t&&!function tt(e){return"function"==typeof e}(t);let l;if(a)l=n;else{const Te=n||{};l=Te.index,r=Te.injector,o=Te.projectableNodes,s=Te.environmentInjector||Te.ngModuleRef}const d=a?t:new gl(sn(t)),D=r||this.parentInjector;if(!s&&null==d.ngModule){const Ye=(a?D:this.parentInjector).get(Qr,null);Ye&&(s=Ye)}sn(d.componentType??{});const ce=d.create(D,o,null,s);return this.insertImpl(ce.hostView,l,Gs(this._hostTNode,null)),ce}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){const o=t._lView;if(function LC(e){return $r(e[En])}(o)){const l=this.indexOf(t);if(-1!==l)this.detach(l);else{const d=o[En],D=new um(d,d[Nn],d[En]);D.detach(D.indexOf(t))}}const s=this._adjustIndex(n),a=this._lContainer;return rl(a,o,s,r),t.attachToViewContainerRef(),wi(qd(a),s,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=dm(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=Za(this._lContainer,n);r&&(yo(qd(this._lContainer),n),Ec(r[ct],r))}detach(t){const n=this._adjustIndex(t,-1),r=Za(this._lContainer,n);return r&&null!=yo(qd(this._lContainer),n)?new sl(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function dm(e){return e[8]}function qd(e){return e[8]||(e[8]=[])}function fm(e,t){let n;const r=t[e.index];return $r(r)?n=r:(n=Rg(r,t,null,e),t[e.index]=n,Sc(t,n)),hm(n,t,e,r),new um(n,e,t)}let hm=function gm(e,t,n,r){if(e[Lo])return;let o;o=8&n.type?Fn(r):function GI(e,t){const n=e[zt],r=n.createComment(""),o=Yr(t,e);return fs(n,bc(n,o),r,function o0(e,t){return e.nextSibling(t)}(n,o),!1),r}(t,n),e[Lo]=o},Qd=()=>!1;class Yd{constructor(t){this.queryList=t,this.matches=null}clone(){return new Yd(this.queryList)}setDirty(){this.queryList.setDirty()}}class Zd{constructor(t=[]){this.queries=t}createEmbeddedView(t){const n=t.queries;if(null!==n){const r=null!==t.contentQueries?t.contentQueries[0]:n.length,o=[];for(let s=0;s<r;s++){const a=n.getByIndex(s);o.push(this.queries[a.indexInDeclarationView].clone())}return new Zd(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)null!==rf(t,n).matches&&this.queries[n].setDirty()}}class mm{constructor(t,n,r=null){this.flags=n,this.read=r,this.predicate="string"==typeof t?function ZI(e){return e.split(",").map(t=>t.trim())}(t):t}}class Jd{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){const o=null!==n?n.length:0,s=this.getByIndex(r).embeddedTView(t,o);s&&(s.indexInDeclarationView=r,null!==n?n.push(s):n=[s])}return null!==n?new Jd(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class ef{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new ef(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1&~this.metadata.flags){const n=this._declarationNodeIndex;let r=t.parent;for(;null!==r&&8&r.type&&r.index!==n;)r=r.parent;return n===(null!==r?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){const r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){const s=r[o];this.matchTNodeWithReadOption(t,n,KI(n,s)),this.matchTNodeWithReadOption(t,n,nc(n,t,s,!1,!1))}else r===al?4&n.type&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,nc(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(null!==r){const o=this.metadata.read;if(null!==o)if(o===ja||o===$c||o===al&&4&n.type)this.addMatch(n.index,-2);else{const s=nc(n,t,o,!1,!1);null!==s&&this.addMatch(n.index,s)}else this.addMatch(n.index,r)}}addMatch(t,n){null===this.matches?this.matches=[t,n]:this.matches.push(t,n)}}function KI(e,t){const n=e.localNames;if(null!==n)for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1];return null}function QI(e,t,n,r){return-1===n?function qI(e,t){return 11&e.type?Fs(e,t):4&e.type?Ac(e,t):null}(t,e):-2===n?function YI(e,t,n){return n===ja?Fs(t,e):n===al?Ac(t,e):n===$c?fm(t,e):void 0}(e,t,r):ds(e,e[ct],n,t)}function vm(e,t,n,r){const o=t[ko].queries[r];if(null===o.matches){const s=e.data,a=n.matches,l=[];for(let d=0;null!==a&&d<a.length;d+=2){const D=a[d];l.push(D<0?null:QI(t,s[D],a[d+1],n.metadata.read))}o.matches=l}return o.matches}function tf(e,t,n,r){const o=e.queries.getByIndex(n),s=o.matches;if(null!==s){const a=vm(e,t,o,n);for(let l=0;l<s.length;l+=2){const d=s[l];if(d>0)r.push(a[l/2]);else{const D=s[l+1],S=t[-d];for(let k=Yn;k<S.length;k++){const ee=S[k];ee[rs]===ee[En]&&tf(ee[ct],ee,D,r)}if(null!==S[gi]){const k=S[gi];for(let ee=0;ee<k.length;ee++){const ce=k[ee];tf(ce[ct],ce,D,r)}}}}}return r}function ym(e,t,n){const r=new Hu(!(4&~n));return function m0(e,t,n,r){const o=xg(t);o.push(n),e.firstCreatePass&&Ng(e).push(r,o.length-1)}(e,t,r,r.destroy),(t[ko]??=new Zd).queries.push(new Yd(r))-1}function Em(e,t,n){null===e.queries&&(e.queries=new Jd),e.queries.track(new ef(t,n))}function rf(e,t){return e.queries.getByIndex(t)}function bm(e,t){const n=e[ct],r=rf(n,t);return r.crossesNgTemplate?tf(n,e,t,[]):vm(n,e,r,t)}function af(e){let t=function km(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),n=!0;const r=[e];for(;t;){let o;if(So(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new Ae(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);const a=e;a.inputs=Bc(e.inputs),a.inputTransforms=Bc(e.inputTransforms),a.declaredInputs=Bc(e.declaredInputs),a.outputs=Bc(e.outputs);const l=o.hostBindings;l&&p_(e,l);const d=o.viewQuery,D=o.contentQueries;if(d&&f_(e,d),D&&h_(e,D),u_(e,o),Wn(e.outputs,o.outputs),So(o)&&o.data.animation){const S=e.data;S.animation=(S.animation||[]).concat(o.data.animation)}}const s=o.features;if(s)for(let a=0;a<s.length;a++){const l=s[a];l&&l.ngInherit&&l(e),l===af&&(n=!1)}}t=Object.getPrototypeOf(t)}!function d_(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){const o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Do(o.hostAttrs,n=Do(n,o.hostAttrs))}}(r)}function u_(e,t){for(const n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;const r=t.inputs[n];if(void 0!==r&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],null!==t.inputTransforms)){const o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function Bc(e){return e===qr?{}:e===un?[]:e}function f_(e,t){const n=e.viewQuery;e.viewQuery=n?(r,o)=>{t(r,o),n(r,o)}:t}function h_(e,t){const n=e.contentQueries;e.contentQueries=n?(r,o,s)=>{t(r,o,s),n(r,o,s)}:t}function p_(e,t){const n=e.hostBindings;e.hostBindings=n?(r,o)=>{t(r,o),n(r,o)}:t}function Bm(e){const t=e.inputConfig,n={};for(const r in t)if(t.hasOwnProperty(r)){const o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}class ps{}class Um{}function C_(e,t){return new lf(e,t??null,[])}class lf extends ps{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new lm(this);const o=pr(t);this._bootstrapComponents=po(o.bootstrap),this._r3Injector=rp(t,n,[{provide:ps,useValue:this},{provide:Pc,useValue:this.componentFactoryResolver},...r],Mt(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class cf extends Um{constructor(t){super(),this.moduleType=t}create(t){return new lf(this.moduleType,t,[])}}class jm extends ps{constructor(t){super(),this.componentFactoryResolver=new lm(this),this.instance=null;const n=new Ko([...t.providers,{provide:ps,useValue:this},{provide:Pc,useValue:this.componentFactoryResolver}],t.parent||Pi(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}}function uf(e,t,n=null){return new jm({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}let Xs=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new Ft.t(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);const n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),0===this.pendingTasks.size&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function jc(e){return!!df(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function df(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function gr(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function Ks(e,t,n,r,o,s,a,l){const d=Je(),D=fn(),S=e+qt,k=D.firstCreatePass?function M_(e,t,n,r,o,s,a,l,d){const D=t.consts,S=Hs(t,e,4,a||null,Yo(D,l));Ad(t,n,S,Yo(D,d)),Ql(t,S);const k=S.tView=Td(2,S,r,o,s,t.directiveRegistry,t.pipeRegistry,null,t.schemas,D,null);return null!==t.queries&&(t.queries.template(t,S),k.queries=t.queries.embeddedTView(S)),S}(S,D,d,t,n,r,o,s,a):D.data[S];Zo(k,!1);const ee=Hm(D,d,k,e);$a()&&Ic(D,d,ee,k),Hr(ee,d);const ce=Rg(ee,d,ee,k);return d[S]=ce,Sc(d,ce),function pm(e,t,n){return Qd(e,t,n)}(ce,k,d),Oa(k)&&Sd(D,d,k),null!=a&&Md(d,k,l),Ks}let Hm=function Gm(e,t,n,r){return Jo(!0),t[zt].createComment("")};function Ef(e,t,n,r){const o=Je();return gr(o,$o(),t)&&(fn(),ni(zn(),o,e,t,n,r)),Ef}function qc(e,t){return e<<17|t<<2}function Ui(e){return e>>17&32767}function bf(e){return 2|e}function vs(e){return(131068&e)>>2}function If(e,t){return-131069&e|t<<2}function _f(e){return 1|e}function Ev(e,t,n,r){const o=e[n+1],s=null===t;let a=r?Ui(o):vs(o),l=!1;for(;0!==a&&(!1===l||s);){const D=e[a+1];hw(e[a],t)&&(l=!0,e[a+1]=r?_f(D):bf(D)),a=r?Ui(D):vs(D)}l&&(e[n+1]=r?bf(o):_f(o))}function hw(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&Br(e,t)>=0}function wf(e,t,n){const r=Je();return gr(r,$o(),t)&&function io(e,t,n,r,o,s,a,l){const d=Yr(t,n);let S,D=t.inputs;!l&&null!=D&&(S=D[r])?(Nd(e,n,S,r,o),mi(t)&&function C0(e,t){const n=fo(t,e);16&n[St]||(n[St]|=64)}(n,t.index)):3&t.type&&(r=function D0(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),o=null!=a?a(o,t.value||"",r):o,s.setProperty(d,r,o))}(fn(),zn(),r,e,t,r[zt],n,!1),wf}function Sf(e,t,n,r,o){const a=o?"class":"style";Nd(e,n,t.inputs[a],a,r)}function Mf(e,t){return function Bo(e,t,n,r){const o=Je(),s=fn(),a=function Di(e){const t=Kt.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}(2);s.firstUpdatePass&&function Rv(e,t,n,r){const o=e.data;if(null===o[n+1]){const s=o[jr()],a=function Av(e,t){return t>=e.expandoStartIndex}(e,n);(function Pv(e,t){return!!(e.flags&(t?8:16))})(s,r)&&null===t&&!a&&(t=!1),t=function bw(e,t,n,r){const o=function Au(e){const t=Kt.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}(e);let s=r?t.residualClasses:t.residualStyles;if(null===o)0===(r?t.classBindings:t.styleBindings)&&(n=bl(n=Tf(null,e,t,n,r),t.attrs,r),s=null);else{const a=t.directiveStylingLast;if(-1===a||e[a]!==o)if(n=Tf(o,e,t,n,r),null===s){let d=function Iw(e,t,n){const r=n?t.classBindings:t.styleBindings;if(0!==vs(r))return e[Ui(r)]}(e,t,r);void 0!==d&&Array.isArray(d)&&(d=Tf(null,e,t,d[1],r),d=bl(d,t.attrs,r),function _w(e,t,n,r){e[Ui(n?t.classBindings:t.styleBindings)]=r}(e,t,r,d))}else s=function ww(e,t,n){let r;const o=t.directiveEnd;for(let s=1+t.directiveStylingLast;s<o;s++)r=bl(r,e[s].hostAttrs,n);return bl(r,t.attrs,n)}(e,t,r)}return void 0!==s&&(r?t.residualClasses=s:t.residualStyles=s),n}(o,s,t,r),function dw(e,t,n,r,o,s){let a=s?t.classBindings:t.styleBindings,l=Ui(a),d=vs(a);e[r]=n;let S,D=!1;if(Array.isArray(n)?(S=n[1],(null===S||Br(n,S)>0)&&(D=!0)):S=n,o)if(0!==d){const ee=Ui(e[l+1]);e[r+1]=qc(ee,l),0!==ee&&(e[ee+1]=If(e[ee+1],r)),e[l+1]=function cw(e,t){return 131071&e|t<<17}(e[l+1],r)}else e[r+1]=qc(l,0),0!==l&&(e[l+1]=If(e[l+1],r)),l=r;else e[r+1]=qc(d,0),0===l?l=r:e[d+1]=If(e[d+1],r),d=r;D&&(e[r+1]=bf(e[r+1])),Ev(e,S,r,!0),Ev(e,S,r,!1),function fw(e,t,n,r,o){const s=o?e.residualClasses:e.residualStyles;null!=s&&"string"==typeof t&&Br(s,t)>=0&&(n[r+1]=_f(n[r+1]))}(t,S,e,r,s),a=qc(l,d),s?t.classBindings=a:t.styleBindings=a}(o,s,t,n,a,r)}}(s,e,a,r),t!==rn&&gr(o,a,t)&&function xv(e,t,n,r,o,s,a,l){if(!(3&t.type))return;const d=e.data,D=d[l+1],S=function uw(e){return!(1&~e)}(D)?Nv(d,t,n,o,vs(D),a):void 0;Qc(S)||(Qc(s)||function lw(e){return!(2&~e)}(D)&&(s=Nv(d,null,n,o,l,a)),function l0(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let s=-1===r.indexOf("-")?void 0:Dc.DashCase;null==o?e.removeStyle(n,r,s):("string"==typeof o&&o.endsWith("!important")&&(o=o.slice(0,-10),s|=Dc.Important),e.setStyle(n,r,o,s))}}(r,a,function Pa(e,t){return Fn(t[e])}(jr(),n),o,s))}(s,s.data[jr()],o,o[zt],e,o[a+1]=function Aw(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=Mt(Li(e)))),e}(t,n),r,a)}(e,t,null,!0),Mf}function Tf(e,t,n,r,o){let s=null;const a=n.directiveEnd;let l=n.directiveStylingLast;for(-1===l?l=n.directiveStart:l++;l<a&&(s=t[l],r=bl(r,s.hostAttrs,o),s!==e);)l++;return null!==e&&(n.directiveStylingLast=l),r}function bl(e,t,n){const r=n?1:2;let o=-1;if(null!==t)for(let s=0;s<t.length;s++){const a=t[s];"number"==typeof a?o=a:o===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),hr(e,a,!!n||t[++s]))}return void 0===e?null:e}function Nv(e,t,n,r,o,s){const a=null===t;let l;for(;o>0;){const d=e[o],D=Array.isArray(d),S=D?d[1]:d,k=null===S;let ee=n[o+1];ee===rn&&(ee=k?un:void 0);let ce=k?di(ee,r):S===r?ee:void 0;if(D&&!Qc(ce)&&(ce=di(d,r)),Qc(ce)&&(l=ce,a))return l;const Te=e[o+1];o=a?Ui(Te):vs(Te)}if(null!==t){let d=s?t.residualClasses:t.residualStyles;null!=d&&(l=di(d,r))}return l}function Qc(e){return void 0!==e}function Yc(e,t,n,r){const o=Je(),s=fn(),a=qt+e,l=o[zt],d=s.firstCreatePass?function Zw(e,t,n,r,o,s){const a=t.consts,d=Hs(t,e,2,r,Yo(a,o));return Ad(t,n,d,Yo(a,s)),null!==d.attrs&&kc(d,d.attrs,!1),null!==d.mergedAttrs&&kc(d,d.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,d),d}(a,s,o,t,n,r):s.data[a],D=$v(s,o,d,l,t,e);o[a]=D;const S=Oa(d);return Zo(d,!0),yg(l,D,d),!function yl(e){return!(32&~e.flags)}(d)&&$a()&&Ic(s,o,D,d),0===function VC(){return Kt.lFrame.elementDepthCount}()&&Hr(D,o),function BC(){Kt.lFrame.elementDepthCount++}(),S&&(Sd(s,o,d),wd(s,d,o)),null!==r&&Md(o,d),Yc}function Zc(){let e=Rn();Su()?Mu():(e=e.parent,Zo(e,!1));const t=e;(function jC(e){return Kt.skipHydrationRootTNode===e})(t)&&function WC(){Kt.skipHydrationRootTNode=null}(),function UC(){Kt.lFrame.elementDepthCount--}();const n=fn();return n.firstCreatePass&&(Ql(n,e),Ts(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function aE(e){return!!(8&e.flags)}(t)&&Sf(n,t,Je(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function lE(e){return!!(16&e.flags)}(t)&&Sf(n,t,Je(),t.stylesWithoutHost,!1),Zc}function Nf(e,t,n,r){return Yc(e,t,n,r),Zc(),Nf}let $v=(e,t,n,r,o,s)=>(Jo(!0),Cc(r,o,function Lh(){return Kt.lFrame.currentNamespace}()));function Pf(e,t,n){const r=Je(),o=fn(),s=e+qt,a=o.firstCreatePass?function tS(e,t,n,r,o){const s=t.consts,a=Yo(s,r),l=Hs(t,e,8,"ng-container",a);return null!==a&&kc(l,a,!0),Ad(t,n,l,Yo(s,o)),null!==t.queries&&t.queries.elementStart(t,l),l}(s,o,r,t,n):o.data[s];Zo(a,!0);const l=Vv(o,r,a,e);return r[s]=l,$a()&&Ic(o,r,l,a),Hr(l,r),Oa(a)&&(Sd(o,r,a),wd(o,a,r)),null!=n&&Md(r,a),Pf}function Ff(){let e=Rn();const t=fn();return Su()?Mu():(e=e.parent,Zo(e,!1)),t.firstCreatePass&&(Ql(t,e),Ts(e)&&t.queries.elementEnd(e)),Ff}function kf(e,t,n){return Pf(e,t,n),Ff(),kf}let Vv=(e,t,n,r)=>(Jo(!0),yd(t[zt],""));function Bv(){return Je()}const ys=void 0;var iS=["en",[["a","p"],["AM","PM"],ys],[["AM","PM"],ys,ys],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],ys,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],ys,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",ys,"{1} 'at' {0}",ys],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function oS(e){const n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return 1===n&&0===r?1:5}];let da={};function Lf(e){const t=function sS(e){return e.toLowerCase().replace(/_/g,"-")}(e);let n=Gv(t);if(n)return n;const r=t.split("-")[0];if(n=Gv(r),n)return n;if("en"===r)return iS;throw new Ae(701,!1)}function Hv(e){return Lf(e)[fa.PluralCase]}function Gv(e){return e in da||(da[e]=Nt.ng&&Nt.ng.common&&Nt.ng.common.locales&&Nt.ng.common.locales[e]),da[e]}var fa=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(fa||{});const ha="en-US";let zv=ha;function Bf(e,t,n,r){const o=Je(),s=fn(),a=Rn();return function Uf(e,t,n,r,o,s,a){const l=Oa(r),D=e.firstCreatePass&&Ng(e),S=t[Pn],k=xg(t);let ee=!0;if(3&r.type||a){const Ye=Yr(r,t),ut=a?a(Ye):Ye,ft=k.length,it=a?yn=>a(Fn(yn[r.index])):r.index;let jt=null;if(!a&&l&&(jt=function nM(e,t,n,r){const o=e.cleanup;if(null!=o)for(let s=0;s<o.length-1;s+=2){const a=o[s];if(a===n&&o[s+1]===r){const l=t[wo],d=o[s+2];return l.length>d?l[d]:null}"string"==typeof a&&(s+=2)}return null}(e,t,o,r.index)),null!==jt)(jt.__ngLastListenerFn__||jt).__ngNextListenerFn__=s,jt.__ngLastListenerFn__=s,ee=!1;else{s=Dy(r,t,S,s,!1);const yn=n.listen(ut,o,s);k.push(s,yn),D&&D.push(o,it,ft,ft+1)}}else s=Dy(r,t,S,s,!1);const ce=r.outputs;let Te;if(ee&&null!==ce&&(Te=ce[o])){const Ye=Te.length;if(Ye)for(let ut=0;ut<Ye;ut+=2){const kn=t[Te[ut]][Te[ut+1]].subscribe(s),Mr=k.length;k.push(s,kn),D&&D.push(o,r.index,Mr,-(Mr+1))}}}(s,o,o[zt],a,e,t,r),Bf}function yy(e,t,n,r){const o=L(null);try{return ro(6,t,n),!1!==n(r)}catch(s){return Mc(e,s),!1}finally{ro(7,t,n),L(o)}}function Dy(e,t,n,r,o){return function s(a){if(a===Function)return r;il(e.componentOffset>-1?fo(e.index,t):t);let d=yy(t,n,r,a),D=s.__ngNextListenerFn__;for(;D;)d=yy(t,n,D,a)&&d,D=D.__ngNextListenerFn__;return o&&!1===d&&a.preventDefault(),d}}function Cy(e=1){return function ZC(e){return(Kt.lFrame.contextLView=function Ih(e,t){for(;e>0;)t=t[Fi],e--;return t}(e,Kt.lFrame.contextLView))[Pn]}(e)}function rM(e,t){let n=null;const r=function wt(e){const t=e.attrs;if(null!=t){const n=t.indexOf(5);if(!(1&n))return t[n+1]}return null}(e);for(let o=0;o<t.length;o++){const s=t[o];if("*"!==s){if(null===r?re(e,s,!0):Eo(r,s))return o}else n=o}return n}function Ey(e){const t=Je()[Gn][Nn];if(!t.projection){const r=t.projection=function no(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}(e?e.length:1,null),o=r.slice();let s=t.child;for(;null!==s;){const a=e?rM(s,e):0;null!==a&&(o[a]?o[a].projectionNext=s:r[a]=s,o[a]=s),s=s.next}}}function by(e,t=0,n){const r=Je(),o=fn(),s=Hs(o,qt+e,16,null,n||null);null===s.projection&&(s.projection=t),Mu(),(!r[lr]||Rs())&&32&~s.flags&&function s0(e,t,n){mg(t[zt],0,t,n,Cd(e,n,t),ug(n.parent||t[Nn],n,t))}(o,r,s)}function xy(e,t,n,r){!function Cm(e,t,n,r){const o=fn();if(o.firstCreatePass){const s=Rn();Em(o,new mm(t,n,r),s.index),function JI(e,t){const n=e.contentQueries||(e.contentQueries=[]);t!==(n.length?n[n.length-1]:-1)&&n.push(e.queries.length-1,t)}(o,e),!(2&~n)&&(o.staticContentQueries=!0)}return ym(o,Je(),n)}(e,t,n,r)}function Ny(e,t,n){!function Dm(e,t,n){const r=fn();return r.firstCreatePass&&(Em(r,new mm(e,t,n),-1),!(2&~t)&&(r.staticViewQueries=!0)),ym(r,Je(),t)}(e,t,n)}function Py(e){const t=Je(),n=fn(),r=Ru();ql(r+1);const o=rf(n,r);if(e.dirty&&function kC(e){return!(4&~e[St])}(t)===!(2&~o.metadata.flags)){if(null===o.matches)e.reset([]);else{const s=bm(t,r);e.reset(s,ap),e.notifyOnChanges()}return!0}return!1}function Fy(){return function nf(e,t){return e[ko].queries[t].queryList}(Je(),Ru())}function zy(e,t=""){const n=Je(),r=fn(),o=e+qt,s=r.firstCreatePass?Hs(r,o,1,t,null):r.data[o],a=Wy(r,n,s,t,e);n[o]=a,$a()&&Ic(r,n,a,s),Zo(s,!1)}let Wy=(e,t,n,r,o)=>(Jo(!0),function vd(e,t){return e.createText(t)}(t[zt],r));function Gf(e,t,n,r,o){if(e=le(e),Array.isArray(e))for(let s=0;s<e.length;s++)Gf(e[s],t,n,r,o);else{const s=fn(),a=Je(),l=Rn();let d=Fo(e)?e:le(e.provide);const D=Ma(e),S=1048575&l.providerIndexes,k=l.directiveStart,ee=l.providerIndexes>>20;if(Fo(e)||!e.multi){const ce=new Va(D,o,js),Te=Wf(d,t,o?S:S+ee,k);-1===Te?($u(ec(l,a),s,d),zf(s,e,t.length),t.push(d),l.directiveStart++,l.directiveEnd++,o&&(l.providerIndexes+=1048576),n.push(ce),a.push(ce)):(n[Te]=ce,a[Te]=ce)}else{const ce=Wf(d,t,S+ee,k),Te=Wf(d,t,S,S+ee),ut=Te>=0&&n[Te];if(o&&!ut||!o&&!(ce>=0&&n[ce])){$u(ec(l,a),s,d);const ft=function _M(e,t,n,r,o){const s=new Va(e,n,js);return s.multi=[],s.index=t,s.componentProviders=0,oD(s,o,r&&!n),s}(o?IM:bM,n.length,o,r,D);!o&&ut&&(n[Te].providerFactory=ft),zf(s,e,t.length,0),t.push(d),l.directiveStart++,l.directiveEnd++,o&&(l.providerIndexes+=1048576),n.push(ft),a.push(ft)}else zf(s,e,ce>-1?ce:Te,oD(n[o?Te:ce],D,!o&&r));!o&&r&&ut&&n[Te].componentProviders++}}}function zf(e,t,n,r){const o=Fo(t),s=function _a(e){return!!e.useClass}(t);if(o||s){const d=(s?le(t.useClass):t).prototype.ngOnDestroy;if(d){const D=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){const S=D.indexOf(n);-1===S?D.push(n,[r,d]):D[S+1].push(r,d)}else D.push(n,d)}}}function oD(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Wf(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function bM(e,t,n,r){return Xf(this.multi,[])}function IM(e,t,n,r){const o=this.multi;let s;if(this.providerFactory){const a=this.providerFactory.componentProviders,l=ds(n,n[ct],this.providerFactory.index,r);s=l.slice(0,a),Xf(o,s);for(let d=a;d<l.length;d++)s.push(l[d])}else s=[],Xf(o,s);return s}function Xf(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function iD(e,t=[]){return n=>{n.providersResolver=(r,o)=>function EM(e,t,n){const r=fn();if(r.firstCreatePass){const o=So(e);Gf(n,r.data,r.blueprint,o,!0),Gf(t,r.data,r.blueprint,o,!1)}}(r,o?o(e):e,t)}}let wM=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){const r=Oi(0,n.type),o=r.length>0?uf([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(const n of this.cachedInjectors.values())null!==n&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=Yt({token:e,providedIn:"environment",factory:()=>new e(qn(Qr))})}}return e})();function sD(e){ri("NgStandalone"),e.getStandaloneInjector=t=>t.get(wM).getOrCreateStandaloneInjector(e)}class TD{constructor(t){this.full=t;const n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}}let AD=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();const PD=new Bt(""),FD=new Bt("");let Jf,$T=(()=>{class e{constructor(n,r,o){this._ngZone=n,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,Jf||(function VT(e){Jf=e}(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{Dr.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(n)||(clearTimeout(r.timeoutId),!1))}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let s=-1;r&&r>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(a=>a.timeoutId!==s),n()},r)),this._callbacks.push({doneCb:n,timeoutId:s,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static{this.\u0275fac=function(r){return new(r||e)(qn(Dr),qn(kD),qn(FD))}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac})}}return e})(),kD=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Jf?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function eh(e){return!!e&&"function"==typeof e.then}function LD(e){return!!e&&"function"==typeof e.subscribe}const $D=new Bt("");let th=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=Ut($D,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const n=[];for(const o of this.appInits){const s=o();if(eh(s))n.push(s);else if(LD(s)){const a=new Promise((l,d)=>{s.subscribe({complete:l,error:d})});n.push(a)}}const r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),0===n.length&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const nh=new Bt("");function UD(e,t){return Array.isArray(t)?t.reduce(UD,e):{...e,...t}}let ji=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=Ut(ip),this.afterRenderEffectManager=Ut(fl),this.externalTestViews=new Set,this.beforeRender=new xt.B,this.afterTick=new xt.B,this.componentTypes=[],this.components=[],this.isStable=Ut(Xs).hasPendingTasks.pipe((0,Be.T)(n=>!n)),this._injector=Ut(Qr)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){const o=n instanceof Kg;if(!this._injector.get(th).done)throw!o&&Ur(n),new Ae(405,!1);let a;a=o?n:this._injector.get(Pc).resolveComponentFactory(n),this.componentTypes.push(a.componentType);const l=function BT(e){return e.isBoundToModule}(a)?void 0:this._injector.get(ps),D=a.create(ho.NULL,[],r||a.selector,l),S=D.location.nativeElement,k=D.injector.get(PD,null);return k?.registerApplication(S),D.onDestroy(()=>{this.detachView(D.hostView),su(this.components,D),k?.unregisterApplication(S)}),this._loadComponent(D),D}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new Ae(101,!1);const r=L(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(o){this.internalErrorHandler(o)}finally{this.afterTick.next(),this._runningTick=!1,L(r)}}detectChangesInAttachedViews(n){let r=0;const o=this.afterRenderEffectManager;for(;;){if(r===$g)throw new Ae(103,!1);if(n){const s=0===r;this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:l}of this._views)jT(a,s,l)}if(r++,o.executeInternalCallbacks(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>rh(s))&&(o.execute(),![...this.externalTestViews.keys(),...this._views].some(({_lView:s})=>rh(s))))break}}attachView(n){const r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){const r=n;su(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);const r=this._injector.get(nh,[]);[...this._bootstrapListeners,...r].forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>su(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new Ae(406,!1);const n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function su(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}function jT(e,t,n){!t&&!rh(e)||function HT(e,t,n){let r;n?(r=0,e[St]|=1024):r=64&e[St]?0:1,Tc(e,t,r)}(e,n,t)}function rh(e){return Iu(e)}class GT{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}}let zT=(()=>{class e{compileModuleSync(n){return new cf(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){const r=this.compileModuleSync(n),s=po(pr(n).declarations).reduce((a,l)=>{const d=sn(l);return d&&a.push(new gl(d)),a},[]);return new GT(r,s)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),KT=(()=>{class e{constructor(){this.zone=Ut(Dr),this.applicationRef=Ut(ji)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function qT(){const e=Ut(Dr),t=Ut(Ci);return n=>e.runOutsideAngular(()=>t.handleError(n))}let YT=(()=>{class e{constructor(){this.subscription=new Pt.yU,this.initialized=!1,this.zone=Ut(Dr),this.pendingTasks=Ut(Xs)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{Dr.assertNotInAngularZone(),queueMicrotask(()=>{null!==n&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{Dr.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const lu=new Bt("",{providedIn:"root",factory:()=>Ut(lu,Qe.Optional|Qe.SkipSelf)||function ZT(){return typeof $localize<"u"&&$localize.locale||ha}()}),JT=new Bt("",{providedIn:"root",factory:()=>"USD"}),oh=new Bt("");let zD=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){const o=function AI(e="zone.js",t){return"noop"===e?new tm:"zone.js"===e?new Dr(t):e}(r?.ngZone,function GD(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return o.run(()=>{const s=function E_(e,t,n){return new lf(e,t,n)}(n.moduleType,this.injector,function HD(e){return[{provide:Dr,useFactory:e},{provide:Ro,multi:!0,useFactory:()=>{const t=Ut(KT,{optional:!0});return()=>t.initialize()}},{provide:Ro,multi:!0,useFactory:()=>{const t=Ut(YT);return()=>{t.initialize()}}},{provide:ip,useFactory:qT}]}(()=>o)),a=s.injector.get(Ci,null);return o.runOutsideAngular(()=>{const l=o.onError.subscribe({next:d=>{a.handleError(d)}});s.onDestroy(()=>{su(this._modules,s),l.unsubscribe()})}),function BD(e,t,n){try{const r=n();return eh(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}(a,o,()=>{const l=s.injector.get(th);return l.runInitializers(),l.donePromise.then(()=>(function Wv(e){"string"==typeof e&&(zv=e.toLowerCase().replace(/_/g,"-"))}(s.injector.get(lu,ha)||ha),this._moduleDoBootstrap(s),s))})})}bootstrapModule(n,r=[]){const o=UD({},r);return function XT(e,t,n){const r=new cf(n);return Promise.resolve(r)}(0,0,n).then(s=>this.bootstrapModuleFactory(s,o))}_moduleDoBootstrap(n){const r=n.injector.get(ji);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(o=>r.bootstrap(o));else{if(!n.instance.ngDoBootstrap)throw new Ae(-403,!1);n.instance.ngDoBootstrap(r)}this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new Ae(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());const n=this._injector.get(oh,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(qn(ho))}}static{this.\u0275prov=Yt({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),Hi=null;const WD=new Bt("");function XD(e,t,n=[]){const r=`Platform: ${t}`,o=new Bt(r);return(s=[])=>{let a=ih();if(!a||a.injector.get(WD,!1)){const l=[...n,...s,{provide:o,useValue:!0}];e?e(l):function tA(e){if(Hi&&!Hi.get(WD,!1))throw new Ae(400,!1);(function VD(){!function ze(e){pt=e}(()=>{throw new Ae(600,!1)})})(),Hi=e;const t=e.get(zD);(function qD(e){e.get(Tp,null)?.forEach(n=>n())})(e)}(function KD(e=[],t){return ho.create({name:t,providers:[{provide:es,useValue:"platform"},{provide:oh,useValue:new Set([()=>Hi=null])},...e]})}(l,r))}return function nA(e){const t=ih();if(!t)throw new Ae(401,!1);return t}()}}function ih(){return Hi?.get(zD)??null}let YD=(()=>{class e{static{this.__NG_ELEMENT_ID__=oA}}return e})();function oA(e){return function iA(e,t,n){if(mi(e)&&!n){const r=fo(e.index,t);return new sl(r,r)}return 47&e.type?new sl(t[Gn],t):null}(Rn(),Je(),!(16&~e))}class tC{constructor(){}supports(t){return jc(t)}create(t){return new uA(t)}}const cA=(e,t)=>t;class uA{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||cA}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,s=null;for(;n||r;){const a=!r||n&&n.currentIndex<rC(r,o,s)?n:r,l=rC(a,o,s),d=a.currentIndex;if(a===r)o--,r=r._nextRemoved;else if(n=n._next,null==a.previousIndex)o++;else{s||(s=[]);const D=l-o,S=d-o;if(D!=S){for(let ee=0;ee<D;ee++){const ce=ee<s.length?s[ee]:s[ee]=0,Te=ce+ee;S<=Te&&Te<D&&(s[ee]=ce+1)}s[a.previousIndex]=S-D}}l!==d&&t(a,l,d)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!jc(t))throw new Ae(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let o,s,a,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let l=0;l<this.length;l++)s=t[l],a=this._trackByFn(l,s),null!==n&&Object.is(n.trackById,a)?(r&&(n=this._verifyReinsertion(n,s,a,l)),Object.is(n.item,s)||this._addIdentityChange(n,s)):(n=this._mismatch(n,s,a,l),r=!0),n=n._next}else o=0,function w_(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Symbol.iterator]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,l=>{a=this._trackByFn(o,l),null!==n&&Object.is(n.trackById,a)?(r&&(n=this._verifyReinsertion(n,l,a,o)),Object.is(n.item,l)||this._addIdentityChange(n,l)):(n=this._mismatch(n,l,a,o),r=!0),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let s;return null===t?s=this._itTail:(s=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,s,o)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,o))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,s,o)):t=this._addAfter(new dA(n,r),s,o),t}_verifyReinsertion(t,n,r,o){let s=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==s?t=this._reinsertAfter(s,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const o=t._prevRemoved,s=t._nextRemoved;return null===o?this._removalsHead=s:o._nextRemoved=s,null===s?this._removalsTail=o:s._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const o=null===n?this._itHead:n._next;return t._next=o,t._prev=n,null===o?this._itTail=t:o._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new nC),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new nC),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class dA{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class fA{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class nC{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new fA,this.map.set(n,r)),r.add(t)}get(t,n){const o=this.map.get(t);return o?o.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function rC(e,t,n){const r=e.previousIndex;if(null===r)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}class oC{constructor(){}supports(t){return t instanceof Map||df(t)}create(){return new hA}}class hA{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let n;for(n=this._mapHead;null!==n;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;null!==n;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;null!==n;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}diff(t){if(t){if(!(t instanceof Map||df(t)))throw new Ae(900,!1)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{const s=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,s)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){const r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){const o=this._records.get(t);this._maybeAddToChanges(o,n);const s=o._prev,a=o._next;return s&&(s._next=a),a&&(a._prev=s),o._next=null,o._prev=null,o}const r=new pA(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}}class pA{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function iC(){return new uh([new tC])}let uh=(()=>{class e{static{this.\u0275prov=Yt({token:e,providedIn:"root",factory:iC})}constructor(n){this.factories=n}static create(n,r){if(null!=r){const o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||iC()),deps:[[e,new ci,new lo]]}}find(n){const r=this.factories.find(o=>o.supports(n));if(null!=r)return r;throw new Ae(901,!1)}}return e})();function sC(){return new dh([new oC])}let dh=(()=>{class e{static{this.\u0275prov=Yt({token:e,providedIn:"root",factory:sC})}constructor(n){this.factories=n}static create(n,r){if(r){const o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||sC()),deps:[[e,new ci,new lo]]}}find(n){const r=this.factories.find(o=>o.supports(n));if(r)return r;throw new Ae(901,!1)}}return e})();const vA=XD(null,"core",[]);let yA=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(qn(ji))}}static{this.\u0275mod=Es({type:e})}static{this.\u0275inj=Or({})}}return e})();function XA(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}function qA(e){const t=L(null);try{return e()}finally{L(t)}}function tR(e,t){const n=sn(e),r=t.elementInjector||Pi();return new gl(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function nR(e){const t=sn(e);if(!t)return null;const n=new gl(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}},4341:(Ct,We,O)=>{"use strict";O.d(We,{YN:()=>Co,zX:()=>yo,VZ:()=>Ao,cz:()=>Ge,kq:()=>ie,vO:()=>Lt});var i=O(4438),ne=(O(177),O(6648)),X=O(1985),Me=O(3073),Ie=O(8750),L=O(3794),Pe=O(4360),je=O(6450),ye=O(8496),De=O(6354);const ie=new i.nKC("");function Ce(_){return null==_||("string"==typeof _||Array.isArray(_))&&0===_.length}const Ge=new i.nKC("");function at(_){return null}function Ue(_){return null!=_}function H(_){return(0,i.jNT)(_)?(0,ne.H)(_):_}function q(_){let w={};return _.forEach(v=>{w=null!=v?{...w,...v}:w}),0===Object.keys(w).length?null:w}function ae(_,w){return w.map(v=>v(_))}function Oe(_){return _.map(w=>function $(_){return!_.validate}(w)?w:v=>w.validate(v))}function Xe(_){return null!=_?function Le(_){if(!_)return null;const w=_.filter(Ue);return 0==w.length?null:function(v){return q(ae(v,w))}}(Oe(_)):null}function Tt(_){return null!=_?function It(_){if(!_)return null;const w=_.filter(Ue);return 0==w.length?null:function(v){return function te(..._){const w=(0,L.ms)(_),{args:v,keys:P}=(0,Me.D)(_),re=new X.c(wt=>{const{length:Gt}=v;if(!Gt)return void wt.complete();const nn=new Array(Gt);let Eo=Gt,bo=Gt;for(let Xo=0;Xo<Gt;Xo++){let No=!1;(0,Ie.Tg)(v[Xo]).subscribe((0,Pe._)(wt,Po=>{No||(No=!0,bo--),nn[Xo]=Po},()=>Eo--,void 0,()=>{(!Eo||!No)&&(bo||wt.next(P?(0,ye.e)(P,nn):nn),wt.complete())}))}});return w?re.pipe((0,je.I)(w)):re}(ae(v,w).map(H)).pipe((0,De.T)(q))}}(Oe(_)):null}class Ae{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(w){this._rawValidators=w||[],this._composedValidatorFn=Xe(this._rawValidators)}_setAsyncValidators(w){this._rawAsyncValidators=w||[],this._composedAsyncValidatorFn=Tt(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(w){this._onDestroyCallbacks.push(w)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(w=>w()),this._onDestroyCallbacks=[]}reset(w=void 0){this.control&&this.control.reset(w)}hasError(w,v){return!!this.control&&this.control.hasError(w,v)}getError(w,v){return this.control?this.control.getError(w,v):null}}class Lt extends Ae{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}const cr=new i.nKC("CallSetDisabledState",{providedIn:"root",factory:()=>Tr}),Tr="always";function fr(_){return"number"==typeof _?_:parseFloat(_)}Promise.resolve(),Promise.resolve();let ar=(()=>{class _{constructor(){this._validator=at}ngOnChanges(v){if(this.inputName in v){const P=this.normalizeInput(v[this.inputName].currentValue);this._enabled=this.enabled(P),this._validator=this._enabled?this.createValidator(P):at,this._onChange&&this._onChange()}}validate(v){return this._validator(v)}registerOnValidatorChange(v){this._onChange=v}enabled(v){return null!=v}static{this.\u0275fac=function(P){return new(P||_)}}static{this.\u0275dir=i.FsC({type:_,features:[i.OA$]})}}return _})();const wi={provide:Ge,useExisting:(0,i.Rfq)(()=>yo),multi:!0};let yo=(()=>{class _ extends ar{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=v=>fr(v),this.createValidator=v=>function de(_){return w=>{if(Ce(w.value)||Ce(_))return null;const v=parseFloat(w.value);return!isNaN(v)&&v>_?{max:{max:_,actual:w.value}}:null}}(v)}static{this.\u0275fac=(()=>{let v;return function(re){return(v||(v=i.xGo(_)))(re||_)}})()}static{this.\u0275dir=i.FsC({type:_,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(P,re){2&P&&i.BMQ("max",re._enabled?re.max:null)},inputs:{max:"max"},features:[i.Jv_([wi]),i.Vt3]})}}return _})();const no={provide:Ge,useExisting:(0,i.Rfq)(()=>Ao),multi:!0};let Ao=(()=>{class _ extends ar{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=v=>fr(v),this.createValidator=v=>function Z(_){return w=>{if(Ce(w.value)||Ce(_))return null;const v=parseFloat(w.value);return!isNaN(v)&&v<_?{min:{min:_,actual:w.value}}:null}}(v)}static{this.\u0275fac=(()=>{let v;return function(re){return(v||(v=i.xGo(_)))(re||_)}})()}static{this.\u0275dir=i.FsC({type:_,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(P,re){2&P&&i.BMQ("min",re._enabled?re.min:null)},inputs:{min:"min"},features:[i.Jv_([no]),i.Vt3]})}}return _})(),co=(()=>{class _{static{this.\u0275fac=function(P){return new(P||_)}}static{this.\u0275mod=i.$C({type:_})}static{this.\u0275inj=i.G2t({})}}return _})(),Co=(()=>{class _{static withConfig(v){return{ngModule:_,providers:[{provide:cr,useValue:v.callSetDisabledState??Tr}]}}static{this.\u0275fac=function(P){return new(P||_)}}static{this.\u0275mod=i.$C({type:_})}static{this.\u0275inj=i.G2t({imports:[co]})}}return _})()},345:(Ct,We,O)=>{"use strict";O.d(We,{Bb:()=>Ft,hE:()=>he,sG:()=>Tt});var i=O(4438),C=O(177);class ne extends C.VF{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class X extends ne{static makeCurrent(){(0,C.ZD)(new X)}onAndCancel(Se,z,ve){return Se.addEventListener(z,ve),()=>{Se.removeEventListener(z,ve)}}dispatchEvent(Se,z){Se.dispatchEvent(z)}remove(Se){Se.parentNode&&Se.parentNode.removeChild(Se)}createElement(Se,z){return(z=z||this.getDefaultDocument()).createElement(Se)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(Se){return Se.nodeType===Node.ELEMENT_NODE}isShadowRoot(Se){return Se instanceof DocumentFragment}getGlobalEventTarget(Se,z){return"window"===z?window:"document"===z?Se:"body"===z?Se.body:null}getBaseHref(Se){const z=function Ie(){return Me=Me||document.querySelector("base"),Me?Me.getAttribute("href"):null}();return null==z?null:function L(le){return new URL(le,document.baseURI).pathname}(z)}resetBaseElement(){Me=null}getUserAgent(){return window.navigator.userAgent}getCookie(Se){return(0,C._b)(document.cookie,Se)}}let Me=null,je=(()=>{class le{build(){return new XMLHttpRequest}static{this.\u0275fac=function(ve){return new(ve||le)}}static{this.\u0275prov=i.jDH({token:le,factory:le.\u0275fac})}}return le})();const ye=new i.nKC("");let te=(()=>{class le{constructor(z,ve){this._zone=ve,this._eventNameToPlugin=new Map,z.forEach(et=>{et.manager=this}),this._plugins=z.slice().reverse()}addEventListener(z,ve,et){return this._findPluginFor(ve).addEventListener(z,ve,et)}getZone(){return this._zone}_findPluginFor(z){let ve=this._eventNameToPlugin.get(z);if(ve)return ve;if(ve=this._plugins.find(gt=>gt.supports(z)),!ve)throw new i.wOt(5101,!1);return this._eventNameToPlugin.set(z,ve),ve}static{this.\u0275fac=function(ve){return new(ve||le)(i.KVO(ye),i.KVO(i.SKi))}}static{this.\u0275prov=i.jDH({token:le,factory:le.\u0275fac})}}return le})();class De{constructor(Se){this._doc=Se}}const pe="ng-app-id";let ue=(()=>{class le{constructor(z,ve,et,gt={}){this.doc=z,this.appId=ve,this.nonce=et,this.platformId=gt,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=(0,C.Vy)(gt),this.resetHostNodes()}addStyles(z){for(const ve of z)1===this.changeUsageCount(ve,1)&&this.onStyleAdded(ve)}removeStyles(z){for(const ve of z)this.changeUsageCount(ve,-1)<=0&&this.onStyleRemoved(ve)}ngOnDestroy(){const z=this.styleNodesInDOM;z&&(z.forEach(ve=>ve.remove()),z.clear());for(const ve of this.getAllStyles())this.onStyleRemoved(ve);this.resetHostNodes()}addHost(z){this.hostNodes.add(z);for(const ve of this.getAllStyles())this.addStyleToHost(z,ve)}removeHost(z){this.hostNodes.delete(z)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(z){for(const ve of this.hostNodes)this.addStyleToHost(ve,z)}onStyleRemoved(z){const ve=this.styleRef;ve.get(z)?.elements?.forEach(et=>et.remove()),ve.delete(z)}collectServerRenderedStyles(){const z=this.doc.head?.querySelectorAll(`style[${pe}="${this.appId}"]`);if(z?.length){const ve=new Map;return z.forEach(et=>{null!=et.textContent&&ve.set(et.textContent,et)}),ve}return null}changeUsageCount(z,ve){const et=this.styleRef;if(et.has(z)){const gt=et.get(z);return gt.usage+=ve,gt.usage}return et.set(z,{usage:ve,elements:[]}),ve}getStyleElement(z,ve){const et=this.styleNodesInDOM,gt=et?.get(ve);if(gt?.parentNode===z)return et.delete(ve),gt.removeAttribute(pe),gt;{const Et=this.doc.createElement("style");return this.nonce&&Et.setAttribute("nonce",this.nonce),Et.textContent=ve,this.platformIsServer&&Et.setAttribute(pe,this.appId),z.appendChild(Et),Et}}addStyleToHost(z,ve){const et=this.getStyleElement(z,ve),gt=this.styleRef,Et=gt.get(ve)?.elements;Et?Et.push(et):gt.set(ve,{elements:[et],usage:1})}resetHostNodes(){const z=this.hostNodes;z.clear(),z.add(this.doc.head)}static{this.\u0275fac=function(ve){return new(ve||le)(i.KVO(C.qQ),i.KVO(i.sZ2),i.KVO(i.BIS,8),i.KVO(i.Agw))}}static{this.\u0275prov=i.jDH({token:le,factory:le.\u0275fac})}}return le})();const ie={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},Ve=/%COMP%/g,Ee=new i.nKC("",{providedIn:"root",factory:()=>!0});function Ge(le,Se){return Se.map(z=>z.replace(Ve,le))}let V=(()=>{class le{constructor(z,ve,et,gt,Et,Qt,kt,ln=null){this.eventManager=z,this.sharedStylesHost=ve,this.appId=et,this.removeStylesOnCompDestroy=gt,this.doc=Et,this.platformId=Qt,this.ngZone=kt,this.nonce=ln,this.rendererByCompId=new Map,this.platformIsServer=(0,C.Vy)(Qt),this.defaultRenderer=new A(z,Et,kt,this.platformIsServer)}createRenderer(z,ve){if(!z||!ve)return this.defaultRenderer;this.platformIsServer&&ve.encapsulation===i.gXe.ShadowDom&&(ve={...ve,encapsulation:i.gXe.Emulated});const et=this.getOrCreateRenderer(z,ve);return et instanceof Ke?et.applyToHost(z):et instanceof Re&&et.applyStyles(),et}getOrCreateRenderer(z,ve){const et=this.rendererByCompId;let gt=et.get(ve.id);if(!gt){const Et=this.doc,Qt=this.ngZone,kt=this.eventManager,ln=this.sharedStylesHost,Sn=this.removeStylesOnCompDestroy,Tn=this.platformIsServer;switch(ve.encapsulation){case i.gXe.Emulated:gt=new Ke(kt,ln,ve,this.appId,Sn,Et,Qt,Tn);break;case i.gXe.ShadowDom:return new _e(kt,ln,z,ve,Et,Qt,this.nonce,Tn);default:gt=new Re(kt,ln,ve,Sn,Et,Qt,Tn)}et.set(ve.id,gt)}return gt}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(ve){return new(ve||le)(i.KVO(te),i.KVO(ue),i.KVO(i.sZ2),i.KVO(Ee),i.KVO(C.qQ),i.KVO(i.Agw),i.KVO(i.SKi),i.KVO(i.BIS))}}static{this.\u0275prov=i.jDH({token:le,factory:le.\u0275fac})}}return le})();class A{constructor(Se,z,ve,et){this.eventManager=Se,this.doc=z,this.ngZone=ve,this.platformIsServer=et,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(Se,z){return z?this.doc.createElementNS(ie[z]||z,Se):this.doc.createElement(Se)}createComment(Se){return this.doc.createComment(Se)}createText(Se){return this.doc.createTextNode(Se)}appendChild(Se,z){(de(Se)?Se.content:Se).appendChild(z)}insertBefore(Se,z,ve){Se&&(de(Se)?Se.content:Se).insertBefore(z,ve)}removeChild(Se,z){Se&&Se.removeChild(z)}selectRootElement(Se,z){let ve="string"==typeof Se?this.doc.querySelector(Se):Se;if(!ve)throw new i.wOt(-5104,!1);return z||(ve.textContent=""),ve}parentNode(Se){return Se.parentNode}nextSibling(Se){return Se.nextSibling}setAttribute(Se,z,ve,et){if(et){z=et+":"+z;const gt=ie[et];gt?Se.setAttributeNS(gt,z,ve):Se.setAttribute(z,ve)}else Se.setAttribute(z,ve)}removeAttribute(Se,z,ve){if(ve){const et=ie[ve];et?Se.removeAttributeNS(et,z):Se.removeAttribute(`${ve}:${z}`)}else Se.removeAttribute(z)}addClass(Se,z){Se.classList.add(z)}removeClass(Se,z){Se.classList.remove(z)}setStyle(Se,z,ve,et){et&(i.czy.DashCase|i.czy.Important)?Se.style.setProperty(z,ve,et&i.czy.Important?"important":""):Se.style[z]=ve}removeStyle(Se,z,ve){ve&i.czy.DashCase?Se.style.removeProperty(z):Se.style[z]=""}setProperty(Se,z,ve){null!=Se&&(Se[z]=ve)}setValue(Se,z){Se.nodeValue=z}listen(Se,z,ve){if("string"==typeof Se&&!(Se=(0,C.QT)().getGlobalEventTarget(this.doc,Se)))throw new Error(`Unsupported event target ${Se} for event ${z}`);return this.eventManager.addEventListener(Se,z,this.decoratePreventDefault(ve))}decoratePreventDefault(Se){return z=>{if("__ngUnwrap__"===z)return Se;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>Se(z)):Se(z))&&z.preventDefault()}}}function de(le){return"TEMPLATE"===le.tagName&&void 0!==le.content}class _e extends A{constructor(Se,z,ve,et,gt,Et,Qt,kt){super(Se,gt,Et,kt),this.sharedStylesHost=z,this.hostEl=ve,this.shadowRoot=ve.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const ln=Ge(et.id,et.styles);for(const Sn of ln){const Tn=document.createElement("style");Qt&&Tn.setAttribute("nonce",Qt),Tn.textContent=Sn,this.shadowRoot.appendChild(Tn)}}nodeOrShadowRoot(Se){return Se===this.hostEl?this.shadowRoot:Se}appendChild(Se,z){return super.appendChild(this.nodeOrShadowRoot(Se),z)}insertBefore(Se,z,ve){return super.insertBefore(this.nodeOrShadowRoot(Se),z,ve)}removeChild(Se,z){return super.removeChild(this.nodeOrShadowRoot(Se),z)}parentNode(Se){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(Se)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class Re extends A{constructor(Se,z,ve,et,gt,Et,Qt,kt){super(Se,gt,Et,Qt),this.sharedStylesHost=z,this.removeStylesOnCompDestroy=et,this.styles=kt?Ge(kt,ve.styles):ve.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class Ke extends Re{constructor(Se,z,ve,et,gt,Et,Qt,kt){const ln=et+"-"+ve.id;super(Se,z,ve,gt,Et,Qt,kt,ln),this.contentAttr=function Ce(le){return"_ngcontent-%COMP%".replace(Ve,le)}(ln),this.hostAttr=function ke(le){return"_nghost-%COMP%".replace(Ve,le)}(ln)}applyToHost(Se){this.applyStyles(),this.setAttribute(Se,this.hostAttr,"")}createElement(Se,z){const ve=super.createElement(Se,z);return super.setAttribute(ve,this.contentAttr,""),ve}}let pt=(()=>{class le extends De{constructor(z){super(z)}supports(z){return!0}addEventListener(z,ve,et){return z.addEventListener(ve,et,!1),()=>this.removeEventListener(z,ve,et)}removeEventListener(z,ve,et){return z.removeEventListener(ve,et)}static{this.\u0275fac=function(ve){return new(ve||le)(i.KVO(C.qQ))}}static{this.\u0275prov=i.jDH({token:le,factory:le.\u0275fac})}}return le})();const Rt=["alt","control","meta","shift"],ze={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},at={alt:le=>le.altKey,control:le=>le.ctrlKey,meta:le=>le.metaKey,shift:le=>le.shiftKey};let Ue=(()=>{class le extends De{constructor(z){super(z)}supports(z){return null!=le.parseEventName(z)}addEventListener(z,ve,et){const gt=le.parseEventName(ve),Et=le.eventCallback(gt.fullKey,et,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>(0,C.QT)().onAndCancel(z,gt.domEventName,Et))}static parseEventName(z){const ve=z.toLowerCase().split("."),et=ve.shift();if(0===ve.length||"keydown"!==et&&"keyup"!==et)return null;const gt=le._normalizeKey(ve.pop());let Et="",Qt=ve.indexOf("code");if(Qt>-1&&(ve.splice(Qt,1),Et="code."),Rt.forEach(ln=>{const Sn=ve.indexOf(ln);Sn>-1&&(ve.splice(Sn,1),Et+=ln+".")}),Et+=gt,0!=ve.length||0===gt.length)return null;const kt={};return kt.domEventName=et,kt.fullKey=Et,kt}static matchEventFullKeyCode(z,ve){let et=ze[z.key]||z.key,gt="";return ve.indexOf("code.")>-1&&(et=z.code,gt="code."),!(null==et||!et)&&(et=et.toLowerCase()," "===et?et="space":"."===et&&(et="dot"),Rt.forEach(Et=>{Et!==et&&(0,at[Et])(z)&&(gt+=Et+".")}),gt+=et,gt===ve)}static eventCallback(z,ve,et){return gt=>{le.matchEventFullKeyCode(gt,z)&&et.runGuarded(()=>ve(gt))}}static _normalizeKey(z){return"esc"===z?"escape":z}static{this.\u0275fac=function(ve){return new(ve||le)(i.KVO(C.qQ))}}static{this.\u0275prov=i.jDH({token:le,factory:le.\u0275fac})}}return le})();const Tt=(0,i.oH4)(i.fpN,"browser",[{provide:i.Agw,useValue:C.AJ},{provide:i.PLl,useValue:function Oe(){X.makeCurrent()},multi:!0},{provide:C.qQ,useFactory:function Xe(){return(0,i.TL$)(document),document},deps:[]}]),vt=new i.nKC(""),xt=[{provide:i.e01,useClass:class Pe{addToWindow(Se){i.JZv.getAngularTestability=(ve,et=!0)=>{const gt=Se.findTestabilityInTree(ve,et);if(null==gt)throw new i.wOt(5103,!1);return gt},i.JZv.getAllAngularTestabilities=()=>Se.getAllTestabilities(),i.JZv.getAllAngularRootElements=()=>Se.getAllRootElements(),i.JZv.frameworkStabilizers||(i.JZv.frameworkStabilizers=[]),i.JZv.frameworkStabilizers.push(ve=>{const et=i.JZv.getAllAngularTestabilities();let gt=et.length;const Et=function(){gt--,0==gt&&ve()};et.forEach(Qt=>{Qt.whenStable(Et)})})}findTestabilityInTree(Se,z,ve){return null==z?null:Se.getTestability(z)??(ve?(0,C.QT)().isShadowRoot(z)?this.findTestabilityInTree(Se,z.host,!0):this.findTestabilityInTree(Se,z.parentElement,!0):null)}},deps:[]},{provide:i.WHO,useClass:i.NYb,deps:[i.SKi,i.giA,i.e01]},{provide:i.NYb,useClass:i.NYb,deps:[i.SKi,i.giA,i.e01]}],Pt=[{provide:i.H8p,useValue:"root"},{provide:i.zcH,useFactory:function Le(){return new i.zcH},deps:[]},{provide:ye,useClass:pt,multi:!0,deps:[C.qQ,i.SKi,i.Agw]},{provide:ye,useClass:Ue,multi:!0,deps:[C.qQ]},V,ue,te,{provide:i._9s,useExisting:V},{provide:C.N0,useClass:je,deps:[]},[]];let Ft=(()=>{class le{constructor(z){}static withServerTransition(z){return{ngModule:le,providers:[{provide:i.sZ2,useValue:z.appId}]}}static{this.\u0275fac=function(ve){return new(ve||le)(i.KVO(vt,12))}}static{this.\u0275mod=i.$C({type:le})}static{this.\u0275inj=i.G2t({providers:[...Pt,...xt],imports:[C.MD,i.Hbi]})}}return le})(),he=(()=>{class le{constructor(z){this._doc=z}getTitle(){return this._doc.title}setTitle(z){this._doc.title=z||""}static{this.\u0275fac=function(ve){return new(ve||le)(i.KVO(C.qQ))}}static{this.\u0275prov=i.jDH({token:le,factory:le.\u0275fac,providedIn:"root"})}}return le})()},70:(Ct,We,O)=>{"use strict";O.d(We,{nX:()=>Vr,Zp:()=>xn,Z:()=>He,Xk:()=>he,Kp:()=>rs,b:()=>Hn,Ix:()=>Dn,Wk:()=>qo,iI:()=>Na,Sd:()=>le});var i=O(467),C=O(4438),ne=O(1985),X=O(8071),Ie=O(6648),L=O(7673),Pe=O(4412),je=O(4572);const te=(0,O(1853).L)(c=>function(){c(this),this.name="EmptyError",this.message="no elements in sequence"});var De=O(1397),pe=O(3669);function ue(c=1/0){return(0,De.Z)(pe.D,c)}var Ve=O(3794);function ge(...c){return function ie(){return ue(1)}()((0,Ie.H)(c,(0,Ve.lI)(c)))}var se=O(8750);function me(c){return new ne.c(p=>{(0,se.Tg)(c()).subscribe(p)})}var Ze=O(1203);function Ee(c,p){const u=(0,X.T)(c)?c:()=>c,g=E=>E.error(u());return new ne.c(p?E=>p.schedule(g,0,E):g)}const Ce=new ne.c(c=>c.complete());var V=O(8359),A=O(9974),Y=O(4360);function Z(){return(0,A.N)((c,p)=>{let u=null;c._refCount++;const g=(0,Y._)(p,void 0,void 0,void 0,()=>{if(!c||c._refCount<=0||0<--c._refCount)return void(u=null);const E=c._connection,R=u;u=null,E&&(!R||E===R)&&E.unsubscribe(),p.unsubscribe()});c.subscribe(g),g.closed||(u=c.connect())})}class de extends ne.c{constructor(p,u){super(),this.source=p,this.subjectFactory=u,this._subject=null,this._refCount=0,this._connection=null,(0,A.S)(p)&&(this.lift=p.lift)}_subscribe(p){return this.getSubject().subscribe(p)}getSubject(){const p=this._subject;return(!p||p.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;const{_connection:p}=this;this._subject=this._connection=null,p?.unsubscribe()}connect(){let p=this._connection;if(!p){p=this._connection=new V.yU;const u=this.getSubject();p.add(this.source.subscribe((0,Y._)(u,void 0,()=>{this._teardown(),u.complete()},g=>{this._teardown(),u.error(g)},()=>this._teardown()))),p.closed&&(this._connection=null,p=V.yU.EMPTY)}return p}refCount(){return Z()(this)}}var _e=O(1413),Re=O(177),Ke=O(6354),pt=O(5558);function Rt(c){return c<=0?()=>Ce:(0,A.N)((p,u)=>{let g=0;p.subscribe((0,Y._)(u,E=>{++g<=c&&(u.next(E),c<=g&&u.complete())}))})}var at=O(5964);function Ue(c){return(0,A.N)((p,u)=>{let g=!1;p.subscribe((0,Y._)(u,E=>{g=!0,u.next(E)},()=>{g||u.next(c),u.complete()}))})}function H(c=q){return(0,A.N)((p,u)=>{let g=!1;p.subscribe((0,Y._)(u,E=>{g=!0,u.next(E)},()=>g?u.complete():u.error(c())))})}function q(){return new te}function ae(c,p){const u=arguments.length>=2;return g=>g.pipe(c?(0,at.p)((E,R)=>c(E,R,g)):pe.D,Rt(1),u?Ue(p):H(()=>new te))}function $(c,p){return(0,X.T)(p)?(0,De.Z)(c,p,1):(0,De.Z)(c,1)}function Oe(c,p,u){const g=(0,X.T)(c)||p||u?{next:c,error:p,complete:u}:c;return g?(0,A.N)((E,R)=>{var W;null===(W=g.subscribe)||void 0===W||W.call(g);let Ne=!0;E.subscribe((0,Y._)(R,we=>{var dt;null===(dt=g.next)||void 0===dt||dt.call(g,we),R.next(we)},()=>{var we;Ne=!1,null===(we=g.complete)||void 0===we||we.call(g),R.complete()},we=>{var dt;Ne=!1,null===(dt=g.error)||void 0===dt||dt.call(g,we),R.error(we)},()=>{var we,dt;Ne&&(null===(we=g.unsubscribe)||void 0===we||we.call(g)),null===(dt=g.finalize)||void 0===dt||dt.call(g)}))}):pe.D}function Le(c){return(0,A.N)((p,u)=>{let R,g=null,E=!1;g=p.subscribe((0,Y._)(u,void 0,void 0,W=>{R=(0,se.Tg)(c(W,Le(c)(p))),g?(g.unsubscribe(),g=null,R.subscribe(u)):E=!0})),E&&(g.unsubscribe(),g=null,R.subscribe(u))})}function Tt(c){return c<=0?()=>Ce:(0,A.N)((p,u)=>{let g=[];p.subscribe((0,Y._)(u,E=>{g.push(E),c<g.length&&g.shift()},()=>{for(const E of g)u.next(E);u.complete()},void 0,()=>{g=null}))})}function Pt(c){return(0,A.N)((p,u)=>{try{p.subscribe(u)}finally{u.add(c)}})}var Ft=O(5343),xe=O(345);const he="primary",Ae=Symbol("RouteTitle");class ht{constructor(p){this.params=p||{}}has(p){return Object.prototype.hasOwnProperty.call(this.params,p)}get(p){if(this.has(p)){const u=this.params[p];return Array.isArray(u)?u[0]:u}return null}getAll(p){if(this.has(p)){const u=this.params[p];return Array.isArray(u)?u:[u]}return[]}get keys(){return Object.keys(this.params)}}function Lt(c){return new ht(c)}function Ht(c,p,u){const g=u.path.split("/");if(g.length>c.length||"full"===u.pathMatch&&(p.hasChildren()||g.length<c.length))return null;const E={};for(let R=0;R<g.length;R++){const W=g[R],Ne=c[R];if(W.startsWith(":"))E[W.substring(1)]=Ne;else if(W!==Ne.path)return null}return{consumed:c.slice(0,g.length),posParams:E}}function $t(c,p){const u=c?Wt(c):void 0,g=p?Wt(p):void 0;if(!u||!g||u.length!=g.length)return!1;let E;for(let R=0;R<u.length;R++)if(E=u[R],!yt(c[E],p[E]))return!1;return!0}function Wt(c){return[...Object.keys(c),...Object.getOwnPropertySymbols(c)]}function yt(c,p){if(Array.isArray(c)&&Array.isArray(p)){if(c.length!==p.length)return!1;const u=[...c].sort(),g=[...p].sort();return u.every((E,R)=>g[R]===E)}return c===p}function At(c){return c.length>0?c[c.length-1]:null}function dn(c){return function Me(c){return!!c&&(c instanceof ne.c||(0,X.T)(c.lift)&&(0,X.T)(c.subscribe))}(c)?c:(0,C.jNT)(c)?(0,Ie.H)(Promise.resolve(c)):(0,L.of)(c)}const On={exact:function yr(c,p,u){if(!tr(c.segments,p.segments)||!Vt(c.segments,p.segments,u)||c.numberOfChildren!==p.numberOfChildren)return!1;for(const g in p.children)if(!c.children[g]||!yr(c.children[g],p.children[g],u))return!1;return!0},subset:Ln},bn={exact:function hn(c,p){return $t(c,p)},subset:function Nt(c,p){return Object.keys(p).length<=Object.keys(c).length&&Object.keys(p).every(u=>yt(c[u],p[u]))},ignored:()=>!0};function er(c,p,u){return On[u.paths](c.root,p.root,u.matrixParams)&&bn[u.queryParams](c.queryParams,p.queryParams)&&!("exact"===u.fragment&&c.fragment!==p.fragment)}function Ln(c,p,u){return Zt(c,p,p.segments,u)}function Zt(c,p,u,g){if(c.segments.length>u.length){const E=c.segments.slice(0,u.length);return!(!tr(E,u)||p.hasChildren()||!Vt(E,u,g))}if(c.segments.length===u.length){if(!tr(c.segments,u)||!Vt(c.segments,u,g))return!1;for(const E in p.children)if(!c.children[E]||!Ln(c.children[E],p.children[E],g))return!1;return!0}{const E=u.slice(0,c.segments.length),R=u.slice(c.segments.length);return!!(tr(c.segments,E)&&Vt(c.segments,E,g)&&c.children[he])&&Zt(c.children[he],p,R,g)}}function Vt(c,p,u){return p.every((g,E)=>bn[u](c[E].parameters,g.parameters))}class Wn{constructor(p=new Mt([],{}),u={},g=null){this.root=p,this.queryParams=u,this.fragment=g}get queryParamMap(){return this._queryParamMap??=Lt(this.queryParams),this._queryParamMap}toString(){return z.serialize(this)}}class Mt{constructor(p,u){this.segments=p,this.children=u,this.parent=null,Object.values(u).forEach(g=>g.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ve(this)}}class $n{constructor(p,u){this.path=p,this.parameters=u}get parameterMap(){return this._parameterMap??=Lt(this.parameters),this._parameterMap}toString(){return Tn(this)}}function tr(c,p){return c.length===p.length&&c.every((u,g)=>u.path===p[g].path)}let le=(()=>{class c{static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:()=>new Se,providedIn:"root"})}}return c})();class Se{parse(p){const u=new Yt(p);return new Wn(u.parseRootSegment(),u.parseQueryParams(),u.parseFragment())}serialize(p){const u=`/${et(p.root,!0)}`,g=function zr(c){const p=Object.entries(c).map(([u,g])=>Array.isArray(g)?g.map(E=>`${Et(u)}=${Et(E)}`).join("&"):`${Et(u)}=${Et(g)}`).filter(u=>u);return p.length?`?${p.join("&")}`:""}(p.queryParams);return`${u}${g}${"string"==typeof p.fragment?`#${function Qt(c){return encodeURI(c)}(p.fragment)}`:""}`}}const z=new Se;function ve(c){return c.segments.map(p=>Tn(p)).join("/")}function et(c,p){if(!c.hasChildren())return ve(c);if(p){const u=c.children[he]?et(c.children[he],!1):"",g=[];return Object.entries(c.children).forEach(([E,R])=>{E!==he&&g.push(`${E}:${et(R,!1)}`)}),g.length>0?`${u}(${g.join("//")})`:u}{const u=function nr(c,p){let u=[];return Object.entries(c.children).forEach(([g,E])=>{g===he&&(u=u.concat(p(E,g)))}),Object.entries(c.children).forEach(([g,E])=>{g!==he&&(u=u.concat(p(E,g)))}),u}(c,(g,E)=>E===he?[et(c.children[he],!1)]:[`${E}:${et(g,!1)}`]);return 1===Object.keys(c.children).length&&null!=c.children[he]?`${ve(c)}/${u[0]}`:`${ve(c)}/(${u.join("//")})`}}function gt(c){return encodeURIComponent(c).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Et(c){return gt(c).replace(/%3B/gi,";")}function kt(c){return gt(c).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function ln(c){return decodeURIComponent(c)}function Sn(c){return ln(c.replace(/\+/g,"%20"))}function Tn(c){return`${kt(c.path)}${function rr(c){return Object.entries(c).map(([p,u])=>`;${kt(p)}=${kt(u)}`).join("")}(c.parameters)}`}const Wr=/^[^\/()?;#]+/;function In(c){const p=c.match(Wr);return p?p[0]:""}const _t=/^[^\/()?;=#]+/,cr=/^[^=?&#]+/,Cr=/^[^&#]+/;class Yt{constructor(p){this.url=p,this.remaining=p}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new Mt([],{}):new Mt([],this.parseChildren())}parseQueryParams(){const p={};if(this.consumeOptional("?"))do{this.parseQueryParam(p)}while(this.consumeOptional("&"));return p}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const p=[];for(this.peekStartsWith("(")||p.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),p.push(this.parseSegment());let u={};this.peekStartsWith("/(")&&(this.capture("/"),u=this.parseParens(!0));let g={};return this.peekStartsWith("(")&&(g=this.parseParens(!1)),(p.length>0||Object.keys(u).length>0)&&(g[he]=new Mt(p,u)),g}parseSegment(){const p=In(this.remaining);if(""===p&&this.peekStartsWith(";"))throw new C.wOt(4009,!1);return this.capture(p),new $n(ln(p),this.parseMatrixParams())}parseMatrixParams(){const p={};for(;this.consumeOptional(";");)this.parseParam(p);return p}parseParam(p){const u=function eo(c){const p=c.match(_t);return p?p[0]:""}(this.remaining);if(!u)return;this.capture(u);let g="";if(this.consumeOptional("=")){const E=In(this.remaining);E&&(g=E,this.capture(g))}p[ln(u)]=ln(g)}parseQueryParam(p){const u=function Tr(c){const p=c.match(cr);return p?p[0]:""}(this.remaining);if(!u)return;this.capture(u);let g="";if(this.consumeOptional("=")){const W=function Ar(c){const p=c.match(Cr);return p?p[0]:""}(this.remaining);W&&(g=W,this.capture(g))}const E=Sn(u),R=Sn(g);if(p.hasOwnProperty(E)){let W=p[E];Array.isArray(W)||(W=[W],p[E]=W),W.push(R)}else p[E]=R}parseParens(p){const u={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const g=In(this.remaining),E=this.remaining[g.length];if("/"!==E&&")"!==E&&";"!==E)throw new C.wOt(4010,!1);let R;g.indexOf(":")>-1?(R=g.slice(0,g.indexOf(":")),this.capture(R),this.capture(":")):p&&(R=he);const W=this.parseChildren();u[R]=1===Object.keys(W).length?W[he]:new Mt([],W),this.consumeOptional("//")}return u}peekStartsWith(p){return this.remaining.startsWith(p)}consumeOptional(p){return!!this.peekStartsWith(p)&&(this.remaining=this.remaining.substring(p.length),!0)}capture(p){if(!this.consumeOptional(p))throw new C.wOt(4011,!1)}}function Rr(c){return c.segments.length>0?new Mt([],{[he]:c}):c}function Or(c){const p={};for(const[g,E]of Object.entries(c.children)){const R=Or(E);if(g===he&&0===R.segments.length&&R.hasChildren())for(const[W,Ne]of Object.entries(R.children))p[W]=Ne;else(R.segments.length>0||R.hasChildren())&&(p[g]=R)}return function Vn(c){if(1===c.numberOfChildren&&c.children[he]){const p=c.children[he];return new Mt(c.segments.concat(p.segments),p.children)}return c}(new Mt(c.segments,p))}function pn(c){return c instanceof Wn}function Nr(c){let p;const E=Rr(function u(R){const W={};for(const we of R.children){const dt=u(we);W[we.outlet]=dt}const Ne=new Mt(R.url,W);return R===c&&(p=Ne),Ne}(c.root));return p??E}function Xn(c,p,u,g){let E=c;for(;E.parent;)E=E.parent;if(0===p.length)return _n(E,E,E,u,g);const R=function Zn(c){if("string"==typeof c[0]&&1===c.length&&"/"===c[0])return new Bt(!0,0,c);let p=0,u=!1;const g=c.reduce((E,R,W)=>{if("object"==typeof R&&null!=R){if(R.outlets){const Ne={};return Object.entries(R.outlets).forEach(([we,dt])=>{Ne[we]="string"==typeof dt?dt.split("/"):dt}),[...E,{outlets:Ne}]}if(R.segmentPath)return[...E,R.segmentPath]}return"string"!=typeof R?[...E,R]:0===W?(R.split("/").forEach((Ne,we)=>{0==we&&"."===Ne||(0==we&&""===Ne?u=!0:".."===Ne?p++:""!=Ne&&E.push(Ne))}),E):[...E,R]},[]);return new Bt(u,p,g)}(p);if(R.toRoot())return _n(E,E,new Mt([],{}),u,g);const W=function tn(c,p,u){if(c.isAbsolute)return new Jt(p,!0,0);if(!u)return new Jt(p,!1,NaN);if(null===u.parent)return new Jt(u,!0,0);const g=Bn(c.commands[0])?0:1;return function ur(c,p,u){let g=c,E=p,R=u;for(;R>E;){if(R-=E,g=g.parent,!g)throw new C.wOt(4005,!1);E=g.segments.length}return new Jt(g,!1,E-R)}(u,u.segments.length-1+g,c.numberOfDoubleDots)}(R,E,c),Ne=W.processChildren?dr(W.segmentGroup,W.index,R.commands):sr(W.segmentGroup,W.index,R.commands);return _n(E,W.segmentGroup,Ne,u,g)}function Bn(c){return"object"==typeof c&&null!=c&&!c.outlets&&!c.segmentPath}function Un(c){return"object"==typeof c&&null!=c&&c.outlets}function _n(c,p,u,g,E){let W,R={};g&&Object.entries(g).forEach(([we,dt])=>{R[we]=Array.isArray(dt)?dt.map(en=>`${en}`):`${dt}`}),W=c===p?u:Er(c,p,u);const Ne=Rr(Or(W));return new Wn(Ne,R,E)}function Er(c,p,u){const g={};return Object.entries(c.children).forEach(([E,R])=>{g[E]=R===p?u:Er(R,p,u)}),new Mt(c.segments,g)}class Bt{constructor(p,u,g){if(this.isAbsolute=p,this.numberOfDoubleDots=u,this.commands=g,p&&g.length>0&&Bn(g[0]))throw new C.wOt(4003,!1);const E=g.find(Un);if(E&&E!==At(g))throw new C.wOt(4004,!1)}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class Jt{constructor(p,u,g){this.segmentGroup=p,this.processChildren=u,this.index=g}}function sr(c,p,u){if(c??=new Mt([],{}),0===c.segments.length&&c.hasChildren())return dr(c,p,u);const g=function B(c,p,u){let g=0,E=p;const R={match:!1,pathIndex:0,commandIndex:0};for(;E<c.segments.length;){if(g>=u.length)return R;const W=c.segments[E],Ne=u[g];if(Un(Ne))break;const we=`${Ne}`,dt=g<u.length-1?u[g+1]:null;if(E>0&&void 0===we)break;if(we&&dt&&"object"==typeof dt&&void 0===dt.outlets){if(!fe(we,dt,W))return R;g+=2}else{if(!fe(we,{},W))return R;g++}E++}return{match:!0,pathIndex:E,commandIndex:g}}(c,p,u),E=u.slice(g.commandIndex);if(g.match&&g.pathIndex<c.segments.length){const R=new Mt(c.segments.slice(0,g.pathIndex),{});return R.children[he]=new Mt(c.segments.slice(g.pathIndex),c.children),dr(R,0,E)}return g.match&&0===E.length?new Mt(c.segments,{}):g.match&&!c.hasChildren()?j(c,p,u):g.match?dr(c,0,E):j(c,p,u)}function dr(c,p,u){if(0===u.length)return new Mt(c.segments,{});{const g=function br(c){return Un(c[0])?c[0].outlets:{[he]:c}}(u),E={};if(Object.keys(g).some(R=>R!==he)&&c.children[he]&&1===c.numberOfChildren&&0===c.children[he].segments.length){const R=dr(c.children[he],p,u);return new Mt(c.segments,R.children)}return Object.entries(g).forEach(([R,W])=>{"string"==typeof W&&(W=[W]),null!==W&&(E[R]=sr(c.children[R],p,W))}),Object.entries(c.children).forEach(([R,W])=>{void 0===g[R]&&(E[R]=W)}),new Mt(c.segments,E)}}function j(c,p,u){const g=c.segments.slice(0,p);let E=0;for(;E<u.length;){const R=u[E];if(Un(R)){const we=T(R.outlets);return new Mt(g,we)}if(0===E&&Bn(u[0])){g.push(new $n(c.segments[p].path,U(u[0]))),E++;continue}const W=Un(R)?R.outlets[he]:`${R}`,Ne=E<u.length-1?u[E+1]:null;W&&Ne&&Bn(Ne)?(g.push(new $n(W,U(Ne))),E+=2):(g.push(new $n(W,{})),E++)}return new Mt(g,{})}function T(c){const p={};return Object.entries(c).forEach(([u,g])=>{"string"==typeof g&&(g=[g]),null!==g&&(p[u]=j(new Mt([],{}),0,g))}),p}function U(c){const p={};return Object.entries(c).forEach(([u,g])=>p[u]=`${g}`),p}function fe(c,p,u){return c==u.path&&$t(p,u.parameters)}const qe="imperative";var rt=function(c){return c[c.NavigationStart=0]="NavigationStart",c[c.NavigationEnd=1]="NavigationEnd",c[c.NavigationCancel=2]="NavigationCancel",c[c.NavigationError=3]="NavigationError",c[c.RoutesRecognized=4]="RoutesRecognized",c[c.ResolveStart=5]="ResolveStart",c[c.ResolveEnd=6]="ResolveEnd",c[c.GuardsCheckStart=7]="GuardsCheckStart",c[c.GuardsCheckEnd=8]="GuardsCheckEnd",c[c.RouteConfigLoadStart=9]="RouteConfigLoadStart",c[c.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",c[c.ChildActivationStart=11]="ChildActivationStart",c[c.ChildActivationEnd=12]="ChildActivationEnd",c[c.ActivationStart=13]="ActivationStart",c[c.ActivationEnd=14]="ActivationEnd",c[c.Scroll=15]="Scroll",c[c.NavigationSkipped=16]="NavigationSkipped",c}(rt||{});class J{constructor(p,u){this.id=p,this.url=u}}class He extends J{constructor(p,u,g="imperative",E=null){super(p,u),this.type=rt.NavigationStart,this.navigationTrigger=g,this.restoredState=E}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class b extends J{constructor(p,u,g){super(p,u),this.urlAfterRedirects=g,this.type=rt.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}var x=function(c){return c[c.Redirect=0]="Redirect",c[c.SupersededByNewNavigation=1]="SupersededByNewNavigation",c[c.NoDataFromResolver=2]="NoDataFromResolver",c[c.GuardRejected=3]="GuardRejected",c}(x||{}),G=function(c){return c[c.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",c[c.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",c}(G||{});class Q extends J{constructor(p,u,g,E){super(p,u),this.reason=g,this.code=E,this.type=rt.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class Fe extends J{constructor(p,u,g,E){super(p,u),this.reason=g,this.code=E,this.type=rt.NavigationSkipped}}class be extends J{constructor(p,u,g,E){super(p,u),this.error=g,this.target=E,this.type=rt.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class st extends J{constructor(p,u,g,E){super(p,u),this.urlAfterRedirects=g,this.state=E,this.type=rt.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class nt extends J{constructor(p,u,g,E){super(p,u),this.urlAfterRedirects=g,this.state=E,this.type=rt.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class lt extends J{constructor(p,u,g,E,R){super(p,u),this.urlAfterRedirects=g,this.state=E,this.shouldActivate=R,this.type=rt.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class ot extends J{constructor(p,u,g,E){super(p,u),this.urlAfterRedirects=g,this.state=E,this.type=rt.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Qe extends J{constructor(p,u,g,E){super(p,u),this.urlAfterRedirects=g,this.state=E,this.type=rt.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class mt{constructor(p){this.route=p,this.type=rt.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class Dt{constructor(p){this.route=p,this.type=rt.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class Ot{constructor(p){this.snapshot=p,this.type=rt.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class mn{constructor(p){this.snapshot=p,this.type=rt.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class f{constructor(p){this.snapshot=p,this.type=rt.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class N{constructor(p){this.snapshot=p,this.type=rt.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class m{constructor(p,u,g){this.routerEvent=p,this.position=u,this.anchor=g,this.type=rt.Scroll}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}class F{}class oe{constructor(p){this.url=p}}class Kn{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new xn,this.attachRef=null}}let xn=(()=>{class c{constructor(){this.contexts=new Map}onChildOutletCreated(u,g){const E=this.getOrCreateContext(u);E.outlet=g,this.contexts.set(u,E)}onChildOutletDestroyed(u){const g=this.getContext(u);g&&(g.outlet=null,g.attachRef=null)}onOutletDeactivated(){const u=this.contexts;return this.contexts=new Map,u}onOutletReAttached(u){this.contexts=u}getOrCreateContext(u){let g=this.getContext(u);return g||(g=new Kn,this.contexts.set(u,g)),g}getContext(u){return this.contexts.get(u)||null}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();class Xr{constructor(p){this._root=p}get root(){return this._root.value}parent(p){const u=this.pathFromRoot(p);return u.length>1?u[u.length-2]:null}children(p){const u=Ir(p,this._root);return u?u.children.map(g=>g.value):[]}firstChild(p){const u=Ir(p,this._root);return u&&u.children.length>0?u.children[0].value:null}siblings(p){const u=jo(p,this._root);return u.length<2?[]:u[u.length-2].children.map(E=>E.value).filter(E=>E!==p)}pathFromRoot(p){return jo(p,this._root).map(u=>u.value)}}function Ir(c,p){if(c===p.value)return p;for(const u of p.children){const g=Ir(c,u);if(g)return g}return null}function jo(c,p){if(c===p.value)return[p];for(const u of p.children){const g=jo(c,u);if(g.length)return g.unshift(p),g}return[]}class jn{constructor(p,u){this.value=p,this.children=u}toString(){return`TreeNode(${this.value})`}}function mo(c){const p={};return c&&c.children.forEach(u=>p[u.value.outlet]=u),p}class qn extends Xr{constructor(p,u){super(p),this.snapshot=u,To(this,p)}toString(){return this.snapshot.toString()}}function vo(c){const p=function Ut(c){const R=new to([],{},{},"",{},he,c,null,{});return new ai("",new jn(R,[]))}(c),u=new Pe.t([new $n("",{})]),g=new Pe.t({}),E=new Pe.t({}),R=new Pe.t({}),W=new Pe.t(""),Ne=new Vr(u,g,R,W,E,he,c,p.root);return Ne.snapshot=p.root,new qn(new jn(Ne,[]),p)}class Vr{constructor(p,u,g,E,R,W,Ne,we){this.urlSubject=p,this.paramsSubject=u,this.queryParamsSubject=g,this.fragmentSubject=E,this.dataSubject=R,this.outlet=W,this.component=Ne,this._futureSnapshot=we,this.title=this.dataSubject?.pipe((0,Ke.T)(dt=>dt[Ae]))??(0,L.of)(void 0),this.url=p,this.params=u,this.queryParams=g,this.fragment=E,this.data=R}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe((0,Ke.T)(p=>Lt(p))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe((0,Ke.T)(p=>Lt(p))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function Ho(c,p,u="emptyOnly"){let g;const{routeConfig:E}=c;return g=null===p||"always"!==u&&""!==E?.path&&(p.component||p.routeConfig?.loadComponent)?{params:{...c.params},data:{...c.data},resolve:{...c.data,...c._resolvedData??{}}}:{params:{...p.params,...c.params},data:{...p.data,...c.data},resolve:{...c.data,...p.data,...E?.data,...c._resolvedData}},E&&_i(E)&&(g.resolve[Ae]=E.title),g}class to{get title(){return this.data?.[Ae]}constructor(p,u,g,E,R,W,Ne,we,dt){this.url=p,this.params=u,this.queryParams=g,this.fragment=E,this.data=R,this.outlet=W,this.component=Ne,this.routeConfig=we,this._resolve=dt}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Lt(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Lt(this.queryParams),this._queryParamMap}toString(){return`Route(url:'${this.url.map(g=>g.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class ai extends Xr{constructor(p,u){super(u),this.url=p,To(this,u)}toString(){return Ii(this._root)}}function To(c,p){p.value._routerState=c,p.children.forEach(u=>To(c,u))}function Ii(c){const p=c.children.length>0?` { ${c.children.map(Ii).join(", ")} } `:"";return`${c.value}${p}`}function li(c){if(c.snapshot){const p=c.snapshot,u=c._futureSnapshot;c.snapshot=u,$t(p.queryParams,u.queryParams)||c.queryParamsSubject.next(u.queryParams),p.fragment!==u.fragment&&c.fragmentSubject.next(u.fragment),$t(p.params,u.params)||c.paramsSubject.next(u.params),function on(c,p){if(c.length!==p.length)return!1;for(let u=0;u<c.length;++u)if(!$t(c[u],p[u]))return!1;return!0}(p.url,u.url)||c.urlSubject.next(u.url),$t(p.data,u.data)||c.dataSubject.next(u.data)}else c.snapshot=c._futureSnapshot,c.dataSubject.next(c._futureSnapshot.data)}function lo(c,p){const u=$t(c.params,p.params)&&function Gr(c,p){return tr(c,p)&&c.every((u,g)=>$t(u.parameters,p[g].parameters))}(c.url,p.url);return u&&!(!c.parent!=!p.parent)&&(!c.parent||lo(c.parent,p.parent))}function _i(c){return"string"==typeof c.title||null===c.title}let ci=(()=>{class c{constructor(){this.activated=null,this._activatedRoute=null,this.name=he,this.activateEvents=new C.bkB,this.deactivateEvents=new C.bkB,this.attachEvents=new C.bkB,this.detachEvents=new C.bkB,this.parentContexts=(0,C.WQX)(xn),this.location=(0,C.WQX)(C.c1b),this.changeDetector=(0,C.WQX)(C.gRc),this.environmentInjector=(0,C.WQX)(C.uvJ),this.inputBinder=(0,C.WQX)(Kr,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(u){if(u.name){const{firstChange:g,previousValue:E}=u.name;if(g)return;this.isTrackedInParentContexts(E)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(E)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(u){return this.parentContexts.getContext(u)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;const u=this.parentContexts.getContext(this.name);u?.route&&(u.attachRef?this.attach(u.attachRef,u.route):this.activateWith(u.route,u.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new C.wOt(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new C.wOt(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new C.wOt(4012,!1);this.location.detach();const u=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(u.instance),u}attach(u,g){this.activated=u,this._activatedRoute=g,this.location.insert(u.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(u.instance)}deactivate(){if(this.activated){const u=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(u)}}activateWith(u,g){if(this.isActivated)throw new C.wOt(4013,!1);this._activatedRoute=u;const E=this.location,W=u.snapshot.component,Ne=this.parentContexts.getOrCreateContext(this.name).children,we=new Go(u,Ne,E.injector);this.activated=E.createComponent(W,{index:E.length,injector:we,environmentInjector:g??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275dir=C.FsC({type:c,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[C.OA$]})}}return c})();class Go{__ngOutletInjector(p){return new Go(this.route,this.childContexts,p)}constructor(p,u,g){this.route=p,this.childContexts=u,this.parent=g}get(p,u){return p===Vr?this.route:p===xn?this.childContexts:this.parent.get(p,u)}}const Kr=new C.nKC("");let ui=(()=>{class c{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(u){this.unsubscribeFromRouteData(u),this.subscribeToRouteData(u)}unsubscribeFromRouteData(u){this.outletDataSubscriptions.get(u)?.unsubscribe(),this.outletDataSubscriptions.delete(u)}subscribeToRouteData(u){const{activatedRoute:g}=u,E=(0,je.z)([g.queryParams,g.params,g.data]).pipe((0,pt.n)(([R,W,Ne],we)=>(Ne={...R,...W,...Ne},0===we?(0,L.of)(Ne):Promise.resolve(Ne)))).subscribe(R=>{if(!u.isActivated||!u.activatedComponentRef||u.activatedRoute!==g||null===g.component)return void this.unsubscribeFromRouteData(u);const W=(0,C.HJs)(g.component);if(W)for(const{templateName:Ne}of W.inputs)u.activatedComponentRef.setInput(Ne,R[Ne]);else this.unsubscribeFromRouteData(u)});this.outletDataSubscriptions.set(u,E)}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac})}}return c})();function ar(c,p,u){if(u&&c.shouldReuseRoute(p.value,u.value.snapshot)){const g=u.value;g._futureSnapshot=p.value;const E=function wi(c,p,u){return p.children.map(g=>{for(const E of u.children)if(c.shouldReuseRoute(g.value,E.value.snapshot))return ar(c,g,E);return ar(c,g)})}(c,p,u);return new jn(g,E)}{if(c.shouldAttach(p.value)){const R=c.retrieve(p.value);if(null!==R){const W=R.route;return W.value._futureSnapshot=p.value,W.children=p.children.map(Ne=>ar(c,Ne)),W}}const g=function yo(c){return new Vr(new Pe.t(c.url),new Pe.t(c.params),new Pe.t(c.queryParams),new Pe.t(c.fragment),new Pe.t(c.data),c.outlet,c.component,c)}(p.value),E=p.children.map(R=>ar(c,R));return new jn(g,E)}}const no="ngNavigationCancelingError";function Ao(c,p){const{redirectTo:u,navigationBehaviorOptions:g}=pn(p)?{redirectTo:p,navigationBehaviorOptions:void 0}:p,E=zo(!1,x.Redirect);return E.url=u,E.navigationBehaviorOptions=g,E}function zo(c,p){const u=new Error(`NavigationCancelingError: ${c||""}`);return u[no]=!0,u.cancellationCode=p,u}function Wo(c){return!!c&&c[no]}let hr=(()=>{class c{static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275cmp=C.VBU({type:c,selectors:[["ng-component"]],standalone:!0,features:[C.aNF],decls:1,vars:0,template:function(g,E){1&g&&C.nrm(0,"router-outlet")},dependencies:[ci],encapsulation:2})}}return c})();function Oo(c){const p=c.children&&c.children.map(Oo),u=p?{...c,children:p}:{...c};return!u.component&&!u.loadComponent&&(p||u.loadChildren)&&u.outlet&&u.outlet!==he&&(u.component=hr),u}function Pr(c){return c.outlet||he}function Lr(c){if(!c)return null;if(c.routeConfig?._injector)return c.routeConfig._injector;for(let p=c.parent;p;p=p.parent){const u=p.routeConfig;if(u?._loadedInjector)return u._loadedInjector;if(u?._injector)return u._injector}return null}class Wi{constructor(p,u,g,E,R){this.routeReuseStrategy=p,this.futureState=u,this.currState=g,this.forwardEvent=E,this.inputBindingEnabled=R}activate(p){const u=this.futureState._root,g=this.currState?this.currState._root:null;this.deactivateChildRoutes(u,g,p),li(this.futureState.root),this.activateChildRoutes(u,g,p)}deactivateChildRoutes(p,u,g){const E=mo(u);p.children.forEach(R=>{const W=R.value.outlet;this.deactivateRoutes(R,E[W],g),delete E[W]}),Object.values(E).forEach(R=>{this.deactivateRouteAndItsChildren(R,g)})}deactivateRoutes(p,u,g){const E=p.value,R=u?u.value:null;if(E===R)if(E.component){const W=g.getContext(E.outlet);W&&this.deactivateChildRoutes(p,u,W.children)}else this.deactivateChildRoutes(p,u,g);else R&&this.deactivateRouteAndItsChildren(u,g)}deactivateRouteAndItsChildren(p,u){p.value.component&&this.routeReuseStrategy.shouldDetach(p.value.snapshot)?this.detachAndStoreRouteSubtree(p,u):this.deactivateRouteAndOutlet(p,u)}detachAndStoreRouteSubtree(p,u){const g=u.getContext(p.value.outlet),E=g&&p.value.component?g.children:u,R=mo(p);for(const W of Object.values(R))this.deactivateRouteAndItsChildren(W,E);if(g&&g.outlet){const W=g.outlet.detach(),Ne=g.children.onOutletDeactivated();this.routeReuseStrategy.store(p.value.snapshot,{componentRef:W,route:p,contexts:Ne})}}deactivateRouteAndOutlet(p,u){const g=u.getContext(p.value.outlet),E=g&&p.value.component?g.children:u,R=mo(p);for(const W of Object.values(R))this.deactivateRouteAndItsChildren(W,E);g&&(g.outlet&&(g.outlet.deactivate(),g.children.onOutletDeactivated()),g.attachRef=null,g.route=null)}activateChildRoutes(p,u,g){const E=mo(u);p.children.forEach(R=>{this.activateRoutes(R,E[R.value.outlet],g),this.forwardEvent(new N(R.value.snapshot))}),p.children.length&&this.forwardEvent(new mn(p.value.snapshot))}activateRoutes(p,u,g){const E=p.value,R=u?u.value:null;if(li(E),E===R)if(E.component){const W=g.getOrCreateContext(E.outlet);this.activateChildRoutes(p,u,W.children)}else this.activateChildRoutes(p,u,g);else if(E.component){const W=g.getOrCreateContext(E.outlet);if(this.routeReuseStrategy.shouldAttach(E.snapshot)){const Ne=this.routeReuseStrategy.retrieve(E.snapshot);this.routeReuseStrategy.store(E.snapshot,null),W.children.onOutletReAttached(Ne.contexts),W.attachRef=Ne.componentRef,W.route=Ne.route.value,W.outlet&&W.outlet.attach(Ne.componentRef,Ne.route.value),li(Ne.route.value),this.activateChildRoutes(p,null,W.children)}else{const Ne=Lr(E.snapshot);W.attachRef=null,W.route=E,W.injector=Ne,W.outlet&&W.outlet.activateWith(E,W.injector),this.activateChildRoutes(p,null,W.children)}}else this.activateChildRoutes(p,null,g)}}class Xi{constructor(p){this.path=p,this.route=this.path[this.path.length-1]}}class Do{constructor(p,u){this.component=p,this.route=u}}function Ki(c,p,u){const g=c._root;return xo(g,p?p._root:null,u,[g.value])}function Co(c,p){const u=Symbol(),g=p.get(c,u);return g===u?"function"!=typeof c||(0,C.LfX)(c)?p.get(c):c:g}function xo(c,p,u,g,E={canDeactivateChecks:[],canActivateChecks:[]}){const R=mo(p);return c.children.forEach(W=>{(function _(c,p,u,g,E={canDeactivateChecks:[],canActivateChecks:[]}){const R=c.value,W=p?p.value:null,Ne=u?u.getContext(c.value.outlet):null;if(W&&R.routeConfig===W.routeConfig){const we=function w(c,p,u){if("function"==typeof u)return u(c,p);switch(u){case"pathParamsChange":return!tr(c.url,p.url);case"pathParamsOrQueryParamsChange":return!tr(c.url,p.url)||!$t(c.queryParams,p.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!lo(c,p)||!$t(c.queryParams,p.queryParams);default:return!lo(c,p)}}(W,R,R.routeConfig.runGuardsAndResolvers);we?E.canActivateChecks.push(new Xi(g)):(R.data=W.data,R._resolvedData=W._resolvedData),xo(c,p,R.component?Ne?Ne.children:null:u,g,E),we&&Ne&&Ne.outlet&&Ne.outlet.isActivated&&E.canDeactivateChecks.push(new Do(Ne.outlet.component,W))}else W&&v(p,Ne,E),E.canActivateChecks.push(new Xi(g)),xo(c,null,R.component?Ne?Ne.children:null:u,g,E)})(W,R[W.value.outlet],u,g.concat([W.value]),E),delete R[W.value.outlet]}),Object.entries(R).forEach(([W,Ne])=>v(Ne,u.getContext(W),E)),E}function v(c,p,u){const g=mo(c),E=c.value;Object.entries(g).forEach(([R,W])=>{v(W,E.component?p?p.children.getContext(R):null:p,u)}),u.canDeactivateChecks.push(new Do(E.component&&p&&p.outlet&&p.outlet.isActivated?p.outlet.component:null,E))}function P(c){return"function"==typeof c}function Xo(c){return c instanceof te||"EmptyError"===c?.name}const No=Symbol("INITIAL_VALUE");function Po(){return(0,pt.n)(c=>(0,je.z)(c.map(p=>p.pipe(Rt(1),function ze(...c){const p=(0,Ve.lI)(c);return(0,A.N)((u,g)=>{(p?ge(c,u,p):ge(c,u)).subscribe(g)})}(No)))).pipe((0,Ke.T)(p=>{for(const u of p)if(!0!==u){if(u===No)return No;if(!1===u||u instanceof Wn)return u}return!0}),(0,at.p)(p=>p!==No),Rt(1)))}function Jn(c){return(0,Ze.F)(Oe(p=>{if(pn(p))throw Ao(0,p)}),(0,Ke.T)(p=>!0===p))}class pr{constructor(p){this.segmentGroup=p||null}}class Yi extends Error{constructor(p){super(),this.urlTree=p}}function Io(c){return Ee(new pr(c))}class Zi{constructor(p,u){this.urlSerializer=p,this.urlTree=u}lineralizeSegments(p,u){let g=[],E=u.root;for(;;){if(g=g.concat(E.segments),0===E.numberOfChildren)return(0,L.of)(g);if(E.numberOfChildren>1||!E.children[he])return Ee(new C.wOt(4e3,!1));E=E.children[he]}}applyRedirectCommands(p,u,g){const E=this.applyRedirectCreateUrlTree(u,this.urlSerializer.parse(u),p,g);if(u.startsWith("/"))throw new Yi(E);return E}applyRedirectCreateUrlTree(p,u,g,E){const R=this.createSegmentGroup(p,u.root,g,E);return new Wn(R,this.createQueryParams(u.queryParams,this.urlTree.queryParams),u.fragment)}createQueryParams(p,u){const g={};return Object.entries(p).forEach(([E,R])=>{if("string"==typeof R&&R.startsWith(":")){const Ne=R.substring(1);g[E]=u[Ne]}else g[E]=R}),g}createSegmentGroup(p,u,g,E){const R=this.createSegments(p,u.segments,g,E);let W={};return Object.entries(u.children).forEach(([Ne,we])=>{W[Ne]=this.createSegmentGroup(p,we,g,E)}),new Mt(R,W)}createSegments(p,u,g,E){return u.map(R=>R.path.startsWith(":")?this.findPosParam(p,R,E):this.findOrReturn(R,g))}findPosParam(p,u,g){const E=g[u.path.substring(1)];if(!E)throw new C.wOt(4001,!1);return E}findOrReturn(p,u){let g=0;for(const E of u){if(E.path===p.path)return u.splice(g),E;g++}return p}}const Ri={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Oi(c,p,u,g,E){const R=xi(c,p,u);return R.matched?(g=function di(c,p){return c.providers&&!c._injector&&(c._injector=(0,C.Ol2)(c.providers,p,`Route: ${c.path}`)),c._injector??p}(p,g),function Ur(c,p,u,g){const E=p.canMatch;if(!E||0===E.length)return(0,L.of)(!0);const R=E.map(W=>{const Ne=Co(W,c);return dn(function bo(c){return c&&P(c.canMatch)}(Ne)?Ne.canMatch(p,u):(0,C.N4e)(c,()=>Ne(p,u)))});return(0,L.of)(R).pipe(Po(),Jn())}(g,p,u).pipe((0,Ke.T)(W=>!0===W?R:{...Ri}))):(0,L.of)(R)}function xi(c,p,u){if("**"===p.path)return function Ji(c){return{matched:!0,parameters:c.length>0?At(c).parameters:{},consumedSegments:c,remainingSegments:[],positionalParamSegments:{}}}(u);if(""===p.path)return"full"===p.pathMatch&&(c.hasChildren()||u.length>0)?{...Ri}:{matched:!0,consumedSegments:[],remainingSegments:u,parameters:{},positionalParamSegments:{}};const E=(p.matcher||Ht)(u,c,p);if(!E)return{...Ri};const R={};Object.entries(E.posParams??{}).forEach(([Ne,we])=>{R[Ne]=we.path});const W=E.consumed.length>0?{...R,...E.consumed[E.consumed.length-1].parameters}:R;return{matched:!0,consumedSegments:E.consumed,remainingSegments:u.slice(E.consumed.length),parameters:W,positionalParamSegments:E.posParams??{}}}function $l(c,p,u,g){return u.length>0&&function Is(c,p,u){return u.some(g=>Ni(c,p,g)&&Pr(g)!==he)}(c,u,g)?{segmentGroup:new Mt(p,Vl(g,new Mt(u,c.children))),slicedSegments:[]}:0===u.length&&function Ia(c,p,u){return u.some(g=>Ni(c,p,g))}(c,u,g)?{segmentGroup:new Mt(c.segments,bs(c,u,g,c.children)),slicedSegments:u}:{segmentGroup:new Mt(c.segments,c.children),slicedSegments:u}}function bs(c,p,u,g){const E={};for(const R of u)if(Ni(c,p,R)&&!g[Pr(R)]){const W=new Mt([],{});E[Pr(R)]=W}return{...g,...E}}function Vl(c,p){const u={};u[he]=p;for(const g of c)if(""===g.path&&Pr(g)!==he){const E=new Mt([],{});u[Pr(g)]=E}return u}function Ni(c,p,u){return(!(c.hasChildren()||p.length>0)||"full"!==u.pathMatch)&&""===u.path}class es{}class _s{constructor(p,u,g,E,R,W,Ne){this.injector=p,this.configLoader=u,this.rootComponentType=g,this.config=E,this.urlTree=R,this.paramsInheritanceStrategy=W,this.urlSerializer=Ne,this.applyRedirects=new Zi(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(p){return new C.wOt(4002,`'${p.segmentGroup}'`)}recognize(){const p=$l(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(p).pipe((0,Ke.T)(u=>{const g=new to([],Object.freeze({}),Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,{},he,this.rootComponentType,null,{}),E=new jn(g,u),R=new ai("",E),W=function xr(c,p,u=null,g=null){return Xn(Nr(c),p,u,g)}(g,[],this.urlTree.queryParams,this.urlTree.fragment);return W.queryParams=this.urlTree.queryParams,R.url=this.urlSerializer.serialize(W),this.inheritParamsAndData(R._root,null),{state:R,tree:W}}))}match(p){return this.processSegmentGroup(this.injector,this.config,p,he).pipe(Le(g=>{if(g instanceof Yi)return this.urlTree=g.urlTree,this.match(g.urlTree.root);throw g instanceof pr?this.noMatchError(g):g}))}inheritParamsAndData(p,u){const g=p.value,E=Ho(g,u,this.paramsInheritanceStrategy);g.params=Object.freeze(E.params),g.data=Object.freeze(E.data),p.children.forEach(R=>this.inheritParamsAndData(R,g))}processSegmentGroup(p,u,g,E){return 0===g.segments.length&&g.hasChildren()?this.processChildren(p,u,g):this.processSegment(p,u,g,g.segments,E,!0).pipe((0,Ke.T)(R=>R instanceof jn?[R]:[]))}processChildren(p,u,g){const E=[];for(const R of Object.keys(g.children))"primary"===R?E.unshift(R):E.push(R);return(0,Ie.H)(E).pipe($(R=>{const W=g.children[R],Ne=function co(c,p){const u=c.filter(g=>Pr(g)===p);return u.push(...c.filter(g=>Pr(g)!==p)),u}(u,R);return this.processSegmentGroup(p,Ne,W,R)}),function It(c,p){return(0,A.N)(function Xe(c,p,u,g,E){return(R,W)=>{let Ne=u,we=p,dt=0;R.subscribe((0,Y._)(W,en=>{const an=dt++;we=Ne?c(we,en,an):(Ne=!0,en),g&&W.next(we)},E&&(()=>{Ne&&W.next(we),W.complete()})))}}(c,p,arguments.length>=2,!0))}((R,W)=>(R.push(...W),R)),Ue(null),function vt(c,p){const u=arguments.length>=2;return g=>g.pipe(c?(0,at.p)((E,R)=>c(E,R,g)):pe.D,Tt(1),u?Ue(p):H(()=>new te))}(),(0,De.Z)(R=>{if(null===R)return Io(g);const W=Ko(R);return function Pi(c){c.sort((p,u)=>p.value.outlet===he?-1:u.value.outlet===he?1:p.value.outlet.localeCompare(u.value.outlet))}(W),(0,L.of)(W)}))}processSegment(p,u,g,E,R,W){return(0,Ie.H)(u).pipe($(Ne=>this.processSegmentAgainstRoute(Ne._injector??p,u,Ne,g,E,R,W).pipe(Le(we=>{if(we instanceof pr)return(0,L.of)(null);throw we}))),ae(Ne=>!!Ne),Le(Ne=>{if(Xo(Ne))return function _a(c,p,u){return 0===p.length&&!c.children[u]}(g,E,R)?(0,L.of)(new es):Io(g);throw Ne}))}processSegmentAgainstRoute(p,u,g,E,R,W,Ne){return function Fo(c,p,u,g){return!!(Pr(c)===g||g!==he&&Ni(p,u,c))&&xi(p,c,u).matched}(g,E,R,W)?void 0===g.redirectTo?this.matchSegmentAgainstRoute(p,E,g,R,W):this.allowRedirects&&Ne?this.expandSegmentAgainstRouteUsingRedirect(p,E,u,g,R,W):Io(E):Io(E)}expandSegmentAgainstRouteUsingRedirect(p,u,g,E,R,W){const{matched:Ne,consumedSegments:we,positionalParamSegments:dt,remainingSegments:en}=xi(u,E,R);if(!Ne)return Io(u);E.redirectTo.startsWith("/")&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>31&&(this.allowRedirects=!1));const an=this.applyRedirects.applyRedirectCommands(we,E.redirectTo,dt);return this.applyRedirects.lineralizeSegments(E,an).pipe((0,De.Z)(ir=>this.processSegment(p,g,u,ir.concat(en),W,!1)))}matchSegmentAgainstRoute(p,u,g,E,R){const W=Oi(u,g,E,p);return"**"===g.path&&(u.children={}),W.pipe((0,pt.n)(Ne=>Ne.matched?this.getChildConfig(p=g._injector??p,g,E).pipe((0,pt.n)(({routes:we})=>{const dt=g._loadedInjector??p,{consumedSegments:en,remainingSegments:an,parameters:ir}=Ne,as=new to(en,ir,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,function Bl(c){return c.data||{}}(g),Pr(g),g.component??g._loadedComponent??null,g,function Ul(c){return c.resolve||{}}(g)),{segmentGroup:vi,slicedSegments:ls}=$l(u,en,an,we);if(0===ls.length&&vi.hasChildren())return this.processChildren(dt,we,vi).pipe((0,Ke.T)(Qo=>null===Qo?null:new jn(as,Qo)));if(0===we.length&&0===ls.length)return(0,L.of)(new jn(as,[]));const ro=Pr(g)===R;return this.processSegment(dt,we,vi,ls,ro?he:R,!0).pipe((0,Ke.T)(Qo=>new jn(as,Qo instanceof jn?[Qo]:[])))})):Io(u)))}getChildConfig(p,u,g){return u.children?(0,L.of)({routes:u.children,injector:p}):u.loadChildren?void 0!==u._loadedRoutes?(0,L.of)({routes:u._loadedRoutes,injector:u._loadedInjector}):function or(c,p,u,g){const E=p.canLoad;if(void 0===E||0===E.length)return(0,L.of)(!0);const R=E.map(W=>{const Ne=Co(W,c);return dn(function wt(c){return c&&P(c.canLoad)}(Ne)?Ne.canLoad(p,u):(0,C.N4e)(c,()=>Ne(p,u)))});return(0,L.of)(R).pipe(Po(),Jn())}(p,u,g).pipe((0,De.Z)(E=>E?this.configLoader.loadChildren(p,u).pipe(Oe(R=>{u._loadedRoutes=R.routes,u._loadedInjector=R.injector})):function Ll(c){return Ee(zo(!1,x.GuardRejected))}())):(0,L.of)({routes:[],injector:p})}}function Qr(c){const p=c.value.routeConfig;return p&&""===p.path}function Ko(c){const p=[],u=new Set;for(const g of c){if(!Qr(g)){p.push(g);continue}const E=p.find(R=>g.value.routeConfig===R.value.routeConfig);void 0!==E?(E.children.push(...g.children),u.add(E)):p.push(g)}for(const g of u){const E=Ko(g.children);p.push(new jn(g.value,E))}return p.filter(g=>!u.has(g))}function Ta(c){const p=c.children.map(u=>Ta(u)).flat();return[c,...p]}function _o(c){return(0,pt.n)(p=>{const u=c(p);return u?(0,Ie.H)(u).pipe((0,Ke.T)(()=>p)):(0,L.of)(p)})}let Ss=(()=>{class c{buildTitle(u){let g,E=u.root;for(;void 0!==E;)g=this.getResolvedTitleForRoute(E)??g,E=E.children.find(R=>R.outlet===he);return g}getResolvedTitleForRoute(u){return u.data[Ae]}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:()=>(0,C.WQX)(ns),providedIn:"root"})}}return c})(),ns=(()=>{class c extends Ss{constructor(u){super(),this.title=u}updateTitle(u){const g=this.buildTitle(u);void 0!==g&&this.title.setTitle(g)}static{this.\u0275fac=function(g){return new(g||c)(C.KVO(xe.hE))}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();const h=new C.nKC("",{providedIn:"root",factory:()=>({})}),M=new C.nKC("");let y=(()=>{class c{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=(0,C.WQX)(C.Ql9)}loadComponent(u){if(this.componentLoaders.get(u))return this.componentLoaders.get(u);if(u._loadedComponent)return(0,L.of)(u._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(u);const g=dn(u.loadComponent()).pipe((0,Ke.T)($e),Oe(R=>{this.onLoadEndListener&&this.onLoadEndListener(u),u._loadedComponent=R}),Pt(()=>{this.componentLoaders.delete(u)})),E=new de(g,()=>new _e.B).pipe(Z());return this.componentLoaders.set(u,E),E}loadChildren(u,g){if(this.childrenLoaders.get(g))return this.childrenLoaders.get(g);if(g._loadedRoutes)return(0,L.of)({routes:g._loadedRoutes,injector:g._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(g);const R=function I(c,p,u,g){return dn(c.loadChildren()).pipe((0,Ke.T)($e),(0,De.Z)(E=>E instanceof C.Co$||Array.isArray(E)?(0,L.of)(E):(0,Ie.H)(p.compileModuleAsync(E))),(0,Ke.T)(E=>{g&&g(c);let R,W,Ne=!1;return Array.isArray(E)?(W=E,!0):(R=E.create(u).injector,W=R.get(M,[],{optional:!0,self:!0}).flat()),{routes:W.map(Oo),injector:R}}))}(g,this.compiler,u,this.onLoadEndListener).pipe(Pt(()=>{this.childrenLoaders.delete(g)})),W=new de(R,()=>new _e.B).pipe(Z());return this.childrenLoaders.set(g,W),W}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();function $e(c){return function K(c){return c&&"object"==typeof c&&"default"in c}(c)?c.default:c}let tt=(()=>{class c{static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:()=>(0,C.WQX)(bt),providedIn:"root"})}}return c})(),bt=(()=>{class c{shouldProcessUrl(u){return!0}extract(u){return u}merge(u,g){return u}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();const Mn=new C.nKC(""),Cn=new C.nKC("");function wn(c,p,u){const g=c.get(Cn),E=c.get(Re.qQ);return c.get(C.SKi).runOutsideAngular(()=>{if(!E.startViewTransition||g.skipNextTransition)return g.skipNextTransition=!1,new Promise(dt=>setTimeout(dt));let R;const W=new Promise(dt=>{R=dt}),Ne=E.startViewTransition(()=>(R(),function cn(c){return new Promise(p=>{(0,C.mal)(p,{injector:c})})}(c))),{onViewTransitionCreated:we}=g;return we&&(0,C.N4e)(c,()=>we({transition:Ne,from:p,to:u})),W})}let An=(()=>{class c{get hasRequestedNavigation(){return 0!==this.navigationId}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new _e.B,this.transitionAbortSubject=new _e.B,this.configLoader=(0,C.WQX)(y),this.environmentInjector=(0,C.WQX)(C.uvJ),this.urlSerializer=(0,C.WQX)(le),this.rootContexts=(0,C.WQX)(xn),this.location=(0,C.WQX)(Re.aZ),this.inputBindingEnabled=null!==(0,C.WQX)(Kr,{optional:!0}),this.titleStrategy=(0,C.WQX)(Ss),this.options=(0,C.WQX)(h,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=(0,C.WQX)(tt),this.createViewTransition=(0,C.WQX)(Mn,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>(0,L.of)(void 0),this.rootComponentType=null,this.configLoader.onLoadEndListener=E=>this.events.next(new Dt(E)),this.configLoader.onLoadStartListener=E=>this.events.next(new mt(E))}complete(){this.transitions?.complete()}handleNavigationRequest(u){const g=++this.navigationId;this.transitions?.next({...this.transitions.value,...u,id:g})}setupNavigations(u,g,E){return this.transitions=new Pe.t({id:0,currentUrlTree:g,currentRawUrl:g,extractedUrl:this.urlHandlingStrategy.extract(g),urlAfterRedirects:this.urlHandlingStrategy.extract(g),rawUrl:g,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:qe,restoredState:null,currentSnapshot:E.snapshot,targetSnapshot:null,currentRouterState:E,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe((0,at.p)(R=>0!==R.id),(0,Ke.T)(R=>({...R,extractedUrl:this.urlHandlingStrategy.extract(R.rawUrl)})),(0,pt.n)(R=>{let W=!1,Ne=!1;return(0,L.of)(R).pipe((0,pt.n)(we=>{if(this.navigationId>R.id)return this.cancelNavigationTransition(R,"",x.SupersededByNewNavigation),Ce;this.currentTransition=R,this.currentNavigation={id:we.id,initialUrl:we.rawUrl,extractedUrl:we.extractedUrl,trigger:we.source,extras:we.extras,previousNavigation:this.lastSuccessfulNavigation?{...this.lastSuccessfulNavigation,previousNavigation:null}:null};const dt=!u.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl();if(!dt&&"reload"!==(we.extras.onSameUrlNavigation??u.onSameUrlNavigation)){const an="";return this.events.next(new Fe(we.id,this.urlSerializer.serialize(we.rawUrl),an,G.IgnoredSameUrlNavigation)),we.resolve(null),Ce}if(this.urlHandlingStrategy.shouldProcessUrl(we.rawUrl))return(0,L.of)(we).pipe((0,pt.n)(an=>{const ir=this.transitions?.getValue();return this.events.next(new He(an.id,this.urlSerializer.serialize(an.extractedUrl),an.source,an.restoredState)),ir!==this.transitions?.getValue()?Ce:Promise.resolve(an)}),function Ma(c,p,u,g,E,R){return(0,De.Z)(W=>function ts(c,p,u,g,E,R,W="emptyOnly"){return new _s(c,p,u,g,E,W,R).recognize()}(c,p,u,g,W.extractedUrl,E,R).pipe((0,Ke.T)(({state:Ne,tree:we})=>({...W,targetSnapshot:Ne,urlAfterRedirects:we}))))}(this.environmentInjector,this.configLoader,this.rootComponentType,u.config,this.urlSerializer,this.paramsInheritanceStrategy),Oe(an=>{R.targetSnapshot=an.targetSnapshot,R.urlAfterRedirects=an.urlAfterRedirects,this.currentNavigation={...this.currentNavigation,finalUrl:an.urlAfterRedirects};const ir=new st(an.id,this.urlSerializer.serialize(an.extractedUrl),this.urlSerializer.serialize(an.urlAfterRedirects),an.targetSnapshot);this.events.next(ir)}));if(dt&&this.urlHandlingStrategy.shouldProcessUrl(we.currentRawUrl)){const{id:an,extractedUrl:ir,source:as,restoredState:vi,extras:ls}=we,ro=new He(an,this.urlSerializer.serialize(ir),as,vi);this.events.next(ro);const Qo=vo(this.rootComponentType).snapshot;return this.currentTransition=R={...we,targetSnapshot:Qo,urlAfterRedirects:ir,extras:{...ls,skipLocationChange:!1,replaceUrl:!1}},this.currentNavigation.finalUrl=ir,(0,L.of)(R)}{const an="";return this.events.next(new Fe(we.id,this.urlSerializer.serialize(we.extractedUrl),an,G.IgnoredByUrlHandlingStrategy)),we.resolve(null),Ce}}),Oe(we=>{const dt=new nt(we.id,this.urlSerializer.serialize(we.extractedUrl),this.urlSerializer.serialize(we.urlAfterRedirects),we.targetSnapshot);this.events.next(dt)}),(0,Ke.T)(we=>(this.currentTransition=R={...we,guards:Ki(we.targetSnapshot,we.currentSnapshot,this.rootContexts)},R)),function Da(c,p){return(0,De.Z)(u=>{const{targetSnapshot:g,currentSnapshot:E,guards:{canActivateChecks:R,canDeactivateChecks:W}}=u;return 0===W.length&&0===R.length?(0,L.of)({...u,guardsResult:!0}):function Fl(c,p,u,g){return(0,Ie.H)(c).pipe((0,De.Z)(E=>function sn(c,p,u,g,E){const R=p&&p.routeConfig?p.routeConfig.canDeactivate:null;if(!R||0===R.length)return(0,L.of)(!0);const W=R.map(Ne=>{const we=Lr(p)??E,dt=Co(Ne,we);return dn(function Eo(c){return c&&P(c.canDeactivate)}(dt)?dt.canDeactivate(c,p,u,g):(0,C.N4e)(we,()=>dt(c,p,u,g))).pipe(ae())});return(0,L.of)(W).pipe(Po())}(E.component,E.route,u,p,g)),ae(E=>!0!==E,!0))}(W,g,E,c).pipe((0,De.Z)(Ne=>Ne&&function re(c){return"boolean"==typeof c}(Ne)?function Ca(c,p,u,g){return(0,Ie.H)(p).pipe($(E=>ge(function Ea(c,p){return null!==c&&p&&p(new Ot(c)),(0,L.of)(!0)}(E.route.parent,g),function Es(c,p){return null!==c&&p&&p(new f(c)),(0,L.of)(!0)}(E.route,g),function Qi(c,p,u){const g=p[p.length-1],R=p.slice(0,p.length-1).reverse().map(W=>function qi(c){const p=c.routeConfig?c.routeConfig.canActivateChild:null;return p&&0!==p.length?{node:c,guards:p}:null}(W)).filter(W=>null!==W).map(W=>me(()=>{const Ne=W.guards.map(we=>{const dt=Lr(W.node)??u,en=Co(we,dt);return dn(function nn(c){return c&&P(c.canActivateChild)}(en)?en.canActivateChild(g,c):(0,C.N4e)(dt,()=>en(g,c))).pipe(ae())});return(0,L.of)(Ne).pipe(Po())}));return(0,L.of)(R).pipe(Po())}(c,E.path,u),function ba(c,p,u){const g=p.routeConfig?p.routeConfig.canActivate:null;if(!g||0===g.length)return(0,L.of)(!0);const E=g.map(R=>me(()=>{const W=Lr(p)??u,Ne=Co(R,W);return dn(function Gt(c){return c&&P(c.canActivate)}(Ne)?Ne.canActivate(p,c):(0,C.N4e)(W,()=>Ne(p,c))).pipe(ae())}));return(0,L.of)(E).pipe(Po())}(c,E.route,u))),ae(E=>!0!==E,!0))}(g,R,c,p):(0,L.of)(Ne)),(0,Ke.T)(Ne=>({...u,guardsResult:Ne})))})}(this.environmentInjector,we=>this.events.next(we)),Oe(we=>{if(R.guardsResult=we.guardsResult,pn(we.guardsResult))throw Ao(0,we.guardsResult);const dt=new lt(we.id,this.urlSerializer.serialize(we.extractedUrl),this.urlSerializer.serialize(we.urlAfterRedirects),we.targetSnapshot,!!we.guardsResult);this.events.next(dt)}),(0,at.p)(we=>!!we.guardsResult||(this.cancelNavigationTransition(we,"",x.GuardRejected),!1)),_o(we=>{if(we.guards.canActivateChecks.length)return(0,L.of)(we).pipe(Oe(dt=>{const en=new ot(dt.id,this.urlSerializer.serialize(dt.extractedUrl),this.urlSerializer.serialize(dt.urlAfterRedirects),dt.targetSnapshot);this.events.next(en)}),(0,pt.n)(dt=>{let en=!1;return(0,L.of)(dt).pipe(function fi(c,p){return(0,De.Z)(u=>{const{targetSnapshot:g,guards:{canActivateChecks:E}}=u;if(!E.length)return(0,L.of)(u);const R=new Set(E.map(we=>we.route)),W=new Set;for(const we of R)if(!W.has(we))for(const dt of Ta(we))W.add(dt);let Ne=0;return(0,Ie.H)(W).pipe($(we=>R.has(we)?function jl(c,p,u,g){const E=c.routeConfig,R=c._resolve;return void 0!==E?.title&&!_i(E)&&(R[Ae]=E.title),function Hl(c,p,u,g){const E=Wt(c);if(0===E.length)return(0,L.of)({});const R={};return(0,Ie.H)(E).pipe((0,De.Z)(W=>function ws(c,p,u,g){const E=Lr(p)??g,R=Co(c,E);return dn(R.resolve?R.resolve(p,u):(0,C.N4e)(E,()=>R(p,u)))}(c[W],p,u,g).pipe(ae(),Oe(Ne=>{R[W]=Ne}))),Tt(1),function xt(c){return(0,Ke.T)(()=>c)}(R),Le(W=>Xo(W)?Ce:Ee(W)))}(R,c,p,g).pipe((0,Ke.T)(W=>(c._resolvedData=W,c.data=Ho(c,c.parent,u).resolve,null)))}(we,g,c,p):(we.data=Ho(we,we.parent,c).resolve,(0,L.of)(void 0))),Oe(()=>Ne++),Tt(1),(0,De.Z)(we=>Ne===W.size?(0,L.of)(u):Ce))})}(this.paramsInheritanceStrategy,this.environmentInjector),Oe({next:()=>en=!0,complete:()=>{en||this.cancelNavigationTransition(dt,"",x.NoDataFromResolver)}}))}),Oe(dt=>{const en=new Qe(dt.id,this.urlSerializer.serialize(dt.extractedUrl),this.urlSerializer.serialize(dt.urlAfterRedirects),dt.targetSnapshot);this.events.next(en)}))}),_o(we=>{const dt=en=>{const an=[];en.routeConfig?.loadComponent&&!en.routeConfig._loadedComponent&&an.push(this.configLoader.loadComponent(en.routeConfig).pipe(Oe(ir=>{en.component=ir}),(0,Ke.T)(()=>{})));for(const ir of en.children)an.push(...dt(ir));return an};return(0,je.z)(dt(we.targetSnapshot.root)).pipe(Ue(null),Rt(1))}),_o(()=>this.afterPreactivation()),(0,pt.n)(()=>{const{currentSnapshot:we,targetSnapshot:dt}=R,en=this.createViewTransition?.(this.environmentInjector,we.root,dt.root);return en?(0,Ie.H)(en).pipe((0,Ke.T)(()=>R)):(0,L.of)(R)}),(0,Ke.T)(we=>{const dt=function fr(c,p,u){const g=ar(c,p._root,u?u._root:void 0);return new qn(g,p)}(u.routeReuseStrategy,we.targetSnapshot,we.currentRouterState);return this.currentTransition=R={...we,targetRouterState:dt},this.currentNavigation.targetRouterState=dt,R}),Oe(()=>{this.events.next(new F)}),((c,p,u,g)=>(0,Ke.T)(E=>(new Wi(p,E.targetRouterState,E.currentRouterState,u,g).activate(c),E)))(this.rootContexts,u.routeReuseStrategy,we=>this.events.next(we),this.inputBindingEnabled),Rt(1),Oe({next:we=>{W=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new b(we.id,this.urlSerializer.serialize(we.extractedUrl),this.urlSerializer.serialize(we.urlAfterRedirects))),this.titleStrategy?.updateTitle(we.targetRouterState.snapshot),we.resolve(!0)},complete:()=>{W=!0}}),function Be(c){return(0,A.N)((p,u)=>{(0,se.Tg)(c).subscribe((0,Y._)(u,()=>u.complete(),Ft.l)),!u.closed&&p.subscribe(u)})}(this.transitionAbortSubject.pipe(Oe(we=>{throw we}))),Pt(()=>{!W&&!Ne&&this.cancelNavigationTransition(R,"",x.SupersededByNewNavigation),this.currentTransition?.id===R.id&&(this.currentNavigation=null,this.currentTransition=null)}),Le(we=>{if(Ne=!0,Wo(we))this.events.next(new Q(R.id,this.urlSerializer.serialize(R.extractedUrl),we.message,we.cancellationCode)),function Gi(c){return Wo(c)&&pn(c.url)}(we)?this.events.next(new oe(we.url)):R.resolve(!1);else{this.events.next(new be(R.id,this.urlSerializer.serialize(R.extractedUrl),we,R.targetSnapshot??void 0));try{R.resolve(u.errorHandler(we))}catch(dt){this.options.resolveNavigationPromiseOnError?R.resolve(!1):R.reject(dt)}}return Ce}))}))}cancelNavigationTransition(u,g,E){const R=new Q(u.id,this.urlSerializer.serialize(u.extractedUrl),g,E);this.events.next(R),u.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){return this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))).toString()!==this.currentTransition?.extractedUrl.toString()&&!this.currentTransition?.extras.skipLocationChange}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();function vn(c){return c!==qe}let Hn=(()=>{class c{static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:()=>(0,C.WQX)(ct),providedIn:"root"})}}return c})();class gn{shouldDetach(p){return!1}store(p,u){}shouldAttach(p){return!1}retrieve(p){return null}shouldReuseRoute(p,u){return p.routeConfig===u.routeConfig}}let ct=(()=>{class c extends gn{static{this.\u0275fac=(()=>{let u;return function(E){return(u||(u=C.xGo(c)))(E||c)}})()}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})(),St=(()=>{class c{static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:()=>(0,C.WQX)(En),providedIn:"root"})}}return c})(),En=(()=>{class c extends St{constructor(){super(...arguments),this.location=(0,C.WQX)(Re.aZ),this.urlSerializer=(0,C.WQX)(le),this.options=(0,C.WQX)(h,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=(0,C.WQX)(tt),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new Wn,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=vo(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return"computed"!==this.canceledNavigationResolution?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(u){return this.location.subscribe(g=>{"popstate"===g.type&&u(g.url,g.state)})}handleRouterEvent(u,g){if(u instanceof He)this.stateMemento=this.createStateMemento();else if(u instanceof Fe)this.rawUrlTree=g.initialUrl;else if(u instanceof st){if("eager"===this.urlUpdateStrategy&&!g.extras.skipLocationChange){const E=this.urlHandlingStrategy.merge(g.finalUrl,g.initialUrl);this.setBrowserUrl(E,g)}}else u instanceof F?(this.currentUrlTree=g.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(g.finalUrl,g.initialUrl),this.routerState=g.targetRouterState,"deferred"===this.urlUpdateStrategy&&(g.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,g))):u instanceof Q&&(u.code===x.GuardRejected||u.code===x.NoDataFromResolver)?this.restoreHistory(g):u instanceof be?this.restoreHistory(g,!0):u instanceof b&&(this.lastSuccessfulId=u.id,this.currentPageId=this.browserPageId)}setBrowserUrl(u,g){const E=this.urlSerializer.serialize(u);if(this.location.isCurrentPathEqualTo(E)||g.extras.replaceUrl){const W={...g.extras.state,...this.generateNgRouterState(g.id,this.browserPageId)};this.location.replaceState(E,"",W)}else{const R={...g.extras.state,...this.generateNgRouterState(g.id,this.browserPageId+1)};this.location.go(E,"",R)}}restoreHistory(u,g=!1){if("computed"===this.canceledNavigationResolution){const R=this.currentPageId-this.browserPageId;0!==R?this.location.historyGo(R):this.currentUrlTree===u.finalUrl&&0===R&&(this.resetState(u),this.resetUrlToCurrentUrlTree())}else"replace"===this.canceledNavigationResolution&&(g&&this.resetState(u),this.resetUrlToCurrentUrlTree())}resetState(u){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,u.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(u,g){return"computed"===this.canceledNavigationResolution?{navigationId:u,\u0275routerPageId:g}:{navigationId:u}}static{this.\u0275fac=(()=>{let u;return function(E){return(u||(u=C.xGo(c)))(E||c)}})()}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();var Qn=function(c){return c[c.COMPLETE=0]="COMPLETE",c[c.FAILED=1]="FAILED",c[c.REDIRECTING=2]="REDIRECTING",c}(Qn||{});function Nn(c,p){c.events.pipe((0,at.p)(u=>u instanceof b||u instanceof Q||u instanceof be||u instanceof Fe),(0,Ke.T)(u=>u instanceof b||u instanceof Fe?Qn.COMPLETE:u instanceof Q&&(u.code===x.Redirect||u.code===x.SupersededByNewNavigation)?Qn.REDIRECTING:Qn.FAILED),(0,at.p)(u=>u!==Qn.REDIRECTING),Rt(1)).subscribe(()=>{p()})}function lr(c){throw c}const wo={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Pn={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let Dn=(()=>{class c{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.isNgZoneEnabled=!1,this.console=(0,C.WQX)(C.H3F),this.stateManager=(0,C.WQX)(St),this.options=(0,C.WQX)(h,{optional:!0})||{},this.pendingTasks=(0,C.WQX)(C.TgB),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=(0,C.WQX)(An),this.urlSerializer=(0,C.WQX)(le),this.location=(0,C.WQX)(Re.aZ),this.urlHandlingStrategy=(0,C.WQX)(tt),this._events=new _e.B,this.errorHandler=this.options.errorHandler||lr,this.navigated=!1,this.routeReuseStrategy=(0,C.WQX)(Hn),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=(0,C.WQX)(M,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!(0,C.WQX)(Kr,{optional:!0}),this.eventsSubscription=new V.yU,this.isNgZoneEnabled=(0,C.WQX)(C.SKi)instanceof C.SKi&&C.SKi.isInAngularZone(),this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:u=>{this.console.warn(u)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){const u=this.navigationTransitions.events.subscribe(g=>{try{const E=this.navigationTransitions.currentTransition,R=this.navigationTransitions.currentNavigation;if(null!==E&&null!==R)if(this.stateManager.handleRouterEvent(g,R),g instanceof Q&&g.code!==x.Redirect&&g.code!==x.SupersededByNewNavigation)this.navigated=!0;else if(g instanceof b)this.navigated=!0;else if(g instanceof oe){const W=this.urlHandlingStrategy.merge(g.url,E.currentRawUrl),Ne={info:E.extras.info,skipLocationChange:E.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy||vn(E.source)};this.scheduleNavigation(W,qe,null,Ne,{resolve:E.resolve,reject:E.reject,promise:E.promise})}(function zt(c){return!(c instanceof F||c instanceof oe)})(g)&&this._events.next(g)}catch(E){this.navigationTransitions.transitionAbortSubject.next(E)}});this.eventsSubscription.add(u)}resetRootComponentType(u){this.routerState.root.component=u,this.navigationTransitions.rootComponentType=u}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),qe,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((u,g)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(u,"popstate",g)},0)})}navigateToSyncWithBrowser(u,g,E){const R={replaceUrl:!0},W=E?.navigationId?E:null;if(E){const we={...E};delete we.navigationId,delete we.\u0275routerPageId,0!==Object.keys(we).length&&(R.state=we)}const Ne=this.parseUrl(u);this.scheduleNavigation(Ne,g,W,R)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(u){this.config=u.map(Oo),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(u,g={}){const{relativeTo:E,queryParams:R,fragment:W,queryParamsHandling:Ne,preserveFragment:we}=g,dt=we?this.currentUrlTree.fragment:W;let an,en=null;switch(Ne){case"merge":en={...this.currentUrlTree.queryParams,...R};break;case"preserve":en=this.currentUrlTree.queryParams;break;default:en=R||null}null!==en&&(en=this.removeEmptyProps(en));try{an=Nr(E?E.snapshot:this.routerState.snapshot.root)}catch{("string"!=typeof u[0]||!u[0].startsWith("/"))&&(u=[]),an=this.currentUrlTree.root}return Xn(an,u,en,dt??null)}navigateByUrl(u,g={skipLocationChange:!1}){const E=pn(u)?u:this.parseUrl(u),R=this.urlHandlingStrategy.merge(E,this.rawUrlTree);return this.scheduleNavigation(R,qe,null,g)}navigate(u,g={skipLocationChange:!1}){return function uo(c){for(let p=0;p<c.length;p++)if(null==c[p])throw new C.wOt(4008,!1)}(u),this.navigateByUrl(this.createUrlTree(u,g),g)}serializeUrl(u){return this.urlSerializer.serialize(u)}parseUrl(u){try{return this.urlSerializer.parse(u)}catch{return this.urlSerializer.parse("/")}}isActive(u,g){let E;if(E=!0===g?{...wo}:!1===g?{...Pn}:g,pn(u))return er(this.currentUrlTree,u,E);const R=this.parseUrl(u);return er(this.currentUrlTree,R,E)}removeEmptyProps(u){return Object.entries(u).reduce((g,[E,R])=>(null!=R&&(g[E]=R),g),{})}scheduleNavigation(u,g,E,R,W){if(this.disposed)return Promise.resolve(!1);let Ne,we,dt;W?(Ne=W.resolve,we=W.reject,dt=W.promise):dt=new Promise((an,ir)=>{Ne=an,we=ir});const en=this.pendingTasks.add();return Nn(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(en))}),this.navigationTransitions.handleNavigationRequest({source:g,restoredState:E,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:u,extras:R,resolve:Ne,reject:we,promise:dt,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),dt.catch(an=>Promise.reject(an))}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})(),qo=(()=>{class c{constructor(u,g,E,R,W,Ne){this.router=u,this.route=g,this.tabIndexAttribute=E,this.renderer=R,this.el=W,this.locationStrategy=Ne,this.href=null,this.commands=null,this.onChanges=new _e.B,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;const we=W.nativeElement.tagName?.toLowerCase();this.isAnchorElement="a"===we||"area"===we,this.isAnchorElement?this.subscription=u.events.subscribe(dt=>{dt instanceof b&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(u){null!=this.tabIndexAttribute||this.isAnchorElement||this.applyAttributeValue("tabindex",u)}ngOnChanges(u){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(u){null!=u?(this.commands=Array.isArray(u)?u:[u],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(u,g,E,R,W){const Ne=this.urlTree;return!!(null===Ne||this.isAnchorElement&&(0!==u||g||E||R||W||"string"==typeof this.target&&"_self"!=this.target))||(this.router.navigateByUrl(Ne,{skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info}),!this.isAnchorElement)}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){const u=this.urlTree;this.href=null!==u&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(u)):null;const g=null===this.href?null:(0,C.n$t)(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",g)}applyAttributeValue(u,g){const E=this.renderer,R=this.el.nativeElement;null!==g?E.setAttribute(R,u,g):E.removeAttribute(R,u)}get urlTree(){return null===this.commands?null:this.router.createUrlTree(this.commands,{relativeTo:void 0!==this.relativeTo?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(g){return new(g||c)(C.rXU(Dn),C.rXU(Vr),C.kS0("tabindex"),C.rXU(C.sFG),C.rXU(C.aKT),C.rXU(Re.hb))}}static{this.\u0275dir=C.FsC({type:c,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(g,E){1&g&&C.bIt("click",function(W){return E.onClick(W.button,W.ctrlKey,W.shiftKey,W.altKey,W.metaKey)}),2&g&&C.BMQ("target",E.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[C.Mj6.HasDecoratorInputTransform,"preserveFragment","preserveFragment",C.L39],skipLocationChange:[C.Mj6.HasDecoratorInputTransform,"skipLocationChange","skipLocationChange",C.L39],replaceUrl:[C.Mj6.HasDecoratorInputTransform,"replaceUrl","replaceUrl",C.L39],routerLink:"routerLink"},standalone:!0,features:[C.GFd,C.OA$]})}}return c})();class Gn{}let rs=(()=>{class c{preload(u,g){return g().pipe(Le(()=>(0,L.of)(null)))}static{this.\u0275fac=function(g){return new(g||c)}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})(),ko=(()=>{class c{constructor(u,g,E,R,W){this.router=u,this.injector=E,this.preloadingStrategy=R,this.loader=W}setUpPreloading(){this.subscription=this.router.events.pipe((0,at.p)(u=>u instanceof b),$(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(u,g){const E=[];for(const R of g){R.providers&&!R._injector&&(R._injector=(0,C.Ol2)(R.providers,u,`Route: ${R.path}`));const W=R._injector??u,Ne=R._loadedInjector??W;(R.loadChildren&&!R._loadedRoutes&&void 0===R.canLoad||R.loadComponent&&!R._loadedComponent)&&E.push(this.preloadConfig(W,R)),(R.children||R._loadedRoutes)&&E.push(this.processRoutes(Ne,R.children??R._loadedRoutes))}return(0,Ie.H)(E).pipe(ue())}preloadConfig(u,g){return this.preloadingStrategy.preload(g,()=>{let E;E=g.loadChildren&&void 0===g.canLoad?this.loader.loadChildren(u,g):(0,L.of)(null);const R=E.pipe((0,De.Z)(W=>null===W?(0,L.of)(void 0):(g._loadedRoutes=W.routes,g._loadedInjector=W.injector,this.processRoutes(W.injector??u,W.routes))));if(g.loadComponent&&!g._loadedComponent){const W=this.loader.loadComponent(g);return(0,Ie.H)([R,W]).pipe(ue())}return R})}static{this.\u0275fac=function(g){return new(g||c)(C.KVO(Dn),C.KVO(C.Ql9),C.KVO(C.uvJ),C.KVO(Gn),C.KVO(y))}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})}}return c})();const hi=new C.nKC("");let Aa=(()=>{class c{constructor(u,g,E,R,W={}){this.urlSerializer=u,this.transitions=g,this.viewportScroller=E,this.zone=R,this.options=W,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},this.environmentInjector=(0,C.WQX)(C.uvJ),W.scrollPositionRestoration||="disabled",W.anchorScrolling||="disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(u=>{u instanceof He?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=u.navigationTrigger,this.restoredId=u.restoredState?u.restoredState.navigationId:0):u instanceof b?(this.lastId=u.id,this.scheduleScrollEvent(u,this.urlSerializer.parse(u.urlAfterRedirects).fragment)):u instanceof Fe&&u.code===G.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(u,this.urlSerializer.parse(u.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(u=>{u instanceof m&&(u.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(u.position):u.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(u.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(u,g){var E=this;this.zone.runOutsideAngular((0,i.A)(function*(){yield new Promise(R=>{setTimeout(()=>{R()}),(0,C.mal)(()=>{R()},{injector:E.environmentInjector})}),E.zone.run(()=>{E.transitions.events.next(new m(u,"popstate"===E.lastSource?E.store[E.restoredId]:null,g))})}))}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(g){C.QTQ()}}static{this.\u0275prov=C.jDH({token:c,factory:c.\u0275fac})}}return c})();function Fr(c,p){return{\u0275kind:c,\u0275providers:p}}function ss(){const c=(0,C.WQX)(C.zZn);return p=>{const u=c.get(C.o8S);if(p!==u.components[0])return;const g=c.get(Dn),E=c.get(gi);1===c.get(Yn)&&g.initialNavigation(),c.get(Ts,null,C.$GK.Optional)?.setUpPreloading(),c.get(hi,null,C.$GK.Optional)?.init(),g.resetRootComponentType(u.componentTypes[0]),E.closed||(E.next(),E.complete(),E.unsubscribe())}}const gi=new C.nKC("",{factory:()=>new _e.B}),Yn=new C.nKC("",{providedIn:"root",factory:()=>1}),Ts=new C.nKC("");function mi(c){return Fr(0,[{provide:Ts,useExisting:ko},{provide:Gn,useExisting:c}])}function vu(c){return Fr(9,[{provide:Mn,useValue:wn},{provide:Cn,useValue:{skipNextTransition:!!c?.skipInitialTransition,...c}}])}const xa=new C.nKC("ROUTER_FORROOT_GUARD"),yu=[Re.aZ,{provide:le,useClass:Se},Dn,xn,{provide:Vr,useFactory:function is(c){return c.routerState.root},deps:[Dn]},y,[]];let Na=(()=>{class c{constructor(u){}static forRoot(u,g){return{ngModule:c,providers:[yu,[],{provide:M,multi:!0,useValue:u},{provide:xa,useFactory:vh,deps:[[Dn,new C.Xx1,new C.kdw]]},{provide:h,useValue:g||{}},g?.useHash?{provide:Re.hb,useClass:Re.fw}:{provide:Re.hb,useClass:Re.Sm},{provide:hi,useFactory:()=>{const c=(0,C.WQX)(Re.Xr),p=(0,C.WQX)(C.SKi),u=(0,C.WQX)(h),g=(0,C.WQX)(An),E=(0,C.WQX)(le);return u.scrollOffset&&c.setOffset(u.scrollOffset),new Aa(E,g,c,p,u)}},g?.preloadingStrategy?mi(g.preloadingStrategy).\u0275providers:[],g?.initialNavigation?yh(g):[],g?.bindToComponentInputs?Fr(8,[ui,{provide:Kr,useExisting:ui}]).\u0275providers:[],g?.enableViewTransitions?vu().\u0275providers:[],[{provide:Du,useFactory:ss},{provide:C.iLQ,multi:!0,useExisting:Du}]]}}static forChild(u){return{ngModule:c,providers:[{provide:M,multi:!0,useValue:u}]}}static{this.\u0275fac=function(g){return new(g||c)(C.KVO(xa,8))}}static{this.\u0275mod=C.$C({type:c})}static{this.\u0275inj=C.G2t({})}}return c})();function vh(c){return"guarded"}function yh(c){return["disabled"===c.initialNavigation?Fr(3,[{provide:C.hnV,multi:!0,useFactory:()=>{const p=(0,C.WQX)(Dn);return()=>{p.setUpLocationChangeListener()}}},{provide:Yn,useValue:2}]).\u0275providers:[],"enabledBlocking"===c.initialNavigation?Fr(2,[{provide:Yn,useValue:0},{provide:C.hnV,multi:!0,deps:[C.zZn],useFactory:p=>{const u=p.get(Re.hj,Promise.resolve());return()=>u.then(()=>new Promise(g=>{const E=p.get(Dn),R=p.get(gi);Nn(E,()=>{g(!0)}),p.get(An).afterPreactivation=()=>(g(!0),R.closed?(0,L.of)(void 0):R),E.initialNavigation()}))}}]).\u0275providers:[]]}const Du=new C.nKC("")},4517:(Ct,We,O)=>{"use strict";O.d(We,{Yq:()=>Nt,TS:()=>bn,sR:()=>er,el:()=>xr,Sb:()=>Qt,QE:()=>_n,CF:()=>et,Rg:()=>Cr,p4:()=>Er,jM:()=>sr,q9:()=>yt,Kb:()=>dr,CE:()=>Nr,pF:()=>Xn,fL:()=>Zn,YV:()=>Or,er:()=>Bt,z3:()=>Jt});var i=O(467),C=O(4438),ne=O(70),X=O(177);class Me{constructor(){this.m=new Map}reset(j){this.m=new Map(Object.entries(j))}get(j,T){const U=this.m.get(j);return void 0!==U?U:T}getBoolean(j,T=!1){const U=this.m.get(j);return void 0===U?T:"string"==typeof U?"true"===U:!!U}getNumber(j,T){const U=parseFloat(this.m.get(j));return isNaN(U)?void 0!==T?T:NaN:U}set(j,T){this.m.set(j,T)}}const Ie=new Me,pe=B=>ie(B),ie=(B=window)=>{if(typeof B>"u")return[];B.Ionic=B.Ionic||{};let j=B.Ionic.platforms;return null==j&&(j=B.Ionic.platforms=Ve(B),j.forEach(T=>B.document.documentElement.classList.add(`plt-${T}`))),j},Ve=B=>{const j=Ie.get("platform");return Object.keys(Rt).filter(T=>{const U=j?.[T];return"function"==typeof U?U(B):Rt[T](B)})},se=B=>!!(Ke(B,/iPad/i)||Ke(B,/Macintosh/i)&&V(B)),Ee=B=>Ke(B,/android|sink/i),V=B=>pt(B,"(any-pointer:coarse)"),Y=B=>Z(B)||de(B),Z=B=>!!(B.cordova||B.phonegap||B.PhoneGap),de=B=>{const j=B.Capacitor;return!!j?.isNative},Ke=(B,j)=>j.test(B.navigator.userAgent),pt=(B,j)=>{var T;return null===(T=B.matchMedia)||void 0===T?void 0:T.call(B,j).matches},Rt={ipad:se,iphone:B=>Ke(B,/iPhone/i),ios:B=>Ke(B,/iPhone|iPod/i)||se(B),android:Ee,phablet:B=>{const j=B.innerWidth,T=B.innerHeight,U=Math.min(j,T),fe=Math.max(j,T);return U>390&&U<520&&fe>620&&fe<800},tablet:B=>{const j=B.innerWidth,T=B.innerHeight,U=Math.min(j,T),fe=Math.max(j,T);return se(B)||(B=>Ee(B)&&!Ke(B,/mobile/i))(B)||U>460&&U<820&&fe>780&&fe<1400},cordova:Z,capacitor:de,electron:B=>Ke(B,/electron/i),pwa:B=>{var j;return!!(null!==(j=B.matchMedia)&&void 0!==j&&j.call(B,"(display-mode: standalone)").matches||B.navigator.standalone)},mobile:V,mobileweb:B=>V(B)&&!Y(B),desktop:B=>!V(B),hybrid:Y};var H=O(6031),q=O(1656),ae=O(1413),$=O(3726),Oe=O(4412),Le=O(4572),Xe=O(7673),It=O(1635),Tt=O(5964),vt=O(5558),xt=O(3669),Pt=O(9974),Ft=O(4360);function xe(B,j){return B===j}var he=O(4341);const Ae=["tabsInner"];let on=(()=>{class B{constructor(T,U){this.doc=T,this.backButton=new ae.B,this.keyboardDidShow=new ae.B,this.keyboardDidHide=new ae.B,this.pause=new ae.B,this.resume=new ae.B,this.resize=new ae.B,U.run(()=>{let fe;this.win=T.defaultView,this.backButton.subscribeWithPriority=function(qe,rt){return this.subscribe(J=>J.register(qe,He=>U.run(()=>rt(He))))},Wt(this.pause,T,"pause",U),Wt(this.resume,T,"resume",U),Wt(this.backButton,T,"ionBackButton",U),Wt(this.resize,this.win,"resize",U),Wt(this.keyboardDidShow,this.win,"ionKeyboardDidShow",U),Wt(this.keyboardDidHide,this.win,"ionKeyboardDidHide",U),this._readyPromise=new Promise(qe=>{fe=qe}),this.win?.cordova?T.addEventListener("deviceready",()=>{fe("cordova")},{once:!0}):fe("dom")})}is(T){return((B,j)=>("string"==typeof B&&(j=B,B=void 0),pe(B).includes(j)))(this.win,T)}platforms(){return pe(this.win)}ready(){return this._readyPromise}get isRTL(){return"rtl"===this.doc.dir}getQueryParam(T){return $t(this.win.location.href,T)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(T){const U=this.win.navigator;return!!(U?.userAgent&&U.userAgent.indexOf(T)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}}return B.\u0275fac=function(T){return new(T||B)(C.KVO(X.qQ),C.KVO(C.SKi))},B.\u0275prov=C.jDH({token:B,factory:B.\u0275fac,providedIn:"root"}),B})();const $t=(B,j)=>{j=j.replace(/[[\]\\]/g,"\\$&");const U=new RegExp("[\\?&]"+j+"=([^&#]*)").exec(B);return U?decodeURIComponent(U[1].replace(/\+/g," ")):null},Wt=(B,j,T,U)=>{j&&j.addEventListener(T,fe=>{U.run(()=>{const qe=fe?.detail;B.next(qe)})})};let yt=(()=>{class B{constructor(T,U,fe,qe){this.location=U,this.serializer=fe,this.router=qe,this.direction=dn,this.animated=On,this.guessDirection="forward",this.lastNavId=-1,qe&&qe.events.subscribe(rt=>{if(rt instanceof ne.Z){const J=rt.restoredState?rt.restoredState.navigationId:rt.id;this.guessDirection=J<this.lastNavId?"back":"forward",this.guessAnimation=rt.restoredState?void 0:this.guessDirection,this.lastNavId="forward"===this.guessDirection?rt.id:J}}),T.backButton.subscribeWithPriority(0,rt=>{this.pop(),rt()})}navigateForward(T,U={}){return this.setDirection("forward",U.animated,U.animationDirection,U.animation),this.navigate(T,U)}navigateBack(T,U={}){return this.setDirection("back",U.animated,U.animationDirection,U.animation),this.navigate(T,U)}navigateRoot(T,U={}){return this.setDirection("root",U.animated,U.animationDirection,U.animation),this.navigate(T,U)}back(T={animated:!0,animationDirection:"back"}){return this.setDirection("back",T.animated,T.animationDirection,T.animation),this.location.back()}pop(){var T=this;return(0,i.A)(function*(){let U=T.topOutlet;for(;U;){if(yield U.pop())return!0;U=U.parentOutlet}return!1})()}setDirection(T,U,fe,qe){this.direction=T,this.animated=At(T,U,fe),this.animationBuilder=qe}setTopOutlet(T){this.topOutlet=T}consumeTransition(){let U,T="root";const fe=this.animationBuilder;return"auto"===this.direction?(T=this.guessDirection,U=this.guessAnimation):(U=this.animated,T=this.direction),this.direction=dn,this.animated=On,this.animationBuilder=void 0,{direction:T,animation:U,animationBuilder:fe}}navigate(T,U){if(Array.isArray(T))return this.router.navigate(T,U);{const fe=this.serializer.parse(T.toString());return void 0!==U.queryParams&&(fe.queryParams={...U.queryParams}),void 0!==U.fragment&&(fe.fragment=U.fragment),this.router.navigateByUrl(fe,U)}}}return B.\u0275fac=function(T){return new(T||B)(C.KVO(on),C.KVO(X.aZ),C.KVO(ne.Sd),C.KVO(ne.Ix,8))},B.\u0275prov=C.jDH({token:B,factory:B.\u0275fac,providedIn:"root"}),B})();const At=(B,j,T)=>{if(!1!==j){if(void 0!==T)return T;if("forward"===B||"back"===B)return B;if("root"===B&&!0===j)return"forward"}},dn="auto",On=void 0;let bn=(()=>{class B{get(T,U){const fe=hn();return fe?fe.get(T,U):null}getBoolean(T,U){const fe=hn();return!!fe&&fe.getBoolean(T,U)}getNumber(T,U){const fe=hn();return fe?fe.getNumber(T,U):0}}return B.\u0275fac=function(T){return new(T||B)},B.\u0275prov=C.jDH({token:B,factory:B.\u0275fac,providedIn:"root"}),B})();const er=new C.nKC("USERCONFIG"),hn=()=>{if(typeof window<"u"){const B=window.Ionic;if(B?.config)return B.config}return null};class yr{constructor(j={}){this.data=j}get(j){return this.data[j]}}let Nt=(()=>{class B{constructor(){this.zone=(0,C.WQX)(C.SKi),this.applicationRef=(0,C.WQX)(C.o8S)}create(T,U,fe){return new Ln(T,U,this.applicationRef,this.zone,fe)}}return B.\u0275fac=function(T){return new(T||B)},B.\u0275prov=C.jDH({token:B,factory:B.\u0275fac}),B})();class Ln{constructor(j,T,U,fe,qe){this.environmentInjector=j,this.injector=T,this.applicationRef=U,this.zone=fe,this.elementReferenceKey=qe,this.elRefMap=new WeakMap,this.elEventsMap=new WeakMap}attachViewToDom(j,T,U,fe){return this.zone.run(()=>new Promise(qe=>{const rt={...U};void 0!==this.elementReferenceKey&&(rt[this.elementReferenceKey]=j),qe(Zt(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,j,T,rt,fe,this.elementReferenceKey))}))}removeViewFromDom(j,T){return this.zone.run(()=>new Promise(U=>{const fe=this.elRefMap.get(T);if(fe){fe.destroy(),this.elRefMap.delete(T);const qe=this.elEventsMap.get(T);qe&&(qe(),this.elEventsMap.delete(T))}U()}))}}const Zt=(B,j,T,U,fe,qe,rt,J,He,b,x)=>{const G=C.zZn.create({providers:$n(He),parent:T}),Q=(0,C.a0P)(J,{environmentInjector:j,elementInjector:G}),Fe=Q.instance,be=Q.location.nativeElement;if(He&&(x&&void 0!==Fe[x]&&console.error(`[Ionic Error]: ${x} is a reserved property when using ${rt.tagName.toLowerCase()}. Rename or remove the "${x}" property from ${J.name}.`),Object.assign(Fe,He)),b)for(const nt of b)be.classList.add(nt);const st=Wn(B,Fe,be);return rt.appendChild(be),U.attachView(Q.hostView),fe.set(be,Q),qe.set(be,st),be},Vt=[H.L,H.a,H.b,H.c,H.d],Wn=(B,j,T)=>B.run(()=>{const U=Vt.filter(fe=>"function"==typeof j[fe]).map(fe=>{const qe=rt=>j[fe](rt.detail);return T.addEventListener(fe,qe),()=>T.removeEventListener(fe,qe)});return()=>U.forEach(fe=>fe())}),Mt=new C.nKC("NavParamsToken"),$n=B=>[{provide:Mt,useValue:B},{provide:yr,useFactory:Gr,deps:[Mt]}],Gr=B=>new yr(B),tr=(B,j)=>{const T=B.prototype;j.forEach(U=>{Object.defineProperty(T,U,{get(){return this.el[U]},set(fe){this.z.runOutsideAngular(()=>this.el[U]=fe)}})})},nr=(B,j)=>{const T=B.prototype;j.forEach(U=>{T[U]=function(){const fe=arguments;return this.z.runOutsideAngular(()=>this.el[U].apply(this.el,fe))}})},le=(B,j,T)=>{T.forEach(U=>B[U]=(0,$.R)(j,U))};function Se(B){return function(T){const{defineCustomElementFn:U,inputs:fe,methods:qe}=B;return void 0!==U&&U(),fe&&tr(T,fe),qe&&nr(T,qe),T}}const z=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],ve=["present","dismiss","onDidDismiss","onWillDismiss"];let et=(()=>{let B=class{constructor(T,U,fe){this.z=fe,this.isCmpOpen=!1,this.el=U.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,T.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,T.detectChanges()}),le(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}};return B.\u0275fac=function(T){return new(T||B)(C.rXU(C.gRc),C.rXU(C.aKT),C.rXU(C.SKi))},B.\u0275dir=C.FsC({type:B,selectors:[["ion-popover"]],contentQueries:function(T,U,fe){if(1&T&&C.wni(fe,C.C4Q,5),2&T){let qe;C.mGM(qe=C.lsd())&&(U.template=qe.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"}}),B=(0,It.Cg)([Se({inputs:z,methods:ve})],B),B})();const gt=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","event","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],Et=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"];let Qt=(()=>{let B=class{constructor(T,U,fe){this.z=fe,this.isCmpOpen=!1,this.el=U.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,T.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,T.detectChanges()}),le(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}};return B.\u0275fac=function(T){return new(T||B)(C.rXU(C.gRc),C.rXU(C.aKT),C.rXU(C.SKi))},B.\u0275dir=C.FsC({type:B,selectors:[["ion-modal"]],contentQueries:function(T,U,fe){if(1&T&&C.wni(fe,C.C4Q,5),2&T){let qe;C.mGM(qe=C.lsd())&&(U.template=qe.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",event:"event",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"}}),B=(0,It.Cg)([Se({inputs:gt,methods:Et})],B),B})();const ln=(B,j)=>((B=B.filter(T=>T.stackId!==j.stackId)).push(j),B),rr=(B,j)=>{const T=B.createUrlTree(["."],{relativeTo:j});return B.serializeUrl(T)},zr=(B,j)=>!j||B.stackId!==j.stackId,Wr=(B,j)=>{if(!B)return;const T=In(j);for(let U=0;U<T.length;U++){if(U>=B.length)return T[U];if(T[U]!==B[U])return}},In=B=>B.split("/").map(j=>j.trim()).filter(j=>""!==j),_t=B=>{B&&(B.ref.destroy(),B.unlistenEvents())};class eo{constructor(j,T,U,fe,qe,rt){this.containerEl=T,this.router=U,this.navCtrl=fe,this.zone=qe,this.location=rt,this.views=[],this.skipTransition=!1,this.nextId=0,this.tabsPrefix=void 0!==j?In(j):void 0}createView(j,T){const U=rr(this.router,T),fe=j?.location?.nativeElement,qe=Wn(this.zone,j.instance,fe);return{id:this.nextId++,stackId:Wr(this.tabsPrefix,U),unlistenEvents:qe,element:fe,ref:j,url:U}}getExistingView(j){const T=rr(this.router,j),U=this.views.find(fe=>fe.url===T);return U&&U.ref.changeDetectorRef.reattach(),U}setActive(j){const T=this.navCtrl.consumeTransition();let{direction:U,animation:fe,animationBuilder:qe}=T;const rt=this.activeView,J=zr(j,rt);J&&(U="back",fe=void 0);const He=this.views.slice();let b;const x=this.router;x.getCurrentNavigation?b=x.getCurrentNavigation():x.navigations?.value&&(b=x.navigations.value),b?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);const G=this.views.includes(j),Q=this.insertView(j,U);G||j.ref.changeDetectorRef.detectChanges();const Fe=j.animationBuilder;return void 0===qe&&"back"===U&&!J&&void 0!==Fe&&(qe=Fe),rt&&(rt.animationBuilder=qe),this.zone.runOutsideAngular(()=>this.wait(()=>(rt&&rt.ref.changeDetectorRef.detach(),j.ref.changeDetectorRef.reattach(),this.transition(j,rt,fe,this.canGoBack(1),!1,qe).then(()=>cr(j,Q,He,this.location,this.zone)).then(()=>({enteringView:j,direction:U,animation:fe,tabSwitch:J})))))}canGoBack(j,T=this.getActiveStackId()){return this.getStack(T).length>j}pop(j,T=this.getActiveStackId()){return this.zone.run(()=>{const U=this.getStack(T);if(U.length<=j)return Promise.resolve(!1);const fe=U[U.length-j-1];let qe=fe.url;const rt=fe.savedData;if(rt){const He=rt.get("primary");He?.route?._routerState?.snapshot.url&&(qe=He.route._routerState.snapshot.url)}const{animationBuilder:J}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(qe,{...fe.savedExtras,animation:J}).then(()=>!0)})}startBackTransition(){const j=this.activeView;if(j){const T=this.getStack(j.stackId),U=T[T.length-2],fe=U.animationBuilder;return this.wait(()=>this.transition(U,j,"back",this.canGoBack(2),!0,fe))}return Promise.resolve()}endBackTransition(j){j?(this.skipTransition=!0,this.pop(1)):this.activeView&&Tr(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(j){const T=this.getStack(j);return T.length>0?T[T.length-1]:void 0}getRootUrl(j){const T=this.getStack(j);return T.length>0?T[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return void 0!==this.runningTask}destroy(){this.containerEl=void 0,this.views.forEach(_t),this.activeView=void 0,this.views=[]}getStack(j){return this.views.filter(T=>T.stackId===j)}insertView(j,T){return this.activeView=j,this.views=((B,j,T)=>"root"===T?ln(B,j):"forward"===T?((B,j)=>(B.indexOf(j)>=0?B=B.filter(U=>U.stackId!==j.stackId||U.id<=j.id):B.push(j),B))(B,j):((B,j)=>B.indexOf(j)>=0?B.filter(U=>U.stackId!==j.stackId||U.id<=j.id):ln(B,j))(B,j))(this.views,j,T),this.views.slice()}transition(j,T,U,fe,qe,rt){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(T===j)return Promise.resolve(!1);const J=j?j.element:void 0,He=T?T.element:void 0,b=this.containerEl;return J&&J!==He&&(J.classList.add("ion-page"),J.classList.add("ion-page-invisible"),J.parentElement!==b&&b.appendChild(J),b.commit)?b.commit(J,He,{duration:void 0===U?0:void 0,direction:U,showGoBack:fe,progressAnimation:qe,animationBuilder:rt}):Promise.resolve(!1)}wait(j){var T=this;return(0,i.A)(function*(){void 0!==T.runningTask&&(yield T.runningTask,T.runningTask=void 0);const U=T.runningTask=j();return U.finally(()=>T.runningTask=void 0),U})()}}const cr=(B,j,T,U,fe)=>"function"==typeof requestAnimationFrame?new Promise(qe=>{requestAnimationFrame(()=>{Tr(B,j,T,U,fe),qe()})}):Promise.resolve(),Tr=(B,j,T,U,fe)=>{fe.run(()=>T.filter(qe=>!j.includes(qe)).forEach(_t)),j.forEach(qe=>{const J=U.path().split("?")[0].split("#")[0];if(qe!==B&&qe.url!==J){const He=qe.element;He.setAttribute("aria-hidden","true"),He.classList.add("ion-page-hidden"),qe.ref.changeDetectorRef.detach()}})};let Cr=(()=>{class B{constructor(T,U,fe,qe,rt,J,He,b){this.parentOutlet=b,this.activatedView=null,this.proxyMap=new WeakMap,this.currentActivatedRoute$=new Oe.t(null),this.activated=null,this._activatedRoute=null,this.name=ne.Xk,this.stackWillChange=new C.bkB,this.stackDidChange=new C.bkB,this.activateEvents=new C.bkB,this.deactivateEvents=new C.bkB,this.parentContexts=(0,C.WQX)(ne.Zp),this.location=(0,C.WQX)(C.c1b),this.environmentInjector=(0,C.WQX)(C.uvJ),this.inputBinder=(0,C.WQX)(Yt,{optional:!0}),this.supportsBindingToComponentInputs=!0,this.config=(0,C.WQX)(bn),this.navCtrl=(0,C.WQX)(yt),this.nativeEl=qe.nativeElement,this.name=T||ne.Xk,this.tabsPrefix="true"===U?rr(rt,He):void 0,this.stackCtrl=new eo(this.tabsPrefix,this.nativeEl,rt,this.navCtrl,J,fe),this.parentContexts.onChildOutletCreated(this.name,this)}get activatedComponentRef(){return this.activated}set animation(T){this.nativeEl.animation=T}set animated(T){this.nativeEl.animated=T}set swipeGesture(T){this._swipeGesture=T,this.nativeEl.swipeHandler=T?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:U=>this.stackCtrl.endBackTransition(U)}:void 0}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){const T=this.getContext();T?.route&&this.activateWith(T.route,T.injector)}new Promise(T=>(0,q.c)(this.nativeEl,T)).then(()=>{void 0===this._swipeGesture&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled","ios"===this.nativeEl.mode))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(T,U){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){const U=this.getContext();this.activatedView.savedData=new Map(U.children.contexts);const fe=this.activatedView.savedData.get("primary");if(fe&&U.route&&(fe.route={...U.route}),this.activatedView.savedExtras={},U.route){const qe=U.route.snapshot;this.activatedView.savedExtras.queryParams=qe.queryParams,this.activatedView.savedExtras.fragment=qe.fragment}}const T=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(T)}}activateWith(T,U){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=T;let fe,qe=this.stackCtrl.getExistingView(T);if(qe){fe=this.activated=qe.ref;const J=qe.savedData;J&&(this.getContext().children.contexts=J),this.updateActivatedRouteProxy(fe.instance,T)}else{const J=T._futureSnapshot,He=this.parentContexts.getOrCreateContext(this.name).children,b=new Oe.t(null),x=this.createActivatedRouteProxy(b,T),G=new Ar(x,He,this.location.injector);fe=this.activated=this.location.createComponent(J.routeConfig.component??J.component,{index:this.location.length,injector:G,environmentInjector:U??this.environmentInjector}),b.next(fe.instance),qe=this.stackCtrl.createView(this.activated,T),this.proxyMap.set(fe.instance,x),this.currentActivatedRoute$.next({component:fe.instance,activatedRoute:T})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=qe,this.navCtrl.setTopOutlet(this);const rt=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:qe,tabSwitch:zr(qe,rt)}),this.stackCtrl.setActive(qe).then(J=>{this.activateEvents.emit(fe.instance),this.stackDidChange.emit(J)})}canGoBack(T=1,U){return this.stackCtrl.canGoBack(T,U)}pop(T=1,U){return this.stackCtrl.pop(T,U)}getLastUrl(T){const U=this.stackCtrl.getLastUrl(T);return U?U.url:void 0}getLastRouteView(T){return this.stackCtrl.getLastUrl(T)}getRootView(T){return this.stackCtrl.getRootUrl(T)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(T,U){const fe=new ne.nX;return fe._futureSnapshot=U._futureSnapshot,fe._routerState=U._routerState,fe.snapshot=U.snapshot,fe.outlet=U.outlet,fe.component=U.component,fe._paramMap=this.proxyObservable(T,"paramMap"),fe._queryParamMap=this.proxyObservable(T,"queryParamMap"),fe.url=this.proxyObservable(T,"url"),fe.params=this.proxyObservable(T,"params"),fe.queryParams=this.proxyObservable(T,"queryParams"),fe.fragment=this.proxyObservable(T,"fragment"),fe.data=this.proxyObservable(T,"data"),fe}proxyObservable(T,U){return T.pipe((0,Tt.p)(fe=>!!fe),(0,vt.n)(fe=>this.currentActivatedRoute$.pipe((0,Tt.p)(qe=>null!==qe&&qe.component===fe),(0,vt.n)(qe=>qe&&qe.activatedRoute[U]),function Be(B,j=xt.D){return B=B??xe,(0,Pt.N)((T,U)=>{let fe,qe=!0;T.subscribe((0,Ft._)(U,rt=>{const J=j(rt);(qe||!B(fe,J))&&(qe=!1,fe=J,U.next(rt))}))})}())))}updateActivatedRouteProxy(T,U){const fe=this.proxyMap.get(T);if(!fe)throw new Error("Could not find activated route proxy for view");fe._futureSnapshot=U._futureSnapshot,fe._routerState=U._routerState,fe.snapshot=U.snapshot,fe.outlet=U.outlet,fe.component=U.component,this.currentActivatedRoute$.next({component:T,activatedRoute:U})}}return B.\u0275fac=function(T){return new(T||B)(C.kS0("name"),C.kS0("tabs"),C.rXU(X.aZ),C.rXU(C.aKT),C.rXU(ne.Ix),C.rXU(C.SKi),C.rXU(ne.nX),C.rXU(B,12))},B.\u0275dir=C.FsC({type:B,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"]}),B})();class Ar{constructor(j,T,U){this.route=j,this.childContexts=T,this.parent=U}get(j,T){return j===ne.nX?this.route:j===ne.Zp?this.childContexts:this.parent.get(j,T)}}const Yt=new C.nKC("");let Rr=(()=>{class B{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(T){this.unsubscribeFromRouteData(T),this.subscribeToRouteData(T)}unsubscribeFromRouteData(T){this.outletDataSubscriptions.get(T)?.unsubscribe(),this.outletDataSubscriptions.delete(T)}subscribeToRouteData(T){const{activatedRoute:U}=T,fe=(0,Le.z)([U.queryParams,U.params,U.data]).pipe((0,vt.n)(([qe,rt,J],He)=>(J={...qe,...rt,...J},0===He?(0,Xe.of)(J):Promise.resolve(J)))).subscribe(qe=>{if(!T.isActivated||!T.activatedComponentRef||T.activatedRoute!==U||null===U.component)return void this.unsubscribeFromRouteData(T);const rt=(0,C.HJs)(U.component);if(rt)for(const{templateName:J}of rt.inputs)T.activatedComponentRef.setInput(J,qe[J]);else this.unsubscribeFromRouteData(T)});this.outletDataSubscriptions.set(T,fe)}}return B.\u0275fac=function(T){return new(T||B)},B.\u0275prov=C.jDH({token:B,factory:B.\u0275fac}),B})();const Or=()=>({provide:Yt,useFactory:Vn,deps:[ne.Ix]});function Vn(B){return B?.componentInputBindingEnabled?new Rr:null}const pn=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"];let xr=(()=>{let B=class{constructor(T,U,fe,qe,rt,J){this.routerOutlet=T,this.navCtrl=U,this.config=fe,this.r=qe,this.z=rt,J.detach(),this.el=this.r.nativeElement}onClick(T){const U=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),T.preventDefault()):null!=U&&(this.navCtrl.navigateBack(U,{animation:this.routerAnimation}),T.preventDefault())}};return B.\u0275fac=function(T){return new(T||B)(C.rXU(Cr,8),C.rXU(yt),C.rXU(bn),C.rXU(C.aKT),C.rXU(C.SKi),C.rXU(C.gRc))},B.\u0275dir=C.FsC({type:B,hostBindings:function(T,U){1&T&&C.bIt("click",function(qe){return U.onClick(qe)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"}}),B=(0,It.Cg)([Se({inputs:pn})],B),B})(),Nr=(()=>{class B{constructor(T,U,fe,qe,rt){this.locationStrategy=T,this.navCtrl=U,this.elementRef=fe,this.router=qe,this.routerLink=rt,this.routerDirection="forward"}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){const T=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=T}}onClick(T){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),T.preventDefault()}}return B.\u0275fac=function(T){return new(T||B)(C.rXU(X.hb),C.rXU(yt),C.rXU(C.aKT),C.rXU(ne.Ix),C.rXU(ne.Wk,8))},B.\u0275dir=C.FsC({type:B,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(T,U){1&T&&C.bIt("click",function(qe){return U.onClick(qe)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},features:[C.OA$]}),B})(),Xn=(()=>{class B{constructor(T,U,fe,qe,rt){this.locationStrategy=T,this.navCtrl=U,this.elementRef=fe,this.router=qe,this.routerLink=rt,this.routerDirection="forward"}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){const T=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=T}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}}return B.\u0275fac=function(T){return new(T||B)(C.rXU(X.hb),C.rXU(yt),C.rXU(C.aKT),C.rXU(ne.Ix),C.rXU(ne.Wk,8))},B.\u0275dir=C.FsC({type:B,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(T,U){1&T&&C.bIt("click",function(){return U.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},features:[C.OA$]}),B})();const Bn=["animated","animation","root","rootParams","swipeGesture"],Un=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"];let _n=(()=>{let B=class{constructor(T,U,fe,qe,rt,J){this.z=rt,J.detach(),this.el=T.nativeElement,T.nativeElement.delegate=qe.create(U,fe),le(this,this.el,["ionNavDidChange","ionNavWillChange"])}};return B.\u0275fac=function(T){return new(T||B)(C.rXU(C.aKT),C.rXU(C.uvJ),C.rXU(C.zZn),C.rXU(Nt),C.rXU(C.SKi),C.rXU(C.gRc))},B.\u0275dir=C.FsC({type:B,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"}}),B=(0,It.Cg)([Se({inputs:Bn,methods:Un})],B),B})(),Er=(()=>{class B{constructor(T){this.navCtrl=T,this.ionTabsWillChange=new C.bkB,this.ionTabsDidChange=new C.bkB,this.tabBarSlot="bottom"}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:T,tabSwitch:U}){const fe=T.stackId;U&&void 0!==fe&&this.ionTabsWillChange.emit({tab:fe})}onStackDidChange({enteringView:T,tabSwitch:U}){const fe=T.stackId;U&&void 0!==fe&&(this.tabBar&&(this.tabBar.selectedTab=fe),this.ionTabsDidChange.emit({tab:fe}))}select(T){const U="string"==typeof T,fe=U?T:T.detail.tab,qe=this.outlet.getActiveStackId()===fe,rt=`${this.outlet.tabsPrefix}/${fe}`;if(U||T.stopPropagation(),qe){const J=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(J)?.url===rt)return;const b=this.outlet.getRootView(fe);return this.navCtrl.navigateRoot(rt,{...b&&rt===b.url&&b.savedExtras,animated:!0,animationDirection:"back"})}{const J=this.outlet.getLastRouteView(fe),He=J?.url||rt,b=J?.savedExtras;return this.navCtrl.navigateRoot(He,{...b,animated:!0,animationDirection:"back"})}}getSelected(){return this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(T=>{const U=T.el.getAttribute("slot");U!==this.tabBarSlot&&(this.tabBarSlot=U,this.relocateTabBar())})}relocateTabBar(){const T=this.tabBar.el;"top"===this.tabBarSlot?this.tabsInner.nativeElement.before(T):this.tabsInner.nativeElement.after(T)}}return B.\u0275fac=function(T){return new(T||B)(C.rXU(yt))},B.\u0275dir=C.FsC({type:B,selectors:[["ion-tabs"]],viewQuery:function(T,U){if(1&T&&C.GBs(Ae,7,C.aKT),2&T){let fe;C.mGM(fe=C.lsd())&&(U.tabsInner=fe.first)}},hostBindings:function(T,U){1&T&&C.bIt("ionTabButtonClick",function(qe){return U.select(qe)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"}}),B})();const Bt=B=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(B):"function"==typeof requestAnimationFrame?requestAnimationFrame(B):setTimeout(B);let Zn=(()=>{class B{constructor(T,U){this.injector=T,this.elementRef=U,this.onChange=()=>{},this.onTouched=()=>{}}writeValue(T){this.elementRef.nativeElement.value=this.lastValue=T,Jt(this.elementRef)}handleValueChange(T,U){T===this.elementRef.nativeElement&&(U!==this.lastValue&&(this.lastValue=U,this.onChange(U)),Jt(this.elementRef))}_handleBlurEvent(T){T===this.elementRef.nativeElement&&(this.onTouched(),Jt(this.elementRef))}registerOnChange(T){this.onChange=T}registerOnTouched(T){this.onTouched=T}setDisabledState(T){this.elementRef.nativeElement.disabled=T}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let T;try{T=this.injector.get(he.vO)}catch{}if(!T)return;T.statusChanges&&(this.statusChanges=T.statusChanges.subscribe(()=>Jt(this.elementRef)));const U=T.control;U&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(qe=>{if(typeof U[qe]<"u"){const rt=U[qe].bind(U);U[qe]=(...J)=>{rt(...J),Jt(this.elementRef)}}})}}return B.\u0275fac=function(T){return new(T||B)(C.rXU(C.zZn),C.rXU(C.aKT))},B.\u0275dir=C.FsC({type:B,hostBindings:function(T,U){1&T&&C.bIt("ionBlur",function(qe){return U._handleBlurEvent(qe.target)})}}),B})();const Jt=B=>{Bt(()=>{const j=B.nativeElement,T=null!=j.value&&j.value.toString().length>0,U=tn(j);ur(j,U);const fe=j.closest("ion-item");fe&&ur(fe,T?[...U,"item-has-value"]:U)})},tn=B=>{const j=B.classList,T=[];for(let U=0;U<j.length;U++){const fe=j.item(U);null!==fe&&br(fe,"ng-")&&T.push(`ion-${fe.substring(3)}`)}return T},ur=(B,j)=>{const T=B.classList;T.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),T.add(...j)},br=(B,j)=>B.substring(0,j.length)===j;class sr{shouldDetach(j){return!1}shouldAttach(j){return!1}store(j,T){}retrieve(j){return null}shouldReuseRoute(j,T){if(j.routeConfig!==T.routeConfig)return!1;const U=j.params,fe=T.params,qe=Object.keys(U),rt=Object.keys(fe);if(qe.length!==rt.length)return!1;for(const J of qe)if(fe[J]!==U[J])return!1;return!0}}class dr{constructor(j){this.ctrl=j}create(j){return this.ctrl.create(j||{})}dismiss(j,T,U){return this.ctrl.dismiss(j,T,U)}getTop(){return this.ctrl.getTop()}}},8974:(Ct,We,O)=>{"use strict";O.d(We,{U1:()=>Tt,Jm:()=>xe,b_:()=>Ae,I9:()=>ht,ME:()=>Lt,tN:()=>on,W9:()=>At,eU:()=>Ln,iq:()=>Zt,uz:()=>Gr,he:()=>ve,nf:()=>et,Rg:()=>sr,BC:()=>Jt,ai:()=>br,bv:()=>mn});var i=O(4438),C=O(4341),ne=O(4517),X=O(1635),Me=O(3726),Ie=O(177),L=O(70),pe=(O(3503),O(7555),O(4569),O(2942),O(405),O(611)),ue=O(464),Ve=(O(5384),O(5516)),se=(O(8476),O(4363));O(8221),O(3113);var ke=O(467);const Ge=pe.i,V=function(){var f=(0,ke.A)(function*(N,m){if(!(typeof window>"u"))return yield Ge(),(0,se.b)(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-input",[[38,"ion-input",{"color":[513],"accept":[1],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[4],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[4],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"size":[2],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"disabled":["disabledChanged"],"placeholder":["placeholderChanged"],"value":["valueChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"legacy":[4],"justify":[1],"alignment":[1],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"legacy":[4],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[1,"ion-skeleton-text",{"animated":[4]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[49,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[4],"download":[1],"fill":[1],"shape":[1],"href":[1],"rel":[1],"lines":[1],"counter":[4],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"counterFormatter":[16],"multipleInputs":[32],"focusable":[32],"counterString":[32]},[[0,"ionInput","handleIonInput"],[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"],"counterFormatter":["counterFormatterChanged"]}],[34,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-note",{"color":[513]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"legacy":[4],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"isExpanded":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-picker-internal",[[33,"ion-picker-internal",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"legacy":[4],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"],"checked":["styleChanged"],"color":["styleChanged"],"disabled":["styleChanged"]}],[0,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[34,"ion-buttons",{"collapse":[4]}]]],["ion-picker-column-internal",[[33,"ion-picker-column-internal",{"disabled":[4],"items":[16],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64]},null,{"value":["valueChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"legacy":[4]},null,{"checked":["styleChanged"],"disabled":["styleChanged"]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]]]'),m)});return function(m,F){return f.apply(this,arguments)}}(),A=["*"],Ue=(f,N)=>{const m=f.prototype;N.forEach(F=>{Object.defineProperty(m,F,{get(){return this.el[F]},set(oe){this.z.runOutsideAngular(()=>this.el[F]=oe)},configurable:!0})})},H=(f,N)=>{const m=f.prototype;N.forEach(F=>{m[F]=function(){const oe=arguments;return this.z.runOutsideAngular(()=>this.el[F].apply(this.el,oe))}})},q=(f,N,m)=>{m.forEach(F=>f[F]=(0,Me.R)(N,F))};function $(f){return function(m){const{defineCustomElementFn:F,inputs:oe,methods:Xt}=f;return void 0!==F&&F(),oe&&Ue(m,oe),Xt&&H(m,Xt),m}}let Tt=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-app"]],ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({})],f),f})(),xe=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement,q(this,this.el,["ionFocus","ionBlur"])}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],f),f})(),Ae=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],f),f})(),ht=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-card-content"]],inputs:{mode:"mode"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["mode"]})],f),f})(),Lt=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["color","mode","translucent"]})],f),f})(),on=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["color","mode"]})],f),f})(),At=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement,q(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-content"]],inputs:{color:"color",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["color","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],f),f})(),Ln=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["collapse","mode","translucent"]})],f),f})(),Zt=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],f),f})(),Gr=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-item"]],inputs:{button:"button",color:"color",counter:"counter",counterFormatter:"counterFormatter",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",fill:"fill",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",target:"target",type:"type"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["button","color","counter","counterFormatter","detail","detailIcon","disabled","download","fill","href","lines","mode","rel","routerAnimation","routerDirection","shape","target","type"]})],f),f})(),ve=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["color","mode","position"]})],f),f})(),et=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],f),f})(),Jt=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["color","size"]})],f),f})(),br=(()=>{let f=class{constructor(m,F,oe){this.z=oe,m.detach(),this.el=F.nativeElement}};return f.\u0275fac=function(m){return new(m||f)(i.rXU(i.gRc),i.rXU(i.aKT),i.rXU(i.SKi))},f.\u0275cmp=i.VBU({type:f,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:A,decls:1,vars:0,template:function(m,F){1&m&&(i.NAR(),i.SdG(0))},encapsulation:2,changeDetection:0}),f=(0,X.Cg)([$({inputs:["color","mode"]})],f),f})(),sr=(()=>{class f extends ne.Rg{constructor(m,F,oe,Xt,Kn,xn,Xr,Ir){super(m,F,oe,Xt,Kn,xn,Xr,Ir),this.parentOutlet=Ir}}return f.\u0275fac=function(m){return new(m||f)(i.kS0("name"),i.kS0("tabs"),i.rXU(Ie.aZ),i.rXU(i.aKT),i.rXU(L.Ix),i.rXU(i.SKi),i.rXU(L.nX),i.rXU(f,12))},f.\u0275dir=i.FsC({type:f,selectors:[["ion-router-outlet"]],features:[i.Vt3]}),f})();const rt={provide:C.cz,useExisting:(0,i.Rfq)(()=>J),multi:!0};let J=(()=>{class f extends C.zX{}return f.\u0275fac=(()=>{let N;return function(F){return(N||(N=i.xGo(f)))(F||f)}})(),f.\u0275dir=i.FsC({type:f,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(m,F){2&m&&i.BMQ("max",F._enabled?F.max:null)},features:[i.Jv_([rt]),i.Vt3]}),f})();const He={provide:C.cz,useExisting:(0,i.Rfq)(()=>b),multi:!0};let b=(()=>{class f extends C.VZ{}return f.\u0275fac=(()=>{let N;return function(F){return(N||(N=i.xGo(f)))(F||f)}})(),f.\u0275dir=i.FsC({type:f,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(m,F){2&m&&i.BMQ("min",F._enabled?F.min:null)},features:[i.Jv_([He]),i.Vt3]}),f})(),nt=(()=>{class f extends ne.Kb{constructor(){super(Ve.m),this.angularDelegate=(0,i.WQX)(ne.Yq),this.injector=(0,i.WQX)(i.zZn),this.environmentInjector=(0,i.WQX)(i.uvJ)}create(m){return super.create({...m,delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")})}}return f.\u0275fac=function(m){return new(m||f)},f.\u0275prov=i.jDH({token:f,factory:f.\u0275fac}),f})();class ot extends ne.Kb{constructor(){super(Ve.c),this.angularDelegate=(0,i.WQX)(ne.Yq),this.injector=(0,i.WQX)(i.zZn),this.environmentInjector=(0,i.WQX)(i.uvJ)}create(N){return super.create({...N,delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")})}}const mt=(f,N,m)=>()=>{const F=N.defaultView;if(F&&typeof window<"u"){(0,ue.s)({...f,_zoneGate:Xt=>m.run(Xt)});const oe="__zone_symbol__addEventListener"in N.body?"__zone_symbol__addEventListener":"addEventListener";return function Ce(){var f=[];if(typeof window<"u"){var N=window;(!N.customElements||N.Element&&(!N.Element.prototype.closest||!N.Element.prototype.matches||!N.Element.prototype.remove||!N.Element.prototype.getRootNode))&&f.push(O.e(7278).then(O.t.bind(O,2190,23))),("function"!=typeof Object.assign||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||N.NodeList&&!N.NodeList.prototype.forEach||!N.fetch||!function(){try{var F=new URL("b","http://a");return F.pathname="c%20d","http://a/c%20d"===F.href&&F.searchParams}catch{return!1}}()||typeof WeakMap>"u")&&f.push(O.e(9329).then(O.t.bind(O,7783,23)))}return Promise.all(f)}().then(()=>V(F,{exclude:["ion-tabs","ion-tab"],syncQueue:!0,raf:ne.er,jmp:Xt=>m.runOutsideAngular(Xt),ael(Xt,Kn,xn,Xr){Xt[oe](Kn,xn,Xr)},rel(Xt,Kn,xn,Xr){Xt.removeEventListener(Kn,xn,Xr)}}))}};let mn=(()=>{class f{static forRoot(m){return{ngModule:f,providers:[{provide:ne.sR,useValue:m},{provide:i.hnV,useFactory:mt,multi:!0,deps:[ne.sR,Ie.qQ,i.SKi]},(0,ne.YV)()]}}}return f.\u0275fac=function(m){return new(m||f)},f.\u0275mod=i.$C({type:f}),f.\u0275inj=i.G2t({providers:[ne.Yq,nt,ot],imports:[Ie.MD]}),f})()},467:(Ct,We,O)=>{"use strict";function i(ne,X,Me,Ie,L,Pe,je){try{var ye=ne[Pe](je),te=ye.value}catch(De){return void Me(De)}ye.done?X(te):Promise.resolve(te).then(Ie,L)}function C(ne){return function(){var X=this,Me=arguments;return new Promise(function(Ie,L){var Pe=ne.apply(X,Me);function je(te){i(Pe,Ie,L,je,ye,"next",te)}function ye(te){i(Pe,Ie,L,je,ye,"throw",te)}je(void 0)})}}O.d(We,{A:()=>C})},1635:(Ct,We,O)=>{"use strict";function Me(H,q,ae,$){var Xe,Oe=arguments.length,Le=Oe<3?q:null===$?$=Object.getOwnPropertyDescriptor(q,ae):$;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)Le=Reflect.decorate(H,q,ae,$);else for(var It=H.length-1;It>=0;It--)(Xe=H[It])&&(Le=(Oe<3?Xe(Le):Oe>3?Xe(q,ae,Le):Xe(q,ae))||Le);return Oe>3&&Le&&Object.defineProperty(q,ae,Le),Le}function De(H,q,ae,$){return new(ae||(ae=Promise))(function(Le,Xe){function It(xt){try{vt($.next(xt))}catch(Pt){Xe(Pt)}}function Tt(xt){try{vt($.throw(xt))}catch(Pt){Xe(Pt)}}function vt(xt){xt.done?Le(xt.value):function Oe(Le){return Le instanceof ae?Le:new ae(function(Xe){Xe(Le)})}(xt.value).then(It,Tt)}vt(($=$.apply(H,q||[])).next())})}function Ee(H){return this instanceof Ee?(this.v=H,this):new Ee(H)}function Ce(H,q,ae){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Oe,$=ae.apply(H,q||[]),Le=[];return Oe=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),It("next"),It("throw"),It("return",function Xe(Be){return function(xe){return Promise.resolve(xe).then(Be,Pt)}}),Oe[Symbol.asyncIterator]=function(){return this},Oe;function It(Be,xe){$[Be]&&(Oe[Be]=function(he){return new Promise(function(Ae,ht){Le.push([Be,he,Ae,ht])>1||Tt(Be,he)})},xe&&(Oe[Be]=xe(Oe[Be])))}function Tt(Be,xe){try{!function vt(Be){Be.value instanceof Ee?Promise.resolve(Be.value.v).then(xt,Pt):Ft(Le[0][2],Be)}($[Be](xe))}catch(he){Ft(Le[0][3],he)}}function xt(Be){Tt("next",Be)}function Pt(Be){Tt("throw",Be)}function Ft(Be,xe){Be(xe),Le.shift(),Le.length&&Tt(Le[0][0],Le[0][1])}}function Ge(H){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var ae,q=H[Symbol.asyncIterator];return q?q.call(H):(H=function Ve(H){var q="function"==typeof Symbol&&Symbol.iterator,ae=q&&H[q],$=0;if(ae)return ae.call(H);if(H&&"number"==typeof H.length)return{next:function(){return H&&$>=H.length&&(H=void 0),{value:H&&H[$++],done:!H}}};throw new TypeError(q?"Object is not iterable.":"Symbol.iterator is not defined.")}(H),ae={},$("next"),$("throw"),$("return"),ae[Symbol.asyncIterator]=function(){return this},ae);function $(Le){ae[Le]=H[Le]&&function(Xe){return new Promise(function(It,Tt){!function Oe(Le,Xe,It,Tt){Promise.resolve(Tt).then(function(vt){Le({value:vt,done:It})},Xe)}(It,Tt,(Xe=H[Le](Xe)).done,Xe.value)})}}}O.d(We,{AQ:()=>Ce,Cg:()=>Me,N3:()=>Ee,sH:()=>De,xN:()=>Ge}),"function"==typeof SuppressedError&&SuppressedError}},Ct=>{Ct(Ct.s=7206)}]);