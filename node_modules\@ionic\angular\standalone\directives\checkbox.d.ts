import { ChangeDetectorRef, <PERSON>ementRef, EventEmitter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { CheckboxChangeEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonCheckbox extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    writeValue(value: boolean): void;
    handleIonChange(el: HTMLIonCheckboxElement | HTMLIonToggleElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonCheckbox, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonCheckbox, "ion-checkbox", never, { "checked": "checked"; "color": "color"; "disabled": "disabled"; "indeterminate": "indeterminate"; "justify": "justify"; "labelPlacement": "labelPlacement"; "legacy": "legacy"; "mode": "mode"; "name": "name"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonCheckbox extends Components.IonCheckbox {
    /**
     * Emitted when the checked property has changed
  as a result of a user action such as a click.
  This event will not emit when programmatically
  setting the checked property.
     */
    ionChange: EventEmitter<CustomEvent<CheckboxChangeEventDetail>>;
    /**
     * Emitted when the checkbox has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the checkbox loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
}
