"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.readJsonFile = void 0;
const schematics_1 = require("@angular-devkit/schematics");
const fs_1 = require("fs");
const jsonc_parser_1 = require("jsonc-parser");
function readJsonFile(path) {
    if (!(0, fs_1.existsSync)(path)) {
        throw new schematics_1.FileDoesNotExistException(path);
    }
    const errors = [];
    const content = (0, jsonc_parser_1.parse)((0, fs_1.readFileSync)(path, 'utf-8'), errors, { allowTrailingComma: true });
    if (errors.length) {
        const { error, offset } = errors[0];
        throw new Error(`Failed to parse "${path}" as JSON AST Object. ${(0, jsonc_parser_1.printParseErrorCode)(error)} at location: ${offset}.`);
    }
    return content;
}
exports.readJsonFile = readJsonFile;
//# sourceMappingURL=data:application/json;base64,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