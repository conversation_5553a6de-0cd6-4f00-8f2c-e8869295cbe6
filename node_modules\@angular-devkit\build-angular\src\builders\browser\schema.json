{"$schema": "http://json-schema.org/draft-07/schema", "title": "Webpack browser schema for Build Facade.", "description": "Browser target options", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/assetPattern"}}, "main": {"type": "string", "description": "The full path for the main entry point to the app, relative to the current workspace."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false, "required": ["input"]}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false, "required": ["input"]}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking, dead-code elimination, inlining of critical CSS and fonts inlining. For more information, see https://angular.io/guide/workspace-config#optimization-configuration.", "default": true, "x-user-analytics": "ep.ng_optimization", "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"description": "Enables optimization of the styles output.", "default": true, "oneOf": [{"type": "object", "properties": {"minify": {"type": "boolean", "description": "Minify CSS definitions by removing extraneous whitespace and comments, merging identifiers and minimizing values.", "default": true}, "inlineCritical": {"type": "boolean", "description": "Extract and inline critical CSS definitions to improve first paint time.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fonts": {"description": "Enables optimization for fonts. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true, "oneOf": [{"type": "object", "properties": {"inline": {"type": "boolean", "description": "Reduce render blocking requests by inlining external Google Fonts and Adobe Fonts CSS definitions in the application's HTML index file. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}}, "additionalProperties": false}, {"type": "boolean"}]}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/fileReplacement"}, "default": []}, "outputPath": {"type": "string", "description": "The full path for the new output directory, relative to the current workspace."}, "resourcesOutputPath": {"type": "string", "description": "The path where style resources will be placed, relative to outputPath."}, "aot": {"type": "boolean", "description": "Build using Ahead of Time compilation.", "x-user-analytics": "ep.ng_aot", "default": true}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.io/guide/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "vendorChunk": {"type": "boolean", "description": "Generate a seperate bundle containing only vendor libraries. This option should only be used for development to reduce the incremental compilation time.", "default": false}, "commonChunk": {"type": "boolean", "description": "Generate a seperate bundle containing code used across multiple bundles.", "default": true}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file.", "default": true}, "buildOptimizer": {"type": "boolean", "description": "Enables advanced build optimizations when using the 'aot' option.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "subresourceIntegrity": {"type": "boolean", "description": "Enables the use of subresource integrity validation.", "default": false}, "serviceWorker": {"type": "boolean", "description": "Generates a service worker config for production builds.", "default": false}, "ngswConfigPath": {"type": "string", "description": "Path to ngsw-config.json."}, "index": {"description": "Configures the generation of the application's HTML index.", "oneOf": [{"type": "string", "description": "The path of a file to use for the application's HTML index. The filename of the specified path will be used for the generated file and will be created in the root of the application's configured output path."}, {"type": "object", "description": "", "properties": {"input": {"type": "string", "minLength": 1, "description": "The path of a file to use for the application's generated HTML index."}, "output": {"type": "string", "minLength": 1, "default": "index.html", "description": "The output path of the application's generated HTML index file. The full provided path will be used and will be considered relative to the application's configured output path."}}, "required": ["input"]}]}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed using tools such as 'webpack-bundle-analyzer'.", "default": false}, "budgets": {"description": "Budget thresholds to ensure parts of your application stay within boundaries which you set.", "type": "array", "items": {"$ref": "#/definitions/budget"}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "crossOrigin": {"type": "string", "description": "Define the crossorigin attribute setting of elements that provide CORS support.", "default": "none", "enum": ["none", "anonymous", "use-credentials"]}, "allowedCommonJsDependencies": {"description": "A list of CommonJS or AMD packages that are allowed to be used without a build time warning. Use `'*'` to allow all.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false, "required": ["outputPath", "index", "main", "tsConfig"], "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "description": "Absolute path within the output."}}, "additionalProperties": false, "required": ["glob", "input", "output"]}, {"type": "string"}]}, "fileReplacement": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "replaceWith": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false, "required": ["src", "replaceWith"]}, {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false, "required": ["replace", "with"]}]}, "budget": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of budget.", "enum": ["all", "allScript", "any", "anyScript", "anyComponentStyle", "bundle", "initial"]}, "name": {"type": "string", "description": "The name of the bundle."}, "baseline": {"type": "string", "description": "The baseline size for comparison."}, "maximumWarning": {"type": "string", "description": "The maximum threshold for warning relative to the baseline."}, "maximumError": {"type": "string", "description": "The maximum threshold for error relative to the baseline."}, "minimumWarning": {"type": "string", "description": "The minimum threshold for warning relative to the baseline."}, "minimumError": {"type": "string", "description": "The minimum threshold for error relative to the baseline."}, "warning": {"type": "string", "description": "The threshold for warning relative to the baseline (min & max)."}, "error": {"type": "string", "description": "The threshold for error relative to the baseline (min & max)."}}, "additionalProperties": false, "required": ["type"]}}}