import { ChangeDetectorRef, ElementRef, EventEmitter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { ToggleChangeEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonToggle extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    writeValue(value: boolean): void;
    handleIonChange(el: HTMLIonToggleElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonToggle, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonToggle, "ion-toggle", never, { "checked": "checked"; "color": "color"; "disabled": "disabled"; "enableOnOffLabels": "enableOnOffLabels"; "justify": "justify"; "labelPlacement": "labelPlacement"; "legacy": "legacy"; "mode": "mode"; "name": "name"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonToggle extends Components.IonToggle {
    /**
     * Emitted when the user switches the toggle on or off. Does not emit
  when programmatically changing the value of the `checked` property.
     */
    ionChange: EventEmitter<CustomEvent<ToggleChangeEventDetail>>;
    /**
     * Emitted when the toggle has focus.
     */
    ionFocus: EventEmitter<CustomEvent<void>>;
    /**
     * Emitted when the toggle loses focus.
     */
    ionBlur: EventEmitter<CustomEvent<void>>;
}
