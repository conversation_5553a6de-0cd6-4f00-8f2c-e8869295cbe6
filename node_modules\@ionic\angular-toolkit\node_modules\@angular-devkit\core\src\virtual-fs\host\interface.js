"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;base64,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