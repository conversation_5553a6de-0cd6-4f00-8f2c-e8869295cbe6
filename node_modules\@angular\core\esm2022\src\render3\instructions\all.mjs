/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/*
 * This file re-exports all symbols contained in this directory.
 *
 * Why is this file not `index.ts`?
 *
 * There seems to be an inconsistent path resolution of an `index.ts` file
 * when only the parent directory is referenced. This could be due to the
 * node module resolution configuration differing from rollup and/or typescript.
 *
 * With commit
 * https://github.com/angular/angular/commit/d5e3f2c64bd13ce83e7c70788b7fc514ca4a9918
 * the `instructions.ts` file was moved to `instructions/instructions.ts` and an
 * `index.ts` file was used to re-export everything. Having had file names that were
 * importing from `instructions' directly (not the from the sub file or the `index.ts`
 * file) caused strange CI issues. `index.ts` had to be renamed to `all.ts` for this
 * to work.
 *
 * Jira Issue = FW-1184
 */
export * from '../../defer/instructions';
export * from './advance';
export * from './attribute';
export * from './attribute_interpolation';
export * from './change_detection';
export * from './class_map_interpolation';
export * from './component_instance';
export * from './control_flow';
export * from './di';
export * from './di_attr';
export * from './element';
export * from './element_container';
export { ɵgetUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode, ɵsetUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode } from './element_validation';
export * from './get_current_view';
export * from './host_property';
export * from './i18n';
export * from './listener';
export * from './namespace';
export * from './next_context';
export * from './projection';
export * from './property';
export * from './property_interpolation';
export * from './queries';
export * from './queries_signals';
export * from './storage';
export * from './style_map_interpolation';
export * from './style_prop_interpolation';
export * from './styling';
export * from './template';
export * from './text';
export * from './text_interpolation';
export * from './two_way';
//# sourceMappingURL=data:application/json;base64,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