{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { RouteReuseStrategy } from '@angular/router';\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\nimport { AppComponent } from './app.component';\nimport { AppRoutingModule } from './app-routing.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: RouteReuseStrategy,\n        useClass: IonicRouteStrategy\n      }],\n      imports: [BrowserModule, IonicModule.forRoot(), AppRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, i1.IonicModule, AppRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "RouteReuseStrategy", "IonicModule", "IonicRouteStrategy", "AppComponent", "AppRoutingModule", "AppModule", "bootstrap", "provide", "useClass", "imports", "forRoot", "declarations", "i1"], "sources": ["D:\\2025\\agmuicon\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { RouteReuseStrategy } from '@angular/router';\n\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\n\nimport { AppComponent } from './app.component';\nimport { AppRoutingModule } from './app-routing.module';\n\n@NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule, IonicModule.forRoot(), AppRoutingModule],\n  providers: [{ provide: RouteReuseStrategy, useClass: IonicRouteStrategy }],\n  bootstrap: [AppComponent],\n})\nexport class AppModule {}\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,kBAAkB,QAAQ,iBAAiB;AAEpD,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,gBAAgB;AAEhE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;;;AAQvD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRH,YAAY;IAAA;EAAA;;;iBADb,CAAC;QAAEI,OAAO,EAAEP,kBAAkB;QAAEQ,QAAQ,EAAEN;MAAkB,CAAE,CAAC;MAAAO,OAAA,GADhEV,aAAa,EAAEE,WAAW,CAACS,OAAO,EAAE,EAAEN,gBAAgB;IAAA;EAAA;;;2EAIrDC,SAAS;IAAAM,YAAA,GALLR,YAAY;IAAAM,OAAA,GACjBV,aAAa,EAAAa,EAAA,CAAAX,WAAA,EAAyBG,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}