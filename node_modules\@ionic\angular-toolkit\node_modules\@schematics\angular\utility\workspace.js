"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.allTargetOptions = exports.allWorkspaceTargets = exports.createDefaultPath = exports.buildDefaultPath = exports.writeWorkspace = exports.getWorkspace = exports.updateWorkspace = void 0;
const core_1 = require("@angular-devkit/core");
const schematics_1 = require("@angular-devkit/schematics");
const workspace_models_1 = require("./workspace-models");
const DEFAULT_WORKSPACE_PATH = '/angular.json';
/**
 * A {@link workspaces.WorkspaceHost} backed by a Schematics {@link Tree} instance.
 */
class TreeWorkspaceHost {
    constructor(tree) {
        this.tree = tree;
    }
    async readFile(path) {
        return this.tree.readText(path);
    }
    async writeFile(path, data) {
        if (this.tree.exists(path)) {
            this.tree.overwrite(path, data);
        }
        else {
            this.tree.create(path, data);
        }
    }
    async isDirectory(path) {
        // approximate a directory check
        return !this.tree.exists(path) && this.tree.getDir(path).subfiles.length > 0;
    }
    async isFile(path) {
        return this.tree.exists(path);
    }
}
/**
 * Updates the workspace file (`angular.json`) found within the root of the schematic's tree.
 * The workspace object model can be directly modified within the provided updater function
 * with changes being written to the workspace file after the updater function returns.
 * The spacing and overall layout of the file (including comments) will be maintained where
 * possible when updating the file.
 *
 * @param updater An update function that can be used to modify the object model for the
 * workspace. A {@link WorkspaceDefinition} is provided as the first argument to the function.
 */
function updateWorkspace(updater) {
    return async (tree) => {
        const host = new TreeWorkspaceHost(tree);
        const { workspace } = await core_1.workspaces.readWorkspace(DEFAULT_WORKSPACE_PATH, host);
        const result = await updater(workspace);
        await core_1.workspaces.writeWorkspace(workspace, host);
        return result || schematics_1.noop;
    };
}
exports.updateWorkspace = updateWorkspace;
// TODO: This should be renamed `readWorkspace` once deep imports are restricted (already exported from `utility` with that name)
/**
 * Reads a workspace file (`angular.json`) from the provided {@link Tree} instance.
 *
 * @param tree A schematics {@link Tree} instance used to access the workspace file.
 * @param path The path where a workspace file should be found. If a file is specified, the file
 * path will be used. If a directory is specified, the file `angular.json` will be used from
 * within the specified directory. Defaults to `/angular.json`.
 * @returns A {@link WorkspaceDefinition} representing the workspace found at the specified path.
 */
async function getWorkspace(tree, path = DEFAULT_WORKSPACE_PATH) {
    const host = new TreeWorkspaceHost(tree);
    const { workspace } = await core_1.workspaces.readWorkspace(path, host);
    return workspace;
}
exports.getWorkspace = getWorkspace;
/**
 * Writes a workspace file (`angular.json`) to the provided {@link Tree} instance.
 * The spacing and overall layout of an exisitng file (including comments) will be maintained where
 * possible when writing the file.
 *
 * @param tree A schematics {@link Tree} instance used to access the workspace file.
 * @param workspace The {@link WorkspaceDefinition} to write.
 * @param path The path where a workspace file should be written. If a file is specified, the file
 * path will be used. If not provided, the definition's underlying file path stored during reading
 * will be used.
 */
async function writeWorkspace(tree, workspace, path) {
    const host = new TreeWorkspaceHost(tree);
    return core_1.workspaces.writeWorkspace(workspace, host, path);
}
exports.writeWorkspace = writeWorkspace;
/**
 * Build a default project path for generating.
 * @param project The project which will have its default path generated.
 */
function buildDefaultPath(project) {
    const root = project.sourceRoot ? `/${project.sourceRoot}/` : `/${project.root}/src/`;
    const projectDirName = project.extensions['projectType'] === workspace_models_1.ProjectType.Application ? 'app' : 'lib';
    return `${root}${projectDirName}`;
}
exports.buildDefaultPath = buildDefaultPath;
async function createDefaultPath(tree, projectName) {
    const workspace = await getWorkspace(tree);
    const project = workspace.projects.get(projectName);
    if (!project) {
        throw new Error(`Project "${projectName}" does not exist.`);
    }
    return buildDefaultPath(project);
}
exports.createDefaultPath = createDefaultPath;
function* allWorkspaceTargets(workspace) {
    for (const [projectName, project] of workspace.projects) {
        for (const [targetName, target] of project.targets) {
            yield [targetName, target, projectName, project];
        }
    }
}
exports.allWorkspaceTargets = allWorkspaceTargets;
function* allTargetOptions(target, skipBaseOptions = false) {
    if (!skipBaseOptions && target.options) {
        yield [undefined, target.options];
    }
    if (!target.configurations) {
        return;
    }
    for (const [name, options] of Object.entries(target.configurations)) {
        if (options !== undefined) {
            yield [name, options];
        }
    }
}
exports.allTargetOptions = allTargetOptions;
//# sourceMappingURL=data:application/json;base64,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