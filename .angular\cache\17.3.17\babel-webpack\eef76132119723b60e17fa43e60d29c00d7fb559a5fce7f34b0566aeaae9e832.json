{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config) {\n  const duration$ = timer(duration, scheduler);\n  return throttle(() => duration$, config);\n}\n//# sourceMappingURL=throttleTime.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}