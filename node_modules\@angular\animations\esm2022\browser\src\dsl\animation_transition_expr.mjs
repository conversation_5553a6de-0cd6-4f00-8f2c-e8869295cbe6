/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { invalidExpression, invalidTransitionAlias } from '../error_helpers';
export const ANY_STATE = '*';
export function parseTransitionExpr(transitionValue, errors) {
    const expressions = [];
    if (typeof transitionValue == 'string') {
        transitionValue
            .split(/\s*,\s*/)
            .forEach((str) => parseInnerTransitionStr(str, expressions, errors));
    }
    else {
        expressions.push(transitionValue);
    }
    return expressions;
}
function parseInnerTransitionStr(eventStr, expressions, errors) {
    if (eventStr[0] == ':') {
        const result = parseAnimationAlias(eventStr, errors);
        if (typeof result == 'function') {
            expressions.push(result);
            return;
        }
        eventStr = result;
    }
    const match = eventStr.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);
    if (match == null || match.length < 4) {
        errors.push(invalidExpression(eventStr));
        return expressions;
    }
    const fromState = match[1];
    const separator = match[2];
    const toState = match[3];
    expressions.push(makeLambdaFromStates(fromState, toState));
    const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;
    if (separator[0] == '<' && !isFullAnyStateExpr) {
        expressions.push(makeLambdaFromStates(toState, fromState));
    }
    return;
}
function parseAnimationAlias(alias, errors) {
    switch (alias) {
        case ':enter':
            return 'void => *';
        case ':leave':
            return '* => void';
        case ':increment':
            return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);
        case ':decrement':
            return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);
        default:
            errors.push(invalidTransitionAlias(alias));
            return '* => *';
    }
}
// DO NOT REFACTOR ... keep the follow set instantiations
// with the values intact (closure compiler for some reason
// removes follow-up lines that add the values outside of
// the constructor...
const TRUE_BOOLEAN_VALUES = new Set(['true', '1']);
const FALSE_BOOLEAN_VALUES = new Set(['false', '0']);
function makeLambdaFromStates(lhs, rhs) {
    const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);
    const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);
    return (fromState, toState) => {
        let lhsMatch = lhs == ANY_STATE || lhs == fromState;
        let rhsMatch = rhs == ANY_STATE || rhs == toState;
        if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {
            lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);
        }
        if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {
            rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);
        }
        return lhsMatch && rhsMatch;
    };
}
//# sourceMappingURL=data:application/json;base64,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