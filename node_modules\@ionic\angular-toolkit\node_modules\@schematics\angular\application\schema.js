"use strict";
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN devkit-admin build (or bazel build ...).
Object.defineProperty(exports, "__esModule", { value: true });
exports.ViewEncapsulation = exports.Style = void 0;
/**
 * The file extension or preprocessor to use for style files.
 */
var Style;
(function (Style) {
    Style["Css"] = "css";
    Style["Less"] = "less";
    Style["Sass"] = "sass";
    Style["Scss"] = "scss";
})(Style = exports.Style || (exports.Style = {}));
/**
 * The view encapsulation strategy to use in the new application.
 */
var ViewEncapsulation;
(function (ViewEncapsulation) {
    ViewEncapsulation["Emulated"] = "Emulated";
    ViewEncapsulation["None"] = "None";
    ViewEncapsulation["ShadowDom"] = "ShadowDom";
})(ViewEncapsulation = exports.ViewEncapsulation || (exports.ViewEncapsulation = {}));
//# sourceMappingURL=data:application/json;base64,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