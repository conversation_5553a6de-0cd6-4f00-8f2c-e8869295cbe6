import { ChangeDetectorRef, ElementRef, EventEmitter, Injector, NgZone } from '@angular/core';
import { ValueAccessor } from '@ionic/angular/common';
import type { RadioGroupChangeEventDetail, Components } from '@ionic/core/components';
import * as i0 from "@angular/core";
export declare class IonRadioGroup extends ValueAccessor {
    protected z: NgZone;
    protected el: HTMLElement;
    constructor(c: ChangeDetectorRef, r: ElementRef, z: NgZone, injector: Injector);
    handleIonChange(el: HTMLIonRadioGroupElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<IonRadioGroup, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<IonRadioGroup, "ion-radio-group", never, { "allowEmptySelection": "allowEmptySelection"; "name": "name"; "value": "value"; }, {}, never, ["*"], true>;
}
export declare interface IonRadioGroup extends Components.IonRadioGroup {
    /**
     * Emitted when the value has changed.
     */
    ionChange: EventEmitter<CustomEvent<RadioGroupChangeEventDetail>>;
}
