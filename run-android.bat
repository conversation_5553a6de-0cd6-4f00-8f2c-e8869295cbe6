@echo off
echo ========================================
echo    تشغيل تطبيق Ionic على Android
echo ========================================

echo.
echo فحص الأجهزة المتصلة...
adb devices

echo.
echo اختر طريقة التشغيل:
echo 1. تشغيل على جهاز متصل
echo 2. تشغيل على محاكي Android
echo 3. فتح في Android Studio
echo 4. تشغيل خادم التطوير (للاختبار في المتصفح)
echo.

set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" (
    echo تشغيل على الجهاز المتصل...
    call npx cap run android
) else if "%choice%"=="2" (
    echo تشغيل على المحاكي...
    call npx cap run android --target=emulator
) else if "%choice%"=="3" (
    echo فتح في Android Studio...
    call npx cap open android
) else if "%choice%"=="4" (
    echo تشغيل خادم التطوير...
    echo افتح المتصفح على: http://localhost:8100
    call npm start
) else (
    echo اختيار غير صحيح!
)

echo.
pause
