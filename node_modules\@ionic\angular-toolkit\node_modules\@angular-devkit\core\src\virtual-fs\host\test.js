"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.test = void 0;
const path_1 = require("../path");
const buffer_1 = require("./buffer");
const memory_1 = require("./memory");
const sync_1 = require("./sync");
// eslint-disable-next-line @typescript-eslint/no-namespace
var test;
(function (test) {
    class TestHost extends memory_1.SimpleMemoryHost {
        constructor(map = {}) {
            super();
            this._records = [];
            this._sync = null;
            for (const filePath of Object.getOwnPropertyNames(map)) {
                this._write((0, path_1.normalize)(filePath), (0, buffer_1.stringToFileBuffer)(map[filePath]));
            }
        }
        get records() {
            return [...this._records];
        }
        clearRecords() {
            this._records = [];
        }
        get files() {
            const sync = this.sync;
            function _visit(p) {
                return sync
                    .list(p)
                    .map((fragment) => (0, path_1.join)(p, fragment))
                    .reduce((files, path) => {
                    if (sync.isDirectory(path)) {
                        return files.concat(_visit(path));
                    }
                    else {
                        return files.concat(path);
                    }
                }, []);
            }
            return _visit((0, path_1.normalize)('/'));
        }
        get sync() {
            if (!this._sync) {
                this._sync = new sync_1.SyncDelegateHost(this);
            }
            return this._sync;
        }
        clone() {
            const newHost = new TestHost();
            newHost._cache = new Map(this._cache);
            return newHost;
        }
        // Override parents functions to keep a record of all operators that were done.
        _write(path, content) {
            this._records.push({ kind: 'write', path });
            return super._write(path, content);
        }
        _read(path) {
            this._records.push({ kind: 'read', path });
            return super._read(path);
        }
        _delete(path) {
            this._records.push({ kind: 'delete', path });
            return super._delete(path);
        }
        _rename(from, to) {
            this._records.push({ kind: 'rename', from, to });
            return super._rename(from, to);
        }
        _list(path) {
            this._records.push({ kind: 'list', path });
            return super._list(path);
        }
        _exists(path) {
            this._records.push({ kind: 'exists', path });
            return super._exists(path);
        }
        _isDirectory(path) {
            this._records.push({ kind: 'isDirectory', path });
            return super._isDirectory(path);
        }
        _isFile(path) {
            this._records.push({ kind: 'isFile', path });
            return super._isFile(path);
        }
        _stat(path) {
            this._records.push({ kind: 'stat', path });
            return super._stat(path);
        }
        _watch(path, options) {
            this._records.push({ kind: 'watch', path });
            return super._watch(path, options);
        }
        $write(path, content) {
            return super._write((0, path_1.normalize)(path), (0, buffer_1.stringToFileBuffer)(content));
        }
        $read(path) {
            return (0, buffer_1.fileBufferToString)(super._read((0, path_1.normalize)(path)));
        }
        $list(path) {
            return super._list((0, path_1.normalize)(path));
        }
        $exists(path) {
            return super._exists((0, path_1.normalize)(path));
        }
        $isDirectory(path) {
            return super._isDirectory((0, path_1.normalize)(path));
        }
        $isFile(path) {
            return super._isFile((0, path_1.normalize)(path));
        }
    }
    test.TestHost = TestHost;
})(test = exports.test || (exports.test = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGVzdC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2FuZ3VsYXJfZGV2a2l0L2NvcmUvc3JjL3ZpcnR1YWwtZnMvaG9zdC90ZXN0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7Ozs7O0dBTUc7OztBQUdILGtDQUE4RDtBQUM5RCxxQ0FBa0U7QUFFbEUscUNBQW1FO0FBQ25FLGlDQUEwQztBQUUxQywyREFBMkQ7QUFDM0QsSUFBaUIsSUFBSSxDQXFKcEI7QUFySkQsV0FBaUIsSUFBSTtJQXFCbkIsTUFBYSxRQUFTLFNBQVEseUJBQWdCO1FBSTVDLFlBQVksTUFBa0MsRUFBRTtZQUM5QyxLQUFLLEVBQUUsQ0FBQztZQUpBLGFBQVEsR0FBb0IsRUFBRSxDQUFDO1lBQy9CLFVBQUssR0FBZ0MsSUFBSSxDQUFDO1lBS2xELEtBQUssTUFBTSxRQUFRLElBQUksTUFBTSxDQUFDLG1CQUFtQixDQUFDLEdBQUcsQ0FBQyxFQUFFO2dCQUN0RCxJQUFJLENBQUMsTUFBTSxDQUFDLElBQUEsZ0JBQVMsRUFBQyxRQUFRLENBQUMsRUFBRSxJQUFBLDJCQUFrQixFQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7YUFDckU7UUFDSCxDQUFDO1FBRUQsSUFBSSxPQUFPO1lBQ1QsT0FBTyxDQUFDLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxDQUFDO1FBQzVCLENBQUM7UUFDRCxZQUFZO1lBQ1YsSUFBSSxDQUFDLFFBQVEsR0FBRyxFQUFFLENBQUM7UUFDckIsQ0FBQztRQUVELElBQUksS0FBSztZQUNQLE1BQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxJQUFJLENBQUM7WUFDdkIsU0FBUyxNQUFNLENBQUMsQ0FBTztnQkFDckIsT0FBTyxJQUFJO3FCQUNSLElBQUksQ0FBQyxDQUFDLENBQUM7cUJBQ1AsR0FBRyxDQUFDLENBQUMsUUFBUSxFQUFFLEVBQUUsQ0FBQyxJQUFBLFdBQUksRUFBQyxDQUFDLEVBQUUsUUFBUSxDQUFDLENBQUM7cUJBQ3BDLE1BQU0sQ0FBQyxDQUFDLEtBQUssRUFBRSxJQUFJLEVBQUUsRUFBRTtvQkFDdEIsSUFBSSxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxFQUFFO3dCQUMxQixPQUFPLEtBQUssQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7cUJBQ25DO3lCQUFNO3dCQUNMLE9BQU8sS0FBSyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQztxQkFDM0I7Z0JBQ0gsQ0FBQyxFQUFFLEVBQVksQ0FBQyxDQUFDO1lBQ3JCLENBQUM7WUFFRCxPQUFPLE1BQU0sQ0FBQyxJQUFBLGdCQUFTLEVBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQztRQUNoQyxDQUFDO1FBRUQsSUFBSSxJQUFJO1lBQ04sSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUU7Z0JBQ2YsSUFBSSxDQUFDLEtBQUssR0FBRyxJQUFJLHVCQUFnQixDQUFLLElBQUksQ0FBQyxDQUFDO2FBQzdDO1lBRUQsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDO1FBQ3BCLENBQUM7UUFFRCxLQUFLO1lBQ0gsTUFBTSxPQUFPLEdBQUcsSUFBSSxRQUFRLEVBQUUsQ0FBQztZQUMvQixPQUFPLENBQUMsTUFBTSxHQUFHLElBQUksR0FBRyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsQ0FBQztZQUV0QyxPQUFPLE9BQU8sQ0FBQztRQUNqQixDQUFDO1FBRUQsK0VBQStFO1FBQzVELE1BQU0sQ0FBQyxJQUFVLEVBQUUsT0FBbUI7WUFDdkQsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7WUFFNUMsT0FBTyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxPQUFPLENBQUMsQ0FBQztRQUNyQyxDQUFDO1FBQ2tCLEtBQUssQ0FBQyxJQUFVO1lBQ2pDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1lBRTNDLE9BQU8sS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUMzQixDQUFDO1FBQ2tCLE9BQU8sQ0FBQyxJQUFVO1lBQ25DLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1lBRTdDLE9BQU8sS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUM3QixDQUFDO1FBQ2tCLE9BQU8sQ0FBQyxJQUFVLEVBQUUsRUFBUTtZQUM3QyxJQUFJLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxFQUFFLElBQUksRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRSxDQUFDLENBQUM7WUFFakQsT0FBTyxLQUFLLENBQUMsT0FBTyxDQUFDLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQztRQUNqQyxDQUFDO1FBQ2tCLEtBQUssQ0FBQyxJQUFVO1lBQ2pDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1lBRTNDLE9BQU8sS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUMzQixDQUFDO1FBQ2tCLE9BQU8sQ0FBQyxJQUFVO1lBQ25DLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1lBRTdDLE9BQU8sS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUM3QixDQUFDO1FBQ2tCLFlBQVksQ0FBQyxJQUFVO1lBQ3hDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLGFBQWEsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1lBRWxELE9BQU8sS0FBSyxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNsQyxDQUFDO1FBQ2tCLE9BQU8sQ0FBQyxJQUFVO1lBQ25DLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1lBRTdDLE9BQU8sS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUM3QixDQUFDO1FBQ2tCLEtBQUssQ0FBQyxJQUFVO1lBQ2pDLElBQUksQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDO1lBRTNDLE9BQU8sS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUMzQixDQUFDO1FBQ2tCLE1BQU0sQ0FBQyxJQUFVLEVBQUUsT0FBMEI7WUFDOUQsSUFBSSxDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUMsRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUM7WUFFNUMsT0FBTyxLQUFLLENBQUMsTUFBTSxDQUFDLElBQUksRUFBRSxPQUFPLENBQUMsQ0FBQztRQUNyQyxDQUFDO1FBRUQsTUFBTSxDQUFDLElBQVksRUFBRSxPQUFlO1lBQ2xDLE9BQU8sS0FBSyxDQUFDLE1BQU0sQ0FBQyxJQUFBLGdCQUFTLEVBQUMsSUFBSSxDQUFDLEVBQUUsSUFBQSwyQkFBa0IsRUFBQyxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQ3BFLENBQUM7UUFFRCxLQUFLLENBQUMsSUFBWTtZQUNoQixPQUFPLElBQUEsMkJBQWtCLEVBQUMsS0FBSyxDQUFDLEtBQUssQ0FBQyxJQUFBLGdCQUFTLEVBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzFELENBQUM7UUFFRCxLQUFLLENBQUMsSUFBWTtZQUNoQixPQUFPLEtBQUssQ0FBQyxLQUFLLENBQUMsSUFBQSxnQkFBUyxFQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7UUFDdEMsQ0FBQztRQUVELE9BQU8sQ0FBQyxJQUFZO1lBQ2xCLE9BQU8sS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFBLGdCQUFTLEVBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztRQUN4QyxDQUFDO1FBRUQsWUFBWSxDQUFDLElBQVk7WUFDdkIsT0FBTyxLQUFLLENBQUMsWUFBWSxDQUFDLElBQUEsZ0JBQVMsRUFBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO1FBQzdDLENBQUM7UUFFRCxPQUFPLENBQUMsSUFBWTtZQUNsQixPQUFPLEtBQUssQ0FBQyxPQUFPLENBQUMsSUFBQSxnQkFBUyxFQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7UUFDeEMsQ0FBQztLQUNGO0lBL0hZLGFBQVEsV0ErSHBCLENBQUE7QUFDSCxDQUFDLEVBckpnQixJQUFJLEdBQUosWUFBSSxLQUFKLFlBQUksUUFxSnBCIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbmltcG9ydCB7IE9ic2VydmFibGUgfSBmcm9tICdyeGpzJztcbmltcG9ydCB7IFBhdGgsIFBhdGhGcmFnbWVudCwgam9pbiwgbm9ybWFsaXplIH0gZnJvbSAnLi4vcGF0aCc7XG5pbXBvcnQgeyBmaWxlQnVmZmVyVG9TdHJpbmcsIHN0cmluZ1RvRmlsZUJ1ZmZlciB9IGZyb20gJy4vYnVmZmVyJztcbmltcG9ydCB7IEZpbGVCdWZmZXIsIEhvc3RXYXRjaEV2ZW50LCBIb3N0V2F0Y2hPcHRpb25zLCBTdGF0cyB9IGZyb20gJy4vaW50ZXJmYWNlJztcbmltcG9ydCB7IFNpbXBsZU1lbW9yeUhvc3QsIFNpbXBsZU1lbW9yeUhvc3RTdGF0cyB9IGZyb20gJy4vbWVtb3J5JztcbmltcG9ydCB7IFN5bmNEZWxlZ2F0ZUhvc3QgfSBmcm9tICcuL3N5bmMnO1xuXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLW5hbWVzcGFjZVxuZXhwb3J0IG5hbWVzcGFjZSB0ZXN0IHtcbiAgZXhwb3J0IHR5cGUgVGVzdExvZ1JlY29yZCA9XG4gICAgfCB7XG4gICAgICAgIGtpbmQ6XG4gICAgICAgICAgfCAnd3JpdGUnXG4gICAgICAgICAgfCAncmVhZCdcbiAgICAgICAgICB8ICdkZWxldGUnXG4gICAgICAgICAgfCAnbGlzdCdcbiAgICAgICAgICB8ICdleGlzdHMnXG4gICAgICAgICAgfCAnaXNEaXJlY3RvcnknXG4gICAgICAgICAgfCAnaXNGaWxlJ1xuICAgICAgICAgIHwgJ3N0YXQnXG4gICAgICAgICAgfCAnd2F0Y2gnO1xuICAgICAgICBwYXRoOiBQYXRoO1xuICAgICAgfVxuICAgIHwge1xuICAgICAgICBraW5kOiAncmVuYW1lJztcbiAgICAgICAgZnJvbTogUGF0aDtcbiAgICAgICAgdG86IFBhdGg7XG4gICAgICB9O1xuXG4gIGV4cG9ydCBjbGFzcyBUZXN0SG9zdCBleHRlbmRzIFNpbXBsZU1lbW9yeUhvc3Qge1xuICAgIHByb3RlY3RlZCBfcmVjb3JkczogVGVzdExvZ1JlY29yZFtdID0gW107XG4gICAgcHJvdGVjdGVkIF9zeW5jOiBTeW5jRGVsZWdhdGVIb3N0PHt9PiB8IG51bGwgPSBudWxsO1xuXG4gICAgY29uc3RydWN0b3IobWFwOiB7IFtwYXRoOiBzdHJpbmddOiBzdHJpbmcgfSA9IHt9KSB7XG4gICAgICBzdXBlcigpO1xuXG4gICAgICBmb3IgKGNvbnN0IGZpbGVQYXRoIG9mIE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzKG1hcCkpIHtcbiAgICAgICAgdGhpcy5fd3JpdGUobm9ybWFsaXplKGZpbGVQYXRoKSwgc3RyaW5nVG9GaWxlQnVmZmVyKG1hcFtmaWxlUGF0aF0pKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBnZXQgcmVjb3JkcygpOiBUZXN0TG9nUmVjb3JkW10ge1xuICAgICAgcmV0dXJuIFsuLi50aGlzLl9yZWNvcmRzXTtcbiAgICB9XG4gICAgY2xlYXJSZWNvcmRzKCkge1xuICAgICAgdGhpcy5fcmVjb3JkcyA9IFtdO1xuICAgIH1cblxuICAgIGdldCBmaWxlcygpOiBQYXRoW10ge1xuICAgICAgY29uc3Qgc3luYyA9IHRoaXMuc3luYztcbiAgICAgIGZ1bmN0aW9uIF92aXNpdChwOiBQYXRoKTogUGF0aFtdIHtcbiAgICAgICAgcmV0dXJuIHN5bmNcbiAgICAgICAgICAubGlzdChwKVxuICAgICAgICAgIC5tYXAoKGZyYWdtZW50KSA9PiBqb2luKHAsIGZyYWdtZW50KSlcbiAgICAgICAgICAucmVkdWNlKChmaWxlcywgcGF0aCkgPT4ge1xuICAgICAgICAgICAgaWYgKHN5bmMuaXNEaXJlY3RvcnkocGF0aCkpIHtcbiAgICAgICAgICAgICAgcmV0dXJuIGZpbGVzLmNvbmNhdChfdmlzaXQocGF0aCkpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgcmV0dXJuIGZpbGVzLmNvbmNhdChwYXRoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LCBbXSBhcyBQYXRoW10pO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gX3Zpc2l0KG5vcm1hbGl6ZSgnLycpKTtcbiAgICB9XG5cbiAgICBnZXQgc3luYygpIHtcbiAgICAgIGlmICghdGhpcy5fc3luYykge1xuICAgICAgICB0aGlzLl9zeW5jID0gbmV3IFN5bmNEZWxlZ2F0ZUhvc3Q8e30+KHRoaXMpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdGhpcy5fc3luYztcbiAgICB9XG5cbiAgICBjbG9uZSgpIHtcbiAgICAgIGNvbnN0IG5ld0hvc3QgPSBuZXcgVGVzdEhvc3QoKTtcbiAgICAgIG5ld0hvc3QuX2NhY2hlID0gbmV3IE1hcCh0aGlzLl9jYWNoZSk7XG5cbiAgICAgIHJldHVybiBuZXdIb3N0O1xuICAgIH1cblxuICAgIC8vIE92ZXJyaWRlIHBhcmVudHMgZnVuY3Rpb25zIHRvIGtlZXAgYSByZWNvcmQgb2YgYWxsIG9wZXJhdG9ycyB0aGF0IHdlcmUgZG9uZS5cbiAgICBwcm90ZWN0ZWQgb3ZlcnJpZGUgX3dyaXRlKHBhdGg6IFBhdGgsIGNvbnRlbnQ6IEZpbGVCdWZmZXIpIHtcbiAgICAgIHRoaXMuX3JlY29yZHMucHVzaCh7IGtpbmQ6ICd3cml0ZScsIHBhdGggfSk7XG5cbiAgICAgIHJldHVybiBzdXBlci5fd3JpdGUocGF0aCwgY29udGVudCk7XG4gICAgfVxuICAgIHByb3RlY3RlZCBvdmVycmlkZSBfcmVhZChwYXRoOiBQYXRoKSB7XG4gICAgICB0aGlzLl9yZWNvcmRzLnB1c2goeyBraW5kOiAncmVhZCcsIHBhdGggfSk7XG5cbiAgICAgIHJldHVybiBzdXBlci5fcmVhZChwYXRoKTtcbiAgICB9XG4gICAgcHJvdGVjdGVkIG92ZXJyaWRlIF9kZWxldGUocGF0aDogUGF0aCkge1xuICAgICAgdGhpcy5fcmVjb3Jkcy5wdXNoKHsga2luZDogJ2RlbGV0ZScsIHBhdGggfSk7XG5cbiAgICAgIHJldHVybiBzdXBlci5fZGVsZXRlKHBhdGgpO1xuICAgIH1cbiAgICBwcm90ZWN0ZWQgb3ZlcnJpZGUgX3JlbmFtZShmcm9tOiBQYXRoLCB0bzogUGF0aCkge1xuICAgICAgdGhpcy5fcmVjb3Jkcy5wdXNoKHsga2luZDogJ3JlbmFtZScsIGZyb20sIHRvIH0pO1xuXG4gICAgICByZXR1cm4gc3VwZXIuX3JlbmFtZShmcm9tLCB0byk7XG4gICAgfVxuICAgIHByb3RlY3RlZCBvdmVycmlkZSBfbGlzdChwYXRoOiBQYXRoKTogUGF0aEZyYWdtZW50W10ge1xuICAgICAgdGhpcy5fcmVjb3Jkcy5wdXNoKHsga2luZDogJ2xpc3QnLCBwYXRoIH0pO1xuXG4gICAgICByZXR1cm4gc3VwZXIuX2xpc3QocGF0aCk7XG4gICAgfVxuICAgIHByb3RlY3RlZCBvdmVycmlkZSBfZXhpc3RzKHBhdGg6IFBhdGgpIHtcbiAgICAgIHRoaXMuX3JlY29yZHMucHVzaCh7IGtpbmQ6ICdleGlzdHMnLCBwYXRoIH0pO1xuXG4gICAgICByZXR1cm4gc3VwZXIuX2V4aXN0cyhwYXRoKTtcbiAgICB9XG4gICAgcHJvdGVjdGVkIG92ZXJyaWRlIF9pc0RpcmVjdG9yeShwYXRoOiBQYXRoKSB7XG4gICAgICB0aGlzLl9yZWNvcmRzLnB1c2goeyBraW5kOiAnaXNEaXJlY3RvcnknLCBwYXRoIH0pO1xuXG4gICAgICByZXR1cm4gc3VwZXIuX2lzRGlyZWN0b3J5KHBhdGgpO1xuICAgIH1cbiAgICBwcm90ZWN0ZWQgb3ZlcnJpZGUgX2lzRmlsZShwYXRoOiBQYXRoKSB7XG4gICAgICB0aGlzLl9yZWNvcmRzLnB1c2goeyBraW5kOiAnaXNGaWxlJywgcGF0aCB9KTtcblxuICAgICAgcmV0dXJuIHN1cGVyLl9pc0ZpbGUocGF0aCk7XG4gICAgfVxuICAgIHByb3RlY3RlZCBvdmVycmlkZSBfc3RhdChwYXRoOiBQYXRoKTogU3RhdHM8U2ltcGxlTWVtb3J5SG9zdFN0YXRzPiB8IG51bGwge1xuICAgICAgdGhpcy5fcmVjb3Jkcy5wdXNoKHsga2luZDogJ3N0YXQnLCBwYXRoIH0pO1xuXG4gICAgICByZXR1cm4gc3VwZXIuX3N0YXQocGF0aCk7XG4gICAgfVxuICAgIHByb3RlY3RlZCBvdmVycmlkZSBfd2F0Y2gocGF0aDogUGF0aCwgb3B0aW9ucz86IEhvc3RXYXRjaE9wdGlvbnMpOiBPYnNlcnZhYmxlPEhvc3RXYXRjaEV2ZW50PiB7XG4gICAgICB0aGlzLl9yZWNvcmRzLnB1c2goeyBraW5kOiAnd2F0Y2gnLCBwYXRoIH0pO1xuXG4gICAgICByZXR1cm4gc3VwZXIuX3dhdGNoKHBhdGgsIG9wdGlvbnMpO1xuICAgIH1cblxuICAgICR3cml0ZShwYXRoOiBzdHJpbmcsIGNvbnRlbnQ6IHN0cmluZykge1xuICAgICAgcmV0dXJuIHN1cGVyLl93cml0ZShub3JtYWxpemUocGF0aCksIHN0cmluZ1RvRmlsZUJ1ZmZlcihjb250ZW50KSk7XG4gICAgfVxuXG4gICAgJHJlYWQocGF0aDogc3RyaW5nKTogc3RyaW5nIHtcbiAgICAgIHJldHVybiBmaWxlQnVmZmVyVG9TdHJpbmcoc3VwZXIuX3JlYWQobm9ybWFsaXplKHBhdGgpKSk7XG4gICAgfVxuXG4gICAgJGxpc3QocGF0aDogc3RyaW5nKTogUGF0aEZyYWdtZW50W10ge1xuICAgICAgcmV0dXJuIHN1cGVyLl9saXN0KG5vcm1hbGl6ZShwYXRoKSk7XG4gICAgfVxuXG4gICAgJGV4aXN0cyhwYXRoOiBzdHJpbmcpIHtcbiAgICAgIHJldHVybiBzdXBlci5fZXhpc3RzKG5vcm1hbGl6ZShwYXRoKSk7XG4gICAgfVxuXG4gICAgJGlzRGlyZWN0b3J5KHBhdGg6IHN0cmluZykge1xuICAgICAgcmV0dXJuIHN1cGVyLl9pc0RpcmVjdG9yeShub3JtYWxpemUocGF0aCkpO1xuICAgIH1cblxuICAgICRpc0ZpbGUocGF0aDogc3RyaW5nKSB7XG4gICAgICByZXR1cm4gc3VwZXIuX2lzRmlsZShub3JtYWxpemUocGF0aCkpO1xuICAgIH1cbiAgfVxufVxuIl19